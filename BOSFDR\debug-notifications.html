<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Notifications Frontend</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 200px; }
        .notification-item { border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .unread { background-color: #f0f8ff; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <h1>🔔 Debug Notifications Frontend</h1>
    
    <div class="test-section">
        <h3>1. Authentication</h3>
        <input type="text" id="username" placeholder="Username" value="admin12345">
        <input type="password" id="password" placeholder="Password" value="admin123">
        <button onclick="testLogin()">Login</button>
        <div id="auth-log" class="log"></div>
    </div>

    <div class="test-section">
        <h3>2. Notification Service Test</h3>
        <button onclick="testNotificationService()">Test Notification Service</button>
        <button onclick="testGetNotifications()">Get Notifications</button>
        <button onclick="testGetUnreadCount()">Get Unread Count</button>
        <div id="service-log" class="log"></div>
    </div>

    <div class="test-section">
        <h3>3. Direct API Test</h3>
        <button onclick="testDirectAPI()">Test Direct API</button>
        <div id="api-log" class="log"></div>
    </div>

    <div class="test-section">
        <h3>4. Notification Display Test</h3>
        <button onclick="displayNotifications()">Display Notifications</button>
        <div id="notifications-container"></div>
        <div id="display-log" class="log"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:7000/api';
        let adminToken = null;
        let notifications = [];

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            element.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            element.scrollTop = element.scrollHeight;
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            log('auth-log', 'Testing admin login...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/admin/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    adminToken = data.data.token;
                    localStorage.setItem('adminToken', adminToken);
                    log('auth-log', '✅ Admin login successful!', 'success');
                    log('auth-log', `Token: ${adminToken.substring(0, 30)}...`, 'info');
                } else {
                    log('auth-log', `❌ Admin login failed: ${data.message}`, 'error');
                }
            } catch (error) {
                log('auth-log', `❌ Admin login error: ${error.message}`, 'error');
            }
        }

        async function testNotificationService() {
            log('service-log', 'Testing notification service...', 'info');
            
            // Check if notification service is available
            if (typeof window.notificationService !== 'undefined') {
                log('service-log', '✅ Notification service is available', 'success');
            } else {
                log('service-log', '❌ Notification service not available - this is expected in standalone test', 'error');
            }
            
            // Test localStorage token
            const token = localStorage.getItem('adminToken');
            if (token) {
                log('service-log', `✅ Admin token found: ${token.substring(0, 30)}...`, 'success');
            } else {
                log('service-log', '❌ No admin token found', 'error');
            }
        }

        async function testGetNotifications() {
            if (!adminToken) {
                log('service-log', '❌ Please login first', 'error');
                return;
            }

            log('service-log', 'Testing get notifications...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/notifications?page=1&limit=10`, {
                    headers: { 'Authorization': `Bearer ${adminToken}` }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    notifications = data.data.notifications;
                    log('service-log', `✅ Got ${notifications.length} notifications`, 'success');
                    log('service-log', `Response structure: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    log('service-log', `❌ Get notifications failed: ${data.message}`, 'error');
                }
            } catch (error) {
                log('service-log', `❌ Get notifications error: ${error.message}`, 'error');
            }
        }

        async function testGetUnreadCount() {
            if (!adminToken) {
                log('service-log', '❌ Please login first', 'error');
                return;
            }

            log('service-log', 'Testing get unread count...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/notifications/unread-count`, {
                    headers: { 'Authorization': `Bearer ${adminToken}` }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    log('service-log', `✅ Unread count: ${data.data.count}`, 'success');
                } else {
                    log('service-log', `❌ Get unread count failed: ${data.message}`, 'error');
                }
            } catch (error) {
                log('service-log', `❌ Get unread count error: ${error.message}`, 'error');
            }
        }

        async function testDirectAPI() {
            log('api-log', 'Testing direct API calls...', 'info');
            
            if (!adminToken) {
                log('api-log', '❌ Please login first', 'error');
                return;
            }

            try {
                // Test 1: Unread count
                const countResponse = await fetch(`${API_BASE}/notifications/unread-count`, {
                    headers: { 'Authorization': `Bearer ${adminToken}` }
                });
                const countData = await countResponse.json();
                log('api-log', `Unread count: ${countData.data.count}`, 'success');

                // Test 2: Notifications list
                const listResponse = await fetch(`${API_BASE}/notifications`, {
                    headers: { 'Authorization': `Bearer ${adminToken}` }
                });
                const listData = await listResponse.json();
                log('api-log', `Notifications count: ${listData.data.notifications.length}`, 'success');
                
                // Test 3: Check data structure
                if (listData.data.notifications.length > 0) {
                    const firstNotif = listData.data.notifications[0];
                    log('api-log', `First notification: ${firstNotif.title} - ${firstNotif.message}`, 'info');
                    log('api-log', `Is read: ${firstNotif.is_read}, Type: ${firstNotif.type}`, 'info');
                }

            } catch (error) {
                log('api-log', `❌ Direct API error: ${error.message}`, 'error');
            }
        }

        async function displayNotifications() {
            log('display-log', 'Displaying notifications...', 'info');
            
            if (notifications.length === 0) {
                log('display-log', 'No notifications to display. Run "Get Notifications" first.', 'error');
                return;
            }

            const container = document.getElementById('notifications-container');
            container.innerHTML = '';

            notifications.forEach(notification => {
                const div = document.createElement('div');
                div.className = `notification-item ${notification.is_read ? '' : 'unread'}`;
                div.innerHTML = `
                    <h5>${notification.title}</h5>
                    <p>${notification.message}</p>
                    <small>Type: ${notification.type} | Priority: ${notification.priority} | ${notification.is_read ? 'Read' : 'Unread'}</small>
                    <br><small>Created: ${new Date(notification.created_at).toLocaleString()}</small>
                `;
                container.appendChild(div);
            });

            log('display-log', `✅ Displayed ${notifications.length} notifications`, 'success');
        }

        // Auto-load admin token if available
        window.addEventListener('load', () => {
            const token = localStorage.getItem('adminToken');
            if (token) {
                adminToken = token;
                log('auth-log', `Admin token loaded: ${token.substring(0, 30)}...`, 'success');
            }
        });
    </script>
</body>
</html>
