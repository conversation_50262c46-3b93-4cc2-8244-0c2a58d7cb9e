{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, withModifiers as _withModifiers, renderList as _renderList, Fragment as _Fragment, vModelSelect as _vModelSelect, withDirectives as _withDirectives, vModelText as _vModelText, vModelRadio as _vModelRadio, vModelCheckbox as _vModelCheckbox, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"barangay-clearance-request\"\n};\nconst _hoisted_2 = {\n  class: \"page-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-content\"\n};\nconst _hoisted_4 = {\n  class: \"progress-steps\"\n};\nconst _hoisted_5 = {\n  class: \"form-container\"\n};\nconst _hoisted_6 = {\n  key: 0,\n  class: \"form-step\"\n};\nconst _hoisted_7 = {\n  class: \"profile-preview\"\n};\nconst _hoisted_8 = {\n  class: \"profile-card\"\n};\nconst _hoisted_9 = {\n  class: \"profile-info\"\n};\nconst _hoisted_10 = {\n  class: \"info-grid\"\n};\nconst _hoisted_11 = {\n  class: \"info-item\"\n};\nconst _hoisted_12 = {\n  class: \"info-item\"\n};\nconst _hoisted_13 = {\n  class: \"info-item\"\n};\nconst _hoisted_14 = {\n  class: \"info-item\"\n};\nconst _hoisted_15 = {\n  class: \"info-item\"\n};\nconst _hoisted_16 = {\n  class: \"info-item\"\n};\nconst _hoisted_17 = {\n  class: \"info-item\"\n};\nconst _hoisted_18 = {\n  class: \"info-item\"\n};\nconst _hoisted_19 = {\n  class: \"profile-actions\"\n};\nconst _hoisted_20 = {\n  class: \"form-section\"\n};\nconst _hoisted_21 = {\n  class: \"document-upload-group\"\n};\nconst _hoisted_22 = {\n  key: 0,\n  class: \"upload-placeholder\"\n};\nconst _hoisted_23 = {\n  key: 1,\n  class: \"uploaded-file\"\n};\nconst _hoisted_24 = {\n  class: \"document-upload-group\"\n};\nconst _hoisted_25 = {\n  key: 0,\n  class: \"upload-placeholder\"\n};\nconst _hoisted_26 = {\n  key: 1,\n  class: \"uploaded-file\"\n};\nconst _hoisted_27 = {\n  class: \"document-upload-group\"\n};\nconst _hoisted_28 = {\n  key: 0,\n  class: \"upload-placeholder\"\n};\nconst _hoisted_29 = {\n  key: 1,\n  class: \"uploaded-file\"\n};\nconst _hoisted_30 = {\n  key: 1,\n  class: \"form-step\"\n};\nconst _hoisted_31 = {\n  class: \"form-section\"\n};\nconst _hoisted_32 = {\n  class: \"form-group\"\n};\nconst _hoisted_33 = [\"value\"];\nconst _hoisted_34 = {\n  class: \"form-group\"\n};\nconst _hoisted_35 = {\n  class: \"form-group\"\n};\nconst _hoisted_36 = {\n  class: \"radio-group\"\n};\nconst _hoisted_37 = {\n  class: \"radio-option\"\n};\nconst _hoisted_38 = {\n  class: \"radio-option\"\n};\nconst _hoisted_39 = {\n  key: 0,\n  class: \"form-group\"\n};\nconst _hoisted_40 = {\n  class: \"form-group\"\n};\nconst _hoisted_41 = {\n  class: \"radio-group\"\n};\nconst _hoisted_42 = {\n  class: \"radio-option\"\n};\nconst _hoisted_43 = {\n  class: \"radio-option\"\n};\nconst _hoisted_44 = {\n  class: \"form-group\"\n};\nconst _hoisted_45 = {\n  key: 2,\n  class: \"form-step\"\n};\nconst _hoisted_46 = {\n  class: \"fee-summary\"\n};\nconst _hoisted_47 = {\n  class: \"fee-card\"\n};\nconst _hoisted_48 = {\n  class: \"fee-items\"\n};\nconst _hoisted_49 = {\n  class: \"fee-item\"\n};\nconst _hoisted_50 = {\n  class: \"fee-item total\"\n};\nconst _hoisted_51 = {\n  class: \"form-section\"\n};\nconst _hoisted_52 = {\n  class: \"payment-methods\"\n};\nconst _hoisted_53 = [\"onClick\"];\nconst _hoisted_54 = {\n  class: \"payment-icon\"\n};\nconst _hoisted_55 = {\n  class: \"payment-info\"\n};\nconst _hoisted_56 = {\n  key: 0\n};\nconst _hoisted_57 = {\n  class: \"payment-radio\"\n};\nconst _hoisted_58 = [\"value\"];\nconst _hoisted_59 = {\n  key: 3,\n  class: \"form-step\"\n};\nconst _hoisted_60 = {\n  class: \"review-sections\"\n};\nconst _hoisted_61 = {\n  class: \"review-section\"\n};\nconst _hoisted_62 = {\n  class: \"review-grid\"\n};\nconst _hoisted_63 = {\n  class: \"review-item\"\n};\nconst _hoisted_64 = {\n  class: \"review-item\"\n};\nconst _hoisted_65 = {\n  class: \"review-item\"\n};\nconst _hoisted_66 = {\n  class: \"review-section\"\n};\nconst _hoisted_67 = {\n  class: \"review-grid\"\n};\nconst _hoisted_68 = {\n  class: \"review-item\"\n};\nconst _hoisted_69 = {\n  class: \"review-item\"\n};\nconst _hoisted_70 = {\n  class: \"review-item\"\n};\nconst _hoisted_71 = {\n  class: \"review-section\"\n};\nconst _hoisted_72 = {\n  class: \"review-grid\"\n};\nconst _hoisted_73 = {\n  class: \"review-item\"\n};\nconst _hoisted_74 = {\n  class: \"review-item\"\n};\nconst _hoisted_75 = {\n  class: \"review-item\"\n};\nconst _hoisted_76 = {\n  key: 0,\n  class: \"review-item\"\n};\nconst _hoisted_77 = {\n  class: \"review-section\"\n};\nconst _hoisted_78 = {\n  class: \"review-grid\"\n};\nconst _hoisted_79 = {\n  class: \"review-item\"\n};\nconst _hoisted_80 = {\n  class: \"review-item\"\n};\nconst _hoisted_81 = {\n  class: \"amount\"\n};\nconst _hoisted_82 = {\n  class: \"terms-section\"\n};\nconst _hoisted_83 = {\n  class: \"checkbox-option\"\n};\nconst _hoisted_84 = {\n  class: \"form-actions\"\n};\nconst _hoisted_85 = [\"disabled\"];\nconst _hoisted_86 = [\"disabled\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" Header \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[33] || (_cache[33] = _createElementVNode(\"div\", {\n    class: \"header-main\"\n  }, [_createElementVNode(\"h1\", {\n    class: \"page-title\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-certificate\"\n  }), _createTextVNode(\" Barangay Clearance Request \")]), _createElementVNode(\"p\", {\n    class: \"page-description\"\n  }, \" Apply for your Barangay Clearance certificate online \")], -1 /* HOISTED */)), _createElementVNode(\"button\", {\n    class: \"back-btn\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))\n  }, _cache[32] || (_cache[32] = [_createElementVNode(\"i\", {\n    class: \"fas fa-arrow-left\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Back \")]))])]), _createCommentVNode(\" Progress Steps \"), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", {\n    class: _normalizeClass([\"step\", {\n      active: $data.currentStep >= 1,\n      completed: $data.currentStep > 1\n    }])\n  }, _cache[34] || (_cache[34] = [_createElementVNode(\"div\", {\n    class: \"step-number\"\n  }, \"1\", -1 /* HOISTED */), _createElementVNode(\"span\", {\n    class: \"step-label\"\n  }, \"Upload Documents\", -1 /* HOISTED */)]), 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"step\", {\n      active: $data.currentStep >= 2,\n      completed: $data.currentStep > 2\n    }])\n  }, _cache[35] || (_cache[35] = [_createElementVNode(\"div\", {\n    class: \"step-number\"\n  }, \"2\", -1 /* HOISTED */), _createElementVNode(\"span\", {\n    class: \"step-label\"\n  }, \"Purpose & Details\", -1 /* HOISTED */)]), 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"step\", {\n      active: $data.currentStep >= 3,\n      completed: $data.currentStep > 3\n    }])\n  }, _cache[36] || (_cache[36] = [_createElementVNode(\"div\", {\n    class: \"step-number\"\n  }, \"3\", -1 /* HOISTED */), _createElementVNode(\"span\", {\n    class: \"step-label\"\n  }, \"Payment\", -1 /* HOISTED */)]), 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"step\", {\n      active: $data.currentStep >= 4\n    }])\n  }, _cache[37] || (_cache[37] = [_createElementVNode(\"div\", {\n    class: \"step-number\"\n  }, \"4\", -1 /* HOISTED */), _createElementVNode(\"span\", {\n    class: \"step-label\"\n  }, \"Review & Submit\", -1 /* HOISTED */)]), 2 /* CLASS */)]), _createCommentVNode(\" Form Container \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"form\", {\n    onSubmit: _cache[31] || (_cache[31] = _withModifiers((...args) => $options.handleSubmit && $options.handleSubmit(...args), [\"prevent\"]))\n  }, [_createCommentVNode(\" Step 1: Personal Information \"), $data.currentStep === 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_cache[61] || (_cache[61] = _createElementVNode(\"div\", {\n    class: \"step-header\"\n  }, [_createElementVNode(\"h2\", null, \"Personal Information\"), _createElementVNode(\"p\", null, \"Your profile information will be used for this request\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"h3\", null, _toDisplayString($options.getFullName()), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_cache[38] || (_cache[38] = _createElementVNode(\"label\", null, \"Email:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.clientData?.email || $data.clientData?.profile?.email || 'Not provided'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_12, [_cache[39] || (_cache[39] = _createElementVNode(\"label\", null, \"Phone:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.clientData?.phone_number || $data.clientData?.profile?.phone_number || 'Not provided'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_13, [_cache[40] || (_cache[40] = _createElementVNode(\"label\", null, \"Address:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($options.getFullAddress()), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_14, [_cache[41] || (_cache[41] = _createElementVNode(\"label\", null, \"Date of Birth:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($options.formatDate($data.clientData?.birth_date || $data.clientData?.profile?.birth_date)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_15, [_cache[42] || (_cache[42] = _createElementVNode(\"label\", null, \"Gender:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.clientData?.gender || $data.clientData?.profile?.gender || 'Not provided'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_16, [_cache[43] || (_cache[43] = _createElementVNode(\"label\", null, \"Civil Status:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($options.getCivilStatusName($data.clientData?.civil_status_id || $data.clientData?.profile?.civil_status_id)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_17, [_cache[44] || (_cache[44] = _createElementVNode(\"label\", null, \"Nationality:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.clientData?.nationality || $data.clientData?.profile?.nationality || 'Not provided'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_18, [_cache[45] || (_cache[45] = _createElementVNode(\"label\", null, \"Years of Residency:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($options.getResidencyDisplay()), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"update-profile-btn\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.updateProfile && $options.updateProfile(...args))\n  }, _cache[46] || (_cache[46] = [_createElementVNode(\"i\", {\n    class: \"fas fa-edit\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Update Profile \")]))])])]), _createCommentVNode(\" Required Documents Upload \"), _createElementVNode(\"div\", _hoisted_20, [_cache[59] || (_cache[59] = _createElementVNode(\"h3\", null, [_createElementVNode(\"i\", {\n    class: \"fas fa-upload\"\n  }), _createTextVNode(\" Required Documents\")], -1 /* HOISTED */)), _cache[60] || (_cache[60] = _createElementVNode(\"p\", {\n    class: \"section-description\"\n  }, \"Please upload the following required documents as per government regulations:\", -1 /* HOISTED */)), _createCommentVNode(\" Valid Government ID \"), _createElementVNode(\"div\", _hoisted_21, [_cache[50] || (_cache[50] = _createElementVNode(\"label\", {\n    class: \"document-label\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-id-card\"\n  }), _createTextVNode(\" Valid Government ID * \"), _createElementVNode(\"span\", {\n    class: \"document-info\"\n  }, \"(Driver's License, Voter's ID, Passport, etc.)\")], -1 /* HOISTED */)), _createElementVNode(\"div\", {\n    class: \"file-upload-area\",\n    onClick: _cache[4] || (_cache[4] = $event => $options.triggerFileInput('government_id')),\n    onDragover: _cache[5] || (_cache[5] = _withModifiers(() => {}, [\"prevent\"])),\n    onDrop: _cache[6] || (_cache[6] = _withModifiers($event => $options.handleFileDrop($event, 'government_id'), [\"prevent\"]))\n  }, [_createElementVNode(\"input\", {\n    ref: \"governmentIdInput\",\n    type: \"file\",\n    accept: \"image/*,.pdf\",\n    onChange: _cache[2] || (_cache[2] = $event => $options.handleFileSelect($event, 'government_id')),\n    style: {\n      \"display\": \"none\"\n    },\n    required: \"\"\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), !$data.uploadedFiles.government_id ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, _cache[47] || (_cache[47] = [_createElementVNode(\"i\", {\n    class: \"fas fa-cloud-upload-alt\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"p\", null, \"Click to upload or drag and drop\", -1 /* HOISTED */), _createElementVNode(\"small\", null, \"JPG, PNG, PDF (Max 5MB)\", -1 /* HOISTED */)]))) : (_openBlock(), _createElementBlock(\"div\", _hoisted_23, [_cache[49] || (_cache[49] = _createElementVNode(\"i\", {\n    class: \"fas fa-file-check\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.uploadedFiles.government_id.name), 1 /* TEXT */), _createElementVNode(\"button\", {\n    type: \"button\",\n    onClick: _cache[3] || (_cache[3] = _withModifiers($event => $options.removeFile('government_id'), [\"stop\"])),\n    class: \"remove-file\"\n  }, _cache[48] || (_cache[48] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times\"\n  }, null, -1 /* HOISTED */)]))]))], 32 /* NEED_HYDRATION */)]), _createCommentVNode(\" Proof of Residency \"), _createElementVNode(\"div\", _hoisted_24, [_cache[54] || (_cache[54] = _createElementVNode(\"label\", {\n    class: \"document-label\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-home\"\n  }), _createTextVNode(\" Proof of Residency * \"), _createElementVNode(\"span\", {\n    class: \"document-info\"\n  }, \"(Utility Bill, Lease Agreement, Barangay Certificate)\")], -1 /* HOISTED */)), _createElementVNode(\"div\", {\n    class: \"file-upload-area\",\n    onClick: _cache[9] || (_cache[9] = $event => $options.triggerFileInput('proof_of_residency')),\n    onDragover: _cache[10] || (_cache[10] = _withModifiers(() => {}, [\"prevent\"])),\n    onDrop: _cache[11] || (_cache[11] = _withModifiers($event => $options.handleFileDrop($event, 'proof_of_residency'), [\"prevent\"]))\n  }, [_createElementVNode(\"input\", {\n    ref: \"proofOfResidencyInput\",\n    type: \"file\",\n    accept: \"image/*,.pdf\",\n    onChange: _cache[7] || (_cache[7] = $event => $options.handleFileSelect($event, 'proof_of_residency')),\n    style: {\n      \"display\": \"none\"\n    },\n    required: \"\"\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), !$data.uploadedFiles.proof_of_residency ? (_openBlock(), _createElementBlock(\"div\", _hoisted_25, _cache[51] || (_cache[51] = [_createElementVNode(\"i\", {\n    class: \"fas fa-cloud-upload-alt\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"p\", null, \"Click to upload or drag and drop\", -1 /* HOISTED */), _createElementVNode(\"small\", null, \"JPG, PNG, PDF (Max 5MB)\", -1 /* HOISTED */)]))) : (_openBlock(), _createElementBlock(\"div\", _hoisted_26, [_cache[53] || (_cache[53] = _createElementVNode(\"i\", {\n    class: \"fas fa-file-check\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.uploadedFiles.proof_of_residency.name), 1 /* TEXT */), _createElementVNode(\"button\", {\n    type: \"button\",\n    onClick: _cache[8] || (_cache[8] = _withModifiers($event => $options.removeFile('proof_of_residency'), [\"stop\"])),\n    class: \"remove-file\"\n  }, _cache[52] || (_cache[52] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times\"\n  }, null, -1 /* HOISTED */)]))]))], 32 /* NEED_HYDRATION */)]), _createCommentVNode(\" Community Tax Certificate (Optional) \"), _createElementVNode(\"div\", _hoisted_27, [_cache[58] || (_cache[58] = _createElementVNode(\"label\", {\n    class: \"document-label\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-certificate\"\n  }), _createTextVNode(\" Community Tax Certificate (Cedula) \"), _createElementVNode(\"span\", {\n    class: \"document-info optional\"\n  }, \"(Optional - if available)\")], -1 /* HOISTED */)), _createElementVNode(\"div\", {\n    class: \"file-upload-area\",\n    onClick: _cache[14] || (_cache[14] = $event => $options.triggerFileInput('cedula')),\n    onDragover: _cache[15] || (_cache[15] = _withModifiers(() => {}, [\"prevent\"])),\n    onDrop: _cache[16] || (_cache[16] = _withModifiers($event => $options.handleFileDrop($event, 'cedula'), [\"prevent\"]))\n  }, [_createElementVNode(\"input\", {\n    ref: \"cedulaInput\",\n    type: \"file\",\n    accept: \"image/*,.pdf\",\n    onChange: _cache[12] || (_cache[12] = $event => $options.handleFileSelect($event, 'cedula')),\n    style: {\n      \"display\": \"none\"\n    }\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), !$data.uploadedFiles.cedula ? (_openBlock(), _createElementBlock(\"div\", _hoisted_28, _cache[55] || (_cache[55] = [_createElementVNode(\"i\", {\n    class: \"fas fa-cloud-upload-alt\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"p\", null, \"Click to upload or drag and drop\", -1 /* HOISTED */), _createElementVNode(\"small\", null, \"JPG, PNG, PDF (Max 5MB)\", -1 /* HOISTED */)]))) : (_openBlock(), _createElementBlock(\"div\", _hoisted_29, [_cache[57] || (_cache[57] = _createElementVNode(\"i\", {\n    class: \"fas fa-file-check\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.uploadedFiles.cedula.name), 1 /* TEXT */), _createElementVNode(\"button\", {\n    type: \"button\",\n    onClick: _cache[13] || (_cache[13] = _withModifiers($event => $options.removeFile('cedula'), [\"stop\"])),\n    class: \"remove-file\"\n  }, _cache[56] || (_cache[56] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times\"\n  }, null, -1 /* HOISTED */)]))]))], 32 /* NEED_HYDRATION */)])]), _createCommentVNode(\" Legal Notice \"), _cache[62] || (_cache[62] = _createStaticVNode(\"<div class=\\\"form-section\\\" data-v-3a008456><div class=\\\"legal-notice\\\" data-v-3a008456><h3 data-v-3a008456><i class=\\\"fas fa-info-circle\\\" data-v-3a008456></i> Important Notice</h3><p data-v-3a008456>This barangay clearance certifies that you are a resident in good standing with no pending legal cases or disputes within the barangay. Only information required by law is collected.</p><div class=\\\"data-privacy-note\\\" data-v-3a008456><small data-v-3a008456><i class=\\\"fas fa-shield-alt\\\" data-v-3a008456></i> Your personal information is protected under the Data Privacy Act of 2012.</small></div></div></div>\", 1))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Step 2: Purpose and Details \"), $data.currentStep === 2 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_30, [_cache[78] || (_cache[78] = _createElementVNode(\"div\", {\n    class: \"step-header\"\n  }, [_createElementVNode(\"h2\", null, \"Purpose and Additional Details\"), _createElementVNode(\"p\", null, \"Please provide the purpose and any additional information\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_cache[64] || (_cache[64] = _createElementVNode(\"label\", {\n    for: \"purpose_category\"\n  }, \"Purpose Category *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    id: \"purpose_category\",\n    \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $data.formData.purpose_category_id = $event),\n    required: \"\",\n    onChange: _cache[18] || (_cache[18] = (...args) => $options.onPurposeChange && $options.onPurposeChange(...args))\n  }, [_cache[63] || (_cache[63] = _createElementVNode(\"option\", {\n    value: \"\"\n  }, \"Select purpose\", -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.purposeCategories, category => {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: category.id,\n      value: category.id\n    }, _toDisplayString(category.category_name), 9 /* TEXT, PROPS */, _hoisted_33);\n  }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.formData.purpose_category_id]])]), _createElementVNode(\"div\", _hoisted_34, [_cache[65] || (_cache[65] = _createElementVNode(\"label\", {\n    for: \"purpose_details\"\n  }, \"Purpose Details *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n    id: \"purpose_details\",\n    \"onUpdate:modelValue\": _cache[19] || (_cache[19] = $event => $data.formData.purpose_details = $event),\n    rows: \"3\",\n    required: \"\",\n    placeholder: \"Please provide specific details about the purpose of this clearance\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.formData.purpose_details]])]), _createElementVNode(\"div\", _hoisted_35, [_cache[70] || (_cache[70] = _createElementVNode(\"label\", {\n    for: \"pending_cases\"\n  }, \"Pending Cases Declaration *\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"label\", _hoisted_37, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"radio\",\n    \"onUpdate:modelValue\": _cache[20] || (_cache[20] = $event => $data.formData.has_pending_cases = $event),\n    value: false,\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.formData.has_pending_cases]]), _cache[66] || (_cache[66] = _createElementVNode(\"span\", {\n    class: \"radio-custom\"\n  }, null, -1 /* HOISTED */)), _cache[67] || (_cache[67] = _createTextVNode(\" No pending cases \"))]), _createElementVNode(\"label\", _hoisted_38, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"radio\",\n    \"onUpdate:modelValue\": _cache[21] || (_cache[21] = $event => $data.formData.has_pending_cases = $event),\n    value: true,\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.formData.has_pending_cases]]), _cache[68] || (_cache[68] = _createElementVNode(\"span\", {\n    class: \"radio-custom\"\n  }, null, -1 /* HOISTED */)), _cache[69] || (_cache[69] = _createTextVNode(\" Has pending cases \"))])])]), $data.formData.has_pending_cases ? (_openBlock(), _createElementBlock(\"div\", _hoisted_39, [_cache[71] || (_cache[71] = _createElementVNode(\"label\", {\n    for: \"pending_cases_details\"\n  }, \"Pending Cases Details *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n    id: \"pending_cases_details\",\n    \"onUpdate:modelValue\": _cache[22] || (_cache[22] = $event => $data.formData.pending_cases_details = $event),\n    rows: \"3\",\n    required: \"\",\n    placeholder: \"Please provide details about pending cases\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.formData.pending_cases_details]])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_40, [_cache[76] || (_cache[76] = _createElementVNode(\"label\", {\n    for: \"voter_registration\"\n  }, \"Voter Registration Status\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_41, [_createElementVNode(\"label\", _hoisted_42, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"radio\",\n    \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $data.formData.is_registered_voter = $event),\n    value: true\n  }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.formData.is_registered_voter]]), _cache[72] || (_cache[72] = _createElementVNode(\"span\", {\n    class: \"radio-custom\"\n  }, null, -1 /* HOISTED */)), _cache[73] || (_cache[73] = _createTextVNode(\" Registered voter \"))]), _createElementVNode(\"label\", _hoisted_43, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"radio\",\n    \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $data.formData.is_registered_voter = $event),\n    value: false\n  }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.formData.is_registered_voter]]), _cache[74] || (_cache[74] = _createElementVNode(\"span\", {\n    class: \"radio-custom\"\n  }, null, -1 /* HOISTED */)), _cache[75] || (_cache[75] = _createTextVNode(\" Not registered \"))])])]), _createElementVNode(\"div\", _hoisted_44, [_cache[77] || (_cache[77] = _createElementVNode(\"label\", {\n    for: \"additional_notes\"\n  }, \"Additional Notes\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n    id: \"additional_notes\",\n    \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $data.formData.additional_notes = $event),\n    rows: \"2\",\n    placeholder: \"Any additional information or special requests\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.formData.additional_notes]])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Step 3: Payment Method \"), $data.currentStep === 3 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_45, [_cache[83] || (_cache[83] = _createElementVNode(\"div\", {\n    class: \"step-header\"\n  }, [_createElementVNode(\"h2\", null, \"Payment Information\"), _createElementVNode(\"p\", null, \"Choose your preferred payment method\")], -1 /* HOISTED */)), _createCommentVNode(\" Fee Summary \"), _createElementVNode(\"div\", _hoisted_46, [_createElementVNode(\"div\", _hoisted_47, [_cache[81] || (_cache[81] = _createElementVNode(\"h3\", null, \"Fee Breakdown\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_48, [_createElementVNode(\"div\", _hoisted_49, [_cache[79] || (_cache[79] = _createElementVNode(\"span\", null, \"Barangay Clearance Fee\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, \"₱\" + _toDisplayString($options.formatCurrency($data.baseFee)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_50, [_cache[80] || (_cache[80] = _createElementVNode(\"span\", null, \"Total Amount\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, \"₱\" + _toDisplayString($options.formatCurrency($data.totalFee)), 1 /* TEXT */)])])])]), _createCommentVNode(\" Payment Methods \"), _createElementVNode(\"div\", _hoisted_51, [_cache[82] || (_cache[82] = _createElementVNode(\"h3\", null, \"Select Payment Method\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_52, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.paymentMethods, method => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: method.id,\n      class: _normalizeClass([\"payment-option\", {\n        selected: $data.formData.payment_method_id === method.id\n      }]),\n      onClick: $event => $options.selectPaymentMethod(method.id)\n    }, [_createElementVNode(\"div\", _hoisted_54, [_createElementVNode(\"i\", {\n      class: _normalizeClass($options.getPaymentIcon(method.method_code))\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_55, [_createElementVNode(\"h4\", null, _toDisplayString(method.method_name), 1 /* TEXT */), method.description ? (_openBlock(), _createElementBlock(\"p\", _hoisted_56, _toDisplayString(method.description), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_57, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      value: method.id,\n      \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.formData.payment_method_id = $event),\n      required: \"\"\n    }, null, 8 /* PROPS */, _hoisted_58), [[_vModelRadio, $data.formData.payment_method_id]])])], 10 /* CLASS, PROPS */, _hoisted_53);\n  }), 128 /* KEYED_FRAGMENT */))])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Step 4: Review and Submit \"), $data.currentStep === 4 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_59, [_cache[103] || (_cache[103] = _createElementVNode(\"div\", {\n    class: \"step-header\"\n  }, [_createElementVNode(\"h2\", null, \"Review Your Request\"), _createElementVNode(\"p\", null, \"Please review all information before submitting\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_60, [_createCommentVNode(\" Personal Information Review \"), _createElementVNode(\"div\", _hoisted_61, [_cache[87] || (_cache[87] = _createElementVNode(\"h3\", null, \"Personal Information\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_62, [_createElementVNode(\"div\", _hoisted_63, [_cache[84] || (_cache[84] = _createElementVNode(\"label\", null, \"Full Name:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($options.getFullName()), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_64, [_cache[85] || (_cache[85] = _createElementVNode(\"label\", null, \"Address:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($options.getFullAddress()), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_65, [_cache[86] || (_cache[86] = _createElementVNode(\"label\", null, \"Phone:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.clientData?.phone_number || $data.clientData?.profile?.phone_number || 'Not provided'), 1 /* TEXT */)])])]), _createCommentVNode(\" Cedula Information Review \"), _createElementVNode(\"div\", _hoisted_66, [_cache[91] || (_cache[91] = _createElementVNode(\"h3\", null, \"Cedula Information\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_67, [_createElementVNode(\"div\", _hoisted_68, [_cache[88] || (_cache[88] = _createElementVNode(\"label\", null, \"Cedula Number:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.formData.cedula_number), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_69, [_cache[89] || (_cache[89] = _createElementVNode(\"label\", null, \"Date Issued:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.formData.cedula_date_issued), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_70, [_cache[90] || (_cache[90] = _createElementVNode(\"label\", null, \"Place Issued:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.formData.cedula_place_issued), 1 /* TEXT */)])])]), _createCommentVNode(\" Purpose Review \"), _createElementVNode(\"div\", _hoisted_71, [_cache[96] || (_cache[96] = _createElementVNode(\"h3\", null, \"Purpose & Details\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_72, [_createElementVNode(\"div\", _hoisted_73, [_cache[92] || (_cache[92] = _createElementVNode(\"label\", null, \"Purpose:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($options.getPurposeCategoryName()), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_74, [_cache[93] || (_cache[93] = _createElementVNode(\"label\", null, \"Details:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.formData.purpose_details), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_75, [_cache[94] || (_cache[94] = _createElementVNode(\"label\", null, \"Pending Cases:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.formData.has_pending_cases ? 'Yes' : 'No'), 1 /* TEXT */)]), $data.formData.has_pending_cases && $data.formData.pending_cases_details ? (_openBlock(), _createElementBlock(\"div\", _hoisted_76, [_cache[95] || (_cache[95] = _createElementVNode(\"label\", null, \"Case Details:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.formData.pending_cases_details), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Payment Review \"), _createElementVNode(\"div\", _hoisted_77, [_cache[99] || (_cache[99] = _createElementVNode(\"h3\", null, \"Payment Information\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_78, [_createElementVNode(\"div\", _hoisted_79, [_cache[97] || (_cache[97] = _createElementVNode(\"label\", null, \"Payment Method:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($options.getPaymentMethodName()), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_80, [_cache[98] || (_cache[98] = _createElementVNode(\"label\", null, \"Total Amount:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_81, \"₱\" + _toDisplayString($options.formatCurrency($data.totalFee)), 1 /* TEXT */)])])])]), _createCommentVNode(\" Terms and Conditions \"), _createElementVNode(\"div\", _hoisted_82, [_createElementVNode(\"label\", _hoisted_83, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"checkbox\",\n    \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $data.formData.agree_to_terms = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.formData.agree_to_terms]]), _cache[100] || (_cache[100] = _createElementVNode(\"span\", {\n    class: \"checkbox-custom\"\n  }, null, -1 /* HOISTED */)), _cache[101] || (_cache[101] = _createTextVNode(\" I agree to the \")), _createElementVNode(\"a\", {\n    href: \"#\",\n    onClick: _cache[28] || (_cache[28] = _withModifiers((...args) => $options.showTerms && $options.showTerms(...args), [\"prevent\"]))\n  }, \"terms and conditions\"), _cache[102] || (_cache[102] = _createTextVNode(\" and certify that all information provided is true and accurate. \"))])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Form Actions \"), _createElementVNode(\"div\", _hoisted_84, [$data.currentStep > 1 ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    type: \"button\",\n    class: \"btn-secondary\",\n    onClick: _cache[29] || (_cache[29] = (...args) => $options.previousStep && $options.previousStep(...args))\n  }, _cache[104] || (_cache[104] = [_createElementVNode(\"i\", {\n    class: \"fas fa-chevron-left\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Previous \")]))) : _createCommentVNode(\"v-if\", true), $data.currentStep < 4 ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 1,\n    type: \"button\",\n    class: \"btn-primary\",\n    onClick: _cache[30] || (_cache[30] = (...args) => $options.nextStep && $options.nextStep(...args)),\n    disabled: !$options.canProceedToNextStep()\n  }, _cache[105] || (_cache[105] = [_createTextVNode(\" Next \"), _createElementVNode(\"i\", {\n    class: \"fas fa-chevron-right\"\n  }, null, -1 /* HOISTED */)]), 8 /* PROPS */, _hoisted_85)) : _createCommentVNode(\"v-if\", true), $data.currentStep === 4 ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 2,\n    type: \"submit\",\n    class: \"btn-submit\",\n    disabled: $data.submitting || !$data.formData.agree_to_terms\n  }, [$data.submitting ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 0\n  }, [_cache[106] || (_cache[106] = _createElementVNode(\"i\", {\n    class: \"fas fa-spinner fa-spin\"\n  }, null, -1 /* HOISTED */)), _cache[107] || (_cache[107] = _createTextVNode(\" Submitting... \"))], 64 /* STABLE_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_cache[108] || (_cache[108] = _createElementVNode(\"i\", {\n    class: \"fas fa-paper-plane\"\n  }, null, -1 /* HOISTED */)), _cache[109] || (_cache[109] = _createTextVNode(\" Submit Request \"))], 64 /* STABLE_FRAGMENT */))], 8 /* PROPS */, _hoisted_86)) : _createCommentVNode(\"v-if\", true)])], 32 /* NEED_HYDRATION */)])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "onClick", "_cache", "args", "$options", "goBack", "_hoisted_4", "_normalizeClass", "active", "$data", "currentStep", "completed", "_hoisted_5", "onSubmit", "_withModifiers", "handleSubmit", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_toDisplayString", "getFullName", "_hoisted_10", "_hoisted_11", "clientData", "email", "profile", "_hoisted_12", "phone_number", "_hoisted_13", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_hoisted_14", "formatDate", "birth_date", "_hoisted_15", "gender", "_hoisted_16", "getCivilStatusName", "civil_status_id", "_hoisted_17", "nationality", "_hoisted_18", "getResidencyDisplay", "_hoisted_19", "type", "updateProfile", "_hoisted_20", "_hoisted_21", "$event", "triggerFileInput", "onDragover", "onDrop", "handleFileDrop", "ref", "accept", "onChange", "handleFileSelect", "style", "required", "uploadedFiles", "government_id", "_hoisted_22", "_hoisted_23", "name", "removeFile", "_hoisted_24", "proof_of_residency", "_hoisted_25", "_hoisted_26", "_hoisted_27", "cedula", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "for", "id", "formData", "purpose_category_id", "onPurposeChange", "value", "_Fragment", "_renderList", "purposeCategories", "category", "key", "category_name", "_hoisted_33", "_hoisted_34", "purpose_details", "rows", "placeholder", "_hoisted_35", "_hoisted_36", "_hoisted_37", "has_pending_cases", "_hoisted_38", "_hoisted_39", "pending_cases_details", "_hoisted_40", "_hoisted_41", "_hoisted_42", "is_registered_voter", "_hoisted_43", "_hoisted_44", "additional_notes", "_hoisted_45", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "formatCurrency", "baseFee", "_hoisted_50", "totalFee", "_hoisted_51", "_hoisted_52", "paymentMethods", "method", "selected", "payment_method_id", "selectPaymentMethod", "_hoisted_54", "getPaymentIcon", "method_code", "_hoisted_55", "method_name", "description", "_hoisted_56", "_hoisted_57", "_hoisted_59", "_hoisted_60", "_hoisted_61", "_hoisted_62", "_hoisted_63", "_hoisted_64", "_hoisted_65", "_hoisted_66", "_hoisted_67", "_hoisted_68", "cedula_number", "_hoisted_69", "cedula_date_issued", "_hoisted_70", "cedula_place_issued", "_hoisted_71", "_hoisted_72", "_hoisted_73", "getPurposeCategoryName", "_hoisted_74", "_hoisted_75", "_hoisted_76", "_hoisted_77", "_hoisted_78", "_hoisted_79", "getPaymentMethodName", "_hoisted_80", "_hoisted_81", "_hoisted_82", "_hoisted_83", "agree_to_terms", "href", "showTerms", "_hoisted_84", "previousStep", "nextStep", "disabled", "canProceedToNextStep", "submitting"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\BarangayClearanceRequest.vue"], "sourcesContent": ["<template>\n  <div class=\"barangay-clearance-request\">\n    <!-- Header -->\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <div class=\"header-main\">\n          <h1 class=\"page-title\">\n            <i class=\"fas fa-certificate\"></i>\n            Barangay Clearance Request\n          </h1>\n          <p class=\"page-description\">\n            Apply for your Barangay Clearance certificate online\n          </p>\n        </div>\n        <button class=\"back-btn\" @click=\"goBack\">\n          <i class=\"fas fa-arrow-left\"></i>\n          Back\n        </button>\n      </div>\n    </div>\n\n    <!-- Progress Steps -->\n    <div class=\"progress-steps\">\n      <div class=\"step\" :class=\"{ active: currentStep >= 1, completed: currentStep > 1 }\">\n        <div class=\"step-number\">1</div>\n        <span class=\"step-label\">Upload Documents</span>\n      </div>\n      <div class=\"step\" :class=\"{ active: currentStep >= 2, completed: currentStep > 2 }\">\n        <div class=\"step-number\">2</div>\n        <span class=\"step-label\">Purpose & Details</span>\n      </div>\n      <div class=\"step\" :class=\"{ active: currentStep >= 3, completed: currentStep > 3 }\">\n        <div class=\"step-number\">3</div>\n        <span class=\"step-label\">Payment</span>\n      </div>\n      <div class=\"step\" :class=\"{ active: currentStep >= 4 }\">\n        <div class=\"step-number\">4</div>\n        <span class=\"step-label\">Review & Submit</span>\n      </div>\n    </div>\n\n    <!-- Form Container -->\n    <div class=\"form-container\">\n      <form @submit.prevent=\"handleSubmit\">\n        \n        <!-- Step 1: Personal Information -->\n        <div v-if=\"currentStep === 1\" class=\"form-step\">\n          <div class=\"step-header\">\n            <h2>Personal Information</h2>\n            <p>Your profile information will be used for this request</p>\n          </div>\n\n          <div class=\"profile-preview\">\n            <div class=\"profile-card\">\n              <div class=\"profile-info\">\n                <h3>{{ getFullName() }}</h3>\n                <div class=\"info-grid\">\n                  <div class=\"info-item\">\n                    <label>Email:</label>\n                    <span>{{ (clientData?.email || clientData?.profile?.email) || 'Not provided' }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Phone:</label>\n                    <span>{{ (clientData?.phone_number || clientData?.profile?.phone_number) || 'Not provided' }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Address:</label>\n                    <span>{{ getFullAddress() }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Date of Birth:</label>\n                    <span>{{ formatDate(clientData?.birth_date || clientData?.profile?.birth_date) }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Gender:</label>\n                    <span>{{ (clientData?.gender || clientData?.profile?.gender) || 'Not provided' }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Civil Status:</label>\n                    <span>{{ getCivilStatusName(clientData?.civil_status_id || clientData?.profile?.civil_status_id) }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Nationality:</label>\n                    <span>{{ (clientData?.nationality || clientData?.profile?.nationality) || 'Not provided' }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Years of Residency:</label>\n                    <span>{{ getResidencyDisplay() }}</span>\n                  </div>\n                </div>\n              </div>\n              <div class=\"profile-actions\">\n                <button type=\"button\" class=\"update-profile-btn\" @click=\"updateProfile\">\n                  <i class=\"fas fa-edit\"></i>\n                  Update Profile\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <!-- Required Documents Upload -->\n          <div class=\"form-section\">\n            <h3><i class=\"fas fa-upload\"></i> Required Documents</h3>\n            <p class=\"section-description\">Please upload the following required documents as per government regulations:</p>\n\n            <!-- Valid Government ID -->\n            <div class=\"document-upload-group\">\n              <label class=\"document-label\">\n                <i class=\"fas fa-id-card\"></i>\n                Valid Government ID *\n                <span class=\"document-info\">(Driver's License, Voter's ID, Passport, etc.)</span>\n              </label>\n              <div class=\"file-upload-area\" @click=\"triggerFileInput('government_id')\" @dragover.prevent @drop.prevent=\"handleFileDrop($event, 'government_id')\">\n                <input\n                  ref=\"governmentIdInput\"\n                  type=\"file\"\n                  accept=\"image/*,.pdf\"\n                  @change=\"handleFileSelect($event, 'government_id')\"\n                  style=\"display: none\"\n                  required\n                />\n                <div v-if=\"!uploadedFiles.government_id\" class=\"upload-placeholder\">\n                  <i class=\"fas fa-cloud-upload-alt\"></i>\n                  <p>Click to upload or drag and drop</p>\n                  <small>JPG, PNG, PDF (Max 5MB)</small>\n                </div>\n                <div v-else class=\"uploaded-file\">\n                  <i class=\"fas fa-file-check\"></i>\n                  <span>{{ uploadedFiles.government_id.name }}</span>\n                  <button type=\"button\" @click.stop=\"removeFile('government_id')\" class=\"remove-file\">\n                    <i class=\"fas fa-times\"></i>\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <!-- Proof of Residency -->\n            <div class=\"document-upload-group\">\n              <label class=\"document-label\">\n                <i class=\"fas fa-home\"></i>\n                Proof of Residency *\n                <span class=\"document-info\">(Utility Bill, Lease Agreement, Barangay Certificate)</span>\n              </label>\n              <div class=\"file-upload-area\" @click=\"triggerFileInput('proof_of_residency')\" @dragover.prevent @drop.prevent=\"handleFileDrop($event, 'proof_of_residency')\">\n                <input\n                  ref=\"proofOfResidencyInput\"\n                  type=\"file\"\n                  accept=\"image/*,.pdf\"\n                  @change=\"handleFileSelect($event, 'proof_of_residency')\"\n                  style=\"display: none\"\n                  required\n                />\n                <div v-if=\"!uploadedFiles.proof_of_residency\" class=\"upload-placeholder\">\n                  <i class=\"fas fa-cloud-upload-alt\"></i>\n                  <p>Click to upload or drag and drop</p>\n                  <small>JPG, PNG, PDF (Max 5MB)</small>\n                </div>\n                <div v-else class=\"uploaded-file\">\n                  <i class=\"fas fa-file-check\"></i>\n                  <span>{{ uploadedFiles.proof_of_residency.name }}</span>\n                  <button type=\"button\" @click.stop=\"removeFile('proof_of_residency')\" class=\"remove-file\">\n                    <i class=\"fas fa-times\"></i>\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <!-- Community Tax Certificate (Optional) -->\n            <div class=\"document-upload-group\">\n              <label class=\"document-label\">\n                <i class=\"fas fa-certificate\"></i>\n                Community Tax Certificate (Cedula)\n                <span class=\"document-info optional\">(Optional - if available)</span>\n              </label>\n              <div class=\"file-upload-area\" @click=\"triggerFileInput('cedula')\" @dragover.prevent @drop.prevent=\"handleFileDrop($event, 'cedula')\">\n                <input\n                  ref=\"cedulaInput\"\n                  type=\"file\"\n                  accept=\"image/*,.pdf\"\n                  @change=\"handleFileSelect($event, 'cedula')\"\n                  style=\"display: none\"\n                />\n                <div v-if=\"!uploadedFiles.cedula\" class=\"upload-placeholder\">\n                  <i class=\"fas fa-cloud-upload-alt\"></i>\n                  <p>Click to upload or drag and drop</p>\n                  <small>JPG, PNG, PDF (Max 5MB)</small>\n                </div>\n                <div v-else class=\"uploaded-file\">\n                  <i class=\"fas fa-file-check\"></i>\n                  <span>{{ uploadedFiles.cedula.name }}</span>\n                  <button type=\"button\" @click.stop=\"removeFile('cedula')\" class=\"remove-file\">\n                    <i class=\"fas fa-times\"></i>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Legal Notice -->\n          <div class=\"form-section\">\n            <div class=\"legal-notice\">\n              <h3><i class=\"fas fa-info-circle\"></i> Important Notice</h3>\n              <p>This barangay clearance certifies that you are a resident in good standing with no pending legal cases or disputes within the barangay. Only information required by law is collected.</p>\n              <div class=\"data-privacy-note\">\n                <small><i class=\"fas fa-shield-alt\"></i> Your personal information is protected under the Data Privacy Act of 2012.</small>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Step 2: Purpose and Details -->\n        <div v-if=\"currentStep === 2\" class=\"form-step\">\n          <div class=\"step-header\">\n            <h2>Purpose and Additional Details</h2>\n            <p>Please provide the purpose and any additional information</p>\n          </div>\n\n          <div class=\"form-section\">\n            <div class=\"form-group\">\n              <label for=\"purpose_category\">Purpose Category *</label>\n              <select\n                id=\"purpose_category\"\n                v-model=\"formData.purpose_category_id\"\n                required\n                @change=\"onPurposeChange\"\n              >\n                <option value=\"\">Select purpose</option>\n                <option\n                  v-for=\"category in purposeCategories\"\n                  :key=\"category.id\"\n                  :value=\"category.id\"\n                >\n                  {{ category.category_name }}\n                </option>\n              </select>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"purpose_details\">Purpose Details *</label>\n              <textarea\n                id=\"purpose_details\"\n                v-model=\"formData.purpose_details\"\n                rows=\"3\"\n                required\n                placeholder=\"Please provide specific details about the purpose of this clearance\"\n              ></textarea>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"pending_cases\">Pending Cases Declaration *</label>\n              <div class=\"radio-group\">\n                <label class=\"radio-option\">\n                  <input\n                    type=\"radio\"\n                    v-model=\"formData.has_pending_cases\"\n                    :value=\"false\"\n                    required\n                  />\n                  <span class=\"radio-custom\"></span>\n                  No pending cases\n                </label>\n                <label class=\"radio-option\">\n                  <input\n                    type=\"radio\"\n                    v-model=\"formData.has_pending_cases\"\n                    :value=\"true\"\n                    required\n                  />\n                  <span class=\"radio-custom\"></span>\n                  Has pending cases\n                </label>\n              </div>\n            </div>\n\n            <div v-if=\"formData.has_pending_cases\" class=\"form-group\">\n              <label for=\"pending_cases_details\">Pending Cases Details *</label>\n              <textarea\n                id=\"pending_cases_details\"\n                v-model=\"formData.pending_cases_details\"\n                rows=\"3\"\n                required\n                placeholder=\"Please provide details about pending cases\"\n              ></textarea>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"voter_registration\">Voter Registration Status</label>\n              <div class=\"radio-group\">\n                <label class=\"radio-option\">\n                  <input\n                    type=\"radio\"\n                    v-model=\"formData.is_registered_voter\"\n                    :value=\"true\"\n                  />\n                  <span class=\"radio-custom\"></span>\n                  Registered voter\n                </label>\n                <label class=\"radio-option\">\n                  <input\n                    type=\"radio\"\n                    v-model=\"formData.is_registered_voter\"\n                    :value=\"false\"\n                  />\n                  <span class=\"radio-custom\"></span>\n                  Not registered\n                </label>\n              </div>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"additional_notes\">Additional Notes</label>\n              <textarea\n                id=\"additional_notes\"\n                v-model=\"formData.additional_notes\"\n                rows=\"2\"\n                placeholder=\"Any additional information or special requests\"\n              ></textarea>\n            </div>\n          </div>\n        </div>\n\n        <!-- Step 3: Payment Method -->\n        <div v-if=\"currentStep === 3\" class=\"form-step\">\n          <div class=\"step-header\">\n            <h2>Payment Information</h2>\n            <p>Choose your preferred payment method</p>\n          </div>\n\n          <!-- Fee Summary -->\n          <div class=\"fee-summary\">\n            <div class=\"fee-card\">\n              <h3>Fee Breakdown</h3>\n              <div class=\"fee-items\">\n                <div class=\"fee-item\">\n                  <span>Barangay Clearance Fee</span>\n                  <span>₱{{ formatCurrency(baseFee) }}</span>\n                </div>\n                <div class=\"fee-item total\">\n                  <span>Total Amount</span>\n                  <span>₱{{ formatCurrency(totalFee) }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Payment Methods -->\n          <div class=\"form-section\">\n            <h3>Select Payment Method</h3>\n            <div class=\"payment-methods\">\n              <div\n                v-for=\"method in paymentMethods\"\n                :key=\"method.id\"\n                class=\"payment-option\"\n                :class=\"{ selected: formData.payment_method_id === method.id }\"\n                @click=\"selectPaymentMethod(method.id)\"\n              >\n                <div class=\"payment-icon\">\n                  <i :class=\"getPaymentIcon(method.method_code)\"></i>\n                </div>\n                <div class=\"payment-info\">\n                  <h4>{{ method.method_name }}</h4>\n                  <p v-if=\"method.description\">{{ method.description }}</p>\n                </div>\n                <div class=\"payment-radio\">\n                  <input\n                    type=\"radio\"\n                    :value=\"method.id\"\n                    v-model=\"formData.payment_method_id\"\n                    required\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Step 4: Review and Submit -->\n        <div v-if=\"currentStep === 4\" class=\"form-step\">\n          <div class=\"step-header\">\n            <h2>Review Your Request</h2>\n            <p>Please review all information before submitting</p>\n          </div>\n\n          <div class=\"review-sections\">\n            <!-- Personal Information Review -->\n            <div class=\"review-section\">\n              <h3>Personal Information</h3>\n              <div class=\"review-grid\">\n                <div class=\"review-item\">\n                  <label>Full Name:</label>\n                  <span>{{ getFullName() }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Address:</label>\n                  <span>{{ getFullAddress() }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Phone:</label>\n                  <span>{{ (clientData?.phone_number || clientData?.profile?.phone_number) || 'Not provided' }}</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- Cedula Information Review -->\n            <div class=\"review-section\">\n              <h3>Cedula Information</h3>\n              <div class=\"review-grid\">\n                <div class=\"review-item\">\n                  <label>Cedula Number:</label>\n                  <span>{{ formData.cedula_number }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Date Issued:</label>\n                  <span>{{ formData.cedula_date_issued }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Place Issued:</label>\n                  <span>{{ formData.cedula_place_issued }}</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- Purpose Review -->\n            <div class=\"review-section\">\n              <h3>Purpose & Details</h3>\n              <div class=\"review-grid\">\n                <div class=\"review-item\">\n                  <label>Purpose:</label>\n                  <span>{{ getPurposeCategoryName() }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Details:</label>\n                  <span>{{ formData.purpose_details }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Pending Cases:</label>\n                  <span>{{ formData.has_pending_cases ? 'Yes' : 'No' }}</span>\n                </div>\n                <div v-if=\"formData.has_pending_cases && formData.pending_cases_details\" class=\"review-item\">\n                  <label>Case Details:</label>\n                  <span>{{ formData.pending_cases_details }}</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- Payment Review -->\n            <div class=\"review-section\">\n              <h3>Payment Information</h3>\n              <div class=\"review-grid\">\n                <div class=\"review-item\">\n                  <label>Payment Method:</label>\n                  <span>{{ getPaymentMethodName() }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Total Amount:</label>\n                  <span class=\"amount\">₱{{ formatCurrency(totalFee) }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Terms and Conditions -->\n          <div class=\"terms-section\">\n            <label class=\"checkbox-option\">\n              <input\n                type=\"checkbox\"\n                v-model=\"formData.agree_to_terms\"\n                required\n              />\n              <span class=\"checkbox-custom\"></span>\n              I agree to the <a href=\"#\" @click.prevent=\"showTerms\">terms and conditions</a> and certify that all information provided is true and accurate.\n            </label>\n          </div>\n        </div>\n\n        <!-- Form Actions -->\n        <div class=\"form-actions\">\n          <button\n            v-if=\"currentStep > 1\"\n            type=\"button\"\n            class=\"btn-secondary\"\n            @click=\"previousStep\"\n          >\n            <i class=\"fas fa-chevron-left\"></i>\n            Previous\n          </button>\n          \n          <button\n            v-if=\"currentStep < 4\"\n            type=\"button\"\n            class=\"btn-primary\"\n            @click=\"nextStep\"\n            :disabled=\"!canProceedToNextStep()\"\n          >\n            Next\n            <i class=\"fas fa-chevron-right\"></i>\n          </button>\n          \n          <button\n            v-if=\"currentStep === 4\"\n            type=\"submit\"\n            class=\"btn-submit\"\n            :disabled=\"submitting || !formData.agree_to_terms\"\n          >\n            <template v-if=\"submitting\">\n              <i class=\"fas fa-spinner fa-spin\"></i>\n              Submitting...\n            </template>\n            <template v-else>\n              <i class=\"fas fa-paper-plane\"></i>\n              Submit Request\n            </template>\n          </button>\n        </div>\n      </form>\n    </div>\n  </div>\n</template>\n\n<script>\nimport documentRequestService from '@/services/documentRequestService';\nimport clientAuthService from '@/services/clientAuthService';\n\nexport default {\n  name: 'BarangayClearanceRequest',\n  data() {\n    return {\n      currentStep: 1,\n      submitting: false,\n      purposeCategories: [],\n      paymentMethods: [],\n      baseFee: 0.00,\n      totalFee: 0.00,\n      formData: {\n        document_type_id: 2, // Barangay Clearance\n        purpose_category_id: '',\n        purpose_details: '',\n        has_pending_cases: false,\n        pending_cases_details: '',\n        payment_method_id: '',\n        agree_to_terms: false\n      },\n      uploadedFiles: {\n        government_id: null,\n        proof_of_residency: null,\n        cedula: null\n      },\n      uploadErrors: {},\n      maxFileSize: 5 * 1024 * 1024, // 5MB\n      allowedFileTypes: ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'],\n      clientData: null, // Fresh profile data\n    };\n  },\n  computed: {\n    // Keep the old method as fallback\n    cachedClientData() {\n      return clientAuthService.getCurrentUser();\n    }\n  },\n  async mounted() {\n    await this.loadFormData();\n  },\n  methods: {\n    async loadFormData() {\n      try {\n        // Load fresh profile data first\n        console.log('Loading fresh profile data...');\n        const profileResponse = await clientAuthService.getProfile();\n        if (profileResponse.success) {\n          this.clientData = profileResponse.data;\n          console.log('Fresh profile data loaded:', this.clientData);\n        } else {\n          // Fallback to cached data\n          this.clientData = this.cachedClientData;\n          console.log('Using cached profile data:', this.clientData);\n        }\n\n        const [purposeResponse, paymentResponse, documentTypesResponse] = await Promise.all([\n          documentRequestService.getPurposeCategories(),\n          documentRequestService.getPaymentMethods(),\n          documentRequestService.getDocumentTypes()\n        ]);\n\n        this.purposeCategories = purposeResponse.data || [];\n        this.paymentMethods = paymentResponse.data || [];\n\n        // Set the actual base fee for Barangay Clearance (document_type_id = 2)\n        const barangayClearanceType = documentTypesResponse.data?.find(type => type.id === 2);\n        if (barangayClearanceType) {\n          this.baseFee = parseFloat(barangayClearanceType.base_fee) || 50.00;\n          this.totalFee = this.baseFee;\n          console.log('Barangay Clearance base fee loaded:', this.baseFee);\n        } else {\n          // Fallback to default fee\n          this.baseFee = 50.00;\n          this.totalFee = this.baseFee;\n          console.warn('Could not load Barangay Clearance fee, using default: ₱50.00');\n        }\n\n      } catch (error) {\n        console.error('Error loading form data:', error);\n        // Fallback to cached data on error\n        this.clientData = this.cachedClientData;\n        this.$toast?.error('Failed to load some form data');\n      }\n    },\n\n    getFullName() {\n      // Try fresh data first, then fallback to cached data structure\n      const profile = this.clientData || this.clientData?.profile;\n      if (!profile) return 'N/A';\n      return `${profile.first_name || ''} ${profile.middle_name || ''} ${profile.last_name || ''}`.trim();\n    },\n\n    getFullAddress() {\n      // Try fresh data first, then fallback to cached data structure\n      const profile = this.clientData || this.clientData?.profile;\n      if (!profile) return 'Not provided';\n\n      const parts = [\n        profile.house_number,\n        profile.street,\n        profile.subdivision,\n        profile.barangay,\n        profile.city_municipality || profile.city,\n        profile.province\n      ].filter(Boolean);\n\n      return parts.length > 0 ? parts.join(', ') : 'Not provided';\n    },\n\n    getCivilStatusName(statusId) {\n      const statuses = {\n        1: 'Single',\n        2: 'Married',\n        3: 'Divorced',\n        4: 'Widowed',\n        5: 'Separated'\n      };\n      return statuses[statusId] || 'Not provided';\n    },\n\n    getResidencyDisplay() {\n      const profile = this.clientData || this.clientData?.profile;\n      if (!profile) return 'Not provided';\n\n      const years = profile.years_of_residency;\n      const months = profile.months_of_residency;\n\n      if (!years && !months) return 'Not provided';\n\n      const parts = [];\n      if (years) parts.push(`${years} year${years > 1 ? 's' : ''}`);\n      if (months) parts.push(`${months} month${months > 1 ? 's' : ''}`);\n\n      return parts.join(' and ');\n    },\n\n    formatDate(dateString) {\n      if (!dateString) return 'Not provided';\n      return new Date(dateString).toLocaleDateString();\n    },\n\n    formatCurrency(amount) {\n      return parseFloat(amount).toFixed(2);\n    },\n\n    canProceedToNextStep() {\n      switch (this.currentStep) {\n        case 1:\n          // Step 1: Required documents must be uploaded\n          return this.uploadedFiles.government_id && this.uploadedFiles.proof_of_residency;\n        case 2:\n          return this.formData.purpose_category_id &&\n                 this.formData.purpose_details &&\n                 this.formData.has_pending_cases !== null;\n        case 3:\n          return this.formData.payment_method_id;\n        default:\n          return true;\n      }\n    },\n\n    nextStep() {\n      if (this.canProceedToNextStep() && this.currentStep < 4) {\n        this.currentStep++;\n      }\n    },\n\n    previousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--;\n      }\n    },\n\n    onPurposeChange() {\n      // Could implement dynamic fee calculation based on purpose\n    },\n\n    redirectToCedula() {\n      // Redirect to Cedula application page\n      this.$router.push('/client/cedula-request');\n    },\n\n    selectPaymentMethod(methodId) {\n      this.formData.payment_method_id = methodId;\n    },\n\n    getPaymentIcon(methodCode) {\n      const icons = {\n        'CASH': 'fas fa-money-bill',\n        'PAYMONGO_CARD': 'fas fa-credit-card',\n        'PAYMONGO_GCASH': 'fab fa-google-pay',\n        'PAYMONGO_GRABPAY': 'fas fa-mobile-alt',\n        'PAYMONGO_PAYMAYA': 'fas fa-wallet'\n      };\n      return icons[methodCode] || 'fas fa-credit-card';\n    },\n\n    getPurposeCategoryName() {\n      const category = this.purposeCategories.find(c => c.id === this.formData.purpose_category_id);\n      return category?.category_name || '';\n    },\n\n    getPaymentMethodName() {\n      const method = this.paymentMethods.find(m => m.id === this.formData.payment_method_id);\n      return method?.method_name || '';\n    },\n\n    async handleSubmit() {\n      if (!this.formData.agree_to_terms) return;\n\n      try {\n        this.submitting = true;\n\n        // Prepare request data with proper validation\n        const requestData = {\n          document_type_id: parseInt(this.formData.document_type_id) || 2,\n          purpose_category_id: parseInt(this.formData.purpose_category_id) || 1,\n          purpose_details: this.formData.purpose_details && this.formData.purpose_details.length >= 10\n            ? this.formData.purpose_details\n            : 'Barangay Clearance request for official purposes',\n          payment_method_id: parseInt(this.formData.payment_method_id) || null,\n          delivery_method: 'pickup',\n          priority: 'normal',\n          // Barangay Clearance specific fields (legally required)\n          has_pending_cases: Boolean(this.formData.has_pending_cases),\n          pending_cases_details: this.formData.pending_cases_details || null,\n          cedula_number: this.formData.cedula_number || null,\n          cedula_date_issued: this.formData.cedula_date_issued || null,\n          cedula_place_issued: this.formData.cedula_place_issued || null,\n          total_fee: this.totalFee || 150.00\n        };\n\n        console.log('Submitting request data:', requestData);\n\n        const response = await documentRequestService.submitRequest(requestData);\n\n        const requestId = response.data.id;\n        console.log('Request created with ID:', requestId);\n\n        // Upload documents if any are selected\n        const hasDocuments = this.uploadedFiles.government_id ||\n                            this.uploadedFiles.proof_of_residency ||\n                            this.uploadedFiles.cedula;\n\n        if (hasDocuments) {\n          console.log('Uploading documents...');\n          await this.uploadDocumentsToRequest(requestId);\n        }\n\n        this.$toast?.success('Request submitted successfully!');\n        this.$router.push({\n          name: 'RequestDetails',\n          params: { id: requestId }\n        });\n\n      } catch (error) {\n        console.error('Error submitting request:', error);\n        console.error('Error details:', {\n          status: error.response?.status,\n          data: error.response?.data,\n          message: error.message\n        });\n\n        let errorMessage = 'Failed to submit request';\n        if (error.response?.data?.message) {\n          errorMessage = error.response.data.message;\n        } else if (error.response?.data?.errors) {\n          errorMessage = error.response.data.errors.map(e => e.msg).join(', ');\n        } else if (error.message) {\n          errorMessage = error.message;\n        }\n\n        this.$toast?.error(errorMessage);\n      } finally {\n        this.submitting = false;\n      }\n    },\n\n    async uploadDocumentsToRequest(requestId) {\n      try {\n        const filesToUpload = [];\n\n        // Collect files to upload\n        Object.entries(this.uploadedFiles).forEach(([type, file]) => {\n          if (file) {\n            filesToUpload.push({ type, file });\n          }\n        });\n\n        if (filesToUpload.length === 0) {\n          return;\n        }\n\n        // Upload documents using the service\n        const uploadResponse = await documentRequestService.uploadDocuments(requestId, filesToUpload);\n\n        if (uploadResponse.success) {\n          console.log('Documents uploaded successfully:', uploadResponse.data);\n          this.$toast?.success(`${uploadResponse.data.total_uploaded} document(s) uploaded successfully`);\n        } else {\n          console.error('Document upload failed:', uploadResponse);\n          this.$toast?.warning('Request submitted but some documents failed to upload');\n        }\n\n      } catch (error) {\n        console.error('Document upload error:', error);\n        this.$toast?.warning('Request submitted but document upload failed. You can upload documents later.');\n      }\n    },\n\n    goBack() {\n      this.$router.push({ name: 'NewDocumentRequest' });\n    },\n\n    updateProfile() {\n      // TODO: Navigate to profile update page\n      console.log('Update profile');\n    },\n\n    // File handling methods\n    triggerFileInput(fileType) {\n      // Convert snake_case to camelCase for ref names\n      const refNameMap = {\n        'government_id': 'governmentIdInput',\n        'proof_of_residency': 'proofOfResidencyInput',\n        'cedula': 'cedulaInput'\n      };\n\n      const inputRef = refNameMap[fileType];\n\n      if (!inputRef) {\n        console.error(`Unknown file type: ${fileType}`);\n        return;\n      }\n\n      // Add safety check for ref existence\n      if (this.$refs[inputRef]) {\n        this.$refs[inputRef].click();\n      } else {\n        console.warn(`File input ref '${inputRef}' not found`);\n        // Try again after next tick\n        this.$nextTick(() => {\n          if (this.$refs[inputRef]) {\n            this.$refs[inputRef].click();\n          } else {\n            console.error(`File input ref '${inputRef}' still not found after nextTick`);\n          }\n        });\n      }\n    },\n\n    handleFileSelect(event, fileType) {\n      const file = event.target.files[0];\n      if (file) {\n        this.validateAndSetFile(file, fileType);\n      }\n    },\n\n    handleFileDrop(event, fileType) {\n      const file = event.dataTransfer.files[0];\n      if (file) {\n        this.validateAndSetFile(file, fileType);\n      }\n    },\n\n    validateAndSetFile(file, fileType) {\n      // Clear previous errors (Vue 3 compatible)\n      delete this.uploadErrors[fileType];\n\n      // Validate file size\n      if (file.size > this.maxFileSize) {\n        this.uploadErrors[fileType] = 'File size must be less than 5MB';\n        this.$toast?.error(`File size must be less than 5MB`);\n        return;\n      }\n\n      // Validate file type\n      if (!this.allowedFileTypes.includes(file.type)) {\n        this.uploadErrors[fileType] = 'Only JPG, PNG, and PDF files are allowed';\n        this.$toast?.error('Only JPG, PNG, and PDF files are allowed');\n        return;\n      }\n\n      // Set the file (Vue 3 compatible)\n      this.uploadedFiles[fileType] = file;\n      this.$toast?.success(`${file.name} uploaded successfully`);\n    },\n\n    removeFile(fileType) {\n      // Vue 3 compatible reactive updates\n      this.uploadedFiles[fileType] = null;\n      delete this.uploadErrors[fileType];\n\n      // Convert snake_case to camelCase for ref names\n      const refNameMap = {\n        'government_id': 'governmentIdInput',\n        'proof_of_residency': 'proofOfResidencyInput',\n        'cedula': 'cedulaInput'\n      };\n\n      const inputRef = refNameMap[fileType];\n\n      // Clear the input with safety check\n      if (inputRef && this.$refs[inputRef]) {\n        this.$refs[inputRef].value = '';\n      } else {\n        console.warn(`File input ref '${inputRef}' not found during removal`);\n      }\n    },\n\n    showTerms() {\n      // TODO: Show terms and conditions modal\n      console.log('Show terms');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.barangay-clearance-request {\n  padding: 2rem;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n\n.page-header {\n  margin-bottom: 2rem;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: 2rem;\n}\n\n.page-title {\n  font-size: 1.75rem;\n  font-weight: 700;\n  color: #1a365d;\n  margin-bottom: 0.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.page-title i {\n  color: #3182ce;\n}\n\n.page-description {\n  font-size: 1rem;\n  color: #4a5568;\n  margin: 0;\n}\n\n.back-btn {\n  background: #e2e8f0;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.5rem;\n  color: #4a5568;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.back-btn:hover {\n  background: #cbd5e0;\n  color: #2d3748;\n}\n\n.progress-steps {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 3rem;\n  position: relative;\n}\n\n.progress-steps::before {\n  content: '';\n  position: absolute;\n  top: 1.5rem;\n  left: 25%;\n  right: 25%;\n  height: 2px;\n  background: #e2e8f0;\n  z-index: 1;\n}\n\n.step {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  flex: 1;\n  position: relative;\n  z-index: 2;\n}\n\n.step-number {\n  width: 3rem;\n  height: 3rem;\n  border-radius: 50%;\n  background: #e2e8f0;\n  color: #a0aec0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  transition: all 0.3s;\n}\n\n.step.active .step-number {\n  background: #3182ce;\n  color: white;\n}\n\n.step.completed .step-number {\n  background: #38a169;\n  color: white;\n}\n\n.step-label {\n  font-size: 0.875rem;\n  color: #718096;\n  text-align: center;\n}\n\n.step.active .step-label {\n  color: #3182ce;\n  font-weight: 500;\n}\n\n.form-container {\n  background: white;\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\n}\n\n.form-step {\n  min-height: 400px;\n}\n\n.step-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.step-header h2 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 0.5rem;\n}\n\n.step-header p {\n  color: #4a5568;\n  margin: 0;\n}\n\n.profile-preview {\n  margin-bottom: 2rem;\n}\n\n.profile-card {\n  background: #f7fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n}\n\n.profile-info h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n\n.info-item {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.info-item label {\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: #718096;\n}\n\n.info-item span {\n  color: #2d3748;\n}\n\n.update-profile-btn {\n  background: #3182ce;\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.5rem;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  transition: all 0.2s;\n}\n\n.update-profile-btn:hover {\n  background: #2c5aa0;\n}\n\n.form-section {\n  margin-bottom: 2rem;\n}\n\n.form-section h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.form-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.form-group label {\n  font-weight: 500;\n  color: #2d3748;\n  font-size: 0.875rem;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  padding: 0.75rem;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  transition: all 0.2s;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #3182ce;\n  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);\n}\n\n.radio-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.radio-option {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  cursor: pointer;\n  padding: 0.5rem;\n  border-radius: 0.5rem;\n  transition: all 0.2s;\n}\n\n.radio-option:hover {\n  background: #f7fafc;\n}\n\n.radio-option input[type=\"radio\"] {\n  display: none;\n}\n\n.radio-custom {\n  width: 1.25rem;\n  height: 1.25rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 50%;\n  position: relative;\n  transition: all 0.2s;\n}\n\n.radio-option input[type=\"radio\"]:checked + .radio-custom {\n  border-color: #3182ce;\n}\n\n.radio-option input[type=\"radio\"]:checked + .radio-custom::after {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 0.5rem;\n  height: 0.5rem;\n  background: #3182ce;\n  border-radius: 50%;\n}\n\n.fee-summary {\n  margin-bottom: 2rem;\n}\n\n.fee-card {\n  background: #f7fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n}\n\n.fee-card h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.fee-items {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.fee-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem 0;\n}\n\n.fee-item.total {\n  border-top: 1px solid #e2e8f0;\n  padding-top: 1rem;\n  font-weight: 600;\n  font-size: 1.125rem;\n  color: #1a365d;\n}\n\n.payment-methods {\n  display: grid;\n  gap: 1rem;\n}\n\n.payment-option {\n  border: 2px solid #e2e8f0;\n  border-radius: 0.75rem;\n  padding: 1rem;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.payment-option:hover {\n  border-color: #cbd5e0;\n}\n\n.payment-option.selected {\n  border-color: #3182ce;\n  background: #ebf8ff;\n}\n\n.payment-icon {\n  width: 3rem;\n  height: 3rem;\n  background: #f7fafc;\n  border-radius: 0.5rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.25rem;\n  color: #4a5568;\n}\n\n.payment-option.selected .payment-icon {\n  background: #3182ce;\n  color: white;\n}\n\n.payment-info {\n  flex: 1;\n}\n\n.payment-info h4 {\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 0.25rem;\n}\n\n.payment-info p {\n  color: #718096;\n  font-size: 0.875rem;\n  margin: 0;\n}\n\n.payment-radio input {\n  width: 1.25rem;\n  height: 1.25rem;\n}\n\n.review-sections {\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n  margin-bottom: 2rem;\n}\n\n.review-section {\n  background: #f7fafc;\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n}\n\n.review-section h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.review-grid {\n  display: grid;\n  gap: 1rem;\n}\n\n.review-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  padding: 0.5rem 0;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.review-item:last-child {\n  border-bottom: none;\n}\n\n.review-item label {\n  font-weight: 500;\n  color: #4a5568;\n  min-width: 120px;\n}\n\n.review-item span {\n  color: #2d3748;\n  text-align: right;\n  flex: 1;\n}\n\n.review-item .amount {\n  font-weight: 600;\n  color: #38a169;\n  font-size: 1.125rem;\n}\n\n.terms-section {\n  margin-bottom: 2rem;\n  padding: 1rem;\n  background: #fffaf0;\n  border: 1px solid #fed7aa;\n  border-radius: 0.5rem;\n}\n\n.checkbox-option {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  cursor: pointer;\n  line-height: 1.5;\n}\n\n.checkbox-option input[type=\"checkbox\"] {\n  display: none;\n}\n\n.checkbox-custom {\n  width: 1.25rem;\n  height: 1.25rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 0.25rem;\n  position: relative;\n  flex-shrink: 0;\n  margin-top: 0.125rem;\n  transition: all 0.2s;\n}\n\n.checkbox-option input[type=\"checkbox\"]:checked + .checkbox-custom {\n  border-color: #3182ce;\n  background: #3182ce;\n}\n\n.checkbox-option input[type=\"checkbox\"]:checked + .checkbox-custom::after {\n  content: '✓';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: white;\n  font-size: 0.875rem;\n  font-weight: bold;\n}\n\n.checkbox-option a {\n  color: #3182ce;\n  text-decoration: underline;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding-top: 2rem;\n  border-top: 1px solid #e2e8f0;\n}\n\n.btn-secondary,\n.btn-primary,\n.btn-submit {\n  padding: 0.75rem 2rem;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  border: none;\n}\n\n.btn-secondary {\n  background: #e2e8f0;\n  color: #4a5568;\n}\n\n.btn-secondary:hover {\n  background: #cbd5e0;\n}\n\n.btn-primary {\n  background: #3182ce;\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background: #2c5aa0;\n}\n\n.btn-submit {\n  background: #38a169;\n  color: white;\n}\n\n.btn-submit:hover:not(:disabled) {\n  background: #2f855a;\n}\n\n.btn-primary:disabled,\n.btn-submit:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n@media (max-width: 768px) {\n  .barangay-clearance-request {\n    padding: 1rem;\n  }\n\n  .header-content {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .progress-steps {\n    flex-wrap: wrap;\n    gap: 1rem;\n  }\n\n  .progress-steps::before {\n    display: none;\n  }\n\n  .form-container {\n    padding: 1.5rem;\n  }\n\n  .form-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .info-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .profile-card {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .form-actions {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .review-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.25rem;\n  }\n\n  .review-item span {\n    text-align: left;\n  }\n}\n\n/* Legal Notice Styles */\n.legal-notice {\n  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);\n  border: 1px solid #2196f3;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 1rem;\n}\n\n.legal-notice h3 {\n  color: #1976d2;\n  margin-bottom: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.legal-notice p {\n  color: #424242;\n  line-height: 1.6;\n  margin-bottom: 1rem;\n}\n\n.data-privacy-note {\n  background: rgba(76, 175, 80, 0.1);\n  border-left: 4px solid #4caf50;\n  padding: 0.75rem;\n  border-radius: 4px;\n}\n\n.data-privacy-note small {\n  color: #2e7d32;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n/* Document Upload Styles */\n.document-upload-group {\n  margin-bottom: 1.5rem;\n}\n\n.document-label {\n  display: block;\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 0.5rem;\n  font-size: 1rem;\n}\n\n.document-label i {\n  margin-right: 0.5rem;\n  color: #3498db;\n}\n\n.document-info {\n  font-weight: 400;\n  color: #7f8c8d;\n  font-size: 0.875rem;\n  display: block;\n  margin-top: 0.25rem;\n}\n\n.document-info.optional {\n  color: #27ae60;\n}\n\n.file-upload-area {\n  border: 2px dashed #bdc3c7;\n  border-radius: 8px;\n  padding: 2rem;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: #f8f9fa;\n}\n\n.file-upload-area:hover {\n  border-color: #3498db;\n  background: #e3f2fd;\n}\n\n.file-upload-area.dragover {\n  border-color: #2ecc71;\n  background: #e8f5e8;\n}\n\n.upload-placeholder i {\n  font-size: 2rem;\n  color: #95a5a6;\n  margin-bottom: 0.5rem;\n}\n\n.upload-placeholder p {\n  margin: 0.5rem 0;\n  color: #2c3e50;\n  font-weight: 500;\n}\n\n.upload-placeholder small {\n  color: #7f8c8d;\n}\n\n.uploaded-file {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  color: #27ae60;\n  font-weight: 500;\n}\n\n.uploaded-file i {\n  color: #27ae60;\n}\n\n.remove-file {\n  background: #e74c3c;\n  color: white;\n  border: none;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  margin-left: 0.5rem;\n  transition: background 0.3s ease;\n}\n\n.remove-file:hover {\n  background: #c0392b;\n}\n\n.upload-error {\n  color: #e74c3c;\n  font-size: 0.875rem;\n  margin-top: 0.5rem;\n}\n\n@media (max-width: 768px) {\n  .file-upload-area {\n    padding: 1.5rem 1rem;\n  }\n\n  .upload-placeholder i {\n    font-size: 1.5rem;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA4B;;EAEhCA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAgB;;EAkBxBA,KAAK,EAAC;AAAgB;;EAoBtBA,KAAK,EAAC;AAAgB;;;EAIOA,KAAK,EAAC;;;EAM7BA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAMrBA,KAAK,EAAC;AAAiB;;EAU3BA,KAAK,EAAC;AAAc;;EAKlBA,KAAK,EAAC;AAAuB;;;EAeWA,KAAK,EAAC;;;;EAKnCA,KAAK,EAAC;;;EAWjBA,KAAK,EAAC;AAAuB;;;EAegBA,KAAK,EAAC;;;;EAKxCA,KAAK,EAAC;;;EAWjBA,KAAK,EAAC;AAAuB;;;EAcIA,KAAK,EAAC;;;;EAK5BA,KAAK,EAAC;;;;EAwBIA,KAAK,EAAC;;;EAM7BA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;;EAmBlBA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAa;;EACfA,KAAK,EAAC;AAAc;;EAUpBA,KAAK,EAAC;AAAc;;;EAaQA,KAAK,EAAC;;;EAWxCA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAa;;EACfA,KAAK,EAAC;AAAc;;EASpBA,KAAK,EAAC;AAAc;;EAY1BA,KAAK,EAAC;AAAY;;;EAaGA,KAAK,EAAC;;;EAO7BA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAU;;EAIhBA,KAAK,EAAC;AAAgB;;EAS5BA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAiB;;;EAQnBA,KAAK,EAAC;AAAc;;EAGpBA,KAAK,EAAC;AAAc;;;;;EAIpBA,KAAK,EAAC;AAAe;;;;EAcJA,KAAK,EAAC;;;EAM7BA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAQvBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAQvBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;;EAIiDA,KAAK,EAAC;;;EAQ9EA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAEhBA,KAAK,EAAC;AAAQ;;EAOvBA,KAAK,EAAC;AAAe;;EACjBA,KAAK,EAAC;AAAiB;;EAa7BA,KAAK,EAAC;AAAc;;;;uBA3d/BC,mBAAA,CAmgBM,OAngBNC,UAmgBM,GAlgBJC,mBAAA,YAAe,EACfC,mBAAA,CAgBM,OAhBNC,UAgBM,GAfJD,mBAAA,CAcM,OAdNE,UAcM,G,4BAbJF,mBAAA,CAQM;IARDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAGK;IAHDJ,KAAK,EAAC;EAAY,IACpBI,mBAAA,CAAkC;IAA/BJ,KAAK,EAAC;EAAoB,I,iBAAK,8BAEpC,E,GACAI,mBAAA,CAEI;IAFDJ,KAAK,EAAC;EAAkB,GAAC,wDAE5B,E,sBAEFI,mBAAA,CAGS;IAHDJ,KAAK,EAAC,UAAU;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,MAAA,IAAAD,QAAA,CAAAC,MAAA,IAAAF,IAAA,CAAM;kCACrCL,mBAAA,CAAiC;IAA9BJ,KAAK,EAAC;EAAmB,4B,iBAAK,QAEnC,E,QAIJG,mBAAA,oBAAuB,EACvBC,mBAAA,CAiBM,OAjBNQ,UAiBM,GAhBJR,mBAAA,CAGM;IAHDJ,KAAK,EAAAa,eAAA,EAAC,MAAM;MAAAC,MAAA,EAAmBC,KAAA,CAAAC,WAAW;MAAAC,SAAA,EAAkBF,KAAA,CAAAC,WAAW;IAAA;kCAC1EZ,mBAAA,CAAgC;IAA3BJ,KAAK,EAAC;EAAa,GAAC,GAAC,qBAC1BI,mBAAA,CAAgD;IAA1CJ,KAAK,EAAC;EAAY,GAAC,kBAAgB,oB,mBAE3CI,mBAAA,CAGM;IAHDJ,KAAK,EAAAa,eAAA,EAAC,MAAM;MAAAC,MAAA,EAAmBC,KAAA,CAAAC,WAAW;MAAAC,SAAA,EAAkBF,KAAA,CAAAC,WAAW;IAAA;kCAC1EZ,mBAAA,CAAgC;IAA3BJ,KAAK,EAAC;EAAa,GAAC,GAAC,qBAC1BI,mBAAA,CAAiD;IAA3CJ,KAAK,EAAC;EAAY,GAAC,mBAAiB,oB,mBAE5CI,mBAAA,CAGM;IAHDJ,KAAK,EAAAa,eAAA,EAAC,MAAM;MAAAC,MAAA,EAAmBC,KAAA,CAAAC,WAAW;MAAAC,SAAA,EAAkBF,KAAA,CAAAC,WAAW;IAAA;kCAC1EZ,mBAAA,CAAgC;IAA3BJ,KAAK,EAAC;EAAa,GAAC,GAAC,qBAC1BI,mBAAA,CAAuC;IAAjCJ,KAAK,EAAC;EAAY,GAAC,SAAO,oB,mBAElCI,mBAAA,CAGM;IAHDJ,KAAK,EAAAa,eAAA,EAAC,MAAM;MAAAC,MAAA,EAAmBC,KAAA,CAAAC,WAAW;IAAA;kCAC7CZ,mBAAA,CAAgC;IAA3BJ,KAAK,EAAC;EAAa,GAAC,GAAC,qBAC1BI,mBAAA,CAA+C;IAAzCJ,KAAK,EAAC;EAAY,GAAC,iBAAe,oB,qBAI5CG,mBAAA,oBAAuB,EACvBC,mBAAA,CAydM,OAzdNc,UAydM,GAxdJd,mBAAA,CAudO;IAvdAe,QAAM,EAAAX,MAAA,SAAAA,MAAA,OAAAY,cAAA,KAAAX,IAAA,KAAUC,QAAA,CAAAW,YAAA,IAAAX,QAAA,CAAAW,YAAA,IAAAZ,IAAA,CAAY;MAEjCN,mBAAA,kCAAqC,EAC1BY,KAAA,CAAAC,WAAW,U,cAAtBf,mBAAA,CAkKM,OAlKNqB,UAkKM,G,4BAjKJlB,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAA6B,YAAzB,sBAAoB,GACxBA,mBAAA,CAA6D,WAA1D,wDAAsD,E,sBAG3DA,mBAAA,CA8CM,OA9CNmB,UA8CM,GA7CJnB,mBAAA,CA4CM,OA5CNoB,UA4CM,GA3CJpB,mBAAA,CAoCM,OApCNqB,UAoCM,GAnCJrB,mBAAA,CAA4B,YAAAsB,gBAAA,CAArBhB,QAAA,CAAAiB,WAAW,oBAClBvB,mBAAA,CAiCM,OAjCNwB,WAiCM,GAhCJxB,mBAAA,CAGM,OAHNyB,WAGM,G,4BAFJzB,mBAAA,CAAqB,eAAd,QAAM,sBACbA,mBAAA,CAAsF,cAAAsB,gBAAA,CAA5EX,KAAA,CAAAe,UAAU,EAAEC,KAAK,IAAIhB,KAAA,CAAAe,UAAU,EAAEE,OAAO,EAAED,KAAK,mC,GAE3D3B,mBAAA,CAGM,OAHN6B,WAGM,G,4BAFJ7B,mBAAA,CAAqB,eAAd,QAAM,sBACbA,mBAAA,CAAoG,cAAAsB,gBAAA,CAA1FX,KAAA,CAAAe,UAAU,EAAEI,YAAY,IAAInB,KAAA,CAAAe,UAAU,EAAEE,OAAO,EAAEE,YAAY,mC,GAEzE9B,mBAAA,CAGM,OAHN+B,WAGM,G,4BAFJ/B,mBAAA,CAAuB,eAAhB,UAAQ,sBACfA,mBAAA,CAAmC,cAAAsB,gBAAA,CAA1BhB,QAAA,CAAA0B,cAAc,mB,GAEzBhC,mBAAA,CAGM,OAHNiC,WAGM,G,4BAFJjC,mBAAA,CAA6B,eAAtB,gBAAc,sBACrBA,mBAAA,CAAwF,cAAAsB,gBAAA,CAA/EhB,QAAA,CAAA4B,UAAU,CAACvB,KAAA,CAAAe,UAAU,EAAES,UAAU,IAAIxB,KAAA,CAAAe,UAAU,EAAEE,OAAO,EAAEO,UAAU,kB,GAE/EnC,mBAAA,CAGM,OAHNoC,WAGM,G,4BAFJpC,mBAAA,CAAsB,eAAf,SAAO,sBACdA,mBAAA,CAAwF,cAAAsB,gBAAA,CAA9EX,KAAA,CAAAe,UAAU,EAAEW,MAAM,IAAI1B,KAAA,CAAAe,UAAU,EAAEE,OAAO,EAAES,MAAM,mC,GAE7DrC,mBAAA,CAGM,OAHNsC,WAGM,G,4BAFJtC,mBAAA,CAA4B,eAArB,eAAa,sBACpBA,mBAAA,CAA0G,cAAAsB,gBAAA,CAAjGhB,QAAA,CAAAiC,kBAAkB,CAAC5B,KAAA,CAAAe,UAAU,EAAEc,eAAe,IAAI7B,KAAA,CAAAe,UAAU,EAAEE,OAAO,EAAEY,eAAe,kB,GAEjGxC,mBAAA,CAGM,OAHNyC,WAGM,G,4BAFJzC,mBAAA,CAA2B,eAApB,cAAY,sBACnBA,mBAAA,CAAkG,cAAAsB,gBAAA,CAAxFX,KAAA,CAAAe,UAAU,EAAEgB,WAAW,IAAI/B,KAAA,CAAAe,UAAU,EAAEE,OAAO,EAAEc,WAAW,mC,GAEvE1C,mBAAA,CAGM,OAHN2C,WAGM,G,4BAFJ3C,mBAAA,CAAkC,eAA3B,qBAAmB,sBAC1BA,mBAAA,CAAwC,cAAAsB,gBAAA,CAA/BhB,QAAA,CAAAsC,mBAAmB,mB,OAIlC5C,mBAAA,CAKM,OALN6C,WAKM,GAJJ7C,mBAAA,CAGS;IAHD8C,IAAI,EAAC,QAAQ;IAAClD,KAAK,EAAC,oBAAoB;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAyC,aAAA,IAAAzC,QAAA,CAAAyC,aAAA,IAAA1C,IAAA,CAAa;kCACpEL,mBAAA,CAA2B;IAAxBJ,KAAK,EAAC;EAAa,4B,iBAAK,kBAE7B,E,UAKNG,mBAAA,+BAAkC,EAClCC,mBAAA,CA+FM,OA/FNgD,WA+FM,G,4BA9FJhD,mBAAA,CAAyD,aAArDA,mBAAA,CAA6B;IAA1BJ,KAAK,EAAC;EAAe,I,iBAAK,qBAAmB,E,kDACpDI,mBAAA,CAAgH;IAA7GJ,KAAK,EAAC;EAAqB,GAAC,+EAA6E,sBAE5GG,mBAAA,yBAA4B,EAC5BC,mBAAA,CA4BM,OA5BNiD,WA4BM,G,4BA3BJjD,mBAAA,CAIQ;IAJDJ,KAAK,EAAC;EAAgB,IAC3BI,mBAAA,CAA8B;IAA3BJ,KAAK,EAAC;EAAgB,I,iBAAK,yBAE9B,GAAAI,mBAAA,CAAiF;IAA3EJ,KAAK,EAAC;EAAe,GAAC,gDAA8C,E,sBAE5EI,mBAAA,CAqBM;IArBDJ,KAAK,EAAC,kBAAkB;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA8C,MAAA,IAAE5C,QAAA,CAAA6C,gBAAgB;IAAoBC,UAAQ,EAAAhD,MAAA,QAAAA,MAAA,MAAAY,cAAA,CAAT,QAAiB;IAAEqC,MAAI,EAAAjD,MAAA,QAAAA,MAAA,MAAAY,cAAA,CAAAkC,MAAA,IAAU5C,QAAA,CAAAgD,cAAc,CAACJ,MAAM;MAC7HlD,mBAAA,CAOE;IANAuD,GAAG,EAAC,mBAAmB;IACvBT,IAAI,EAAC,MAAM;IACXU,MAAM,EAAC,cAAc;IACpBC,QAAM,EAAArD,MAAA,QAAAA,MAAA,MAAA8C,MAAA,IAAE5C,QAAA,CAAAoD,gBAAgB,CAACR,MAAM;IAChCS,KAAqB,EAArB;MAAA;IAAA,CAAqB;IACrBC,QAAQ,EAAR;mDAEUjD,KAAA,CAAAkD,aAAa,CAACC,aAAa,I,cAAvCjE,mBAAA,CAIM,OAJNkE,WAIM,EAAA3D,MAAA,SAAAA,MAAA,QAHJJ,mBAAA,CAAuC;IAApCJ,KAAK,EAAC;EAAyB,4BAClCI,mBAAA,CAAuC,WAApC,kCAAgC,qBACnCA,mBAAA,CAAsC,eAA/B,yBAAuB,oB,qBAEhCH,mBAAA,CAMM,OANNmE,WAMM,G,4BALJhE,mBAAA,CAAiC;IAA9BJ,KAAK,EAAC;EAAmB,6BAC5BI,mBAAA,CAAmD,cAAAsB,gBAAA,CAA1CX,KAAA,CAAAkD,aAAa,CAACC,aAAa,CAACG,IAAI,kBACzCjE,mBAAA,CAES;IAFD8C,IAAI,EAAC,QAAQ;IAAE3C,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAY,cAAA,CAAAkC,MAAA,IAAO5C,QAAA,CAAA4D,UAAU;IAAmBtE,KAAK,EAAC;kCACpEI,mBAAA,CAA4B;IAAzBJ,KAAK,EAAC;EAAc,2B,oCAM/BG,mBAAA,wBAA2B,EAC3BC,mBAAA,CA4BM,OA5BNmE,WA4BM,G,4BA3BJnE,mBAAA,CAIQ;IAJDJ,KAAK,EAAC;EAAgB,IAC3BI,mBAAA,CAA2B;IAAxBJ,KAAK,EAAC;EAAa,I,iBAAK,wBAE3B,GAAAI,mBAAA,CAAwF;IAAlFJ,KAAK,EAAC;EAAe,GAAC,uDAAqD,E,sBAEnFI,mBAAA,CAqBM;IArBDJ,KAAK,EAAC,kBAAkB;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA8C,MAAA,IAAE5C,QAAA,CAAA6C,gBAAgB;IAAyBC,UAAQ,EAAAhD,MAAA,SAAAA,MAAA,OAAAY,cAAA,CAAT,QAAiB;IAAEqC,MAAI,EAAAjD,MAAA,SAAAA,MAAA,OAAAY,cAAA,CAAAkC,MAAA,IAAU5C,QAAA,CAAAgD,cAAc,CAACJ,MAAM;MAClIlD,mBAAA,CAOE;IANAuD,GAAG,EAAC,uBAAuB;IAC3BT,IAAI,EAAC,MAAM;IACXU,MAAM,EAAC,cAAc;IACpBC,QAAM,EAAArD,MAAA,QAAAA,MAAA,MAAA8C,MAAA,IAAE5C,QAAA,CAAAoD,gBAAgB,CAACR,MAAM;IAChCS,KAAqB,EAArB;MAAA;IAAA,CAAqB;IACrBC,QAAQ,EAAR;mDAEUjD,KAAA,CAAAkD,aAAa,CAACO,kBAAkB,I,cAA5CvE,mBAAA,CAIM,OAJNwE,WAIM,EAAAjE,MAAA,SAAAA,MAAA,QAHJJ,mBAAA,CAAuC;IAApCJ,KAAK,EAAC;EAAyB,4BAClCI,mBAAA,CAAuC,WAApC,kCAAgC,qBACnCA,mBAAA,CAAsC,eAA/B,yBAAuB,oB,qBAEhCH,mBAAA,CAMM,OANNyE,WAMM,G,4BALJtE,mBAAA,CAAiC;IAA9BJ,KAAK,EAAC;EAAmB,6BAC5BI,mBAAA,CAAwD,cAAAsB,gBAAA,CAA/CX,KAAA,CAAAkD,aAAa,CAACO,kBAAkB,CAACH,IAAI,kBAC9CjE,mBAAA,CAES;IAFD8C,IAAI,EAAC,QAAQ;IAAE3C,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAY,cAAA,CAAAkC,MAAA,IAAO5C,QAAA,CAAA4D,UAAU;IAAwBtE,KAAK,EAAC;kCACzEI,mBAAA,CAA4B;IAAzBJ,KAAK,EAAC;EAAc,2B,oCAM/BG,mBAAA,0CAA6C,EAC7CC,mBAAA,CA2BM,OA3BNuE,WA2BM,G,4BA1BJvE,mBAAA,CAIQ;IAJDJ,KAAK,EAAC;EAAgB,IAC3BI,mBAAA,CAAkC;IAA/BJ,KAAK,EAAC;EAAoB,I,iBAAK,sCAElC,GAAAI,mBAAA,CAAqE;IAA/DJ,KAAK,EAAC;EAAwB,GAAC,2BAAyB,E,sBAEhEI,mBAAA,CAoBM;IApBDJ,KAAK,EAAC,kBAAkB;IAAEO,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA8C,MAAA,IAAE5C,QAAA,CAAA6C,gBAAgB;IAAaC,UAAQ,EAAAhD,MAAA,SAAAA,MAAA,OAAAY,cAAA,CAAT,QAAiB;IAAEqC,MAAI,EAAAjD,MAAA,SAAAA,MAAA,OAAAY,cAAA,CAAAkC,MAAA,IAAU5C,QAAA,CAAAgD,cAAc,CAACJ,MAAM;MACtHlD,mBAAA,CAME;IALAuD,GAAG,EAAC,aAAa;IACjBT,IAAI,EAAC,MAAM;IACXU,MAAM,EAAC,cAAc;IACpBC,QAAM,EAAArD,MAAA,SAAAA,MAAA,OAAA8C,MAAA,IAAE5C,QAAA,CAAAoD,gBAAgB,CAACR,MAAM;IAChCS,KAAqB,EAArB;MAAA;IAAA;mDAEUhD,KAAA,CAAAkD,aAAa,CAACW,MAAM,I,cAAhC3E,mBAAA,CAIM,OAJN4E,WAIM,EAAArE,MAAA,SAAAA,MAAA,QAHJJ,mBAAA,CAAuC;IAApCJ,KAAK,EAAC;EAAyB,4BAClCI,mBAAA,CAAuC,WAApC,kCAAgC,qBACnCA,mBAAA,CAAsC,eAA/B,yBAAuB,oB,qBAEhCH,mBAAA,CAMM,OANN6E,WAMM,G,4BALJ1E,mBAAA,CAAiC;IAA9BJ,KAAK,EAAC;EAAmB,6BAC5BI,mBAAA,CAA4C,cAAAsB,gBAAA,CAAnCX,KAAA,CAAAkD,aAAa,CAACW,MAAM,CAACP,IAAI,kBAClCjE,mBAAA,CAES;IAFD8C,IAAI,EAAC,QAAQ;IAAE3C,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAY,cAAA,CAAAkC,MAAA,IAAO5C,QAAA,CAAA4D,UAAU;IAAYtE,KAAK,EAAC;kCAC7DI,mBAAA,CAA4B;IAAzBJ,KAAK,EAAC;EAAc,2B,sCAOjCG,mBAAA,kBAAqB,E,ksBAYvBA,mBAAA,iCAAoC,EACzBY,KAAA,CAAAC,WAAW,U,cAAtBf,mBAAA,CA4GM,OA5GN8E,WA4GM,G,4BA3GJ3E,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAuC,YAAnC,gCAA8B,GAClCA,mBAAA,CAAgE,WAA7D,2DAAyD,E,sBAG9DA,mBAAA,CAqGM,OArGN4E,WAqGM,GApGJ5E,mBAAA,CAiBM,OAjBN6E,WAiBM,G,4BAhBJ7E,mBAAA,CAAwD;IAAjD8E,GAAG,EAAC;EAAkB,GAAC,oBAAkB,sB,gBAChD9E,mBAAA,CAcS;IAbP+E,EAAE,EAAC,kBAAkB;iEACZpE,KAAA,CAAAqE,QAAQ,CAACC,mBAAmB,GAAA/B,MAAA;IACrCU,QAAQ,EAAR,EAAQ;IACPH,QAAM,EAAArD,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEC,QAAA,CAAA4E,eAAA,IAAA5E,QAAA,CAAA4E,eAAA,IAAA7E,IAAA,CAAe;kCAExBL,mBAAA,CAAwC;IAAhCmF,KAAK,EAAC;EAAE,GAAC,gBAAc,uB,kBAC/BtF,mBAAA,CAMSuF,SAAA,QAAAC,WAAA,CALY1E,KAAA,CAAA2E,iBAAiB,EAA7BC,QAAQ;yBADjB1F,mBAAA,CAMS;MAJN2F,GAAG,EAAED,QAAQ,CAACR,EAAE;MAChBI,KAAK,EAAEI,QAAQ,CAACR;wBAEdQ,QAAQ,CAACE,aAAa,wBAAAC,WAAA;2FAVlB/E,KAAA,CAAAqE,QAAQ,CAACC,mBAAmB,E,KAezCjF,mBAAA,CASM,OATN2F,WASM,G,4BARJ3F,mBAAA,CAAsD;IAA/C8E,GAAG,EAAC;EAAiB,GAAC,mBAAiB,sB,gBAC9C9E,mBAAA,CAMY;IALV+E,EAAE,EAAC,iBAAiB;iEACXpE,KAAA,CAAAqE,QAAQ,CAACY,eAAe,GAAA1C,MAAA;IACjC2C,IAAI,EAAC,GAAG;IACRjC,QAAQ,EAAR,EAAQ;IACRkC,WAAW,EAAC;iDAHHnF,KAAA,CAAAqE,QAAQ,CAACY,eAAe,E,KAOrC5F,mBAAA,CAwBM,OAxBN+F,WAwBM,G,4BAvBJ/F,mBAAA,CAA8D;IAAvD8E,GAAG,EAAC;EAAe,GAAC,6BAA2B,sBACtD9E,mBAAA,CAqBM,OArBNgG,WAqBM,GApBJhG,mBAAA,CASQ,SATRiG,WASQ,G,gBARNjG,mBAAA,CAKE;IAJA8C,IAAI,EAAC,OAAO;iEACHnC,KAAA,CAAAqE,QAAQ,CAACkB,iBAAiB,GAAAhD,MAAA;IAClCiC,KAAK,EAAE,KAAK;IACbvB,QAAQ,EAAR;kDAFSjD,KAAA,CAAAqE,QAAQ,CAACkB,iBAAiB,E,+BAIrClG,mBAAA,CAAkC;IAA5BJ,KAAK,EAAC;EAAc,6B,6CAAQ,oBAEpC,G,GACAI,mBAAA,CASQ,SATRmG,WASQ,G,gBARNnG,mBAAA,CAKE;IAJA8C,IAAI,EAAC,OAAO;iEACHnC,KAAA,CAAAqE,QAAQ,CAACkB,iBAAiB,GAAAhD,MAAA;IAClCiC,KAAK,EAAE,IAAI;IACZvB,QAAQ,EAAR;kDAFSjD,KAAA,CAAAqE,QAAQ,CAACkB,iBAAiB,E,+BAIrClG,mBAAA,CAAkC;IAA5BJ,KAAK,EAAC;EAAc,6B,6CAAQ,qBAEpC,G,OAIOe,KAAA,CAAAqE,QAAQ,CAACkB,iBAAiB,I,cAArCrG,mBAAA,CASM,OATNuG,WASM,G,4BARJpG,mBAAA,CAAkE;IAA3D8E,GAAG,EAAC;EAAuB,GAAC,yBAAuB,sB,gBAC1D9E,mBAAA,CAMY;IALV+E,EAAE,EAAC,uBAAuB;iEACjBpE,KAAA,CAAAqE,QAAQ,CAACqB,qBAAqB,GAAAnD,MAAA;IACvC2C,IAAI,EAAC,GAAG;IACRjC,QAAQ,EAAR,EAAQ;IACRkC,WAAW,EAAC;iDAHHnF,KAAA,CAAAqE,QAAQ,CAACqB,qBAAqB,E,0CAO3CrG,mBAAA,CAsBM,OAtBNsG,WAsBM,G,4BArBJtG,mBAAA,CAAiE;IAA1D8E,GAAG,EAAC;EAAoB,GAAC,2BAAyB,sBACzD9E,mBAAA,CAmBM,OAnBNuG,WAmBM,GAlBJvG,mBAAA,CAQQ,SARRwG,WAQQ,G,gBAPNxG,mBAAA,CAIE;IAHA8C,IAAI,EAAC,OAAO;iEACHnC,KAAA,CAAAqE,QAAQ,CAACyB,mBAAmB,GAAAvD,MAAA;IACpCiC,KAAK,EAAE;kDADCxE,KAAA,CAAAqE,QAAQ,CAACyB,mBAAmB,E,+BAGvCzG,mBAAA,CAAkC;IAA5BJ,KAAK,EAAC;EAAc,6B,6CAAQ,oBAEpC,G,GACAI,mBAAA,CAQQ,SARR0G,WAQQ,G,gBAPN1G,mBAAA,CAIE;IAHA8C,IAAI,EAAC,OAAO;iEACHnC,KAAA,CAAAqE,QAAQ,CAACyB,mBAAmB,GAAAvD,MAAA;IACpCiC,KAAK,EAAE;kDADCxE,KAAA,CAAAqE,QAAQ,CAACyB,mBAAmB,E,+BAGvCzG,mBAAA,CAAkC;IAA5BJ,KAAK,EAAC;EAAc,6B,6CAAQ,kBAEpC,G,OAIJI,mBAAA,CAQM,OARN2G,WAQM,G,4BAPJ3G,mBAAA,CAAsD;IAA/C8E,GAAG,EAAC;EAAkB,GAAC,kBAAgB,sB,gBAC9C9E,mBAAA,CAKY;IAJV+E,EAAE,EAAC,kBAAkB;iEACZpE,KAAA,CAAAqE,QAAQ,CAAC4B,gBAAgB,GAAA1D,MAAA;IAClC2C,IAAI,EAAC,GAAG;IACRC,WAAW,EAAC;iDAFHnF,KAAA,CAAAqE,QAAQ,CAAC4B,gBAAgB,E,8CAQ1C7G,mBAAA,4BAA+B,EACpBY,KAAA,CAAAC,WAAW,U,cAAtBf,mBAAA,CAoDM,OApDNgH,WAoDM,G,4BAnDJ7G,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAA4B,YAAxB,qBAAmB,GACvBA,mBAAA,CAA2C,WAAxC,sCAAoC,E,sBAGzCD,mBAAA,iBAAoB,EACpBC,mBAAA,CAcM,OAdN8G,WAcM,GAbJ9G,mBAAA,CAYM,OAZN+G,WAYM,G,4BAXJ/G,mBAAA,CAAsB,YAAlB,eAAa,sBACjBA,mBAAA,CASM,OATNgH,WASM,GARJhH,mBAAA,CAGM,OAHNiH,WAGM,G,4BAFJjH,mBAAA,CAAmC,cAA7B,wBAAsB,sBAC5BA,mBAAA,CAA2C,cAArC,GAAC,GAAAsB,gBAAA,CAAGhB,QAAA,CAAA4G,cAAc,CAACvG,KAAA,CAAAwG,OAAO,kB,GAElCnH,mBAAA,CAGM,OAHNoH,WAGM,G,4BAFJpH,mBAAA,CAAyB,cAAnB,cAAY,sBAClBA,mBAAA,CAA4C,cAAtC,GAAC,GAAAsB,gBAAA,CAAGhB,QAAA,CAAA4G,cAAc,CAACvG,KAAA,CAAA0G,QAAQ,kB,SAMzCtH,mBAAA,qBAAwB,EACxBC,mBAAA,CA2BM,OA3BNsH,WA2BM,G,4BA1BJtH,mBAAA,CAA8B,YAA1B,uBAAqB,sBACzBA,mBAAA,CAwBM,OAxBNuH,WAwBM,I,kBAvBJ1H,mBAAA,CAsBMuF,SAAA,QAAAC,WAAA,CArBa1E,KAAA,CAAA6G,cAAc,EAAxBC,MAAM;yBADf5H,mBAAA,CAsBM;MApBH2F,GAAG,EAAEiC,MAAM,CAAC1C,EAAE;MACfnF,KAAK,EAAAa,eAAA,EAAC,gBAAgB;QAAAiH,QAAA,EACF/G,KAAA,CAAAqE,QAAQ,CAAC2C,iBAAiB,KAAKF,MAAM,CAAC1C;MAAE;MAC3D5E,OAAK,EAAA+C,MAAA,IAAE5C,QAAA,CAAAsH,mBAAmB,CAACH,MAAM,CAAC1C,EAAE;QAErC/E,mBAAA,CAEM,OAFN6H,WAEM,GADJ7H,mBAAA,CAAmD;MAA/CJ,KAAK,EAAAa,eAAA,CAAEH,QAAA,CAAAwH,cAAc,CAACL,MAAM,CAACM,WAAW;+BAE9C/H,mBAAA,CAGM,OAHNgI,WAGM,GAFJhI,mBAAA,CAAiC,YAAAsB,gBAAA,CAA1BmG,MAAM,CAACQ,WAAW,kBAChBR,MAAM,CAACS,WAAW,I,cAA3BrI,mBAAA,CAAyD,KAAAsI,WAAA,EAAA7G,gBAAA,CAAzBmG,MAAM,CAACS,WAAW,oB,qCAEpDlI,mBAAA,CAOM,OAPNoI,WAOM,G,gBANJpI,mBAAA,CAKE;MAJA8C,IAAI,EAAC,OAAO;MACXqC,KAAK,EAAEsC,MAAM,CAAC1C,EAAE;mEACRpE,KAAA,CAAAqE,QAAQ,CAAC2C,iBAAiB,GAAAzE,MAAA;MACnCU,QAAQ,EAAR;0DADSjD,KAAA,CAAAqE,QAAQ,CAAC2C,iBAAiB,E;6EAS/C5H,mBAAA,+BAAkC,EACvBY,KAAA,CAAAC,WAAW,U,cAAtBf,mBAAA,CAgGM,OAhGNwI,WAgGM,G,8BA/FJrI,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAA4B,YAAxB,qBAAmB,GACvBA,mBAAA,CAAsD,WAAnD,iDAA+C,E,sBAGpDA,mBAAA,CA4EM,OA5ENsI,WA4EM,GA3EJvI,mBAAA,iCAAoC,EACpCC,mBAAA,CAgBM,OAhBNuI,WAgBM,G,4BAfJvI,mBAAA,CAA6B,YAAzB,sBAAoB,sBACxBA,mBAAA,CAaM,OAbNwI,WAaM,GAZJxI,mBAAA,CAGM,OAHNyI,WAGM,G,4BAFJzI,mBAAA,CAAyB,eAAlB,YAAU,sBACjBA,mBAAA,CAAgC,cAAAsB,gBAAA,CAAvBhB,QAAA,CAAAiB,WAAW,mB,GAEtBvB,mBAAA,CAGM,OAHN0I,WAGM,G,4BAFJ1I,mBAAA,CAAuB,eAAhB,UAAQ,sBACfA,mBAAA,CAAmC,cAAAsB,gBAAA,CAA1BhB,QAAA,CAAA0B,cAAc,mB,GAEzBhC,mBAAA,CAGM,OAHN2I,WAGM,G,4BAFJ3I,mBAAA,CAAqB,eAAd,QAAM,sBACbA,mBAAA,CAAoG,cAAAsB,gBAAA,CAA1FX,KAAA,CAAAe,UAAU,EAAEI,YAAY,IAAInB,KAAA,CAAAe,UAAU,EAAEE,OAAO,EAAEE,YAAY,mC,OAK7E/B,mBAAA,+BAAkC,EAClCC,mBAAA,CAgBM,OAhBN4I,WAgBM,G,4BAfJ5I,mBAAA,CAA2B,YAAvB,oBAAkB,sBACtBA,mBAAA,CAaM,OAbN6I,WAaM,GAZJ7I,mBAAA,CAGM,OAHN8I,WAGM,G,4BAFJ9I,mBAAA,CAA6B,eAAtB,gBAAc,sBACrBA,mBAAA,CAAyC,cAAAsB,gBAAA,CAAhCX,KAAA,CAAAqE,QAAQ,CAAC+D,aAAa,iB,GAEjC/I,mBAAA,CAGM,OAHNgJ,WAGM,G,4BAFJhJ,mBAAA,CAA2B,eAApB,cAAY,sBACnBA,mBAAA,CAA8C,cAAAsB,gBAAA,CAArCX,KAAA,CAAAqE,QAAQ,CAACiE,kBAAkB,iB,GAEtCjJ,mBAAA,CAGM,OAHNkJ,WAGM,G,4BAFJlJ,mBAAA,CAA4B,eAArB,eAAa,sBACpBA,mBAAA,CAA+C,cAAAsB,gBAAA,CAAtCX,KAAA,CAAAqE,QAAQ,CAACmE,mBAAmB,iB,OAK3CpJ,mBAAA,oBAAuB,EACvBC,mBAAA,CAoBM,OApBNoJ,WAoBM,G,4BAnBJpJ,mBAAA,CAA0B,YAAtB,mBAAiB,sBACrBA,mBAAA,CAiBM,OAjBNqJ,WAiBM,GAhBJrJ,mBAAA,CAGM,OAHNsJ,WAGM,G,4BAFJtJ,mBAAA,CAAuB,eAAhB,UAAQ,sBACfA,mBAAA,CAA2C,cAAAsB,gBAAA,CAAlChB,QAAA,CAAAiJ,sBAAsB,mB,GAEjCvJ,mBAAA,CAGM,OAHNwJ,WAGM,G,4BAFJxJ,mBAAA,CAAuB,eAAhB,UAAQ,sBACfA,mBAAA,CAA2C,cAAAsB,gBAAA,CAAlCX,KAAA,CAAAqE,QAAQ,CAACY,eAAe,iB,GAEnC5F,mBAAA,CAGM,OAHNyJ,WAGM,G,4BAFJzJ,mBAAA,CAA6B,eAAtB,gBAAc,sBACrBA,mBAAA,CAA4D,cAAAsB,gBAAA,CAAnDX,KAAA,CAAAqE,QAAQ,CAACkB,iBAAiB,gC,GAE1BvF,KAAA,CAAAqE,QAAQ,CAACkB,iBAAiB,IAAIvF,KAAA,CAAAqE,QAAQ,CAACqB,qBAAqB,I,cAAvExG,mBAAA,CAGM,OAHN6J,WAGM,G,4BAFJ1J,mBAAA,CAA4B,eAArB,eAAa,sBACpBA,mBAAA,CAAiD,cAAAsB,gBAAA,CAAxCX,KAAA,CAAAqE,QAAQ,CAACqB,qBAAqB,iB,4CAK7CtG,mBAAA,oBAAuB,EACvBC,mBAAA,CAYM,OAZN2J,WAYM,G,4BAXJ3J,mBAAA,CAA4B,YAAxB,qBAAmB,sBACvBA,mBAAA,CASM,OATN4J,WASM,GARJ5J,mBAAA,CAGM,OAHN6J,WAGM,G,4BAFJ7J,mBAAA,CAA8B,eAAvB,iBAAe,sBACtBA,mBAAA,CAAyC,cAAAsB,gBAAA,CAAhChB,QAAA,CAAAwJ,oBAAoB,mB,GAE/B9J,mBAAA,CAGM,OAHN+J,WAGM,G,4BAFJ/J,mBAAA,CAA4B,eAArB,eAAa,sBACpBA,mBAAA,CAA2D,QAA3DgK,WAA2D,EAAtC,GAAC,GAAA1I,gBAAA,CAAGhB,QAAA,CAAA4G,cAAc,CAACvG,KAAA,CAAA0G,QAAQ,kB,SAMxDtH,mBAAA,0BAA6B,EAC7BC,mBAAA,CAUM,OAVNiK,WAUM,GATJjK,mBAAA,CAQQ,SARRkK,WAQQ,G,gBAPNlK,mBAAA,CAIE;IAHA8C,IAAI,EAAC,UAAU;iEACNnC,KAAA,CAAAqE,QAAQ,CAACmF,cAAc,GAAAjH,MAAA;IAChCU,QAAQ,EAAR;qDADSjD,KAAA,CAAAqE,QAAQ,CAACmF,cAAc,E,iCAGlCnK,mBAAA,CAAqC;IAA/BJ,KAAK,EAAC;EAAiB,6B,+CAAQ,kBACtB,IAAAI,mBAAA,CAA+D;IAA5DoK,IAAI,EAAC,GAAG;IAAEjK,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAY,cAAA,KAAAX,IAAA,KAAUC,QAAA,CAAA+J,SAAA,IAAA/J,QAAA,CAAA+J,SAAA,IAAAhK,IAAA,CAAS;KAAE,sBAAoB,G,+CAAI,mEAChF,G,4CAIJN,mBAAA,kBAAqB,EACrBC,mBAAA,CAqCM,OArCNsK,WAqCM,GAnCI3J,KAAA,CAAAC,WAAW,Q,cADnBf,mBAAA,CAQS;;IANPiD,IAAI,EAAC,QAAQ;IACblD,KAAK,EAAC,eAAe;IACpBO,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEC,QAAA,CAAAiK,YAAA,IAAAjK,QAAA,CAAAiK,YAAA,IAAAlK,IAAA,CAAY;oCAEpBL,mBAAA,CAAmC;IAAhCJ,KAAK,EAAC;EAAqB,4B,iBAAK,YAErC,E,yCAGQe,KAAA,CAAAC,WAAW,Q,cADnBf,mBAAA,CASS;;IAPPiD,IAAI,EAAC,QAAQ;IACblD,KAAK,EAAC,aAAa;IAClBO,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEC,QAAA,CAAAkK,QAAA,IAAAlK,QAAA,CAAAkK,QAAA,IAAAnK,IAAA,CAAQ;IACfoK,QAAQ,GAAGnK,QAAA,CAAAoK,oBAAoB;qDACjC,QAEC,GAAA1K,mBAAA,CAAoC;IAAjCJ,KAAK,EAAC;EAAsB,2B,qEAIzBe,KAAA,CAAAC,WAAW,U,cADnBf,mBAAA,CAcS;;IAZPiD,IAAI,EAAC,QAAQ;IACblD,KAAK,EAAC,YAAY;IACjB6K,QAAQ,EAAE9J,KAAA,CAAAgK,UAAU,KAAKhK,KAAA,CAAAqE,QAAQ,CAACmF;MAEnBxJ,KAAA,CAAAgK,UAAU,I,cAA1B9K,mBAAA,CAGWuF,SAAA;IAAAI,GAAA;EAAA,I,8BAFTxF,mBAAA,CAAsC;IAAnCJ,KAAK,EAAC;EAAwB,6B,+CAAK,iBAExC,G,8CACAC,mBAAA,CAGWuF,SAAA;IAAAI,GAAA;EAAA,I,8BAFTxF,mBAAA,CAAkC;IAA/BJ,KAAK,EAAC;EAAoB,6B,+CAAK,kBAEpC,G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}