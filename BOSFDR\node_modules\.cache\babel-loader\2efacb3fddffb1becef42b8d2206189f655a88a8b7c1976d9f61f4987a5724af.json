{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, vModelText as _vModelText, withKeys as _withKeys, withDirectives as _withDirectives, renderList as _renderList, Fragment as _Fragment, vModelSelect as _vModelSelect, vModelRadio as _vModelRadio, withModifiers as _withModifiers, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"admin-requests\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"d-flex justify-content-center align-items-center\",\n  style: {\n    \"min-height\": \"400px\"\n  }\n};\nconst _hoisted_4 = {\n  class: \"container-fluid py-4\"\n};\nconst _hoisted_5 = {\n  key: 0,\n  class: \"alert alert-danger alert-dismissible fade show\",\n  role: \"alert\"\n};\nconst _hoisted_6 = {\n  class: \"row mb-4\"\n};\nconst _hoisted_7 = {\n  class: \"col-12\"\n};\nconst _hoisted_8 = {\n  class: \"d-flex justify-content-between align-items-center flex-wrap\"\n};\nconst _hoisted_9 = {\n  class: \"text-muted mb-0\"\n};\nconst _hoisted_10 = {\n  key: 0,\n  class: \"ms-2 small\"\n};\nconst _hoisted_11 = {\n  class: \"d-flex gap-2 align-items-center\"\n};\nconst _hoisted_12 = {\n  class: \"real-time-status me-2\"\n};\nconst _hoisted_13 = {\n  key: 0,\n  class: \"fas fa-circle pulse\"\n};\nconst _hoisted_14 = {\n  key: 1,\n  class: \"fas fa-pause\"\n};\nconst _hoisted_15 = [\"title\"];\nconst _hoisted_16 = [\"disabled\"];\nconst _hoisted_17 = {\n  class: \"row mb-3\"\n};\nconst _hoisted_18 = {\n  class: \"col-6 col-md-3 mb-2\"\n};\nconst _hoisted_19 = {\n  class: \"card border-left-primary shadow py-1\"\n};\nconst _hoisted_20 = {\n  class: \"card-body p-2\"\n};\nconst _hoisted_21 = {\n  class: \"d-flex align-items-center\"\n};\nconst _hoisted_22 = {\n  class: \"flex-grow-1\"\n};\nconst _hoisted_23 = {\n  class: \"h6 mb-0 fw-bold text-dark\"\n};\nconst _hoisted_24 = {\n  class: \"col-6 col-md-3 mb-2\"\n};\nconst _hoisted_25 = {\n  class: \"card border-left-warning shadow py-1\"\n};\nconst _hoisted_26 = {\n  class: \"card-body p-2\"\n};\nconst _hoisted_27 = {\n  class: \"d-flex align-items-center\"\n};\nconst _hoisted_28 = {\n  class: \"flex-grow-1\"\n};\nconst _hoisted_29 = {\n  class: \"h6 mb-0 fw-bold text-dark\"\n};\nconst _hoisted_30 = {\n  class: \"col-6 col-md-3 mb-2\"\n};\nconst _hoisted_31 = {\n  class: \"card border-left-success shadow py-1\"\n};\nconst _hoisted_32 = {\n  class: \"card-body p-2\"\n};\nconst _hoisted_33 = {\n  class: \"d-flex align-items-center\"\n};\nconst _hoisted_34 = {\n  class: \"flex-grow-1\"\n};\nconst _hoisted_35 = {\n  class: \"h6 mb-0 fw-bold text-dark\"\n};\nconst _hoisted_36 = {\n  class: \"col-6 col-md-3 mb-2\"\n};\nconst _hoisted_37 = {\n  class: \"card border-left-info shadow py-1\"\n};\nconst _hoisted_38 = {\n  class: \"card-body p-2\"\n};\nconst _hoisted_39 = {\n  class: \"d-flex align-items-center\"\n};\nconst _hoisted_40 = {\n  class: \"flex-grow-1\"\n};\nconst _hoisted_41 = {\n  class: \"h6 mb-0 fw-bold text-dark\"\n};\nconst _hoisted_42 = {\n  key: 1,\n  class: \"card shadow mb-4\"\n};\nconst _hoisted_43 = {\n  class: \"card-body\"\n};\nconst _hoisted_44 = {\n  class: \"row\"\n};\nconst _hoisted_45 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_46 = {\n  class: \"col-md-2 mb-3\"\n};\nconst _hoisted_47 = [\"value\"];\nconst _hoisted_48 = {\n  class: \"col-md-2 mb-3\"\n};\nconst _hoisted_49 = {\n  class: \"col-md-2 mb-3\"\n};\nconst _hoisted_50 = {\n  class: \"col-md-2 mb-3\"\n};\nconst _hoisted_51 = {\n  class: \"col-md-1 mb-3 d-flex align-items-end\"\n};\nconst _hoisted_52 = {\n  class: \"d-flex gap-1 w-100\"\n};\nconst _hoisted_53 = {\n  key: 2,\n  class: \"card shadow mb-4\"\n};\nconst _hoisted_54 = {\n  class: \"card-header py-3 bg-warning\"\n};\nconst _hoisted_55 = {\n  class: \"m-0 fw-bold text-dark\"\n};\nconst _hoisted_56 = {\n  class: \"card-body\"\n};\nconst _hoisted_57 = {\n  class: \"row align-items-end\"\n};\nconst _hoisted_58 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_59 = [\"value\"];\nconst _hoisted_60 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_61 = {\n  class: \"d-flex gap-2\"\n};\nconst _hoisted_62 = [\"disabled\"];\nconst _hoisted_63 = {\n  class: \"d-flex justify-content-between align-items-center mb-4\"\n};\nconst _hoisted_64 = {\n  class: \"d-flex align-items-center gap-3\"\n};\nconst _hoisted_65 = {\n  class: \"btn-group\",\n  role: \"group\",\n  \"aria-label\": \"View toggle\"\n};\nconst _hoisted_66 = {\n  class: \"d-flex align-items-center gap-2\"\n};\nconst _hoisted_67 = {\n  class: \"text-muted small\"\n};\nconst _hoisted_68 = {\n  class: \"d-flex align-items-center gap-2\"\n};\nconst _hoisted_69 = {\n  key: 3,\n  class: \"requests-grid\"\n};\nconst _hoisted_70 = {\n  key: 0,\n  class: \"empty-state text-center py-5\"\n};\nconst _hoisted_71 = {\n  class: \"row g-4\"\n};\nconst _hoisted_72 = {\n  class: \"request-card-header\"\n};\nconst _hoisted_73 = {\n  class: \"d-flex justify-content-between align-items-start\"\n};\nconst _hoisted_74 = {\n  class: \"d-flex align-items-center gap-2\"\n};\nconst _hoisted_75 = [\"checked\", \"onChange\"];\nconst _hoisted_76 = {\n  class: \"request-number\"\n};\nconst _hoisted_77 = {\n  class: \"badge bg-primary\"\n};\nconst _hoisted_78 = {\n  class: \"request-actions-simple\"\n};\nconst _hoisted_79 = [\"onClick\"];\nconst _hoisted_80 = {\n  class: \"request-card-body\"\n};\nconst _hoisted_81 = {\n  class: \"client-info mb-3\"\n};\nconst _hoisted_82 = {\n  class: \"d-flex align-items-center gap-2 mb-2\"\n};\nconst _hoisted_83 = {\n  class: \"mb-0 fw-bold\"\n};\nconst _hoisted_84 = {\n  class: \"text-muted\"\n};\nconst _hoisted_85 = {\n  class: \"client-details-grid mt-2\"\n};\nconst _hoisted_86 = {\n  class: \"row g-1\"\n};\nconst _hoisted_87 = {\n  key: 0,\n  class: \"col-6\"\n};\nconst _hoisted_88 = {\n  class: \"fw-medium\"\n};\nconst _hoisted_89 = {\n  key: 1,\n  class: \"col-6\"\n};\nconst _hoisted_90 = {\n  class: \"fw-medium\"\n};\nconst _hoisted_91 = {\n  key: 2,\n  class: \"col-12\"\n};\nconst _hoisted_92 = {\n  class: \"fw-medium\"\n};\nconst _hoisted_93 = {\n  class: \"document-type mb-3\"\n};\nconst _hoisted_94 = {\n  class: \"d-flex align-items-center gap-2\"\n};\nconst _hoisted_95 = {\n  class: \"badge bg-info-subtle text-info-emphasis px-3 py-2\"\n};\nconst _hoisted_96 = {\n  class: \"request-meta mb-3\"\n};\nconst _hoisted_97 = {\n  class: \"row g-2\"\n};\nconst _hoisted_98 = {\n  class: \"col-6\"\n};\nconst _hoisted_99 = {\n  class: \"meta-item\"\n};\nconst _hoisted_100 = {\n  class: \"col-6\"\n};\nconst _hoisted_101 = {\n  class: \"meta-item\"\n};\nconst _hoisted_102 = {\n  class: \"fw-bold text-success\"\n};\nconst _hoisted_103 = {\n  class: \"request-date\"\n};\nconst _hoisted_104 = {\n  class: \"text-muted\"\n};\nconst _hoisted_105 = {\n  class: \"request-card-footer\"\n};\nconst _hoisted_106 = {\n  class: \"d-grid\"\n};\nconst _hoisted_107 = [\"onClick\"];\nconst _hoisted_108 = {\n  class: \"modern-table-container\"\n};\nconst _hoisted_109 = {\n  key: 0,\n  class: \"modern-table-empty\"\n};\nconst _hoisted_110 = {\n  class: \"compact-table-wrapper\"\n};\nconst _hoisted_111 = {\n  class: \"compact-table-header\"\n};\nconst _hoisted_112 = {\n  class: \"header-cell selection-header\"\n};\nconst _hoisted_113 = [\"checked\"];\nconst _hoisted_114 = {\n  class: \"compact-table-body\"\n};\nconst _hoisted_115 = {\n  class: \"row-cell selection-cell\"\n};\nconst _hoisted_116 = [\"checked\", \"onChange\"];\nconst _hoisted_117 = {\n  class: \"row-cell request-id-cell\"\n};\nconst _hoisted_118 = {\n  class: \"request-id-content\"\n};\nconst _hoisted_119 = {\n  class: \"request-number\"\n};\nconst _hoisted_120 = {\n  class: \"request-id-small\"\n};\nconst _hoisted_121 = {\n  class: \"row-cell client-cell\"\n};\nconst _hoisted_122 = {\n  class: \"client-compact\"\n};\nconst _hoisted_123 = {\n  class: \"client-info-compact\"\n};\nconst _hoisted_124 = {\n  class: \"client-name-compact\"\n};\nconst _hoisted_125 = {\n  class: \"client-email-compact\"\n};\nconst _hoisted_126 = {\n  class: \"client-details-compact\"\n};\nconst _hoisted_127 = {\n  key: 0,\n  class: \"detail-item\"\n};\nconst _hoisted_128 = {\n  key: 1,\n  class: \"detail-item\"\n};\nconst _hoisted_129 = {\n  key: 2,\n  class: \"detail-item\"\n};\nconst _hoisted_130 = {\n  class: \"row-cell document-cell\"\n};\nconst _hoisted_131 = {\n  class: \"document-badge\"\n};\nconst _hoisted_132 = {\n  class: \"row-cell status-cell\"\n};\nconst _hoisted_133 = {\n  class: \"row-cell amount-cell\"\n};\nconst _hoisted_134 = {\n  class: \"amount-compact\"\n};\nconst _hoisted_135 = {\n  class: \"row-cell date-cell\"\n};\nconst _hoisted_136 = {\n  class: \"date-compact\"\n};\nconst _hoisted_137 = {\n  class: \"date-main\"\n};\nconst _hoisted_138 = {\n  class: \"time-small\"\n};\nconst _hoisted_139 = {\n  class: \"row-cell actions-cell\"\n};\nconst _hoisted_140 = {\n  class: \"actions-simple\"\n};\nconst _hoisted_141 = [\"onClick\"];\nconst _hoisted_142 = {\n  key: 5,\n  class: \"pagination-container\"\n};\nconst _hoisted_143 = {\n  \"aria-label\": \"Requests pagination\"\n};\nconst _hoisted_144 = {\n  class: \"pagination pagination-sm justify-content-center mb-0\"\n};\nconst _hoisted_145 = [\"onClick\"];\nconst _hoisted_146 = {\n  key: 2,\n  class: \"modal fade show d-block\",\n  tabindex: \"-1\",\n  style: {\n    \"background-color\": \"rgba(0,0,0,0.5)\"\n  }\n};\nconst _hoisted_147 = {\n  class: \"modal-dialog modal-xl modal-dialog-scrollable\"\n};\nconst _hoisted_148 = {\n  class: \"modal-content\"\n};\nconst _hoisted_149 = {\n  class: \"modal-header\"\n};\nconst _hoisted_150 = {\n  class: \"modal-title\"\n};\nconst _hoisted_151 = {\n  class: \"modal-body\"\n};\nconst _hoisted_152 = {\n  class: \"row\"\n};\nconst _hoisted_153 = {\n  class: \"col-lg-8\"\n};\nconst _hoisted_154 = {\n  class: \"card mb-4\"\n};\nconst _hoisted_155 = {\n  class: \"card-body\"\n};\nconst _hoisted_156 = {\n  class: \"row\"\n};\nconst _hoisted_157 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_158 = {\n  class: \"mb-3\"\n};\nconst _hoisted_159 = {\n  class: \"mb-0\"\n};\nconst _hoisted_160 = {\n  class: \"mb-3\"\n};\nconst _hoisted_161 = {\n  class: \"mb-0\"\n};\nconst _hoisted_162 = {\n  class: \"badge bg-info\"\n};\nconst _hoisted_163 = {\n  class: \"mb-3\"\n};\nconst _hoisted_164 = {\n  class: \"mb-0\"\n};\nconst _hoisted_165 = {\n  class: \"mb-3\"\n};\nconst _hoisted_166 = {\n  class: \"mb-0\"\n};\nconst _hoisted_167 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_168 = {\n  class: \"mb-3\"\n};\nconst _hoisted_169 = {\n  class: \"mb-0\"\n};\nconst _hoisted_170 = {\n  class: \"mb-3\"\n};\nconst _hoisted_171 = {\n  class: \"mb-0\"\n};\nconst _hoisted_172 = {\n  class: \"mb-3\"\n};\nconst _hoisted_173 = {\n  class: \"mb-0\"\n};\nconst _hoisted_174 = {\n  class: \"mb-3\"\n};\nconst _hoisted_175 = {\n  class: \"mb-0\"\n};\nconst _hoisted_176 = {\n  class: \"card mb-4\"\n};\nconst _hoisted_177 = {\n  class: \"card-body\"\n};\nconst _hoisted_178 = {\n  class: \"row\"\n};\nconst _hoisted_179 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_180 = {\n  class: \"mb-3\"\n};\nconst _hoisted_181 = {\n  key: 0\n};\nconst _hoisted_182 = {\n  key: 1,\n  class: \"not-provided\"\n};\nconst _hoisted_183 = {\n  class: \"mb-3\"\n};\nconst _hoisted_184 = [\"href\"];\nconst _hoisted_185 = {\n  key: 1,\n  class: \"not-provided\"\n};\nconst _hoisted_186 = {\n  class: \"mb-3\"\n};\nconst _hoisted_187 = [\"href\"];\nconst _hoisted_188 = {\n  key: 1,\n  class: \"not-provided\"\n};\nconst _hoisted_189 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_190 = {\n  class: \"mb-3\"\n};\nconst _hoisted_191 = {\n  key: 0\n};\nconst _hoisted_192 = {\n  key: 1,\n  class: \"not-provided\"\n};\nconst _hoisted_193 = {\n  class: \"row\"\n};\nconst _hoisted_194 = {\n  class: \"col-12\"\n};\nconst _hoisted_195 = {\n  class: \"mb-3\"\n};\nconst _hoisted_196 = {\n  key: 0\n};\nconst _hoisted_197 = {\n  key: 1,\n  class: \"not-provided\"\n};\nconst _hoisted_198 = {\n  class: \"row\"\n};\nconst _hoisted_199 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_200 = {\n  class: \"mb-3\"\n};\nconst _hoisted_201 = {\n  key: 0\n};\nconst _hoisted_202 = {\n  key: 1,\n  class: \"not-provided\"\n};\nconst _hoisted_203 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_204 = {\n  class: \"mb-3\"\n};\nconst _hoisted_205 = {\n  key: 0\n};\nconst _hoisted_206 = {\n  key: 1,\n  class: \"not-provided\"\n};\nconst _hoisted_207 = {\n  class: \"card mb-4\"\n};\nconst _hoisted_208 = {\n  class: \"card-body\"\n};\nconst _hoisted_209 = {\n  key: 0\n};\nconst _hoisted_210 = {\n  class: \"row g-3\"\n};\nconst _hoisted_211 = {\n  class: \"document-preview-card\"\n};\nconst _hoisted_212 = {\n  class: \"document-preview-header\"\n};\nconst _hoisted_213 = {\n  class: \"document-type-badge\"\n};\nconst _hoisted_214 = {\n  class: \"document-preview-content\"\n};\nconst _hoisted_215 = [\"onClick\", \"onMouseenter\"];\nconst _hoisted_216 = [\"src\", \"alt\"];\nconst _hoisted_217 = [\"onClick\"];\nconst _hoisted_218 = {\n  class: \"pdf-preview\"\n};\nconst _hoisted_219 = {\n  class: \"pdf-info\"\n};\nconst _hoisted_220 = {\n  class: \"mb-1 fw-bold\"\n};\nconst _hoisted_221 = {\n  class: \"text-muted\"\n};\nconst _hoisted_222 = [\"onClick\"];\nconst _hoisted_223 = {\n  class: \"file-preview\"\n};\nconst _hoisted_224 = {\n  class: \"file-info\"\n};\nconst _hoisted_225 = {\n  class: \"mb-1 fw-bold\"\n};\nconst _hoisted_226 = {\n  class: \"text-muted\"\n};\nconst _hoisted_227 = [\"onClick\"];\nconst _hoisted_228 = {\n  class: \"document-preview-footer\"\n};\nconst _hoisted_229 = {\n  class: \"text-muted\"\n};\nconst _hoisted_230 = {\n  key: 1,\n  class: \"no-documents\"\n};\nconst _hoisted_231 = {\n  class: \"text-center py-4\"\n};\nconst _hoisted_232 = {\n  class: \"text-muted mb-0\"\n};\nconst _hoisted_233 = {\n  key: 0\n};\nconst _hoisted_234 = {\n  key: 1\n};\nconst _hoisted_235 = {\n  class: \"col-lg-4\"\n};\nconst _hoisted_236 = {\n  class: \"card mb-4\"\n};\nconst _hoisted_237 = {\n  class: \"card-body\"\n};\nconst _hoisted_238 = {\n  class: \"mb-3\"\n};\nconst _hoisted_239 = [\"disabled\"];\nconst _hoisted_240 = {\n  value: \"\"\n};\nconst _hoisted_241 = [\"value\"];\nconst _hoisted_242 = {\n  key: 0,\n  class: \"form-text text-muted\"\n};\nconst _hoisted_243 = {\n  class: \"d-grid\"\n};\nconst _hoisted_244 = [\"disabled\", \"title\"];\nconst _hoisted_245 = {\n  class: \"card mb-4\"\n};\nconst _hoisted_246 = {\n  class: \"card-body\"\n};\nconst _hoisted_247 = {\n  class: \"mb-3\"\n};\nconst _hoisted_248 = {\n  class: \"mb-0\"\n};\nconst _hoisted_249 = {\n  class: \"mb-3\"\n};\nconst _hoisted_250 = {\n  class: \"mb-0\"\n};\nconst _hoisted_251 = {\n  class: \"row\"\n};\nconst _hoisted_252 = {\n  class: \"col-6\"\n};\nconst _hoisted_253 = {\n  class: \"mb-2\"\n};\nconst _hoisted_254 = {\n  class: \"mb-0\"\n};\nconst _hoisted_255 = {\n  class: \"col-6\"\n};\nconst _hoisted_256 = {\n  class: \"mb-2\"\n};\nconst _hoisted_257 = {\n  class: \"mb-0\"\n};\nconst _hoisted_258 = {\n  class: \"col-6\"\n};\nconst _hoisted_259 = {\n  class: \"mb-2\"\n};\nconst _hoisted_260 = {\n  class: \"mb-0\"\n};\nconst _hoisted_261 = {\n  class: \"col-6\"\n};\nconst _hoisted_262 = {\n  class: \"mb-2\"\n};\nconst _hoisted_263 = {\n  class: \"mb-0 fw-bold text-primary\"\n};\nconst _hoisted_264 = {\n  key: 0,\n  class: \"mt-4 p-3 border rounded bg-light\"\n};\nconst _hoisted_265 = {\n  class: \"row\"\n};\nconst _hoisted_266 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_267 = {\n  class: \"mb-3\"\n};\nconst _hoisted_268 = [\"min\"];\nconst _hoisted_269 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_270 = {\n  class: \"mb-3\"\n};\nconst _hoisted_271 = {\n  class: \"d-grid\"\n};\nconst _hoisted_272 = [\"disabled\"];\nconst _hoisted_273 = {\n  key: 0\n};\nconst _hoisted_274 = {\n  key: 1\n};\nconst _hoisted_275 = {\n  key: 1,\n  class: \"mt-4 p-3 border rounded bg-light\"\n};\nconst _hoisted_276 = {\n  class: \"row\"\n};\nconst _hoisted_277 = {\n  class: \"col-md-4\"\n};\nconst _hoisted_278 = {\n  class: \"mb-3\"\n};\nconst _hoisted_279 = [\"min\"];\nconst _hoisted_280 = {\n  class: \"col-md-4\"\n};\nconst _hoisted_281 = {\n  class: \"mb-3\"\n};\nconst _hoisted_282 = {\n  class: \"col-md-4\"\n};\nconst _hoisted_283 = {\n  class: \"mb-3\"\n};\nconst _hoisted_284 = {\n  class: \"d-grid\"\n};\nconst _hoisted_285 = [\"disabled\"];\nconst _hoisted_286 = {\n  key: 0\n};\nconst _hoisted_287 = {\n  key: 1\n};\nconst _hoisted_288 = {\n  class: \"card\"\n};\nconst _hoisted_289 = {\n  class: \"card-body\"\n};\nconst _hoisted_290 = {\n  key: 0,\n  class: \"timeline\"\n};\nconst _hoisted_291 = {\n  class: \"timeline-content\"\n};\nconst _hoisted_292 = {\n  class: \"timeline-header\"\n};\nconst _hoisted_293 = {\n  class: \"text-muted ms-2\"\n};\nconst _hoisted_294 = {\n  class: \"timeline-body\"\n};\nconst _hoisted_295 = {\n  class: \"mb-1\"\n};\nconst _hoisted_296 = {\n  key: 0,\n  class: \"mb-1\"\n};\nconst _hoisted_297 = {\n  key: 1,\n  class: \"mb-0\"\n};\nconst _hoisted_298 = {\n  key: 1,\n  class: \"text-center text-muted py-3\"\n};\nconst _hoisted_299 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_300 = {\n  key: 3,\n  class: \"modal fade show d-block\",\n  tabindex: \"-1\",\n  style: {\n    \"background-color\": \"rgba(0,0,0,0.5)\"\n  }\n};\nconst _hoisted_301 = {\n  class: \"modal-dialog\"\n};\nconst _hoisted_302 = {\n  class: \"modal-content\"\n};\nconst _hoisted_303 = {\n  class: \"modal-header\"\n};\nconst _hoisted_304 = {\n  class: \"modal-body\"\n};\nconst _hoisted_305 = {\n  class: \"mb-3\"\n};\nconst _hoisted_306 = {\n  class: \"list-unstyled mt-2\"\n};\nconst _hoisted_307 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_308 = [\"disabled\"];\nconst _hoisted_309 = [\"disabled\"];\nconst _hoisted_310 = {\n  key: 0\n};\nconst _hoisted_311 = {\n  key: 1\n};\nconst _hoisted_312 = {\n  key: 4,\n  class: \"modal fade show d-block\",\n  tabindex: \"-1\",\n  style: {\n    \"background-color\": \"rgba(0,0,0,0.5)\"\n  }\n};\nconst _hoisted_313 = {\n  class: \"modal-dialog\"\n};\nconst _hoisted_314 = {\n  class: \"modal-content\"\n};\nconst _hoisted_315 = {\n  class: \"modal-header\"\n};\nconst _hoisted_316 = {\n  class: \"modal-body\"\n};\nconst _hoisted_317 = {\n  class: \"mb-3\"\n};\nconst _hoisted_318 = {\n  class: \"list-unstyled mt-2\"\n};\nconst _hoisted_319 = {\n  key: 0,\n  class: \"alert alert-danger\"\n};\nconst _hoisted_320 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_321 = [\"disabled\"];\nconst _hoisted_322 = [\"disabled\"];\nconst _hoisted_323 = {\n  key: 0\n};\nconst _hoisted_324 = {\n  key: 1\n};\nconst _hoisted_325 = {\n  class: \"modal-dialog modal-xl modal-dialog-centered\"\n};\nconst _hoisted_326 = {\n  class: \"modal-content\"\n};\nconst _hoisted_327 = {\n  class: \"modal-header sticky-header\"\n};\nconst _hoisted_328 = {\n  class: \"modal-title\"\n};\nconst _hoisted_329 = {\n  class: \"header-controls\"\n};\nconst _hoisted_330 = [\"disabled\"];\nconst _hoisted_331 = {\n  class: \"modal-body text-center p-0\"\n};\nconst _hoisted_332 = {\n  class: \"image-modal-container\"\n};\nconst _hoisted_333 = [\"src\", \"alt\"];\nconst _hoisted_334 = {\n  class: \"error-placeholder modal-error\"\n};\nconst _hoisted_335 = [\"disabled\"];\nconst _hoisted_336 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_337 = {\n  class: \"d-flex justify-content-between align-items-center w-100\"\n};\nconst _hoisted_338 = {\n  class: \"image-info\"\n};\nconst _hoisted_339 = {\n  class: \"badge bg-info me-2\"\n};\nconst _hoisted_340 = {\n  class: \"text-muted\"\n};\nconst _hoisted_341 = {\n  class: \"image-actions\"\n};\nconst _hoisted_342 = [\"disabled\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_AdminHeader = _resolveComponent(\"AdminHeader\");\n  const _component_AdminSidebar = _resolveComponent(\"AdminSidebar\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createElementVNode(\"div\", _hoisted_1, [_createVNode(_component_AdminHeader, {\n    userName: $data.adminData?.first_name || 'Admin',\n    showUserDropdown: $data.showUserDropdown,\n    sidebarCollapsed: $data.sidebarCollapsed,\n    activeMenu: $options.activeMenu,\n    onSidebarToggle: $options.handleSidebarToggle,\n    onUserDropdownToggle: $options.handleUserDropdownToggle,\n    onMenuAction: $options.handleMenuAction,\n    onOpenRequestModal: $options.handleOpenRequestModal,\n    onLogout: $options.handleLogout\n  }, null, 8 /* PROPS */, [\"userName\", \"showUserDropdown\", \"sidebarCollapsed\", \"activeMenu\", \"onSidebarToggle\", \"onUserDropdownToggle\", \"onMenuAction\", \"onOpenRequestModal\", \"onLogout\"]), _createCommentVNode(\" Mobile Overlay \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"mobile-overlay\", {\n      active: !$data.sidebarCollapsed && $data.isMobile\n    }]),\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.closeMobileSidebar && $options.closeMobileSidebar(...args))\n  }, null, 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_AdminSidebar, {\n    collapsed: $data.sidebarCollapsed,\n    activeMenu: $options.activeMenu,\n    onMenuChange: $options.handleMenuChange,\n    onLogout: $options.handleLogout,\n    onToggleSidebar: $options.handleSidebarToggle\n  }, null, 8 /* PROPS */, [\"collapsed\", \"activeMenu\", \"onMenuChange\", \"onLogout\", \"onToggleSidebar\"]), _createElementVNode(\"main\", {\n    class: _normalizeClass([\"main-content\", {\n      'sidebar-collapsed': $data.sidebarCollapsed\n    }])\n  }, [_createCommentVNode(\" Loading State \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, _cache[51] || (_cache[51] = [_createElementVNode(\"div\", {\n    class: \"spinner-border text-primary\",\n    role: \"status\"\n  }, [_createElementVNode(\"span\", {\n    class: \"visually-hidden\"\n  }, \"Loading...\")], -1 /* HOISTED */)]))) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Main Content \"), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" Error Message \"), $data.errorMessage ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_cache[52] || (_cache[52] = _createElementVNode(\"i\", {\n    class: \"fas fa-exclamation-triangle me-2\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.errorMessage) + \" \", 1 /* TEXT */), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn-close\",\n    onClick: _cache[1] || (_cache[1] = $event => $data.errorMessage = ''),\n    \"aria-label\": \"Close\"\n  })])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Page Header \"), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", null, [_createElementVNode(\"p\", _hoisted_9, [$data.lastRefresh ? (_openBlock(), _createElementBlock(\"span\", _hoisted_10, [_cache[53] || (_cache[53] = _createElementVNode(\"i\", {\n    class: \"fas fa-clock text-muted\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" Last updated: \" + _toDisplayString($options.formatTime($data.lastRefresh)), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_11, [_createCommentVNode(\" Real-time status indicator \"), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"badge\", $data.autoRefreshEnabled ? 'bg-success' : 'bg-secondary'])\n  }, [$data.autoRefreshEnabled ? (_openBlock(), _createElementBlock(\"i\", _hoisted_13)) : (_openBlock(), _createElementBlock(\"i\", _hoisted_14)), _createTextVNode(\" \" + _toDisplayString($data.autoRefreshEnabled ? 'Live' : 'Paused'), 1 /* TEXT */)], 2 /* CLASS */)]), _createElementVNode(\"button\", {\n    class: \"btn btn-outline-secondary btn-sm\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.toggleAutoRefresh && $options.toggleAutoRefresh(...args)),\n    title: $data.autoRefreshEnabled ? 'Disable auto-refresh' : 'Enable auto-refresh'\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass([\"fas\", $data.autoRefreshEnabled ? 'fa-pause' : 'fa-play'])\n  }, null, 2 /* CLASS */)], 8 /* PROPS */, _hoisted_15), _createElementVNode(\"button\", {\n    class: \"btn btn-outline-primary btn-sm\",\n    onClick: _cache[3] || (_cache[3] = $event => $data.showFilters = !$data.showFilters)\n  }, [_cache[54] || (_cache[54] = _createElementVNode(\"i\", {\n    class: \"fas fa-filter me-1\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.showFilters ? 'Hide' : 'Show') + \" Filters \", 1 /* TEXT */)]), _createCommentVNode(\" <button class=\\\"btn btn-success btn-sm\\\" @click=\\\"exportRequests\\\" :disabled=\\\"loading\\\">\\n                    <i class=\\\"fas fa-download me-1\\\"></i>\\n                    Export CSV\\n                  </button> \"), _createElementVNode(\"button\", {\n    class: \"btn btn-primary btn-sm\",\n    onClick: _cache[4] || (_cache[4] = (...args) => $options.refreshRequestsData && $options.refreshRequestsData(...args)),\n    disabled: $data.loading\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass([\"fas fa-sync-alt me-1\", {\n      'fa-spin': $data.loading\n    }])\n  }, null, 2 /* CLASS */), _cache[55] || (_cache[55] = _createTextVNode(\" Refresh \"))], 8 /* PROPS */, _hoisted_16)])])])]), _createCommentVNode(\" Request Statistics \"), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_cache[56] || (_cache[56] = _createElementVNode(\"div\", {\n    class: \"text-xs fw-bold text-primary text-uppercase mb-1\"\n  }, \"Total Requests\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_23, _toDisplayString($data.requestStats.total || 0), 1 /* TEXT */)]), _cache[57] || (_cache[57] = _createElementVNode(\"i\", {\n    class: \"fas fa-file-alt fa-lg text-muted ms-2\"\n  }, null, -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_cache[58] || (_cache[58] = _createElementVNode(\"div\", {\n    class: \"text-xs fw-bold text-warning text-uppercase mb-1\"\n  }, \"Pending\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_29, _toDisplayString($data.requestStats.pending || 0), 1 /* TEXT */)]), _cache[59] || (_cache[59] = _createElementVNode(\"i\", {\n    class: \"fas fa-clock fa-lg text-muted ms-2\"\n  }, null, -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"div\", _hoisted_34, [_cache[60] || (_cache[60] = _createElementVNode(\"div\", {\n    class: \"text-xs fw-bold text-success text-uppercase mb-1\"\n  }, \"Completed\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_35, _toDisplayString($data.requestStats.completed || 0), 1 /* TEXT */)]), _cache[61] || (_cache[61] = _createElementVNode(\"i\", {\n    class: \"fas fa-check-circle fa-lg text-muted ms-2\"\n  }, null, -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [_createElementVNode(\"div\", _hoisted_40, [_cache[62] || (_cache[62] = _createElementVNode(\"div\", {\n    class: \"text-xs fw-bold text-info text-uppercase mb-1\"\n  }, \"Approved\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_41, _toDisplayString($data.requestStats.approved || 0), 1 /* TEXT */)]), _cache[63] || (_cache[63] = _createElementVNode(\"i\", {\n    class: \"fas fa-thumbs-up fa-lg text-muted ms-2\"\n  }, null, -1 /* HOISTED */))])])])])]), _createCommentVNode(\" Filters Panel \"), $data.showFilters ? (_openBlock(), _createElementBlock(\"div\", _hoisted_42, [_cache[73] || (_cache[73] = _createElementVNode(\"div\", {\n    class: \"card-header py-3\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"m-0 fw-bold text-primary\"\n  }, \"Filter Requests\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_43, [_createElementVNode(\"div\", _hoisted_44, [_createElementVNode(\"div\", _hoisted_45, [_cache[64] || (_cache[64] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Search\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.filters.search = $event),\n    placeholder: \"Search by name, email, or request number\",\n    onKeyup: _cache[6] || (_cache[6] = _withKeys((...args) => $options.applyFilters && $options.applyFilters(...args), [\"enter\"]))\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $data.filters.search]])]), _createElementVNode(\"div\", _hoisted_46, [_cache[66] || (_cache[66] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Status\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select\",\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.filters.status = $event)\n  }, [_cache[65] || (_cache[65] = _createElementVNode(\"option\", {\n    value: \"\"\n  }, \"All Statuses\", -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.statusOptions, status => {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: status.id,\n      value: status.status_name\n    }, _toDisplayString($options.formatStatus(status.status_name)), 9 /* TEXT, PROPS */, _hoisted_47);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.filters.status]])]), _createElementVNode(\"div\", _hoisted_48, [_cache[68] || (_cache[68] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Document Type\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select\",\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.filters.document_type = $event)\n  }, _cache[67] || (_cache[67] = [_createElementVNode(\"option\", {\n    value: \"\"\n  }, \"All Types\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"barangay_clearance\"\n  }, \"Barangay Clearance\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"cedula\"\n  }, \"Cedula\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.filters.document_type]])]), _createElementVNode(\"div\", _hoisted_49, [_cache[69] || (_cache[69] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Date From\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"date\",\n    class: \"form-control\",\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.filters.date_from = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.filters.date_from]])]), _createElementVNode(\"div\", _hoisted_50, [_cache[70] || (_cache[70] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Date To\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"date\",\n    class: \"form-control\",\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.filters.date_to = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.filters.date_to]])]), _createElementVNode(\"div\", _hoisted_51, [_createElementVNode(\"div\", _hoisted_52, [_createElementVNode(\"button\", {\n    class: \"btn btn-primary btn-sm\",\n    onClick: _cache[11] || (_cache[11] = (...args) => $options.applyFilters && $options.applyFilters(...args))\n  }, _cache[71] || (_cache[71] = [_createElementVNode(\"i\", {\n    class: \"fas fa-search\"\n  }, null, -1 /* HOISTED */)])), _createElementVNode(\"button\", {\n    class: \"btn btn-outline-secondary btn-sm\",\n    onClick: _cache[12] || (_cache[12] = (...args) => $options.clearFilters && $options.clearFilters(...args))\n  }, _cache[72] || (_cache[72] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times\"\n  }, null, -1 /* HOISTED */)]))])])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Bulk Actions Panel \"), $data.selectedRequests.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_53, [_createElementVNode(\"div\", _hoisted_54, [_createElementVNode(\"h6\", _hoisted_55, [_cache[74] || (_cache[74] = _createElementVNode(\"i\", {\n    class: \"fas fa-tasks me-2\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" Bulk Actions (\" + _toDisplayString($data.selectedRequests.length) + \" selected) \", 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_56, [_createElementVNode(\"div\", _hoisted_57, [_createElementVNode(\"div\", _hoisted_58, [_cache[76] || (_cache[76] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Action\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select\",\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.bulkAction = $event)\n  }, [_cache[75] || (_cache[75] = _createElementVNode(\"option\", {\n    value: \"\"\n  }, \"Select Action\", -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.statusOptions, status => {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: status.id,\n      value: status.id\n    }, \" Change to \" + _toDisplayString($options.formatStatus(status.status_name)), 9 /* TEXT, PROPS */, _hoisted_59);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.bulkAction]])]), _createElementVNode(\"div\", _hoisted_60, [_createElementVNode(\"div\", _hoisted_61, [_createElementVNode(\"button\", {\n    class: \"btn btn-warning\",\n    onClick: _cache[14] || (_cache[14] = (...args) => $options.performBulkAction && $options.performBulkAction(...args)),\n    disabled: !$data.bulkAction\n  }, _cache[77] || (_cache[77] = [_createElementVNode(\"i\", {\n    class: \"fas fa-play me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Apply \")]), 8 /* PROPS */, _hoisted_62), _createElementVNode(\"button\", {\n    class: \"btn btn-outline-secondary\",\n    onClick: _cache[15] || (_cache[15] = $event => $data.selectedRequests = [])\n  }, _cache[78] || (_cache[78] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Cancel \")]))])])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" View Toggle \"), _createElementVNode(\"div\", _hoisted_63, [_createElementVNode(\"div\", _hoisted_64, [_createElementVNode(\"div\", _hoisted_65, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"radio\",\n    class: \"btn-check\",\n    name: \"viewMode\",\n    id: \"cardView\",\n    \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.viewMode = $event),\n    value: \"card\",\n    autocomplete: \"off\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.viewMode]]), _cache[79] || (_cache[79] = _createElementVNode(\"label\", {\n    class: \"btn btn-outline-primary btn-sm\",\n    for: \"cardView\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-th-large me-1\"\n  }), _createTextVNode(\"Cards \")], -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"radio\",\n    class: \"btn-check\",\n    name: \"viewMode\",\n    id: \"tableView\",\n    \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $data.viewMode = $event),\n    value: \"table\",\n    autocomplete: \"off\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.viewMode]]), _cache[80] || (_cache[80] = _createElementVNode(\"label\", {\n    class: \"btn btn-outline-primary btn-sm\",\n    for: \"tableView\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-table me-1\"\n  }), _createTextVNode(\"Table \")], -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_66, [_createElementVNode(\"span\", _hoisted_67, \" Showing \" + _toDisplayString(($data.pagination.currentPage - 1) * $data.pagination.itemsPerPage + 1) + \" - \" + _toDisplayString(Math.min($data.pagination.currentPage * $data.pagination.itemsPerPage, $data.pagination.totalItems)) + \" of \" + _toDisplayString($data.pagination.totalItems) + \" requests \", 1 /* TEXT */), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select form-select-sm\",\n    style: {\n      \"width\": \"auto\"\n    },\n    \"onUpdate:modelValue\": _cache[18] || (_cache[18] = $event => $data.pagination.itemsPerPage = $event),\n    onChange: _cache[19] || (_cache[19] = $event => $options.changeItemsPerPage($data.pagination.itemsPerPage))\n  }, _cache[81] || (_cache[81] = [_createElementVNode(\"option\", {\n    value: \"10\"\n  }, \"10 per page\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"25\"\n  }, \"25 per page\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"50\"\n  }, \"50 per page\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"100\"\n  }, \"100 per page\", -1 /* HOISTED */)]), 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.pagination.itemsPerPage]])])]), _createElementVNode(\"div\", _hoisted_68, [$data.requests.length > 0 ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    class: \"btn btn-sm btn-outline-secondary\",\n    onClick: _cache[20] || (_cache[20] = (...args) => $options.selectAllRequests && $options.selectAllRequests(...args))\n  }, [_cache[82] || (_cache[82] = _createElementVNode(\"i\", {\n    class: \"fas fa-check-square me-1\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.selectedRequests.length === $data.requests.length ? 'Deselect All' : 'Select All'), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Card View \"), $data.viewMode === 'card' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_69, [_createCommentVNode(\" Empty State \"), $data.requests.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_70, _cache[83] || (_cache[83] = [_createElementVNode(\"div\", {\n    class: \"empty-state-icon mb-3\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-inbox fa-4x text-muted\"\n  })], -1 /* HOISTED */), _createElementVNode(\"h5\", {\n    class: \"text-muted mb-2\"\n  }, \"No Document Requests Found\", -1 /* HOISTED */), _createElementVNode(\"p\", {\n    class: \"text-muted\"\n  }, \"There are no document requests matching your current filters.\", -1 /* HOISTED */)]))) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Request Cards \"), _createElementVNode(\"div\", _hoisted_71, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.requests, request => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: request.id,\n      class: \"col-xl-4 col-lg-6 col-md-6\"\n    }, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"request-card\", {\n        'selected': $data.selectedRequests.includes(request.id)\n      }])\n    }, [_createCommentVNode(\" Card Header \"), _createElementVNode(\"div\", _hoisted_72, [_createElementVNode(\"div\", _hoisted_73, [_createElementVNode(\"div\", _hoisted_74, [_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      class: \"form-check-input\",\n      checked: $data.selectedRequests.includes(request.id),\n      onChange: $event => $options.toggleRequestSelection(request.id)\n    }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_75), _createElementVNode(\"div\", _hoisted_76, [_createElementVNode(\"span\", _hoisted_77, _toDisplayString(request.request_number), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_78, [_createElementVNode(\"button\", {\n      class: \"btn btn-sm btn-primary\",\n      onClick: $event => $options.viewRequestDetails(request.id),\n      title: \"View & Manage Request\"\n    }, [...(_cache[84] || (_cache[84] = [_createElementVNode(\"i\", {\n      class: \"fas fa-edit me-1\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\"Manage \")]))], 8 /* PROPS */, _hoisted_79)])])]), _createCommentVNode(\" Card Body \"), _createElementVNode(\"div\", _hoisted_80, [_createCommentVNode(\" Client Info \"), _createElementVNode(\"div\", _hoisted_81, [_createElementVNode(\"div\", _hoisted_82, [_cache[85] || (_cache[85] = _createElementVNode(\"div\", {\n      class: \"client-avatar\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-user-circle fa-2x text-primary\"\n    })], -1 /* HOISTED */)), _createElementVNode(\"div\", null, [_createElementVNode(\"h6\", _hoisted_83, _toDisplayString(request.client_name), 1 /* TEXT */), _createElementVNode(\"small\", _hoisted_84, _toDisplayString(request.client_email), 1 /* TEXT */)])]), _createCommentVNode(\" Additional Client Details \"), _createElementVNode(\"div\", _hoisted_85, [_createElementVNode(\"div\", _hoisted_86, [$options.getCivilStatusName(request.client_civil_status_id) ? (_openBlock(), _createElementBlock(\"div\", _hoisted_87, [_cache[86] || (_cache[86] = _createElementVNode(\"small\", {\n      class: \"text-muted d-block\"\n    }, \"Civil Status\", -1 /* HOISTED */)), _createElementVNode(\"small\", _hoisted_88, _toDisplayString($options.getCivilStatusName(request.client_civil_status_id)), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), request.client_nationality ? (_openBlock(), _createElementBlock(\"div\", _hoisted_89, [_cache[87] || (_cache[87] = _createElementVNode(\"small\", {\n      class: \"text-muted d-block\"\n    }, \"Nationality\", -1 /* HOISTED */)), _createElementVNode(\"small\", _hoisted_90, _toDisplayString(request.client_nationality), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), $options.getResidencyDisplay(request) ? (_openBlock(), _createElementBlock(\"div\", _hoisted_91, [_cache[88] || (_cache[88] = _createElementVNode(\"small\", {\n      class: \"text-muted d-block\"\n    }, \"Years of Residency\", -1 /* HOISTED */)), _createElementVNode(\"small\", _hoisted_92, _toDisplayString($options.getResidencyDisplay(request)), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])])]), _createCommentVNode(\" Document Type \"), _createElementVNode(\"div\", _hoisted_93, [_createElementVNode(\"div\", _hoisted_94, [_cache[89] || (_cache[89] = _createElementVNode(\"i\", {\n      class: \"fas fa-file-alt text-info\"\n    }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_95, _toDisplayString(request.document_type), 1 /* TEXT */)])]), _createCommentVNode(\" Status and Amount \"), _createElementVNode(\"div\", _hoisted_96, [_createElementVNode(\"div\", _hoisted_97, [_createElementVNode(\"div\", _hoisted_98, [_createElementVNode(\"div\", _hoisted_99, [_cache[90] || (_cache[90] = _createElementVNode(\"small\", {\n      class: \"text-muted d-block\"\n    }, \"Status\", -1 /* HOISTED */)), _createElementVNode(\"span\", {\n      class: _normalizeClass([\"badge\", `bg-${$options.getStatusColor(request.status_name)}`])\n    }, _toDisplayString($options.formatStatus(request.status_name)), 3 /* TEXT, CLASS */)])]), _createElementVNode(\"div\", _hoisted_100, [_createElementVNode(\"div\", _hoisted_101, [_cache[91] || (_cache[91] = _createElementVNode(\"small\", {\n      class: \"text-muted d-block\"\n    }, \"Amount\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_102, _toDisplayString($options.formatCurrency(request.total_fee)), 1 /* TEXT */)])])])]), _createCommentVNode(\" Date \"), _createElementVNode(\"div\", _hoisted_103, [_createElementVNode(\"small\", _hoisted_104, [_cache[92] || (_cache[92] = _createElementVNode(\"i\", {\n      class: \"fas fa-calendar-alt me-1\"\n    }, null, -1 /* HOISTED */)), _createTextVNode(\" Submitted \" + _toDisplayString($options.formatDate(request.requested_at)), 1 /* TEXT */)])])]), _createCommentVNode(\" Card Footer \"), _createElementVNode(\"div\", _hoisted_105, [_createElementVNode(\"div\", _hoisted_106, [_createElementVNode(\"button\", {\n      class: \"btn btn-sm btn-primary\",\n      onClick: $event => $options.viewRequestDetails(request.id)\n    }, [...(_cache[93] || (_cache[93] = [_createElementVNode(\"i\", {\n      class: \"fas fa-edit me-1\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\"Manage Request \")]))], 8 /* PROPS */, _hoisted_107)])])], 2 /* CLASS */)]);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 4\n  }, [_createCommentVNode(\" Table View \"), _createElementVNode(\"div\", _hoisted_108, [_createCommentVNode(\" Empty State \"), $data.requests.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_109, _cache[94] || (_cache[94] = [_createStaticVNode(\"<div class=\\\"empty-content\\\" data-v-1d5e65f3><div class=\\\"empty-icon\\\" data-v-1d5e65f3><i class=\\\"fas fa-inbox\\\" data-v-1d5e65f3></i></div><h6 class=\\\"empty-title\\\" data-v-1d5e65f3>No Document Requests Found</h6><p class=\\\"empty-text\\\" data-v-1d5e65f3>There are no document requests matching your current filters.</p></div>\", 1)]))) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Modern Compact Table \"), _createElementVNode(\"div\", _hoisted_110, [_createCommentVNode(\" Table Header \"), _createElementVNode(\"div\", _hoisted_111, [_createElementVNode(\"div\", _hoisted_112, [_createElementVNode(\"input\", {\n    type: \"checkbox\",\n    class: \"form-check-input\",\n    checked: $data.selectedRequests.length === $data.requests.length && $data.requests.length > 0,\n    onChange: _cache[21] || (_cache[21] = (...args) => $options.selectAllRequests && $options.selectAllRequests(...args))\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_113)]), _cache[95] || (_cache[95] = _createStaticVNode(\"<div class=\\\"header-cell\\\" data-v-1d5e65f3>Request ID</div><div class=\\\"header-cell\\\" data-v-1d5e65f3>Client</div><div class=\\\"header-cell\\\" data-v-1d5e65f3>Document</div><div class=\\\"header-cell\\\" data-v-1d5e65f3>Status</div><div class=\\\"header-cell\\\" data-v-1d5e65f3>Amount</div><div class=\\\"header-cell\\\" data-v-1d5e65f3>Date</div><div class=\\\"header-cell\\\" data-v-1d5e65f3>Actions</div>\", 7))]), _createCommentVNode(\" Table Body \"), _createElementVNode(\"div\", _hoisted_114, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.requests, request => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: request.id,\n      class: _normalizeClass([\"compact-row\", {\n        'selected': $data.selectedRequests.includes(request.id)\n      }])\n    }, [_createCommentVNode(\" Selection \"), _createElementVNode(\"div\", _hoisted_115, [_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      class: \"form-check-input\",\n      checked: $data.selectedRequests.includes(request.id),\n      onChange: $event => $options.toggleRequestSelection(request.id)\n    }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_116)]), _createCommentVNode(\" Request ID \"), _createElementVNode(\"div\", _hoisted_117, [_createElementVNode(\"div\", _hoisted_118, [_createElementVNode(\"span\", _hoisted_119, _toDisplayString(request.request_number), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_120, _toDisplayString(request.id), 1 /* TEXT */)])]), _createCommentVNode(\" Client \"), _createElementVNode(\"div\", _hoisted_121, [_createElementVNode(\"div\", _hoisted_122, [_cache[99] || (_cache[99] = _createElementVNode(\"div\", {\n      class: \"client-avatar-tiny\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-user\"\n    })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_123, [_createElementVNode(\"div\", _hoisted_124, _toDisplayString(request.client_name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_125, _toDisplayString(request.client_email), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_126, [$options.getCivilStatusName(request.client_civil_status_id) ? (_openBlock(), _createElementBlock(\"span\", _hoisted_127, [_cache[96] || (_cache[96] = _createElementVNode(\"i\", {\n      class: \"fas fa-ring me-1\"\n    }, null, -1 /* HOISTED */)), _createTextVNode(_toDisplayString($options.getCivilStatusName(request.client_civil_status_id)), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), request.client_nationality ? (_openBlock(), _createElementBlock(\"span\", _hoisted_128, [_cache[97] || (_cache[97] = _createElementVNode(\"i\", {\n      class: \"fas fa-flag me-1\"\n    }, null, -1 /* HOISTED */)), _createTextVNode(_toDisplayString(request.client_nationality), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), $options.getResidencyDisplay(request) ? (_openBlock(), _createElementBlock(\"span\", _hoisted_129, [_cache[98] || (_cache[98] = _createElementVNode(\"i\", {\n      class: \"fas fa-home me-1\"\n    }, null, -1 /* HOISTED */)), _createTextVNode(_toDisplayString($options.getResidencyDisplay(request)), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])])])]), _createCommentVNode(\" Document Type \"), _createElementVNode(\"div\", _hoisted_130, [_createElementVNode(\"span\", _hoisted_131, [_cache[100] || (_cache[100] = _createElementVNode(\"i\", {\n      class: \"fas fa-file-alt\"\n    }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString(request.document_type), 1 /* TEXT */)])]), _createCommentVNode(\" Status \"), _createElementVNode(\"div\", _hoisted_132, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"status-compact\", `status-${$options.getStatusColor(request.status_name)}`])\n    }, [_cache[101] || (_cache[101] = _createElementVNode(\"i\", {\n      class: \"fas fa-circle\"\n    }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($options.formatStatus(request.status_name)), 1 /* TEXT */)], 2 /* CLASS */)]), _createCommentVNode(\" Amount \"), _createElementVNode(\"div\", _hoisted_133, [_createElementVNode(\"span\", _hoisted_134, _toDisplayString($options.formatCurrency(request.total_fee)), 1 /* TEXT */)]), _createCommentVNode(\" Date \"), _createElementVNode(\"div\", _hoisted_135, [_createElementVNode(\"div\", _hoisted_136, [_createElementVNode(\"span\", _hoisted_137, _toDisplayString($options.formatDate(request.requested_at)), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_138, _toDisplayString($options.formatTime(request.requested_at)), 1 /* TEXT */)])]), _createCommentVNode(\" Actions \"), _createElementVNode(\"div\", _hoisted_139, [_createElementVNode(\"div\", _hoisted_140, [_createElementVNode(\"button\", {\n      class: \"action-btn-sm primary-btn-sm\",\n      onClick: $event => $options.viewRequestDetails(request.id),\n      title: \"View & Manage Request\"\n    }, [...(_cache[102] || (_cache[102] = [_createElementVNode(\"i\", {\n      class: \"fas fa-edit\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_141)])])], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */))])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" Pagination \"), $data.pagination.totalPages > 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_142, [_createElementVNode(\"nav\", _hoisted_143, [_createElementVNode(\"ul\", _hoisted_144, [_createElementVNode(\"li\", {\n    class: _normalizeClass([\"page-item\", {\n      disabled: $data.pagination.currentPage === 1\n    }])\n  }, [_createElementVNode(\"a\", {\n    class: \"page-link\",\n    href: \"#\",\n    onClick: _cache[22] || (_cache[22] = _withModifiers($event => $options.changePage($data.pagination.currentPage - 1), [\"prevent\"]))\n  }, _cache[103] || (_cache[103] = [_createElementVNode(\"i\", {\n    class: \"fas fa-chevron-left\"\n  }, null, -1 /* HOISTED */)]))], 2 /* CLASS */), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(Math.min($data.pagination.totalPages, 10), page => {\n    return _openBlock(), _createElementBlock(\"li\", {\n      key: page,\n      class: _normalizeClass([\"page-item\", {\n        active: page === $data.pagination.currentPage\n      }])\n    }, [_createElementVNode(\"a\", {\n      class: \"page-link\",\n      href: \"#\",\n      onClick: _withModifiers($event => $options.changePage(page), [\"prevent\"])\n    }, _toDisplayString(page), 9 /* TEXT, PROPS */, _hoisted_145)], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */)), _createElementVNode(\"li\", {\n    class: _normalizeClass([\"page-item\", {\n      disabled: $data.pagination.currentPage === $data.pagination.totalPages\n    }])\n  }, [_createElementVNode(\"a\", {\n    class: \"page-link\",\n    href: \"#\",\n    onClick: _cache[23] || (_cache[23] = _withModifiers($event => $options.changePage($data.pagination.currentPage + 1), [\"prevent\"]))\n  }, _cache[104] || (_cache[104] = [_createElementVNode(\"i\", {\n    class: \"fas fa-chevron-right\"\n  }, null, -1 /* HOISTED */)]))], 2 /* CLASS */)])])])) : _createCommentVNode(\"v-if\", true)])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" Request Details Modal \"), $data.showRequestDetails && $data.currentRequest ? (_openBlock(), _createElementBlock(\"div\", _hoisted_146, [_createElementVNode(\"div\", _hoisted_147, [_createElementVNode(\"div\", _hoisted_148, [_createElementVNode(\"div\", _hoisted_149, [_createElementVNode(\"h5\", _hoisted_150, [_cache[105] || (_cache[105] = _createElementVNode(\"i\", {\n    class: \"fas fa-file-alt me-2\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" Request Details - \" + _toDisplayString($data.currentRequest.request_number), 1 /* TEXT */)]), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn-close\",\n    onClick: _cache[24] || (_cache[24] = $event => $data.showRequestDetails = false)\n  })]), _createElementVNode(\"div\", _hoisted_151, [_createElementVNode(\"div\", _hoisted_152, [_createCommentVNode(\" Left Column - Request Information \"), _createElementVNode(\"div\", _hoisted_153, [_createCommentVNode(\" Basic Information \"), _createElementVNode(\"div\", _hoisted_154, [_cache[114] || (_cache[114] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-info-circle me-2\"\n  }), _createTextVNode(\"Request Information\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_155, [_createElementVNode(\"div\", _hoisted_156, [_createElementVNode(\"div\", _hoisted_157, [_createElementVNode(\"div\", _hoisted_158, [_cache[106] || (_cache[106] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Request Number\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_159, _toDisplayString($data.currentRequest.request_number), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_160, [_cache[107] || (_cache[107] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Document Type\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_161, [_createElementVNode(\"span\", _hoisted_162, _toDisplayString($data.currentRequest.document_type), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_163, [_cache[108] || (_cache[108] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Purpose Category\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_164, _toDisplayString($data.currentRequest.purpose_category), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_165, [_cache[109] || (_cache[109] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Purpose Details\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_166, _toDisplayString($data.currentRequest.purpose_details || 'Not specified'), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_167, [_createElementVNode(\"div\", _hoisted_168, [_cache[110] || (_cache[110] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Current Status\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_169, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"badge\", `bg-${$options.getStatusColor($data.currentRequest.status_name)}`])\n  }, _toDisplayString($options.formatStatus($data.currentRequest.status_name)), 3 /* TEXT, CLASS */)])]), _createElementVNode(\"div\", _hoisted_170, [_cache[111] || (_cache[111] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Priority\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_171, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"badge\", $data.currentRequest.priority === 'high' ? 'bg-danger' : $data.currentRequest.priority === 'medium' ? 'bg-warning' : 'bg-secondary'])\n  }, _toDisplayString($data.currentRequest.priority || 'Normal'), 3 /* TEXT, CLASS */)])]), _createElementVNode(\"div\", _hoisted_172, [_cache[112] || (_cache[112] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Delivery Method\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_173, _toDisplayString($data.currentRequest.delivery_method || 'Pickup'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_174, [_cache[113] || (_cache[113] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Date Submitted\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_175, _toDisplayString($options.formatDateTime($data.currentRequest.requested_at)), 1 /* TEXT */)])])])])]), _createCommentVNode(\" Client Information \"), _createElementVNode(\"div\", _hoisted_176, [_cache[122] || (_cache[122] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user me-2\"\n  }), _createTextVNode(\"Client Information\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_177, [_createCommentVNode(\" Basic Information \"), _createElementVNode(\"div\", _hoisted_178, [_createElementVNode(\"div\", _hoisted_179, [_createElementVNode(\"div\", _hoisted_180, [_cache[115] || (_cache[115] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Full Name\", -1 /* HOISTED */)), _createElementVNode(\"p\", {\n    class: _normalizeClass([\"mb-0\", {\n      'text-muted': $options.getClientFullName($data.currentRequest) === 'Not provided'\n    }])\n  }, [$options.getClientFullName($data.currentRequest) !== 'Not provided' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_181, _toDisplayString($options.getClientFullName($data.currentRequest)), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_182, \"Not provided\"))], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_183, [_cache[116] || (_cache[116] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Email Address\", -1 /* HOISTED */)), _createElementVNode(\"p\", {\n    class: _normalizeClass([\"mb-0\", {\n      'text-muted': !$data.currentRequest.client_email\n    }])\n  }, [$data.currentRequest.client_email ? (_openBlock(), _createElementBlock(\"a\", {\n    key: 0,\n    href: `mailto:${$data.currentRequest.client_email}`\n  }, _toDisplayString($data.currentRequest.client_email), 9 /* TEXT, PROPS */, _hoisted_184)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_185, \"Not provided\"))], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_186, [_cache[117] || (_cache[117] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Phone Number\", -1 /* HOISTED */)), _createElementVNode(\"p\", {\n    class: _normalizeClass([\"mb-0\", {\n      'text-muted': !$data.currentRequest.client_phone\n    }])\n  }, [$data.currentRequest.client_phone ? (_openBlock(), _createElementBlock(\"a\", {\n    key: 0,\n    href: `tel:${$data.currentRequest.client_phone}`\n  }, _toDisplayString($data.currentRequest.client_phone), 9 /* TEXT, PROPS */, _hoisted_187)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_188, \"Not provided\"))], 2 /* CLASS */)])]), _createElementVNode(\"div\", _hoisted_189, [_createElementVNode(\"div\", _hoisted_190, [_cache[118] || (_cache[118] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Civil Status\", -1 /* HOISTED */)), _createElementVNode(\"p\", {\n    class: _normalizeClass([\"mb-0\", {\n      'text-muted': !$data.currentRequest.client_civil_status_id\n    }])\n  }, [$options.getCivilStatusName($data.currentRequest.client_civil_status_id) ? (_openBlock(), _createElementBlock(\"span\", _hoisted_191, _toDisplayString($options.getCivilStatusName($data.currentRequest.client_civil_status_id)), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_192, \"Not provided\"))], 2 /* CLASS */)])])]), _createCommentVNode(\" Address Information \"), _createElementVNode(\"div\", _hoisted_193, [_createElementVNode(\"div\", _hoisted_194, [_createElementVNode(\"div\", _hoisted_195, [_cache[119] || (_cache[119] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Complete Address\", -1 /* HOISTED */)), _createElementVNode(\"p\", {\n    class: _normalizeClass([\"mb-0\", {\n      'text-muted': !$options.getClientFullAddress($data.currentRequest)\n    }])\n  }, [$options.getClientFullAddress($data.currentRequest) ? (_openBlock(), _createElementBlock(\"span\", _hoisted_196, _toDisplayString($options.getClientFullAddress($data.currentRequest)), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_197, \"Not provided\"))], 2 /* CLASS */)])])]), _createCommentVNode(\" Additional Information \"), _createElementVNode(\"div\", _hoisted_198, [_createElementVNode(\"div\", _hoisted_199, [_createElementVNode(\"div\", _hoisted_200, [_cache[120] || (_cache[120] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Nationality\", -1 /* HOISTED */)), _createElementVNode(\"p\", {\n    class: _normalizeClass([\"mb-0\", {\n      'text-muted': !$data.currentRequest.client_nationality\n    }])\n  }, [$data.currentRequest.client_nationality ? (_openBlock(), _createElementBlock(\"span\", _hoisted_201, _toDisplayString($data.currentRequest.client_nationality), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_202, \"Not provided\"))], 2 /* CLASS */)])]), _createElementVNode(\"div\", _hoisted_203, [_createElementVNode(\"div\", _hoisted_204, [_cache[121] || (_cache[121] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Years of Residency\", -1 /* HOISTED */)), _createElementVNode(\"p\", {\n    class: _normalizeClass([\"mb-0\", {\n      'text-muted': !$options.getResidencyDisplay($data.currentRequest)\n    }])\n  }, [$options.getResidencyDisplay($data.currentRequest) ? (_openBlock(), _createElementBlock(\"span\", _hoisted_205, _toDisplayString($options.getResidencyDisplay($data.currentRequest)), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_206, \"Not provided\"))], 2 /* CLASS */)])])])])]), _createCommentVNode(\" Uploaded Documents \"), _createElementVNode(\"div\", _hoisted_207, [_cache[135] || (_cache[135] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-paperclip me-2\"\n  }), _createTextVNode(\"Uploaded Documents\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_208, [$data.currentRequest.uploaded_documents && $data.currentRequest.uploaded_documents.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_209, [_createElementVNode(\"div\", _hoisted_210, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.currentRequest.uploaded_documents, document => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: document.id,\n      class: \"col-md-4\"\n    }, [_createElementVNode(\"div\", _hoisted_211, [_createElementVNode(\"div\", _hoisted_212, [_createElementVNode(\"div\", _hoisted_213, [_cache[123] || (_cache[123] = _createElementVNode(\"i\", {\n      class: \"fas fa-file-alt me-1\"\n    }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($options.getDocumentTypeDisplayName(document.document_type)), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_214, [_createCommentVNode(\" Image Preview \"), $options.isImageFile(document.mime_type) ? (_openBlock(), _createElementBlock(\"div\", {\n      key: 0,\n      class: \"image-preview\",\n      onClick: $event => $options.openImageModal(document),\n      onMouseenter: $event => $options.preloadImage(document)\n    }, [_createCommentVNode(\" Successfully loaded image \"), $data.documentUrls[document.id] ? (_openBlock(), _createElementBlock(\"img\", {\n      key: 0,\n      src: $data.documentUrls[document.id],\n      alt: document.document_name,\n      class: \"document-image\",\n      onError: _cache[25] || (_cache[25] = (...args) => $options.handleImageError && $options.handleImageError(...args))\n    }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_216)) : $data.loadingDocuments.has(document.id) ? (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createCommentVNode(\" Loading state \"), _cache[124] || (_cache[124] = _createElementVNode(\"div\", {\n      class: \"loading-placeholder\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-spinner fa-spin\"\n    }), _createElementVNode(\"span\", null, \"Loading image...\")], -1 /* HOISTED */))], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : $data.failedDocuments.has(document.id) ? (_openBlock(), _createElementBlock(_Fragment, {\n      key: 2\n    }, [_createCommentVNode(\" Failed state with retry option \"), _createElementVNode(\"div\", {\n      class: \"error-placeholder\",\n      onClick: _withModifiers($event => $options.retryLoadDocument(document), [\"stop\"])\n    }, [...(_cache[125] || (_cache[125] = [_createElementVNode(\"i\", {\n      class: \"fas fa-exclamation-triangle\"\n    }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Failed to load\", -1 /* HOISTED */), _createElementVNode(\"small\", null, \"Click to retry\", -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_217)], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n      key: 3\n    }, [_createCommentVNode(\" Initial state (not yet attempted) \"), _cache[126] || (_cache[126] = _createElementVNode(\"div\", {\n      class: \"loading-placeholder\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-image\"\n    }), _createElementVNode(\"span\", null, \"Click to load\")], -1 /* HOISTED */))], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _cache[127] || (_cache[127] = _createElementVNode(\"div\", {\n      class: \"image-overlay\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-search-plus\"\n    }), _createElementVNode(\"span\", null, \"Click to view\")], -1 /* HOISTED */))], 40 /* PROPS, NEED_HYDRATION */, _hoisted_215)) : $options.isPdfFile(document.mime_type) ? (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createCommentVNode(\" PDF Preview \"), _createElementVNode(\"div\", _hoisted_218, [_cache[129] || (_cache[129] = _createElementVNode(\"div\", {\n      class: \"pdf-icon\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-file-pdf fa-3x text-danger\"\n    })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_219, [_createElementVNode(\"p\", _hoisted_220, _toDisplayString(document.document_name), 1 /* TEXT */), _createElementVNode(\"small\", _hoisted_221, _toDisplayString($options.formatFileSize(document.file_size)), 1 /* TEXT */)]), _createElementVNode(\"button\", {\n      class: \"btn btn-sm btn-outline-primary mt-2\",\n      onClick: $event => $options.downloadDocument(document)\n    }, [...(_cache[128] || (_cache[128] = [_createElementVNode(\"i\", {\n      class: \"fas fa-download me-1\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\"Download \")]))], 8 /* PROPS */, _hoisted_222)])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n      key: 2\n    }, [_createCommentVNode(\" Other File Types \"), _createElementVNode(\"div\", _hoisted_223, [_cache[131] || (_cache[131] = _createElementVNode(\"div\", {\n      class: \"file-icon\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-file fa-3x text-secondary\"\n    })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_224, [_createElementVNode(\"p\", _hoisted_225, _toDisplayString(document.document_name), 1 /* TEXT */), _createElementVNode(\"small\", _hoisted_226, _toDisplayString($options.formatFileSize(document.file_size)), 1 /* TEXT */)]), _createElementVNode(\"button\", {\n      class: \"btn btn-sm btn-outline-primary mt-2\",\n      onClick: $event => $options.downloadDocument(document)\n    }, [...(_cache[130] || (_cache[130] = [_createElementVNode(\"i\", {\n      class: \"fas fa-download me-1\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\"Download \")]))], 8 /* PROPS */, _hoisted_227)])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))]), _createElementVNode(\"div\", _hoisted_228, [_createElementVNode(\"small\", _hoisted_229, [_cache[132] || (_cache[132] = _createElementVNode(\"i\", {\n      class: \"fas fa-clock me-1\"\n    }, null, -1 /* HOISTED */)), _createTextVNode(\" Uploaded \" + _toDisplayString($options.formatDate(document.created_at)), 1 /* TEXT */)])])])]);\n  }), 128 /* KEYED_FRAGMENT */))])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_230, [_createElementVNode(\"div\", _hoisted_231, [_cache[133] || (_cache[133] = _createElementVNode(\"i\", {\n    class: \"fas fa-folder-open fa-3x text-muted mb-3\"\n  }, null, -1 /* HOISTED */)), _cache[134] || (_cache[134] = _createElementVNode(\"h6\", {\n    class: \"text-muted\"\n  }, \"No Documents Uploaded\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_232, [$data.currentRequest.document_type === 'Cedula' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_233, \" Cedula requests typically don't require supporting documents. \")) : (_openBlock(), _createElementBlock(\"span\", _hoisted_234, \" The client hasn't uploaded any supporting documents yet. \"))])])]))])])]), _createCommentVNode(\" Right Column - Status Management \"), _createElementVNode(\"div\", _hoisted_235, [_createCommentVNode(\" Status Management \"), _createElementVNode(\"div\", _hoisted_236, [_cache[139] || (_cache[139] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-tasks me-2\"\n  }), _createTextVNode(\"Status Management\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_237, [_createElementVNode(\"div\", _hoisted_238, [_cache[137] || (_cache[137] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Change Status\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select\",\n    \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.statusUpdateForm.status_id = $event),\n    disabled: $options.getAvailableStatusOptions().length === 0\n  }, [_createElementVNode(\"option\", _hoisted_240, _toDisplayString($options.getAvailableStatusOptions().length === 0 ? 'No status changes available' : 'Select new status'), 1 /* TEXT */), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.getAvailableStatusOptions(), status => {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: status.id,\n      value: status.id\n    }, _toDisplayString($options.formatStatus(status.status_name)), 9 /* TEXT, PROPS */, _hoisted_241);\n  }), 128 /* KEYED_FRAGMENT */))], 8 /* PROPS */, _hoisted_239), [[_vModelSelect, $data.statusUpdateForm.status_id]]), $options.getAvailableStatusOptions().length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_242, [_cache[136] || (_cache[136] = _createElementVNode(\"i\", {\n    class: \"fas fa-info-circle me-1\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" This request status cannot be changed (\" + _toDisplayString($options.formatStatus($data.currentRequest.status_name)) + \") \", 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" Single Action Button \"), _createElementVNode(\"div\", _hoisted_243, [_createElementVNode(\"button\", {\n    class: \"btn btn-primary\",\n    onClick: _cache[27] || (_cache[27] = (...args) => $options.updateRequestStatusFromModal && $options.updateRequestStatusFromModal(...args)),\n    disabled: !$data.statusUpdateForm.status_id || !$options.isValidStatusChange($data.currentRequest.status_name, $data.statusUpdateForm.status_id),\n    title: $options.getUpdateButtonTitle()\n  }, [_cache[138] || (_cache[138] = _createElementVNode(\"i\", {\n    class: \"fas fa-save me-1\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($options.getActionButtonText()), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_244)])])]), _createCommentVNode(\" Payment Information \"), _createElementVNode(\"div\", _hoisted_245, [_cache[157] || (_cache[157] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-credit-card me-2\"\n  }), _createTextVNode(\"Payment Information\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_246, [_createElementVNode(\"div\", _hoisted_247, [_cache[140] || (_cache[140] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Payment Method\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_248, _toDisplayString($data.currentRequest.payment_method || 'Not specified'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_249, [_cache[141] || (_cache[141] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Payment Status\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_250, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"badge\", $options.getPaymentStatusColor($data.currentRequest.payment_status)])\n  }, _toDisplayString($options.formatPaymentStatus($data.currentRequest.payment_status)), 3 /* TEXT, CLASS */)])]), _createElementVNode(\"div\", _hoisted_251, [_createElementVNode(\"div\", _hoisted_252, [_createElementVNode(\"div\", _hoisted_253, [_cache[142] || (_cache[142] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold small\"\n  }, \"Base Fee\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_254, _toDisplayString($options.formatCurrency($data.currentRequest.base_fee)), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_255, [_createElementVNode(\"div\", _hoisted_256, [_cache[143] || (_cache[143] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold small\"\n  }, \"Additional Fees\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_257, _toDisplayString($options.formatCurrency($data.currentRequest.additional_fees)), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_258, [_createElementVNode(\"div\", _hoisted_259, [_cache[144] || (_cache[144] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold small\"\n  }, \"Processing Fee\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_260, _toDisplayString($options.formatCurrency($data.currentRequest.processing_fee)), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_261, [_createElementVNode(\"div\", _hoisted_262, [_cache[145] || (_cache[145] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold small\"\n  }, \"Total Amount\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_263, _toDisplayString($options.formatCurrency($data.currentRequest.total_fee)), 1 /* TEXT */)])])]), _createCommentVNode(\" In-Person Payment Verification \"), $options.needsPaymentVerification($data.currentRequest) ? (_openBlock(), _createElementBlock(\"div\", _hoisted_264, [_cache[150] || (_cache[150] = _createElementVNode(\"h6\", {\n    class: \"text-primary mb-3\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-money-bill me-2\"\n  }), _createTextVNode(\" Verify In-Person Payment \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_265, [_createElementVNode(\"div\", _hoisted_266, [_createElementVNode(\"div\", _hoisted_267, [_cache[146] || (_cache[146] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Amount Received *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"number\",\n    class: \"form-control\",\n    \"onUpdate:modelValue\": _cache[28] || (_cache[28] = $event => $data.paymentVerificationForm.amount_received = $event),\n    min: $data.currentRequest.total_fee,\n    step: \"0.01\",\n    placeholder: \"Enter amount received\"\n  }, null, 8 /* PROPS */, _hoisted_268), [[_vModelText, $data.paymentVerificationForm.amount_received]])])]), _createElementVNode(\"div\", _hoisted_269, [_createElementVNode(\"div\", _hoisted_270, [_cache[147] || (_cache[147] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Receipt Number\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    \"onUpdate:modelValue\": _cache[29] || (_cache[29] = $event => $data.paymentVerificationForm.receipt_number = $event),\n    placeholder: \"Enter receipt number\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.paymentVerificationForm.receipt_number]])])])]), _createElementVNode(\"div\", _hoisted_271, [_createElementVNode(\"button\", {\n    class: \"btn btn-success\",\n    onClick: _cache[30] || (_cache[30] = (...args) => $options.verifyInPersonPayment && $options.verifyInPersonPayment(...args)),\n    disabled: !$data.paymentVerificationForm.amount_received || $data.paymentVerificationForm.loading\n  }, [_cache[149] || (_cache[149] = _createElementVNode(\"i\", {\n    class: \"fas fa-check-circle me-1\"\n  }, null, -1 /* HOISTED */)), $data.paymentVerificationForm.loading ? (_openBlock(), _createElementBlock(\"span\", _hoisted_273, _cache[148] || (_cache[148] = [_createElementVNode(\"i\", {\n    class: \"fas fa-spinner fa-spin me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Verifying... \")]))) : (_openBlock(), _createElementBlock(\"span\", _hoisted_274, \"Verify Payment\"))], 8 /* PROPS */, _hoisted_272)])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Pickup Scheduling \"), $options.canSchedulePickup($data.currentRequest) ? (_openBlock(), _createElementBlock(\"div\", _hoisted_275, [_cache[156] || (_cache[156] = _createElementVNode(\"h6\", {\n    class: \"text-info mb-3\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-calendar-alt me-2\"\n  }), _createTextVNode(\" Schedule Pickup Appointment \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_276, [_createElementVNode(\"div\", _hoisted_277, [_createElementVNode(\"div\", _hoisted_278, [_cache[151] || (_cache[151] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Date *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"date\",\n    class: \"form-control\",\n    \"onUpdate:modelValue\": _cache[31] || (_cache[31] = $event => $data.pickupScheduleForm.scheduled_date = $event),\n    min: $options.getTomorrowDate()\n  }, null, 8 /* PROPS */, _hoisted_279), [[_vModelText, $data.pickupScheduleForm.scheduled_date]])])]), _createElementVNode(\"div\", _hoisted_280, [_createElementVNode(\"div\", _hoisted_281, [_cache[152] || (_cache[152] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Start Time *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"time\",\n    class: \"form-control\",\n    \"onUpdate:modelValue\": _cache[32] || (_cache[32] = $event => $data.pickupScheduleForm.scheduled_time_start = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.pickupScheduleForm.scheduled_time_start]])])]), _createElementVNode(\"div\", _hoisted_282, [_createElementVNode(\"div\", _hoisted_283, [_cache[153] || (_cache[153] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"End Time *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"time\",\n    class: \"form-control\",\n    \"onUpdate:modelValue\": _cache[33] || (_cache[33] = $event => $data.pickupScheduleForm.scheduled_time_end = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.pickupScheduleForm.scheduled_time_end]])])])]), _createElementVNode(\"div\", _hoisted_284, [_createElementVNode(\"button\", {\n    class: \"btn btn-info\",\n    onClick: _cache[34] || (_cache[34] = (...args) => $options.schedulePickup && $options.schedulePickup(...args)),\n    disabled: !$options.isPickupFormValid() || $data.pickupScheduleForm.loading\n  }, [_cache[155] || (_cache[155] = _createElementVNode(\"i\", {\n    class: \"fas fa-calendar-check me-1\"\n  }, null, -1 /* HOISTED */)), $data.pickupScheduleForm.loading ? (_openBlock(), _createElementBlock(\"span\", _hoisted_286, _cache[154] || (_cache[154] = [_createElementVNode(\"i\", {\n    class: \"fas fa-spinner fa-spin me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Scheduling... \")]))) : (_openBlock(), _createElementBlock(\"span\", _hoisted_287, \"Schedule Pickup\"))], 8 /* PROPS */, _hoisted_285)])])) : _createCommentVNode(\"v-if\", true)])])])]), _createCommentVNode(\" Status History Timeline \"), _createElementVNode(\"div\", _hoisted_288, [_cache[163] || (_cache[163] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-history me-2\"\n  }), _createTextVNode(\"Status History\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_289, [$data.currentRequest.status_history && $data.currentRequest.status_history.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_290, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.currentRequest.status_history, (history, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: history.id,\n      class: _normalizeClass([\"timeline-item\", {\n        'timeline-item-last': index === $data.currentRequest.status_history.length - 1\n      }])\n    }, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"timeline-marker\", `bg-${$options.getStatusColor(history.new_status_name)}`])\n    }, [...(_cache[158] || (_cache[158] = [_createElementVNode(\"i\", {\n      class: \"fas fa-circle\"\n    }, null, -1 /* HOISTED */)]))], 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_291, [_createElementVNode(\"div\", _hoisted_292, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"badge\", `bg-${$options.getStatusColor(history.new_status_name)}`])\n    }, _toDisplayString($options.formatStatus(history.new_status_name)), 3 /* TEXT, CLASS */), _createElementVNode(\"small\", _hoisted_293, _toDisplayString($options.formatDateTime(history.changed_at)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_294, [_createElementVNode(\"p\", _hoisted_295, [_cache[159] || (_cache[159] = _createElementVNode(\"strong\", null, \"Changed by:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString(history.changed_by_name), 1 /* TEXT */)]), history.old_status_name ? (_openBlock(), _createElementBlock(\"p\", _hoisted_296, [_cache[160] || (_cache[160] = _createElementVNode(\"strong\", null, \"From:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($options.formatStatus(history.old_status_name)), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), history.change_reason ? (_openBlock(), _createElementBlock(\"p\", _hoisted_297, [_cache[161] || (_cache[161] = _createElementVNode(\"strong\", null, \"Reason:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString(history.change_reason), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])])], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */))])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_298, _cache[162] || (_cache[162] = [_createElementVNode(\"i\", {\n    class: \"fas fa-history fa-2x mb-2\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"p\", null, \"No status history available\", -1 /* HOISTED */)])))])])]), _createElementVNode(\"div\", _hoisted_299, [_createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-secondary\",\n    onClick: _cache[35] || (_cache[35] = $event => $data.showRequestDetails = false)\n  }, _cache[164] || (_cache[164] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Close \")])), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-primary\",\n    onClick: _cache[36] || (_cache[36] = (...args) => $options.refreshRequestDetails && $options.refreshRequestDetails(...args))\n  }, _cache[165] || (_cache[165] = [_createElementVNode(\"i\", {\n    class: \"fas fa-sync-alt me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Refresh \")]))])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Quick Reject Modal \"), $data.showQuickReject && $data.selectedRequestForReject ? (_openBlock(), _createElementBlock(\"div\", _hoisted_300, [_createElementVNode(\"div\", _hoisted_301, [_createElementVNode(\"div\", _hoisted_302, [_createElementVNode(\"div\", _hoisted_303, [_cache[166] || (_cache[166] = _createElementVNode(\"h5\", {\n    class: \"modal-title\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-times-circle text-danger me-2\"\n  }), _createTextVNode(\" Reject Request \")], -1 /* HOISTED */)), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn-close\",\n    onClick: _cache[37] || (_cache[37] = (...args) => $options.closeQuickRejectModal && $options.closeQuickRejectModal(...args))\n  })]), _createElementVNode(\"div\", _hoisted_304, [_cache[171] || (_cache[171] = _createElementVNode(\"div\", {\n    class: \"alert alert-warning\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-exclamation-triangle me-2\"\n  }), _createTextVNode(\" You are about to reject this document request. This action will notify the client immediately. \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_305, [_cache[170] || (_cache[170] = _createElementVNode(\"strong\", null, \"Request Details:\", -1 /* HOISTED */)), _createElementVNode(\"ul\", _hoisted_306, [_createElementVNode(\"li\", null, [_cache[167] || (_cache[167] = _createElementVNode(\"strong\", null, \"Request Number:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.selectedRequestForReject.request_number), 1 /* TEXT */)]), _createElementVNode(\"li\", null, [_cache[168] || (_cache[168] = _createElementVNode(\"strong\", null, \"Document Type:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.selectedRequestForReject.document_type), 1 /* TEXT */)]), _createElementVNode(\"li\", null, [_cache[169] || (_cache[169] = _createElementVNode(\"strong\", null, \"Client:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.selectedRequestForReject.client_name), 1 /* TEXT */)])])])]), _createElementVNode(\"div\", _hoisted_307, [_createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-secondary\",\n    onClick: _cache[38] || (_cache[38] = (...args) => $options.closeQuickRejectModal && $options.closeQuickRejectModal(...args)),\n    disabled: $data.quickRejectForm.loading\n  }, _cache[172] || (_cache[172] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Cancel \")]), 8 /* PROPS */, _hoisted_308), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-danger\",\n    onClick: _cache[39] || (_cache[39] = (...args) => $options.confirmQuickReject && $options.confirmQuickReject(...args)),\n    disabled: $data.quickRejectForm.loading\n  }, [_cache[174] || (_cache[174] = _createElementVNode(\"i\", {\n    class: \"fas fa-times-circle me-1\"\n  }, null, -1 /* HOISTED */)), $data.quickRejectForm.loading ? (_openBlock(), _createElementBlock(\"span\", _hoisted_310, _cache[173] || (_cache[173] = [_createElementVNode(\"i\", {\n    class: \"fas fa-spinner fa-spin me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Rejecting... \")]))) : (_openBlock(), _createElementBlock(\"span\", _hoisted_311, \"Reject Request\"))], 8 /* PROPS */, _hoisted_309)])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Quick Approve Modal \"), $data.showQuickApprove && $data.selectedRequestForApprove ? (_openBlock(), _createElementBlock(\"div\", _hoisted_312, [_createElementVNode(\"div\", _hoisted_313, [_createElementVNode(\"div\", _hoisted_314, [_createElementVNode(\"div\", _hoisted_315, [_cache[175] || (_cache[175] = _createElementVNode(\"h5\", {\n    class: \"modal-title\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-check-circle text-success me-2\"\n  }), _createTextVNode(\" Approve Request \")], -1 /* HOISTED */)), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn-close\",\n    onClick: _cache[40] || (_cache[40] = (...args) => $options.closeQuickApproveModal && $options.closeQuickApproveModal(...args))\n  })]), _createElementVNode(\"div\", _hoisted_316, [_cache[181] || (_cache[181] = _createElementVNode(\"div\", {\n    class: \"alert alert-info\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-info-circle me-2\"\n  }), _createTextVNode(\" You are about to approve this document request. This action will notify the client immediately and move the request to the next processing stage. \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_317, [_cache[179] || (_cache[179] = _createElementVNode(\"strong\", null, \"Request Details:\", -1 /* HOISTED */)), _createElementVNode(\"ul\", _hoisted_318, [_createElementVNode(\"li\", null, [_cache[176] || (_cache[176] = _createElementVNode(\"strong\", null, \"Request Number:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.selectedRequestForApprove.request_number), 1 /* TEXT */)]), _createElementVNode(\"li\", null, [_cache[177] || (_cache[177] = _createElementVNode(\"strong\", null, \"Document Type:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.selectedRequestForApprove.document_type), 1 /* TEXT */)]), _createElementVNode(\"li\", null, [_cache[178] || (_cache[178] = _createElementVNode(\"strong\", null, \"Client:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.selectedRequestForApprove.client_name), 1 /* TEXT */)])])]), $data.quickApproveForm.error ? (_openBlock(), _createElementBlock(\"div\", _hoisted_319, [_cache[180] || (_cache[180] = _createElementVNode(\"i\", {\n    class: \"fas fa-exclamation-triangle me-2\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.quickApproveForm.error), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_320, [_createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-secondary\",\n    onClick: _cache[41] || (_cache[41] = (...args) => $options.closeQuickApproveModal && $options.closeQuickApproveModal(...args)),\n    disabled: $data.quickApproveForm.loading\n  }, _cache[182] || (_cache[182] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Cancel \")]), 8 /* PROPS */, _hoisted_321), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-success\",\n    onClick: _cache[42] || (_cache[42] = (...args) => $options.confirmQuickApprove && $options.confirmQuickApprove(...args)),\n    disabled: $data.quickApproveForm.loading\n  }, [_cache[184] || (_cache[184] = _createElementVNode(\"i\", {\n    class: \"fas fa-check-circle me-1\"\n  }, null, -1 /* HOISTED */)), $data.quickApproveForm.loading ? (_openBlock(), _createElementBlock(\"span\", _hoisted_323, _cache[183] || (_cache[183] = [_createElementVNode(\"i\", {\n    class: \"fas fa-spinner fa-spin me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Approving... \")]))) : (_openBlock(), _createElementBlock(\"span\", _hoisted_324, \"Approve Request\"))], 8 /* PROPS */, _hoisted_322)])])])])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)])]), _createCommentVNode(\" Image Modal \"), $data.showImageModal && $data.selectedImage ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"modal fade show d-block\",\n    tabindex: \"-1\",\n    style: {\n      \"background-color\": \"rgba(0,0,0,0.8)\"\n    },\n    onClick: _cache[50] || (_cache[50] = _withModifiers((...args) => $options.closeImageModal && $options.closeImageModal(...args), [\"self\"]))\n  }, [_createElementVNode(\"div\", _hoisted_325, [_createElementVNode(\"div\", _hoisted_326, [_createElementVNode(\"div\", _hoisted_327, [_createElementVNode(\"h5\", _hoisted_328, [_cache[185] || (_cache[185] = _createElementVNode(\"i\", {\n    class: \"fas fa-image me-2\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.selectedImage.document_name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_329, [_createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-outline-light btn-sm me-2\",\n    onClick: _cache[43] || (_cache[43] = $event => $options.downloadDocument($data.selectedImage)),\n    disabled: !$data.documentUrls[$data.selectedImage.id] || $data.imageLoadingInModal,\n    title: \"Download\"\n  }, _cache[186] || (_cache[186] = [_createElementVNode(\"i\", {\n    class: \"fas fa-download\"\n  }, null, -1 /* HOISTED */)]), 8 /* PROPS */, _hoisted_330), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn-close btn-close-white\",\n    onClick: _cache[44] || (_cache[44] = (...args) => $options.closeImageModal && $options.closeImageModal(...args)),\n    \"aria-label\": \"Close\",\n    title: \"Close\"\n  })])]), _createElementVNode(\"div\", _hoisted_331, [_createElementVNode(\"div\", _hoisted_332, [_createCommentVNode(\" Successfully loaded image \"), $data.documentUrls[$data.selectedImage.id] && !$data.imageLoadingInModal ? (_openBlock(), _createElementBlock(\"img\", {\n    key: 0,\n    src: $data.documentUrls[$data.selectedImage.id],\n    alt: $data.selectedImage.document_name,\n    class: \"modal-image\",\n    onError: _cache[45] || (_cache[45] = (...args) => $options.handleImageError && $options.handleImageError(...args)),\n    onLoad: _cache[46] || (_cache[46] = (...args) => $options.onModalImageLoad && $options.onModalImageLoad(...args)),\n    loading: \"lazy\"\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_333)) : $data.imageLoadingInModal || $data.loadingDocuments.has($data.selectedImage.id) ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Loading state \"), _cache[187] || (_cache[187] = _createStaticVNode(\"<div class=\\\"loading-placeholder modal-loading\\\" data-v-1d5e65f3><div class=\\\"loading-content\\\" data-v-1d5e65f3><i class=\\\"fas fa-spinner fa-spin fa-3x mb-3\\\" data-v-1d5e65f3></i><span class=\\\"loading-text\\\" data-v-1d5e65f3>Loading high-resolution image...</span><div class=\\\"loading-progress mt-2\\\" data-v-1d5e65f3><div class=\\\"progress-bar\\\" data-v-1d5e65f3></div></div></div></div>\", 1))], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : $data.modalImageError || $data.failedDocuments.has($data.selectedImage.id) ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" Failed state \"), _createElementVNode(\"div\", _hoisted_334, [_cache[189] || (_cache[189] = _createElementVNode(\"i\", {\n    class: \"fas fa-exclamation-triangle fa-3x mb-3\"\n  }, null, -1 /* HOISTED */)), _cache[190] || (_cache[190] = _createElementVNode(\"span\", {\n    class: \"error-text\"\n  }, \"Failed to load image\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n    class: \"btn btn-outline-light mt-3\",\n    onClick: _cache[47] || (_cache[47] = $event => $options.retryLoadDocument($data.selectedImage)),\n    disabled: $data.imageLoadingInModal\n  }, _cache[188] || (_cache[188] = [_createElementVNode(\"i\", {\n    class: \"fas fa-redo me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\"Retry \")]), 8 /* PROPS */, _hoisted_335)])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 3\n  }, [_createCommentVNode(\" Fallback \"), _cache[191] || (_cache[191] = _createElementVNode(\"div\", {\n    class: \"loading-placeholder modal-loading\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-image fa-3x mb-3\"\n  }), _createElementVNode(\"span\", {\n    class: \"loading-text\"\n  }, \"Preparing image...\")], -1 /* HOISTED */))], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_336, [_createElementVNode(\"div\", _hoisted_337, [_createElementVNode(\"div\", _hoisted_338, [_createElementVNode(\"span\", _hoisted_339, _toDisplayString($options.getDocumentTypeDisplayName($data.selectedImage.document_type)), 1 /* TEXT */), _createElementVNode(\"small\", _hoisted_340, _toDisplayString($options.formatFileSize($data.selectedImage.file_size)) + \" • Uploaded \" + _toDisplayString($options.formatDate($data.selectedImage.created_at)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_341, [_createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-outline-primary me-2\",\n    onClick: _cache[48] || (_cache[48] = $event => $options.downloadDocument($data.selectedImage)),\n    disabled: !$data.documentUrls[$data.selectedImage.id] || $data.imageLoadingInModal\n  }, _cache[192] || (_cache[192] = [_createElementVNode(\"i\", {\n    class: \"fas fa-download me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\"Download \")]), 8 /* PROPS */, _hoisted_342), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-secondary\",\n    onClick: _cache[49] || (_cache[49] = (...args) => $options.closeImageModal && $options.closeImageModal(...args))\n  }, _cache[193] || (_cache[193] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\"Close \")]))])])])])])])) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["class", "style", "role", "tabindex", "value", "_createElementVNode", "_hoisted_1", "_createVNode", "_component_AdminHeader", "userName", "$data", "adminData", "first_name", "showUserDropdown", "sidebarCollapsed", "activeMenu", "$options", "onSidebarToggle", "handleSidebarToggle", "onUserDropdownToggle", "handleUserDropdownToggle", "onMenuAction", "handleMenuAction", "onOpenRequestModal", "handleOpenRequestModal", "onLogout", "handleLogout", "_createCommentVNode", "_normalizeClass", "active", "isMobile", "onClick", "_cache", "args", "closeMobileSidebar", "_hoisted_2", "_component_AdminSidebar", "collapsed", "onMenuChange", "handleMenuChange", "onToggleSidebar", "loading", "_createElementBlock", "_hoisted_3", "_Fragment", "key", "_hoisted_4", "errorMessage", "_hoisted_5", "_toDisplayString", "type", "$event", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "lastRefresh", "_hoisted_10", "formatTime", "_hoisted_11", "_hoisted_12", "autoRefreshEnabled", "_hoisted_13", "_hoisted_14", "toggleAutoRefresh", "title", "showFilters", "refreshRequestsData", "disabled", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "requestStats", "total", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "pending", "_hoisted_30", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "completed", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "approved", "_hoisted_42", "_hoisted_43", "_hoisted_44", "_hoisted_45", "filters", "search", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "applyFilters", "_hoisted_46", "status", "_renderList", "statusOptions", "id", "status_name", "formatStatus", "_hoisted_47", "_hoisted_48", "document_type", "_hoisted_49", "date_from", "_hoisted_50", "date_to", "_hoisted_51", "_hoisted_52", "clearFilters", "selectedRequests", "length", "_hoisted_53", "_hoisted_54", "_hoisted_55", "_hoisted_56", "_hoisted_57", "_hoisted_58", "bulkAction", "_hoisted_59", "_hoisted_60", "_hoisted_61", "performBulkAction", "_hoisted_63", "_hoisted_64", "_hoisted_65", "name", "viewMode", "autocomplete", "for", "_hoisted_66", "_hoisted_67", "pagination", "currentPage", "itemsPerPage", "Math", "min", "totalItems", "onChange", "changeItemsPerPage", "_hoisted_68", "requests", "selectAllRequests", "_hoisted_69", "_hoisted_70", "_hoisted_71", "request", "includes", "_hoisted_72", "_hoisted_73", "_hoisted_74", "checked", "toggleRequestSelection", "_hoisted_76", "_hoisted_77", "request_number", "_hoisted_78", "viewRequestDetails", "_hoisted_80", "_hoisted_81", "_hoisted_82", "_hoisted_83", "client_name", "_hoisted_84", "client_email", "_hoisted_85", "_hoisted_86", "getCivilStatusName", "client_civil_status_id", "_hoisted_87", "_hoisted_88", "client_nationality", "_hoisted_89", "_hoisted_90", "getResidencyDisplay", "_hoisted_91", "_hoisted_92", "_hoisted_93", "_hoisted_94", "_hoisted_95", "_hoisted_96", "_hoisted_97", "_hoisted_98", "_hoisted_99", "getStatusColor", "_hoisted_100", "_hoisted_101", "_hoisted_102", "formatCurrency", "total_fee", "_hoisted_103", "_hoisted_104", "formatDate", "requested_at", "_hoisted_105", "_hoisted_106", "_hoisted_108", "_hoisted_109", "_hoisted_110", "_hoisted_111", "_hoisted_112", "_hoisted_114", "_hoisted_115", "_hoisted_117", "_hoisted_118", "_hoisted_119", "_hoisted_120", "_hoisted_121", "_hoisted_122", "_hoisted_123", "_hoisted_124", "_hoisted_125", "_hoisted_126", "_hoisted_127", "_hoisted_128", "_hoisted_129", "_hoisted_130", "_hoisted_131", "_hoisted_132", "_hoisted_133", "_hoisted_134", "_hoisted_135", "_hoisted_136", "_hoisted_137", "_hoisted_138", "_hoisted_139", "_hoisted_140", "totalPages", "_hoisted_142", "_hoisted_143", "_hoisted_144", "href", "_withModifiers", "changePage", "page", "_hoisted_145", "showRequestDetails", "currentRequest", "_hoisted_146", "_hoisted_147", "_hoisted_148", "_hoisted_149", "_hoisted_150", "_hoisted_151", "_hoisted_152", "_hoisted_153", "_hoisted_154", "_hoisted_155", "_hoisted_156", "_hoisted_157", "_hoisted_158", "_hoisted_159", "_hoisted_160", "_hoisted_161", "_hoisted_162", "_hoisted_163", "_hoisted_164", "purpose_category", "_hoisted_165", "_hoisted_166", "purpose_details", "_hoisted_167", "_hoisted_168", "_hoisted_169", "_hoisted_170", "_hoisted_171", "priority", "_hoisted_172", "_hoisted_173", "delivery_method", "_hoisted_174", "_hoisted_175", "formatDateTime", "_hoisted_176", "_hoisted_177", "_hoisted_178", "_hoisted_179", "_hoisted_180", "getClientFullName", "_hoisted_181", "_hoisted_182", "_hoisted_183", "_hoisted_184", "_hoisted_185", "_hoisted_186", "client_phone", "_hoisted_187", "_hoisted_188", "_hoisted_189", "_hoisted_190", "_hoisted_191", "_hoisted_192", "_hoisted_193", "_hoisted_194", "_hoisted_195", "getClientFullAddress", "_hoisted_196", "_hoisted_197", "_hoisted_198", "_hoisted_199", "_hoisted_200", "_hoisted_201", "_hoisted_202", "_hoisted_203", "_hoisted_204", "_hoisted_205", "_hoisted_206", "_hoisted_207", "_hoisted_208", "uploaded_documents", "_hoisted_209", "_hoisted_210", "document", "_hoisted_211", "_hoisted_212", "_hoisted_213", "getDocumentTypeDisplayName", "_hoisted_214", "isImageFile", "mime_type", "openImageModal", "onMouseenter", "preloadImage", "documentUrls", "src", "alt", "document_name", "onError", "handleImageError", "loadingDocuments", "has", "failedDocuments", "retryLoadDocument", "isPdfFile", "_hoisted_218", "_hoisted_219", "_hoisted_220", "_hoisted_221", "formatFileSize", "file_size", "downloadDocument", "_hoisted_223", "_hoisted_224", "_hoisted_225", "_hoisted_226", "_hoisted_228", "_hoisted_229", "created_at", "_hoisted_230", "_hoisted_231", "_hoisted_232", "_hoisted_233", "_hoisted_234", "_hoisted_235", "_hoisted_236", "_hoisted_237", "_hoisted_238", "statusUpdateForm", "status_id", "getAvailableStatusOptions", "_hoisted_240", "_hoisted_241", "_hoisted_242", "_hoisted_243", "updateRequestStatusFromModal", "isValidStatusChange", "getUpdateButtonTitle", "getActionButtonText", "_hoisted_245", "_hoisted_246", "_hoisted_247", "_hoisted_248", "payment_method", "_hoisted_249", "_hoisted_250", "getPaymentStatusColor", "payment_status", "formatPaymentStatus", "_hoisted_251", "_hoisted_252", "_hoisted_253", "_hoisted_254", "base_fee", "_hoisted_255", "_hoisted_256", "_hoisted_257", "additional_fees", "_hoisted_258", "_hoisted_259", "_hoisted_260", "processing_fee", "_hoisted_261", "_hoisted_262", "_hoisted_263", "needsPaymentVerification", "_hoisted_264", "_hoisted_265", "_hoisted_266", "_hoisted_267", "paymentVerificationForm", "amount_received", "step", "_hoisted_269", "_hoisted_270", "receipt_number", "_hoisted_271", "verifyInPersonPayment", "_hoisted_273", "_hoisted_274", "canSchedulePickup", "_hoisted_275", "_hoisted_276", "_hoisted_277", "_hoisted_278", "pickupScheduleForm", "scheduled_date", "getTomorrowDate", "_hoisted_280", "_hoisted_281", "scheduled_time_start", "_hoisted_282", "_hoisted_283", "scheduled_time_end", "_hoisted_284", "schedulePickup", "isPickupFormValid", "_hoisted_286", "_hoisted_287", "_hoisted_288", "_hoisted_289", "status_history", "_hoisted_290", "history", "index", "new_status_name", "_hoisted_291", "_hoisted_292", "_hoisted_293", "changed_at", "_hoisted_294", "_hoisted_295", "changed_by_name", "old_status_name", "_hoisted_296", "change_reason", "_hoisted_297", "_hoisted_298", "_hoisted_299", "refreshRequestDetails", "showQuickReject", "selectedRequestForReject", "_hoisted_300", "_hoisted_301", "_hoisted_302", "_hoisted_303", "closeQuickRejectModal", "_hoisted_304", "_hoisted_305", "_hoisted_306", "_hoisted_307", "quickRejectForm", "confirmQuickReject", "_hoisted_310", "_hoisted_311", "showQuickApprove", "selectedRequestForApprove", "_hoisted_312", "_hoisted_313", "_hoisted_314", "_hoisted_315", "closeQuickApproveModal", "_hoisted_316", "_hoisted_317", "_hoisted_318", "quickApproveForm", "error", "_hoisted_319", "_hoisted_320", "confirmQuickApprove", "_hoisted_323", "_hoisted_324", "showImageModal", "selectedImage", "closeImageModal", "_hoisted_325", "_hoisted_326", "_hoisted_327", "_hoisted_328", "_hoisted_329", "imageLoadingInModal", "_hoisted_331", "_hoisted_332", "onLoad", "onModalImageLoad", "modalImageError", "_hoisted_334", "_hoisted_336", "_hoisted_337", "_hoisted_338", "_hoisted_339", "_hoisted_340", "_hoisted_341"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminRequests.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-requests\">\n    <AdminHeader\n      :userName=\"adminData?.first_name || 'Admin'\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @open-request-modal=\"handleOpenRequestModal\"\n      @logout=\"handleLogout\"\n    />\n\n    <!-- Mobile Overlay -->\n    <div\n      class=\"mobile-overlay\"\n      :class=\"{ active: !sidebarCollapsed && isMobile }\"\n      @click=\"closeMobileSidebar\"\n    ></div>\n\n    <div class=\"dashboard-container\">\n      <AdminSidebar\n        :collapsed=\"sidebarCollapsed\"\n        :activeMenu=\"activeMenu\"\n        @menu-change=\"handleMenuChange\"\n        @logout=\"handleLogout\"\n        @toggle-sidebar=\"handleSidebarToggle\"\n      />\n\n      <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n        <!-- Loading State -->\n        <div v-if=\"loading\" class=\"d-flex justify-content-center align-items-center\" style=\"min-height: 400px;\">\n          <div class=\"spinner-border text-primary\" role=\"status\">\n            <span class=\"visually-hidden\">Loading...</span>\n          </div>\n        </div>\n\n        <!-- Main Content -->\n        <div v-else class=\"container-fluid py-4\">\n          <!-- Error Message -->\n          <div v-if=\"errorMessage\" class=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">\n            <i class=\"fas fa-exclamation-triangle me-2\"></i>\n            {{ errorMessage }}\n            <button type=\"button\" class=\"btn-close\" @click=\"errorMessage = ''\" aria-label=\"Close\"></button>\n          </div>\n\n          <!-- Page Header -->\n          <div class=\"row mb-4\">\n            <div class=\"col-12\">\n              <div class=\"d-flex justify-content-between align-items-center flex-wrap\">\n                <div>\n                  <p class=\"text-muted mb-0\">\n                    <span v-if=\"lastRefresh\" class=\"ms-2 small\">\n                      <i class=\"fas fa-clock text-muted\"></i>\n                      Last updated: {{ formatTime(lastRefresh) }}\n                    </span>\n                  </p>\n                </div>\n                <div class=\"d-flex gap-2 align-items-center\">\n                  <!-- Real-time status indicator -->\n                  <div class=\"real-time-status me-2\">\n                    <span class=\"badge\" :class=\"autoRefreshEnabled ? 'bg-success' : 'bg-secondary'\">\n                      <i class=\"fas fa-circle pulse\" v-if=\"autoRefreshEnabled\"></i>\n                      <i class=\"fas fa-pause\" v-else></i>\n                      {{ autoRefreshEnabled ? 'Live' : 'Paused' }}\n                    </span>\n                  </div>\n\n                  <button class=\"btn btn-outline-secondary btn-sm\" @click=\"toggleAutoRefresh\" :title=\"autoRefreshEnabled ? 'Disable auto-refresh' : 'Enable auto-refresh'\">\n                    <i class=\"fas\" :class=\"autoRefreshEnabled ? 'fa-pause' : 'fa-play'\"></i>\n                  </button>\n                  <button class=\"btn btn-outline-primary btn-sm\" @click=\"showFilters = !showFilters\">\n                    <i class=\"fas fa-filter me-1\"></i>\n                    {{ showFilters ? 'Hide' : 'Show' }} Filters\n                  </button>\n                  <!-- <button class=\"btn btn-success btn-sm\" @click=\"exportRequests\" :disabled=\"loading\">\n                    <i class=\"fas fa-download me-1\"></i>\n                    Export CSV\n                  </button> -->\n                  <button class=\"btn btn-primary btn-sm\" @click=\"refreshRequestsData\" :disabled=\"loading\">\n                    <i class=\"fas fa-sync-alt me-1\" :class=\"{ 'fa-spin': loading }\"></i>\n                    Refresh\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Request Statistics -->\n          <div class=\"row mb-3\">\n            <div class=\"col-6 col-md-3 mb-2\">\n              <div class=\"card border-left-primary shadow py-1\">\n                <div class=\"card-body p-2\">\n                  <div class=\"d-flex align-items-center\">\n                    <div class=\"flex-grow-1\">\n                      <div class=\"text-xs fw-bold text-primary text-uppercase mb-1\">Total Requests</div>\n                      <div class=\"h6 mb-0 fw-bold text-dark\">{{ requestStats.total || 0 }}</div>\n                    </div>\n                    <i class=\"fas fa-file-alt fa-lg text-muted ms-2\"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-md-3 mb-2\">\n              <div class=\"card border-left-warning shadow py-1\">\n                <div class=\"card-body p-2\">\n                  <div class=\"d-flex align-items-center\">\n                    <div class=\"flex-grow-1\">\n                      <div class=\"text-xs fw-bold text-warning text-uppercase mb-1\">Pending</div>\n                      <div class=\"h6 mb-0 fw-bold text-dark\">{{ requestStats.pending || 0 }}</div>\n                    </div>\n                    <i class=\"fas fa-clock fa-lg text-muted ms-2\"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-md-3 mb-2\">\n              <div class=\"card border-left-success shadow py-1\">\n                <div class=\"card-body p-2\">\n                  <div class=\"d-flex align-items-center\">\n                    <div class=\"flex-grow-1\">\n                      <div class=\"text-xs fw-bold text-success text-uppercase mb-1\">Completed</div>\n                      <div class=\"h6 mb-0 fw-bold text-dark\">{{ requestStats.completed || 0 }}</div>\n                    </div>\n                    <i class=\"fas fa-check-circle fa-lg text-muted ms-2\"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-md-3 mb-2\">\n              <div class=\"card border-left-info shadow py-1\">\n                <div class=\"card-body p-2\">\n                  <div class=\"d-flex align-items-center\">\n                    <div class=\"flex-grow-1\">\n                      <div class=\"text-xs fw-bold text-info text-uppercase mb-1\">Approved</div>\n                      <div class=\"h6 mb-0 fw-bold text-dark\">{{ requestStats.approved || 0 }}</div>\n                    </div>\n                    <i class=\"fas fa-thumbs-up fa-lg text-muted ms-2\"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Filters Panel -->\n          <div v-if=\"showFilters\" class=\"card shadow mb-4\">\n            <div class=\"card-header py-3\">\n              <h6 class=\"m-0 fw-bold text-primary\">Filter Requests</h6>\n            </div>\n            <div class=\"card-body\">\n              <div class=\"row\">\n                <div class=\"col-md-3 mb-3\">\n                  <label class=\"form-label\">Search</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    v-model=\"filters.search\"\n                    placeholder=\"Search by name, email, or request number\"\n                    @keyup.enter=\"applyFilters\"\n                  >\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Status</label>\n                  <select class=\"form-select\" v-model=\"filters.status\">\n                    <option value=\"\">All Statuses</option>\n                    <option v-for=\"status in statusOptions\" :key=\"status.id\" :value=\"status.status_name\">\n                      {{ formatStatus(status.status_name) }}\n                    </option>\n                  </select>\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Document Type</label>\n                  <select class=\"form-select\" v-model=\"filters.document_type\">\n                    <option value=\"\">All Types</option>\n                    <option value=\"barangay_clearance\">Barangay Clearance</option>\n                    <option value=\"cedula\">Cedula</option>\n                  </select>\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Date From</label>\n                  <input type=\"date\" class=\"form-control\" v-model=\"filters.date_from\">\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Date To</label>\n                  <input type=\"date\" class=\"form-control\" v-model=\"filters.date_to\">\n                </div>\n                <div class=\"col-md-1 mb-3 d-flex align-items-end\">\n                  <div class=\"d-flex gap-1 w-100\">\n                    <button class=\"btn btn-primary btn-sm\" @click=\"applyFilters\">\n                      <i class=\"fas fa-search\"></i>\n                    </button>\n                    <button class=\"btn btn-outline-secondary btn-sm\" @click=\"clearFilters\">\n                      <i class=\"fas fa-times\"></i>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Bulk Actions Panel -->\n          <div v-if=\"selectedRequests.length > 0\" class=\"card shadow mb-4\">\n            <div class=\"card-header py-3 bg-warning\">\n              <h6 class=\"m-0 fw-bold text-dark\">\n                <i class=\"fas fa-tasks me-2\"></i>\n                Bulk Actions ({{ selectedRequests.length }} selected)\n              </h6>\n            </div>\n            <div class=\"card-body\">\n              <div class=\"row align-items-end\">\n                <div class=\"col-md-3 mb-3\">\n                  <label class=\"form-label\">Action</label>\n                  <select class=\"form-select\" v-model=\"bulkAction\">\n                    <option value=\"\">Select Action</option>\n                    <option v-for=\"status in statusOptions\" :key=\"status.id\" :value=\"status.id\">\n                      Change to {{ formatStatus(status.status_name) }}\n                    </option>\n                  </select>\n                </div>\n\n                <div class=\"col-md-3 mb-3\">\n                  <div class=\"d-flex gap-2\">\n                    <button class=\"btn btn-warning\" @click=\"performBulkAction\" :disabled=\"!bulkAction\">\n                      <i class=\"fas fa-play me-1\"></i>\n                      Apply\n                    </button>\n                    <button class=\"btn btn-outline-secondary\" @click=\"selectedRequests = []\">\n                      <i class=\"fas fa-times me-1\"></i>\n                      Cancel\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- View Toggle -->\n          <div class=\"d-flex justify-content-between align-items-center mb-4\">\n            <div class=\"d-flex align-items-center gap-3\">\n              <div class=\"btn-group\" role=\"group\" aria-label=\"View toggle\">\n                <input type=\"radio\" class=\"btn-check\" name=\"viewMode\" id=\"cardView\" v-model=\"viewMode\" value=\"card\" autocomplete=\"off\">\n                <label class=\"btn btn-outline-primary btn-sm\" for=\"cardView\">\n                  <i class=\"fas fa-th-large me-1\"></i>Cards\n                </label>\n\n                <input type=\"radio\" class=\"btn-check\" name=\"viewMode\" id=\"tableView\" v-model=\"viewMode\" value=\"table\" autocomplete=\"off\">\n                <label class=\"btn btn-outline-primary btn-sm\" for=\"tableView\">\n                  <i class=\"fas fa-table me-1\"></i>Table\n                </label>\n              </div>\n\n              <div class=\"d-flex align-items-center gap-2\">\n                <span class=\"text-muted small\">\n                  Showing {{ ((pagination.currentPage - 1) * pagination.itemsPerPage) + 1 }} -\n                  {{ Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems) }}\n                  of {{ pagination.totalItems }} requests\n                </span>\n                <select class=\"form-select form-select-sm\" style=\"width: auto;\" v-model=\"pagination.itemsPerPage\" @change=\"changeItemsPerPage(pagination.itemsPerPage)\">\n                  <option value=\"10\">10 per page</option>\n                  <option value=\"25\">25 per page</option>\n                  <option value=\"50\">50 per page</option>\n                  <option value=\"100\">100 per page</option>\n                </select>\n              </div>\n            </div>\n\n            <div class=\"d-flex align-items-center gap-2\">\n              <button class=\"btn btn-sm btn-outline-secondary\" @click=\"selectAllRequests\" v-if=\"requests.length > 0\">\n                <i class=\"fas fa-check-square me-1\"></i>\n                {{ selectedRequests.length === requests.length ? 'Deselect All' : 'Select All' }}\n              </button>\n            </div>\n          </div>\n\n          <!-- Card View -->\n          <div v-if=\"viewMode === 'card'\" class=\"requests-grid\">\n            <!-- Empty State -->\n            <div v-if=\"requests.length === 0\" class=\"empty-state text-center py-5\">\n              <div class=\"empty-state-icon mb-3\">\n                <i class=\"fas fa-inbox fa-4x text-muted\"></i>\n              </div>\n              <h5 class=\"text-muted mb-2\">No Document Requests Found</h5>\n              <p class=\"text-muted\">There are no document requests matching your current filters.</p>\n            </div>\n\n            <!-- Request Cards -->\n            <div v-else class=\"row g-4\">\n              <div v-for=\"request in requests\" :key=\"request.id\" class=\"col-xl-4 col-lg-6 col-md-6\">\n                <div class=\"request-card\" :class=\"{ 'selected': selectedRequests.includes(request.id) }\">\n                  <!-- Card Header -->\n                  <div class=\"request-card-header\">\n                    <div class=\"d-flex justify-content-between align-items-start\">\n                      <div class=\"d-flex align-items-center gap-2\">\n                        <input\n                          type=\"checkbox\"\n                          class=\"form-check-input\"\n                          :checked=\"selectedRequests.includes(request.id)\"\n                          @change=\"toggleRequestSelection(request.id)\"\n                        >\n                        <div class=\"request-number\">\n                          <span class=\"badge bg-primary\">{{ request.request_number }}</span>\n                        </div>\n                      </div>\n                      <div class=\"request-actions-simple\">\n                        <button class=\"btn btn-sm btn-primary\" @click=\"viewRequestDetails(request.id)\" title=\"View & Manage Request\">\n                          <i class=\"fas fa-edit me-1\"></i>Manage\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Card Body -->\n                  <div class=\"request-card-body\">\n                    <!-- Client Info -->\n                    <div class=\"client-info mb-3\">\n                      <div class=\"d-flex align-items-center gap-2 mb-2\">\n                        <div class=\"client-avatar\">\n                          <i class=\"fas fa-user-circle fa-2x text-primary\"></i>\n                        </div>\n                        <div>\n                          <h6 class=\"mb-0 fw-bold\">{{ request.client_name }}</h6>\n                          <small class=\"text-muted\">{{ request.client_email }}</small>\n                        </div>\n                      </div>\n\n                      <!-- Additional Client Details -->\n                      <div class=\"client-details-grid mt-2\">\n                        <div class=\"row g-1\">\n                          <div class=\"col-6\" v-if=\"getCivilStatusName(request.client_civil_status_id)\">\n                            <small class=\"text-muted d-block\">Civil Status</small>\n                            <small class=\"fw-medium\">{{ getCivilStatusName(request.client_civil_status_id) }}</small>\n                          </div>\n                          <div class=\"col-6\" v-if=\"request.client_nationality\">\n                            <small class=\"text-muted d-block\">Nationality</small>\n                            <small class=\"fw-medium\">{{ request.client_nationality }}</small>\n                          </div>\n                          <div class=\"col-12\" v-if=\"getResidencyDisplay(request)\">\n                            <small class=\"text-muted d-block\">Years of Residency</small>\n                            <small class=\"fw-medium\">{{ getResidencyDisplay(request) }}</small>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Document Type -->\n                    <div class=\"document-type mb-3\">\n                      <div class=\"d-flex align-items-center gap-2\">\n                        <i class=\"fas fa-file-alt text-info\"></i>\n                        <span class=\"badge bg-info-subtle text-info-emphasis px-3 py-2\">\n                          {{ request.document_type }}\n                        </span>\n                      </div>\n                    </div>\n\n                    <!-- Status and Amount -->\n                    <div class=\"request-meta mb-3\">\n                      <div class=\"row g-2\">\n                        <div class=\"col-6\">\n                          <div class=\"meta-item\">\n                            <small class=\"text-muted d-block\">Status</small>\n                            <span class=\"badge\" :class=\"`bg-${getStatusColor(request.status_name)}`\">\n                              {{ formatStatus(request.status_name) }}\n                            </span>\n                          </div>\n                        </div>\n                        <div class=\"col-6\">\n                          <div class=\"meta-item\">\n                            <small class=\"text-muted d-block\">Amount</small>\n                            <span class=\"fw-bold text-success\">{{ formatCurrency(request.total_fee) }}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Date -->\n                    <div class=\"request-date\">\n                      <small class=\"text-muted\">\n                        <i class=\"fas fa-calendar-alt me-1\"></i>\n                        Submitted {{ formatDate(request.requested_at) }}\n                      </small>\n                    </div>\n                  </div>\n\n                  <!-- Card Footer -->\n                  <div class=\"request-card-footer\">\n                    <div class=\"d-grid\">\n                      <button class=\"btn btn-sm btn-primary\" @click=\"viewRequestDetails(request.id)\">\n                        <i class=\"fas fa-edit me-1\"></i>Manage Request\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Table View -->\n          <div v-else class=\"modern-table-container\">\n\n            <!-- Empty State -->\n            <div v-if=\"requests.length === 0\" class=\"modern-table-empty\">\n              <div class=\"empty-content\">\n                <div class=\"empty-icon\">\n                  <i class=\"fas fa-inbox\"></i>\n                </div>\n                <h6 class=\"empty-title\">No Document Requests Found</h6>\n                <p class=\"empty-text\">There are no document requests matching your current filters.</p>\n              </div>\n            </div>\n\n            <!-- Modern Compact Table -->\n            <div v-else class=\"compact-table-wrapper\">\n              <!-- Table Header -->\n              <div class=\"compact-table-header\">\n                <div class=\"header-cell selection-header\">\n                  <input\n                    type=\"checkbox\"\n                    class=\"form-check-input\"\n                    :checked=\"selectedRequests.length === requests.length && requests.length > 0\"\n                    @change=\"selectAllRequests\"\n                  >\n                </div>\n                <div class=\"header-cell\">Request ID</div>\n                <div class=\"header-cell\">Client</div>\n                <div class=\"header-cell\">Document</div>\n                <div class=\"header-cell\">Status</div>\n                <div class=\"header-cell\">Amount</div>\n                <div class=\"header-cell\">Date</div>\n                <div class=\"header-cell\">Actions</div>\n              </div>\n\n              <!-- Table Body -->\n              <div class=\"compact-table-body\">\n                <div v-for=\"request in requests\" :key=\"request.id\"\n                     class=\"compact-row\"\n                     :class=\"{ 'selected': selectedRequests.includes(request.id) }\">\n\n                  <!-- Selection -->\n                  <div class=\"row-cell selection-cell\">\n                    <input\n                      type=\"checkbox\"\n                      class=\"form-check-input\"\n                      :checked=\"selectedRequests.includes(request.id)\"\n                      @change=\"toggleRequestSelection(request.id)\"\n                    >\n                  </div>\n\n                  <!-- Request ID -->\n                  <div class=\"row-cell request-id-cell\">\n                    <div class=\"request-id-content\">\n                      <span class=\"request-number\">{{ request.request_number }}</span>\n                      <span class=\"request-id-small\">{{ request.id }}</span>\n                    </div>\n                  </div>\n\n                  <!-- Client -->\n                  <div class=\"row-cell client-cell\">\n                    <div class=\"client-compact\">\n                      <div class=\"client-avatar-tiny\">\n                        <i class=\"fas fa-user\"></i>\n                      </div>\n                      <div class=\"client-info-compact\">\n                        <div class=\"client-name-compact\">{{ request.client_name }}</div>\n                        <div class=\"client-email-compact\">{{ request.client_email }}</div>\n                        <div class=\"client-details-compact\">\n                          <span v-if=\"getCivilStatusName(request.client_civil_status_id)\" class=\"detail-item\">\n                            <i class=\"fas fa-ring me-1\"></i>{{ getCivilStatusName(request.client_civil_status_id) }}\n                          </span>\n                          <span v-if=\"request.client_nationality\" class=\"detail-item\">\n                            <i class=\"fas fa-flag me-1\"></i>{{ request.client_nationality }}\n                          </span>\n                          <span v-if=\"getResidencyDisplay(request)\" class=\"detail-item\">\n                            <i class=\"fas fa-home me-1\"></i>{{ getResidencyDisplay(request) }}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Document Type -->\n                  <div class=\"row-cell document-cell\">\n                    <span class=\"document-badge\">\n                      <i class=\"fas fa-file-alt\"></i>\n                      {{ request.document_type }}\n                    </span>\n                  </div>\n\n                  <!-- Status -->\n                  <div class=\"row-cell status-cell\">\n                    <span class=\"status-compact\" :class=\"`status-${getStatusColor(request.status_name)}`\">\n                      <i class=\"fas fa-circle\"></i>\n                      {{ formatStatus(request.status_name) }}\n                    </span>\n                  </div>\n\n                  <!-- Amount -->\n                  <div class=\"row-cell amount-cell\">\n                    <span class=\"amount-compact\">{{ formatCurrency(request.total_fee) }}</span>\n                  </div>\n\n                  <!-- Date -->\n                  <div class=\"row-cell date-cell\">\n                    <div class=\"date-compact\">\n                      <span class=\"date-main\">{{ formatDate(request.requested_at) }}</span>\n                      <span class=\"time-small\">{{ formatTime(request.requested_at) }}</span>\n                    </div>\n                  </div>\n\n                  <!-- Actions -->\n                  <div class=\"row-cell actions-cell\">\n                    <div class=\"actions-simple\">\n                      <button class=\"action-btn-sm primary-btn-sm\" @click=\"viewRequestDetails(request.id)\" title=\"View & Manage Request\">\n                        <i class=\"fas fa-edit\"></i>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Pagination -->\n          <div v-if=\"pagination.totalPages > 1\" class=\"pagination-container\">\n              <nav aria-label=\"Requests pagination\">\n                <ul class=\"pagination pagination-sm justify-content-center mb-0\">\n                  <li class=\"page-item\" :class=\"{ disabled: pagination.currentPage === 1 }\">\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(pagination.currentPage - 1)\">\n                      <i class=\"fas fa-chevron-left\"></i>\n                    </a>\n                  </li>\n                  <li\n                    v-for=\"page in Math.min(pagination.totalPages, 10)\"\n                    :key=\"page\"\n                    class=\"page-item\"\n                    :class=\"{ active: page === pagination.currentPage }\"\n                  >\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(page)\">{{ page }}</a>\n                  </li>\n                  <li class=\"page-item\" :class=\"{ disabled: pagination.currentPage === pagination.totalPages }\">\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(pagination.currentPage + 1)\">\n                      <i class=\"fas fa-chevron-right\"></i>\n                    </a>\n                  </li>\n                </ul>\n              </nav>\n            </div>\n          </div>\n\n          <!-- Request Details Modal -->\n          <div v-if=\"showRequestDetails && currentRequest\" class=\"modal fade show d-block\" tabindex=\"-1\" style=\"background-color: rgba(0,0,0,0.5);\">\n            <div class=\"modal-dialog modal-xl modal-dialog-scrollable\">\n              <div class=\"modal-content\">\n                <div class=\"modal-header\">\n                  <h5 class=\"modal-title\">\n                    <i class=\"fas fa-file-alt me-2\"></i>\n                    Request Details - {{ currentRequest.request_number }}\n                  </h5>\n                  <button type=\"button\" class=\"btn-close\" @click=\"showRequestDetails = false\"></button>\n                </div>\n                <div class=\"modal-body\">\n                  <div class=\"row\">\n                    <!-- Left Column - Request Information -->\n                    <div class=\"col-lg-8\">\n                      <!-- Basic Information -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-info-circle me-2\"></i>Request Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"row\">\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Request Number</label>\n                                <p class=\"mb-0\">{{ currentRequest.request_number }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Document Type</label>\n                                <p class=\"mb-0\">\n                                  <span class=\"badge bg-info\">{{ currentRequest.document_type }}</span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Purpose Category</label>\n                                <p class=\"mb-0\">{{ currentRequest.purpose_category }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Purpose Details</label>\n                                <p class=\"mb-0\">{{ currentRequest.purpose_details || 'Not specified' }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Current Status</label>\n                                <p class=\"mb-0\">\n                                  <span class=\"badge\" :class=\"`bg-${getStatusColor(currentRequest.status_name)}`\">\n                                    {{ formatStatus(currentRequest.status_name) }}\n                                  </span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Priority</label>\n                                <p class=\"mb-0\">\n                                  <span class=\"badge\" :class=\"currentRequest.priority === 'high' ? 'bg-danger' : currentRequest.priority === 'medium' ? 'bg-warning' : 'bg-secondary'\">\n                                    {{ currentRequest.priority || 'Normal' }}\n                                  </span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Delivery Method</label>\n                                <p class=\"mb-0\">{{ currentRequest.delivery_method || 'Pickup' }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Date Submitted</label>\n                                <p class=\"mb-0\">{{ formatDateTime(currentRequest.requested_at) }}</p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Client Information -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-user me-2\"></i>Client Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <!-- Basic Information -->\n                          <div class=\"row\">\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Full Name</label>\n                                <p class=\"mb-0\" :class=\"{ 'text-muted': getClientFullName(currentRequest) === 'Not provided' }\">\n                                  <span v-if=\"getClientFullName(currentRequest) !== 'Not provided'\">{{ getClientFullName(currentRequest) }}</span>\n                                  <span v-else class=\"not-provided\">Not provided</span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Email Address</label>\n                                <p class=\"mb-0\" :class=\"{ 'text-muted': !currentRequest.client_email }\">\n                                  <a v-if=\"currentRequest.client_email\" :href=\"`mailto:${currentRequest.client_email}`\">{{ currentRequest.client_email }}</a>\n                                  <span v-else class=\"not-provided\">Not provided</span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Phone Number</label>\n                                <p class=\"mb-0\" :class=\"{ 'text-muted': !currentRequest.client_phone }\">\n                                  <a v-if=\"currentRequest.client_phone\" :href=\"`tel:${currentRequest.client_phone}`\">{{ currentRequest.client_phone }}</a>\n                                  <span v-else class=\"not-provided\">Not provided</span>\n                                </p>\n                              </div>\n                            </div>\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Civil Status</label>\n                                <p class=\"mb-0\" :class=\"{ 'text-muted': !currentRequest.client_civil_status_id }\">\n                                  <span v-if=\"getCivilStatusName(currentRequest.client_civil_status_id)\">{{ getCivilStatusName(currentRequest.client_civil_status_id) }}</span>\n                                  <span v-else class=\"not-provided\">Not provided</span>\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n\n                          <!-- Address Information -->\n                          <div class=\"row\">\n                            <div class=\"col-12\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Complete Address</label>\n                                <p class=\"mb-0\" :class=\"{ 'text-muted': !getClientFullAddress(currentRequest) }\">\n                                  <span v-if=\"getClientFullAddress(currentRequest)\">{{ getClientFullAddress(currentRequest) }}</span>\n                                  <span v-else class=\"not-provided\">Not provided</span>\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n\n                          <!-- Additional Information -->\n                          <div class=\"row\">\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Nationality</label>\n                                <p class=\"mb-0\" :class=\"{ 'text-muted': !currentRequest.client_nationality }\">\n                                  <span v-if=\"currentRequest.client_nationality\">{{ currentRequest.client_nationality }}</span>\n                                  <span v-else class=\"not-provided\">Not provided</span>\n                                </p>\n                              </div>\n                            </div>\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Years of Residency</label>\n                                <p class=\"mb-0\" :class=\"{ 'text-muted': !getResidencyDisplay(currentRequest) }\">\n                                  <span v-if=\"getResidencyDisplay(currentRequest)\">{{ getResidencyDisplay(currentRequest) }}</span>\n                                  <span v-else class=\"not-provided\">Not provided</span>\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Uploaded Documents -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-paperclip me-2\"></i>Uploaded Documents</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div v-if=\"currentRequest.uploaded_documents && currentRequest.uploaded_documents.length > 0\">\n                            <div class=\"row g-3\">\n                              <div v-for=\"document in currentRequest.uploaded_documents\" :key=\"document.id\" class=\"col-md-4\">\n                                <div class=\"document-preview-card\">\n                                  <div class=\"document-preview-header\">\n                                    <div class=\"document-type-badge\">\n                                      <i class=\"fas fa-file-alt me-1\"></i>\n                                      {{ getDocumentTypeDisplayName(document.document_type) }}\n                                    </div>\n                                  </div>\n                                  <div class=\"document-preview-content\">\n                                    <!-- Image Preview -->\n                                    <div v-if=\"isImageFile(document.mime_type)\"\n                                         class=\"image-preview\"\n                                         @click=\"openImageModal(document)\"\n                                         @mouseenter=\"preloadImage(document)\">\n                                      <!-- Successfully loaded image -->\n                                      <img\n                                        v-if=\"documentUrls[document.id]\"\n                                        :src=\"documentUrls[document.id]\"\n                                        :alt=\"document.document_name\"\n                                        class=\"document-image\"\n                                        @error=\"handleImageError\"\n                                      />\n\n                                      <!-- Loading state -->\n                                      <div v-else-if=\"loadingDocuments.has(document.id)\" class=\"loading-placeholder\">\n                                        <i class=\"fas fa-spinner fa-spin\"></i>\n                                        <span>Loading image...</span>\n                                      </div>\n\n                                      <!-- Failed state with retry option -->\n                                      <div v-else-if=\"failedDocuments.has(document.id)\" class=\"error-placeholder\" @click.stop=\"retryLoadDocument(document)\">\n                                        <i class=\"fas fa-exclamation-triangle\"></i>\n                                        <span>Failed to load</span>\n                                        <small>Click to retry</small>\n                                      </div>\n\n                                      <!-- Initial state (not yet attempted) -->\n                                      <div v-else class=\"loading-placeholder\">\n                                        <i class=\"fas fa-image\"></i>\n                                        <span>Click to load</span>\n                                      </div>\n                                      <div class=\"image-overlay\">\n                                        <i class=\"fas fa-search-plus\"></i>\n                                        <span>Click to view</span>\n                                      </div>\n                                    </div>\n                                    <!-- PDF Preview -->\n                                    <div v-else-if=\"isPdfFile(document.mime_type)\" class=\"pdf-preview\">\n                                      <div class=\"pdf-icon\">\n                                        <i class=\"fas fa-file-pdf fa-3x text-danger\"></i>\n                                      </div>\n                                      <div class=\"pdf-info\">\n                                        <p class=\"mb-1 fw-bold\">{{ document.document_name }}</p>\n                                        <small class=\"text-muted\">{{ formatFileSize(document.file_size) }}</small>\n                                      </div>\n                                      <button\n                                        class=\"btn btn-sm btn-outline-primary mt-2\"\n                                        @click=\"downloadDocument(document)\"\n                                      >\n                                        <i class=\"fas fa-download me-1\"></i>Download\n                                      </button>\n                                    </div>\n                                    <!-- Other File Types -->\n                                    <div v-else class=\"file-preview\">\n                                      <div class=\"file-icon\">\n                                        <i class=\"fas fa-file fa-3x text-secondary\"></i>\n                                      </div>\n                                      <div class=\"file-info\">\n                                        <p class=\"mb-1 fw-bold\">{{ document.document_name }}</p>\n                                        <small class=\"text-muted\">{{ formatFileSize(document.file_size) }}</small>\n                                      </div>\n                                      <button\n                                        class=\"btn btn-sm btn-outline-primary mt-2\"\n                                        @click=\"downloadDocument(document)\"\n                                      >\n                                        <i class=\"fas fa-download me-1\"></i>Download\n                                      </button>\n                                    </div>\n                                  </div>\n                                  <div class=\"document-preview-footer\">\n                                    <small class=\"text-muted\">\n                                      <i class=\"fas fa-clock me-1\"></i>\n                                      Uploaded {{ formatDate(document.created_at) }}\n                                    </small>\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                          <div v-else class=\"no-documents\">\n                            <div class=\"text-center py-4\">\n                              <i class=\"fas fa-folder-open fa-3x text-muted mb-3\"></i>\n                              <h6 class=\"text-muted\">No Documents Uploaded</h6>\n                              <p class=\"text-muted mb-0\">\n                                <span v-if=\"currentRequest.document_type === 'Cedula'\">\n                                  Cedula requests typically don't require supporting documents.\n                                </span>\n                                <span v-else>\n                                  The client hasn't uploaded any supporting documents yet.\n                                </span>\n                              </p>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Right Column - Status Management -->\n                    <div class=\"col-lg-4\">\n                      <!-- Status Management -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-tasks me-2\"></i>Status Management</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Change Status</label>\n                            <select\n                              class=\"form-select\"\n                              v-model=\"statusUpdateForm.status_id\"\n                              :disabled=\"getAvailableStatusOptions().length === 0\"\n                            >\n                              <option value=\"\">\n                                {{ getAvailableStatusOptions().length === 0 ? 'No status changes available' : 'Select new status' }}\n                              </option>\n                              <option v-for=\"status in getAvailableStatusOptions()\" :key=\"status.id\" :value=\"status.id\">\n                                {{ formatStatus(status.status_name) }}\n                              </option>\n                            </select>\n                            <div v-if=\"getAvailableStatusOptions().length === 0\" class=\"form-text text-muted\">\n                              <i class=\"fas fa-info-circle me-1\"></i>\n                              This request status cannot be changed ({{ formatStatus(currentRequest.status_name) }})\n                            </div>\n                          </div>\n\n                          <!-- Single Action Button -->\n                          <div class=\"d-grid\">\n                            <button\n                              class=\"btn btn-primary\"\n                              @click=\"updateRequestStatusFromModal\"\n                              :disabled=\"!statusUpdateForm.status_id || !isValidStatusChange(currentRequest.status_name, statusUpdateForm.status_id)\"\n                              :title=\"getUpdateButtonTitle()\"\n                            >\n                              <i class=\"fas fa-save me-1\"></i>\n                              {{ getActionButtonText() }}\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Payment Information -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-credit-card me-2\"></i>Payment Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Payment Method</label>\n                            <p class=\"mb-0\">{{ currentRequest.payment_method || 'Not specified' }}</p>\n                          </div>\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Payment Status</label>\n                            <p class=\"mb-0\">\n                              <span class=\"badge\" :class=\"getPaymentStatusColor(currentRequest.payment_status)\">\n                                {{ formatPaymentStatus(currentRequest.payment_status) }}\n                              </span>\n                            </p>\n                          </div>\n                          <div class=\"row\">\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Base Fee</label>\n                                <p class=\"mb-0\">{{ formatCurrency(currentRequest.base_fee) }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Additional Fees</label>\n                                <p class=\"mb-0\">{{ formatCurrency(currentRequest.additional_fees) }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Processing Fee</label>\n                                <p class=\"mb-0\">{{ formatCurrency(currentRequest.processing_fee) }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Total Amount</label>\n                                <p class=\"mb-0 fw-bold text-primary\">{{ formatCurrency(currentRequest.total_fee) }}</p>\n                              </div>\n                            </div>\n                          </div>\n\n                          <!-- In-Person Payment Verification -->\n                          <div v-if=\"needsPaymentVerification(currentRequest)\" class=\"mt-4 p-3 border rounded bg-light\">\n                            <h6 class=\"text-primary mb-3\">\n                              <i class=\"fas fa-money-bill me-2\"></i>\n                              Verify In-Person Payment\n                            </h6>\n                            <div class=\"row\">\n                              <div class=\"col-md-6\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Amount Received *</label>\n                                  <input\n                                    type=\"number\"\n                                    class=\"form-control\"\n                                    v-model=\"paymentVerificationForm.amount_received\"\n                                    :min=\"currentRequest.total_fee\"\n                                    step=\"0.01\"\n                                    placeholder=\"Enter amount received\"\n                                  >\n                                </div>\n                              </div>\n                              <div class=\"col-md-6\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Receipt Number</label>\n                                  <input\n                                    type=\"text\"\n                                    class=\"form-control\"\n                                    v-model=\"paymentVerificationForm.receipt_number\"\n                                    placeholder=\"Enter receipt number\"\n                                  >\n                                </div>\n                              </div>\n                            </div>\n\n                            <div class=\"d-grid\">\n                              <button\n                                class=\"btn btn-success\"\n                                @click=\"verifyInPersonPayment\"\n                                :disabled=\"!paymentVerificationForm.amount_received || paymentVerificationForm.loading\"\n                              >\n                                <i class=\"fas fa-check-circle me-1\"></i>\n                                <span v-if=\"paymentVerificationForm.loading\">\n                                  <i class=\"fas fa-spinner fa-spin me-1\"></i>\n                                  Verifying...\n                                </span>\n                                <span v-else>Verify Payment</span>\n                              </button>\n                            </div>\n                          </div>\n\n                          <!-- Pickup Scheduling -->\n                          <div v-if=\"canSchedulePickup(currentRequest)\" class=\"mt-4 p-3 border rounded bg-light\">\n                            <h6 class=\"text-info mb-3\">\n                              <i class=\"fas fa-calendar-alt me-2\"></i>\n                              Schedule Pickup Appointment\n                            </h6>\n                            <div class=\"row\">\n                              <div class=\"col-md-4\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Date *</label>\n                                  <input\n                                    type=\"date\"\n                                    class=\"form-control\"\n                                    v-model=\"pickupScheduleForm.scheduled_date\"\n                                    :min=\"getTomorrowDate()\"\n                                  >\n                                </div>\n                              </div>\n                              <div class=\"col-md-4\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Start Time *</label>\n                                  <input\n                                    type=\"time\"\n                                    class=\"form-control\"\n                                    v-model=\"pickupScheduleForm.scheduled_time_start\"\n                                  >\n                                </div>\n                              </div>\n                              <div class=\"col-md-4\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">End Time *</label>\n                                  <input\n                                    type=\"time\"\n                                    class=\"form-control\"\n                                    v-model=\"pickupScheduleForm.scheduled_time_end\"\n                                  >\n                                </div>\n                              </div>\n                            </div>\n\n                            <div class=\"d-grid\">\n                              <button\n                                class=\"btn btn-info\"\n                                @click=\"schedulePickup\"\n                                :disabled=\"!isPickupFormValid() || pickupScheduleForm.loading\"\n                              >\n                                <i class=\"fas fa-calendar-check me-1\"></i>\n                                <span v-if=\"pickupScheduleForm.loading\">\n                                  <i class=\"fas fa-spinner fa-spin me-1\"></i>\n                                  Scheduling...\n                                </span>\n                                <span v-else>Schedule Pickup</span>\n                              </button>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Status History Timeline -->\n                  <div class=\"card\">\n                    <div class=\"card-header\">\n                      <h6 class=\"mb-0\"><i class=\"fas fa-history me-2\"></i>Status History</h6>\n                    </div>\n                    <div class=\"card-body\">\n                      <div v-if=\"currentRequest.status_history && currentRequest.status_history.length > 0\" class=\"timeline\">\n                        <div\n                          v-for=\"(history, index) in currentRequest.status_history\"\n                          :key=\"history.id\"\n                          class=\"timeline-item\"\n                          :class=\"{ 'timeline-item-last': index === currentRequest.status_history.length - 1 }\"\n                        >\n                          <div class=\"timeline-marker\" :class=\"`bg-${getStatusColor(history.new_status_name)}`\">\n                            <i class=\"fas fa-circle\"></i>\n                          </div>\n                          <div class=\"timeline-content\">\n                            <div class=\"timeline-header\">\n                              <span class=\"badge\" :class=\"`bg-${getStatusColor(history.new_status_name)}`\">\n                                {{ formatStatus(history.new_status_name) }}\n                              </span>\n                              <small class=\"text-muted ms-2\">{{ formatDateTime(history.changed_at) }}</small>\n                            </div>\n                            <div class=\"timeline-body\">\n                              <p class=\"mb-1\">\n                                <strong>Changed by:</strong> {{ history.changed_by_name }}\n                              </p>\n                              <p v-if=\"history.old_status_name\" class=\"mb-1\">\n                                <strong>From:</strong> {{ formatStatus(history.old_status_name) }}\n                              </p>\n                              <p v-if=\"history.change_reason\" class=\"mb-0\">\n                                <strong>Reason:</strong> {{ history.change_reason }}\n                              </p>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      <div v-else class=\"text-center text-muted py-3\">\n                        <i class=\"fas fa-history fa-2x mb-2\"></i>\n                        <p>No status history available</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <div class=\"modal-footer\">\n                  <button type=\"button\" class=\"btn btn-secondary\" @click=\"showRequestDetails = false\">\n                    <i class=\"fas fa-times me-1\"></i>\n                    Close\n                  </button>\n                  <button type=\"button\" class=\"btn btn-primary\" @click=\"refreshRequestDetails\">\n                    <i class=\"fas fa-sync-alt me-1\"></i>\n                    Refresh\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Quick Reject Modal -->\n          <div v-if=\"showQuickReject && selectedRequestForReject\" class=\"modal fade show d-block\" tabindex=\"-1\" style=\"background-color: rgba(0,0,0,0.5);\">\n            <div class=\"modal-dialog\">\n              <div class=\"modal-content\">\n                <div class=\"modal-header\">\n                  <h5 class=\"modal-title\">\n                    <i class=\"fas fa-times-circle text-danger me-2\"></i>\n                    Reject Request\n                  </h5>\n                  <button type=\"button\" class=\"btn-close\" @click=\"closeQuickRejectModal\"></button>\n                </div>\n                <div class=\"modal-body\">\n                  <div class=\"alert alert-warning\">\n                    <i class=\"fas fa-exclamation-triangle me-2\"></i>\n                    You are about to reject this document request. This action will notify the client immediately.\n                  </div>\n\n                  <div class=\"mb-3\">\n                    <strong>Request Details:</strong>\n                    <ul class=\"list-unstyled mt-2\">\n                      <li><strong>Request Number:</strong> {{ selectedRequestForReject.request_number }}</li>\n                      <li><strong>Document Type:</strong> {{ selectedRequestForReject.document_type }}</li>\n                      <li><strong>Client:</strong> {{ selectedRequestForReject.client_name }}</li>\n                    </ul>\n                  </div>\n\n\n                </div>\n                <div class=\"modal-footer\">\n                  <button type=\"button\" class=\"btn btn-secondary\" @click=\"closeQuickRejectModal\" :disabled=\"quickRejectForm.loading\">\n                    <i class=\"fas fa-times me-1\"></i>\n                    Cancel\n                  </button>\n                  <button type=\"button\" class=\"btn btn-danger\" @click=\"confirmQuickReject\" :disabled=\"quickRejectForm.loading\">\n                    <i class=\"fas fa-times-circle me-1\"></i>\n                    <span v-if=\"quickRejectForm.loading\">\n                      <i class=\"fas fa-spinner fa-spin me-1\"></i>\n                      Rejecting...\n                    </span>\n                    <span v-else>Reject Request</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Quick Approve Modal -->\n          <div v-if=\"showQuickApprove && selectedRequestForApprove\" class=\"modal fade show d-block\" tabindex=\"-1\" style=\"background-color: rgba(0,0,0,0.5);\">\n            <div class=\"modal-dialog\">\n              <div class=\"modal-content\">\n                <div class=\"modal-header\">\n                  <h5 class=\"modal-title\">\n                    <i class=\"fas fa-check-circle text-success me-2\"></i>\n                    Approve Request\n                  </h5>\n                  <button type=\"button\" class=\"btn-close\" @click=\"closeQuickApproveModal\"></button>\n                </div>\n                <div class=\"modal-body\">\n                  <div class=\"alert alert-info\">\n                    <i class=\"fas fa-info-circle me-2\"></i>\n                    You are about to approve this document request. This action will notify the client immediately and move the request to the next processing stage.\n                  </div>\n\n                  <div class=\"mb-3\">\n                    <strong>Request Details:</strong>\n                    <ul class=\"list-unstyled mt-2\">\n                      <li><strong>Request Number:</strong> {{ selectedRequestForApprove.request_number }}</li>\n                      <li><strong>Document Type:</strong> {{ selectedRequestForApprove.document_type }}</li>\n                      <li><strong>Client:</strong> {{ selectedRequestForApprove.client_name }}</li>\n                    </ul>\n                  </div>\n\n                  <div v-if=\"quickApproveForm.error\" class=\"alert alert-danger\">\n                    <i class=\"fas fa-exclamation-triangle me-2\"></i>\n                    {{ quickApproveForm.error }}\n                  </div>\n                </div>\n                <div class=\"modal-footer\">\n                  <button type=\"button\" class=\"btn btn-secondary\" @click=\"closeQuickApproveModal\" :disabled=\"quickApproveForm.loading\">\n                    <i class=\"fas fa-times me-1\"></i>\n                    Cancel\n                  </button>\n                  <button type=\"button\" class=\"btn btn-success\" @click=\"confirmQuickApprove\" :disabled=\"quickApproveForm.loading\">\n                    <i class=\"fas fa-check-circle me-1\"></i>\n                    <span v-if=\"quickApproveForm.loading\">\n                      <i class=\"fas fa-spinner fa-spin me-1\"></i>\n                      Approving...\n                    </span>\n                    <span v-else>Approve Request</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n\n    <!-- Image Modal -->\n    <div v-if=\"showImageModal && selectedImage\" class=\"modal fade show d-block\" tabindex=\"-1\" style=\"background-color: rgba(0,0,0,0.8);\" @click.self=\"closeImageModal\">\n      <div class=\"modal-dialog modal-xl modal-dialog-centered\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header sticky-header\">\n            <h5 class=\"modal-title\">\n              <i class=\"fas fa-image me-2\"></i>\n              {{ selectedImage.document_name }}\n            </h5>\n            <div class=\"header-controls\">\n              <button\n                type=\"button\"\n                class=\"btn btn-outline-light btn-sm me-2\"\n                @click=\"downloadDocument(selectedImage)\"\n                :disabled=\"!documentUrls[selectedImage.id] || imageLoadingInModal\"\n                title=\"Download\">\n                <i class=\"fas fa-download\"></i>\n              </button>\n              <button\n                type=\"button\"\n                class=\"btn-close btn-close-white\"\n                @click=\"closeImageModal\"\n                aria-label=\"Close\"\n                title=\"Close\">\n              </button>\n            </div>\n          </div>\n          <div class=\"modal-body text-center p-0\">\n            <div class=\"image-modal-container\">\n              <!-- Successfully loaded image -->\n              <img\n                v-if=\"documentUrls[selectedImage.id] && !imageLoadingInModal\"\n                :src=\"documentUrls[selectedImage.id]\"\n                :alt=\"selectedImage.document_name\"\n                class=\"modal-image\"\n                @error=\"handleImageError\"\n                @load=\"onModalImageLoad\"\n                loading=\"lazy\"\n              />\n\n              <!-- Loading state -->\n              <div v-else-if=\"imageLoadingInModal || loadingDocuments.has(selectedImage.id)\" class=\"loading-placeholder modal-loading\">\n                <div class=\"loading-content\">\n                  <i class=\"fas fa-spinner fa-spin fa-3x mb-3\"></i>\n                  <span class=\"loading-text\">Loading high-resolution image...</span>\n                  <div class=\"loading-progress mt-2\">\n                    <div class=\"progress-bar\"></div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Failed state -->\n              <div v-else-if=\"modalImageError || failedDocuments.has(selectedImage.id)\" class=\"error-placeholder modal-error\">\n                <i class=\"fas fa-exclamation-triangle fa-3x mb-3\"></i>\n                <span class=\"error-text\">Failed to load image</span>\n                <button\n                  class=\"btn btn-outline-light mt-3\"\n                  @click=\"retryLoadDocument(selectedImage)\"\n                  :disabled=\"imageLoadingInModal\">\n                  <i class=\"fas fa-redo me-2\"></i>Retry\n                </button>\n              </div>\n\n              <!-- Fallback -->\n              <div v-else class=\"loading-placeholder modal-loading\">\n                <i class=\"fas fa-image fa-3x mb-3\"></i>\n                <span class=\"loading-text\">Preparing image...</span>\n              </div>\n            </div>\n          </div>\n          <div class=\"modal-footer\">\n            <div class=\"d-flex justify-content-between align-items-center w-100\">\n              <div class=\"image-info\">\n                <span class=\"badge bg-info me-2\">{{ getDocumentTypeDisplayName(selectedImage.document_type) }}</span>\n                <small class=\"text-muted\">\n                  {{ formatFileSize(selectedImage.file_size) }} •\n                  Uploaded {{ formatDate(selectedImage.created_at) }}\n                </small>\n              </div>\n              <div class=\"image-actions\">\n                <button\n                  type=\"button\"\n                  class=\"btn btn-outline-primary me-2\"\n                  @click=\"downloadDocument(selectedImage)\"\n                  :disabled=\"!documentUrls[selectedImage.id] || imageLoadingInModal\">\n                  <i class=\"fas fa-download me-1\"></i>Download\n                </button>\n                <button type=\"button\" class=\"btn btn-secondary\" @click=\"closeImageModal\">\n                  <i class=\"fas fa-times me-1\"></i>Close\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n</template>\n\n<script>\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\nimport adminAuthService from '@/services/adminAuthService';\nimport adminDocumentService from '@/services/adminDocumentService';\nimport api from '@/services/api';\nimport notificationService from '@/services/notificationService';\n\nexport default {\n  name: 'AdminRequests',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n\n\n\n  data() {\n    return {\n      // UI State\n      loading: true,\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      errorMessage: '',\n      viewMode: 'table', // 'card' or 'table' - default to table view\n\n      // Request Management Data\n      requests: [],\n      selectedRequests: [],\n      currentRequest: null,\n      statusOptions: [],\n\n      // Pagination\n      pagination: {\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n      },\n\n      // Filters\n      filters: {\n        status: '',\n        document_type: '',\n        priority: '',\n        search: '',\n        date_from: '',\n        date_to: ''\n      },\n\n      // Statistics\n      requestStats: {\n        total: 0,\n        pending: 0,\n        approved: 0,\n        completed: 0,\n        thisMonth: 0\n      },\n\n      // UI State\n      showFilters: false,\n      showBulkActions: false,\n      showRequestDetails: false,\n      showRejectForm: false,\n      showQuickReject: false,\n      showQuickApprove: false,\n      showImageModal: false,\n      selectedImage: null,\n      bulkAction: '',\n      documentUrls: {}, // Store blob URLs for documents\n      loadingDocuments: new Set(), // Track which documents are currently loading\n      failedDocuments: new Set(), // Track which documents failed to load\n      imageLoadingInModal: false, // Track if modal image is loading\n      modalImageError: false, // Track if modal image failed\n\n      // Status Update Forms\n      statusUpdateForm: {\n        status_id: ''\n      },\n      rejectForm: {\n        reason: ''\n      },\n      quickRejectForm: {\n        loading: false,\n        error: ''\n      },\n      selectedRequestForReject: null,\n      quickApproveForm: {\n        loading: false,\n        error: ''\n      },\n      selectedRequestForApprove: null,\n\n      // Payment verification form\n      paymentVerificationForm: {\n        amount_received: '',\n        receipt_number: '',\n        loading: false,\n        error: ''\n      },\n\n      // Pickup scheduling form\n      pickupScheduleForm: {\n        scheduled_date: '',\n        scheduled_time_start: '',\n        scheduled_time_end: '',\n        loading: false,\n        error: ''\n      },\n\n      // Real-time features\n      refreshInterval: null,\n      autoRefreshEnabled: true,\n      refreshRate: 30000, // 30 seconds\n      lastRefresh: null\n    };\n  },\n\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Load component data\n    await this.loadComponentData();\n\n    // Initialize real-time features\n    this.initializeRealTimeFeatures();\n  },\n\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n\n    // Clean up real-time features\n    this.cleanupRealTimeFeatures();\n\n    // Clean up blob URLs to prevent memory leaks\n    this.cleanupDocumentUrls();\n  },\n\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    }\n  },\n\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    // Handle opening request modal from notifications\n    async handleOpenRequestModal(modalData) {\n      console.log('🔔 AdminRequests: Opening request modal from notification:', modalData);\n\n      try {\n        const { requestId, focusTab } = modalData;\n\n        if (!requestId) {\n          console.error('❌ No request ID provided for modal');\n          return;\n        }\n\n        // Use the existing viewRequestDetails method to open the modal\n        await this.viewRequestDetails(requestId);\n\n        // If a specific tab should be focused, handle that after modal opens\n        if (focusTab) {\n          // Wait a bit for the modal to fully render\n          setTimeout(() => {\n            this.focusModalTab(focusTab);\n          }, 300);\n        }\n\n        console.log('✅ Request modal opened successfully');\n\n      } catch (error) {\n        console.error('❌ Error opening request modal:', error);\n        // Show error message to user\n        this.showErrorMessage('Failed to open request details');\n      }\n    },\n\n    // Focus on a specific tab in the request details modal\n    focusModalTab(tabName) {\n      try {\n        console.log('🎯 Focusing on modal tab:', tabName);\n\n        // Map tab names to actual tab elements or actions\n        const tabMappings = {\n          'payment': () => {\n            // Focus on payment section in the modal\n            const paymentSection = document.querySelector('#requestDetailsModal .payment-section');\n            if (paymentSection) {\n              paymentSection.scrollIntoView({ behavior: 'smooth', block: 'center' });\n              paymentSection.classList.add('highlight-section');\n              setTimeout(() => paymentSection.classList.remove('highlight-section'), 2000);\n            }\n          },\n          'status': () => {\n            // Focus on status section\n            const statusSection = document.querySelector('#requestDetailsModal .status-section');\n            if (statusSection) {\n              statusSection.scrollIntoView({ behavior: 'smooth', block: 'center' });\n              statusSection.classList.add('highlight-section');\n              setTimeout(() => statusSection.classList.remove('highlight-section'), 2000);\n            }\n          },\n          'documents': () => {\n            // Focus on documents section\n            const documentsSection = document.querySelector('#requestDetailsModal .documents-section');\n            if (documentsSection) {\n              documentsSection.scrollIntoView({ behavior: 'smooth', block: 'center' });\n              documentsSection.classList.add('highlight-section');\n              setTimeout(() => documentsSection.classList.remove('highlight-section'), 2000);\n            }\n          }\n        };\n\n        const focusAction = tabMappings[tabName];\n        if (focusAction) {\n          focusAction();\n        } else {\n          console.log('⚠️ Unknown tab name:', tabName);\n        }\n\n      } catch (error) {\n        console.error('❌ Error focusing modal tab:', error);\n      }\n    },\n\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n\n    // Load admin profile\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin profile:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n\n    // Load all component data\n    async loadComponentData() {\n      this.loading = true;\n      try {\n        await Promise.all([\n          this.loadAdminProfile(),\n          this.loadStatusOptions(),\n          this.loadRequests(),\n          this.loadDashboardStats()\n        ]);\n      } catch (error) {\n        console.error('Failed to load component data:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load request data';\n\n        if (errorData.status === 401) {\n          adminAuthService.logout();\n          this.$router.push('/admin/login');\n        }\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // Load status options\n    async loadStatusOptions() {\n      try {\n        console.log('🔄 Loading status options...');\n        const response = await adminDocumentService.getStatusOptions();\n        console.log('📋 Status options response:', response);\n\n        if (response.success) {\n          this.statusOptions = response.data || [];\n          console.log('✅ Status options loaded:', this.statusOptions);\n        } else {\n          console.error('❌ Failed to load status options:', response.message);\n          this.statusOptions = [];\n        }\n      } catch (error) {\n        console.error('❌ Error loading status options:', error);\n        this.statusOptions = [];\n        this.showToast('Error', 'Failed to load status options', 'error');\n      }\n    },\n\n    // Load dashboard statistics\n    async loadDashboardStats() {\n      try {\n        console.log('🔄 Loading dashboard stats...');\n        const response = await adminDocumentService.getDashboardStats();\n        console.log('📊 Dashboard stats response:', response);\n\n        if (response.success) {\n          // Map the backend response structure to frontend expectations\n          const data = response.data;\n          this.requestStats = {\n            total: data.overview?.total_requests || 0,\n            pending: data.overview?.pending_requests || 0,\n            approved: data.overview?.approved_requests || 0,\n            completed: data.overview?.completed_requests || 0,\n            thisMonth: data.time_based?.today_requests || 0\n          };\n          console.log('✅ Request stats updated:', this.requestStats);\n        } else {\n          console.error('❌ Failed to load dashboard stats:', response.message);\n        }\n      } catch (error) {\n        console.error('❌ Error loading dashboard stats:', error);\n        // Set default values on error\n        this.requestStats = {\n          total: 0,\n          pending: 0,\n          approved: 0,\n          completed: 0,\n          thisMonth: 0\n        };\n      }\n    },\n\n    // Load requests with current filters and pagination\n    async loadRequests() {\n      try {\n        const params = {\n          page: this.pagination.currentPage,\n          limit: this.pagination.itemsPerPage,\n          ...this.filters\n        };\n\n        const response = await adminDocumentService.getAllRequests(params);\n        if (response.success) {\n          this.requests = response.data.requests || [];\n          this.pagination = {\n            currentPage: response.data.pagination?.current_page || 1,\n            totalPages: response.data.pagination?.total_pages || 1,\n            totalItems: response.data.pagination?.total_records || 0,\n            itemsPerPage: response.data.pagination?.per_page || 10\n          };\n        }\n      } catch (error) {\n        console.error('Failed to load requests:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load requests';\n        this.requests = [];\n      }\n    },\n\n    // Filter and search methods\n    applyFilters() {\n      this.pagination.currentPage = 1;\n      this.loadRequests();\n    },\n\n    clearFilters() {\n      this.filters = {\n        status: '',\n        document_type: '',\n        priority: '',\n        search: '',\n        date_from: '',\n        date_to: ''\n      };\n      this.applyFilters();\n    },\n\n    // Pagination methods\n    changePage(page) {\n      if (page >= 1 && page <= this.pagination.totalPages) {\n        this.pagination.currentPage = page;\n        this.loadRequests();\n      }\n    },\n\n    changeItemsPerPage(itemsPerPage) {\n      this.pagination.itemsPerPage = itemsPerPage;\n      this.pagination.currentPage = 1;\n      this.loadRequests();\n    },\n\n    goBack() {\n      this.$router.push('/admin/dashboard');\n    },\n\n    // Request selection methods\n    toggleRequestSelection(requestId) {\n      const index = this.selectedRequests.indexOf(requestId);\n      if (index > -1) {\n        this.selectedRequests.splice(index, 1);\n      } else {\n        this.selectedRequests.push(requestId);\n      }\n    },\n\n    selectAllRequests() {\n      if (this.selectedRequests.length === this.requests.length) {\n        this.selectedRequests = [];\n      } else {\n        this.selectedRequests = this.requests.map(r => r.id);\n      }\n    },\n\n    // Request details\n    async viewRequestDetails(requestId) {\n      console.log('🚀 View details clicked for request ID:', requestId);\n\n      try {\n        const response = await adminDocumentService.getRequestDetails(requestId);\n        console.log('📋 API Response received:', response);\n\n        if (response.success) {\n          console.log('✅ Response successful, data:', response.data);\n\n          // Debug client profile fields\n          const data = response.data;\n          console.log('🎯 COMPLETE RESPONSE DATA:', data);\n          console.log('🎯 ALL DATA KEYS:', Object.keys(data));\n          console.log('🎯 CLIENT PROFILE FIELDS DEBUG:');\n          console.log('   Civil Status ID:', data.client_civil_status_id);\n          console.log('   Nationality:', data.client_nationality);\n          console.log('   Years of Residency:', data.client_years_of_residency);\n          console.log('   Months of Residency:', data.client_months_of_residency);\n\n          // Check if fields exist with different names\n          console.log('🔍 SEARCHING FOR SIMILAR FIELDS:');\n          Object.keys(data).forEach(key => {\n            if (key.includes('civil') || key.includes('nationality') || key.includes('residency')) {\n              console.log(`   Found: ${key} = ${data[key]}`);\n            }\n          });\n\n          this.currentRequest = response.data;\n          this.showRequestDetails = true;\n          // Reset forms\n          this.statusUpdateForm = { status_id: '' };\n          this.rejectForm = { reason: '' };\n          this.showRejectForm = false;\n          console.log('📋 Request details loaded:', response.data);\n\n          // Load document URLs for images\n          if (response.data.uploaded_documents && response.data.uploaded_documents.length > 0) {\n            this.loadDocumentUrls(response.data.uploaded_documents);\n          }\n\n          this.showToast('Success', `Request details loaded for ${response.data.request_number}`, 'success');\n        }\n      } catch (error) {\n        console.error('Failed to load request details:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load request details';\n        this.showToast('Error', 'Failed to load request details', 'error');\n      }\n    },\n\n    // Refresh request details\n    async refreshRequestDetails() {\n      if (this.currentRequest) {\n        await this.viewRequestDetails(this.currentRequest.id);\n      }\n    },\n\n    // Update request status from modal\n    async updateRequestStatusFromModal() {\n      console.log('🔄 Updating request status...');\n      console.log('📋 Status form data:', this.statusUpdateForm);\n      console.log('📋 Current request:', this.currentRequest);\n\n      if (!this.statusUpdateForm.status_id || !this.currentRequest) {\n        console.error('❌ Missing required data for status update');\n        this.showToast('Error', 'Please select a status to update', 'error');\n        return;\n      }\n\n      // Enhanced debugging for status validation\n      const currentStatus = this.currentRequest.status_name;\n      const newStatusId = this.statusUpdateForm.status_id;\n      const newStatus = this.statusOptions.find(s => s.id == newStatusId);\n\n      console.log('🔍 Status validation debug:');\n      console.log('   Current status:', currentStatus);\n      console.log('   New status ID:', newStatusId, '(type:', typeof newStatusId, ')');\n      console.log('   New status object:', newStatus);\n      console.log('   Available transitions:', this.getAllowedStatusTransitions(currentStatus.toLowerCase()));\n      console.log('   Available status options:', this.getAvailableStatusOptions());\n      console.log('   All status options:', this.statusOptions);\n\n      if (!this.isValidStatusChange(currentStatus, newStatusId)) {\n        console.error('❌ Invalid status change attempted');\n        console.error('   From:', currentStatus, 'To:', newStatus?.status_name);\n        this.showToast('Error', 'This status change is not allowed', 'error');\n        return;\n      }\n\n      try {\n        const updateData = {\n          status_id: parseInt(this.statusUpdateForm.status_id)\n        };\n\n        console.log('📤 Sending status update:', updateData);\n\n        const response = await adminDocumentService.updateRequestStatus(\n          this.currentRequest.id,\n          updateData\n        );\n\n        console.log('📥 Status update response:', response);\n\n        if (response.success) {\n          // Refresh the request details\n          await this.refreshRequestDetails();\n          // Refresh the main requests list\n          await this.loadRequests();\n          // Reset form\n          this.statusUpdateForm = { status_id: '' };\n\n          // Show success message\n          this.errorMessage = '';\n          this.showToast('Success', 'Request status updated successfully', 'success');\n        } else {\n          console.error('❌ Status update failed:', response.message);\n          this.showToast('Error', response.message || 'Failed to update request status', 'error');\n        }\n      } catch (error) {\n        console.error('❌ Error updating request status:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to update request status';\n        this.showToast('Error', errorData.message || 'Failed to update request status', 'error');\n      }\n    },\n\n\n\n    // Reject request from modal\n    async rejectRequestFromModal() {\n      if (!this.currentRequest || !this.rejectForm.reason.trim()) return;\n\n      try {\n        const response = await adminDocumentService.rejectRequest(\n          this.currentRequest.id,\n          { reason: this.rejectForm.reason }\n        );\n\n        if (response.success) {\n          await this.refreshRequestDetails();\n          await this.loadRequests();\n          this.rejectForm = { reason: '' };\n          this.showRejectForm = false;\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to reject request';\n      }\n    },\n\n    // Status update methods\n    async updateRequestStatus(requestId, statusId, reason = '') {\n      try {\n        const response = await adminDocumentService.updateRequestStatus(requestId, {\n          status_id: statusId,\n          reason: reason\n        });\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to update request status:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to update request status';\n      }\n    },\n\n    async approveRequest(requestId, reason = '') {\n      try {\n        const response = await adminDocumentService.approveRequest(requestId, { reason });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to approve request';\n      }\n    },\n\n    async rejectRequest(requestId, reason) {\n      if (!reason || reason.trim() === '') {\n        this.errorMessage = 'Rejection reason is required';\n        return;\n      }\n\n      try {\n        const response = await adminDocumentService.rejectRequest(requestId, { reason });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to reject request';\n      }\n    },\n\n    // Quick approval/rejection methods\n    canApprove(request) {\n      // Can approve if 'approved' is in allowed transitions\n      const allowedTransitions = this.getAllowedStatusTransitions(request.status_name.toLowerCase());\n      return allowedTransitions.includes('approved');\n    },\n\n    canReject(request) {\n      // Can reject if 'rejected' is in allowed transitions\n      const allowedTransitions = this.getAllowedStatusTransitions(request.status_name.toLowerCase());\n      return allowedTransitions.includes('rejected');\n    },\n\n    // Helper method to get status explanation for disabled buttons\n    getStatusExplanation(request, action) {\n      const status = request.status_name.toLowerCase();\n      const allowedTransitions = this.getAllowedStatusTransitions(status);\n\n      if (action === 'approve') {\n        if (allowedTransitions.includes('approved')) {\n          return 'Click to approve this request';\n        } else if (status === 'approved') {\n          return 'This request has already been approved';\n        } else if (status === 'rejected') {\n          return 'Rejected requests can be resubmitted, not directly approved';\n        } else if (status === 'completed') {\n          return 'This request has already been completed';\n        } else {\n          return `Cannot approve from ${this.formatStatus(status)} status`;\n        }\n      } else if (action === 'reject') {\n        if (allowedTransitions.includes('rejected')) {\n          return 'Click to reject this request';\n        } else if (status === 'rejected') {\n          return 'This request has already been rejected';\n        } else if (status === 'completed') {\n          return 'Cannot reject a completed request';\n        } else {\n          return `Cannot reject from ${this.formatStatus(status)} status`;\n        }\n      }\n\n      return `Request status: ${this.formatStatus(status)}`;\n    },\n\n    // Check if status change is valid\n    isValidStatusChange(currentStatus, newStatusId) {\n      if (!currentStatus || !newStatusId) return false;\n\n      // Find the new status name\n      const newStatus = this.statusOptions.find(s => s.id == newStatusId);\n      if (!newStatus) return false;\n\n      const currentStatusName = currentStatus.toLowerCase();\n      const newStatusName = newStatus.status_name.toLowerCase();\n\n      // Same status - no change needed\n      if (currentStatusName === newStatusName) {\n        return false;\n      }\n\n      // Check if transition is allowed based on workflow rules\n      const allowedTransitions = this.getAllowedStatusTransitions(currentStatusName);\n\n      return allowedTransitions.includes(newStatusName);\n    },\n\n    // Check if request needs payment verification\n    needsPaymentVerification(request) {\n      return request.status_name === 'payment_pending' &&\n             request.payment_method &&\n             !request.payment_method.includes('PayMongo') &&\n             request.payment_status !== 'paid';\n    },\n\n    // Check if pickup can be scheduled\n    canSchedulePickup(request) {\n      return request.status_name === 'ready_for_pickup';\n    },\n\n    // Get payment status color\n    getPaymentStatusColor(status) {\n      const colors = {\n        'pending': 'bg-warning',\n        'processing': 'bg-info',\n        'paid': 'bg-success',\n        'failed': 'bg-danger',\n        'refunded': 'bg-secondary',\n        'cancelled': 'bg-dark'\n      };\n      return colors[status] || 'bg-secondary';\n    },\n\n    // Format payment status\n    formatPaymentStatus(status) {\n      const statuses = {\n        'pending': 'Pending',\n        'processing': 'Processing',\n        'paid': 'Paid',\n        'failed': 'Failed',\n        'refunded': 'Refunded',\n        'cancelled': 'Cancelled'\n      };\n      return statuses[status] || 'Unknown';\n    },\n\n    // Get tomorrow's date for pickup scheduling\n    getTomorrowDate() {\n      const tomorrow = new Date();\n      tomorrow.setDate(tomorrow.getDate() + 1);\n      return tomorrow.toISOString().split('T')[0];\n    },\n\n    // Validate pickup form\n    isPickupFormValid() {\n      return this.pickupScheduleForm.scheduled_date &&\n             this.pickupScheduleForm.scheduled_time_start &&\n             this.pickupScheduleForm.scheduled_time_end &&\n             this.pickupScheduleForm.scheduled_time_start < this.pickupScheduleForm.scheduled_time_end;\n    },\n\n    // Get filtered status options based on current status\n    getAvailableStatusOptions() {\n      if (!this.currentRequest || !this.statusOptions) return [];\n\n      const currentStatus = this.currentRequest.status_name.toLowerCase();\n\n      // Only these states are truly final (cannot be changed)\n      const finalStates = ['completed', 'cancelled'];\n\n      // If current status is final, no changes allowed\n      if (finalStates.includes(currentStatus)) {\n        return [];\n      }\n\n      // Define allowed transitions based on current status\n      const allowedTransitions = this.getAllowedStatusTransitions(currentStatus);\n\n      // Return only allowed status options\n      return this.statusOptions.filter(status =>\n        allowedTransitions.includes(status.status_name.toLowerCase())\n      );\n    },\n\n    // Define allowed status transitions based on government workflow best practices\n    // This must match the backend validateStatusTransition logic exactly\n    getAllowedStatusTransitions(currentStatus) {\n      const transitions = {\n        'pending': ['under_review', 'approved', 'cancelled', 'rejected'],\n        'under_review': ['approved', 'rejected', 'cancelled'], // Removed additional_info_required\n        // Removed 'additional_info_required' status entirely\n        'approved': ['payment_pending', 'cancelled'], // Updated to match backend: approved must go to payment_pending first\n        'payment_pending': ['payment_confirmed', 'payment_failed', 'cancelled'],\n        'payment_confirmed': ['processing'], // Automatic transition after payment\n        'payment_failed': ['payment_pending', 'cancelled'],\n        'processing': ['ready_for_pickup'], // Processing can only complete successfully\n        'ready_for_pickup': ['pickup_scheduled', 'completed', 'cancelled'],\n        'pickup_scheduled': ['completed', 'ready_for_pickup', 'cancelled'], // Can reschedule\n        'rejected': ['pending', 'under_review'], // Allow resubmission after corrections\n        // Final states - no transitions allowed\n        'completed': [],\n        'cancelled': []\n      };\n\n      return transitions[currentStatus] || [];\n    },\n\n    // Get title for update button based on validation state\n    getUpdateButtonTitle() {\n      if (!this.statusUpdateForm.status_id) {\n        return 'Please select a new status';\n      }\n      if (!this.isValidStatusChange(this.currentRequest.status_name, this.statusUpdateForm.status_id)) {\n        return 'Invalid status change';\n      }\n      return 'Update request status';\n    },\n\n    // Get dynamic button text based on selected status\n    getActionButtonText() {\n      if (!this.statusUpdateForm.status_id) {\n        return 'Update Status';\n      }\n\n      const selectedStatus = this.statusOptions.find(s => s.id === parseInt(this.statusUpdateForm.status_id));\n      if (!selectedStatus) {\n        return 'Update Status';\n      }\n\n      const statusName = selectedStatus.status_name.toLowerCase();\n\n      // Special button text for common actions\n      switch (statusName) {\n        case 'approved':\n          return 'Approve Request';\n        case 'rejected':\n          return 'Reject Request';\n        case 'under_review':\n          return 'Move to Review';\n        case 'processing':\n          return 'Start Processing';\n        case 'ready_for_pickup':\n          return 'Mark Ready for Pickup';\n        case 'completed':\n          return 'Complete Request';\n        default:\n          return `Update to ${selectedStatus.status_name}`;\n      }\n    },\n\n    async quickApprove(request) {\n      console.log('🚀 Quick approve clicked for request:', request);\n\n      try {\n        this.loading = true;\n        const response = await adminDocumentService.approveRequest(request.id, {\n          reason: 'Quick approval from admin interface'\n        });\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.showToast('Success', `Request ${request.request_number} approved successfully`, 'success');\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.showToast('Error', errorData.message || 'Failed to approve request', 'error');\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    showQuickRejectModal(request) {\n      console.log('🚀 Quick reject clicked for request:', request);\n\n      this.selectedRequestForReject = request;\n      this.quickRejectForm = {\n        loading: false,\n        error: ''\n      };\n      this.showQuickReject = true;\n    },\n\n    closeQuickRejectModal() {\n      this.showQuickReject = false;\n      this.selectedRequestForReject = null;\n      this.quickRejectForm = {\n        loading: false,\n        error: ''\n      };\n    },\n\n    async confirmQuickReject() {\n      this.quickRejectForm.loading = true;\n      this.quickRejectForm.error = '';\n\n      try {\n        const response = await adminDocumentService.rejectRequest(\n          this.selectedRequestForReject.id,\n          { reason: 'Request rejected by admin' }\n        );\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.showToast('Success', `Request ${this.selectedRequestForReject.request_number} rejected successfully`, 'success');\n          this.closeQuickRejectModal();\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.quickRejectForm.error = errorData.message || 'Failed to reject request';\n      } finally {\n        this.quickRejectForm.loading = false;\n      }\n    },\n\n    showQuickApproveModal(request) {\n      console.log('🚀 Quick approve clicked for request:', request);\n\n      this.selectedRequestForApprove = request;\n      this.quickApproveForm = {\n        loading: false,\n        error: ''\n      };\n      this.showQuickApprove = true;\n    },\n\n    closeQuickApproveModal() {\n      this.showQuickApprove = false;\n      this.selectedRequestForApprove = null;\n      this.quickApproveForm = {\n        loading: false,\n        error: ''\n      };\n    },\n\n    async confirmQuickApprove() {\n      this.quickApproveForm.loading = true;\n      this.quickApproveForm.error = '';\n\n      try {\n        const response = await adminDocumentService.approveRequest(\n          this.selectedRequestForApprove.id,\n          { reason: 'Quick approval from admin interface' }\n        );\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.showToast('Success', `Request ${this.selectedRequestForApprove.request_number} approved successfully`, 'success');\n          this.closeQuickApproveModal();\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.quickApproveForm.error = errorData.message || 'Failed to approve request';\n      } finally {\n        this.quickApproveForm.loading = false;\n      }\n    },\n\n    // Bulk operations\n    async performBulkAction() {\n      if (this.selectedRequests.length === 0) {\n        this.errorMessage = 'Please select at least one request';\n        return;\n      }\n\n      if (!this.bulkAction) {\n        this.errorMessage = 'Please select a bulk action';\n        return;\n      }\n\n      try {\n        const response = await adminDocumentService.bulkUpdateRequests({\n          request_ids: this.selectedRequests,\n          status_id: parseInt(this.bulkAction)\n        });\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.selectedRequests = [];\n          this.bulkAction = '';\n          this.showBulkActions = false;\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to perform bulk action:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to perform bulk action';\n      }\n    },\n\n    // Export functionality\n    async exportRequests() {\n      try {\n        const csvData = await adminDocumentService.exportRequests(this.filters);\n        const filename = `document_requests_${new Date().toISOString().split('T')[0]}.csv`;\n        adminDocumentService.downloadCSV(csvData, filename);\n      } catch (error) {\n        console.error('Failed to export requests:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to export requests';\n      }\n    },\n\n    // Verify in-person payment\n    async verifyInPersonPayment() {\n      if (!this.paymentVerificationForm.amount_received || !this.currentRequest) {\n        this.showToast('Error', 'Please enter the amount received', 'error');\n        return;\n      }\n\n      const totalFee = parseFloat(this.currentRequest.total_fee);\n      const amountReceived = parseFloat(this.paymentVerificationForm.amount_received);\n\n      if (amountReceived < totalFee) {\n        this.showToast('Error', `Insufficient payment. Required: ${this.formatCurrency(totalFee)}`, 'error');\n        return;\n      }\n\n      this.paymentVerificationForm.loading = true;\n      this.paymentVerificationForm.error = '';\n\n      try {\n        const paymentData = {\n          amount_received: amountReceived,\n          payment_method_id: this.currentRequest.payment_method_id || 1, // Default to cash\n          receipt_number: this.paymentVerificationForm.receipt_number\n        };\n\n        const response = await adminDocumentService.verifyInPersonPayment(this.currentRequest.id, paymentData);\n\n        if (response.success) {\n          await this.refreshRequestDetails();\n          await this.loadRequests();\n\n          // Reset form\n          this.paymentVerificationForm = {\n            amount_received: '',\n            receipt_number: '',\n            loading: false,\n            error: ''\n          };\n\n          this.showToast('Success', 'Payment verified successfully', 'success');\n        }\n      } catch (error) {\n        console.error('Failed to verify payment:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.paymentVerificationForm.error = errorData.message || 'Failed to verify payment';\n        this.showToast('Error', errorData.message || 'Failed to verify payment', 'error');\n      } finally {\n        this.paymentVerificationForm.loading = false;\n      }\n    },\n\n    // Schedule pickup appointment\n    async schedulePickup() {\n      if (!this.isPickupFormValid() || !this.currentRequest) {\n        this.showToast('Error', 'Please fill in all required fields', 'error');\n        return;\n      }\n\n      this.pickupScheduleForm.loading = true;\n      this.pickupScheduleForm.error = '';\n\n      try {\n        const scheduleData = {\n          scheduled_date: this.pickupScheduleForm.scheduled_date,\n          scheduled_time_start: this.pickupScheduleForm.scheduled_time_start,\n          scheduled_time_end: this.pickupScheduleForm.scheduled_time_end\n        };\n\n        const response = await adminDocumentService.schedulePickup(this.currentRequest.id, scheduleData);\n\n        if (response.success) {\n          await this.refreshRequestDetails();\n          await this.loadRequests();\n\n          // Reset form\n          this.pickupScheduleForm = {\n            scheduled_date: '',\n            scheduled_time_start: '',\n            scheduled_time_end: '',\n            loading: false,\n            error: ''\n          };\n\n          this.showToast('Success', 'Pickup scheduled successfully', 'success');\n        }\n      } catch (error) {\n        console.error('Failed to schedule pickup:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.pickupScheduleForm.error = errorData.message || 'Failed to schedule pickup';\n        this.showToast('Error', errorData.message || 'Failed to schedule pickup', 'error');\n      } finally {\n        this.pickupScheduleForm.loading = false;\n      }\n    },\n\n    // Utility methods\n    formatStatus(status) {\n      return adminDocumentService.formatStatus(status);\n    },\n\n    getStatusColor(status) {\n      return adminDocumentService.getStatusColor(status);\n    },\n\n    formatDate(dateString) {\n      console.log('🗓️ formatDate called with:', dateString);\n      if (!dateString) {\n        console.log('🗓️ formatDate: No date provided, returning null');\n        return null;\n      }\n      const date = new Date(dateString);\n      const formatted = date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n      console.log('🗓️ formatDate result:', formatted);\n      return formatted;\n    },\n\n    formatCurrency(amount) {\n      return new Intl.NumberFormat('en-PH', {\n        style: 'currency',\n        currency: 'PHP'\n      }).format(amount || 0);\n    },\n\n    formatDateTime(dateString) {\n      if (!dateString) return 'N/A';\n      return new Date(dateString).toLocaleString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    // New helper methods for complete client information\n    getClientFullName(request) {\n      if (!request) return 'Not provided';\n      const parts = [\n        request.client_first_name,\n        request.client_middle_name,\n        request.client_last_name,\n        request.client_suffix\n      ].filter(Boolean);\n      return parts.length > 0 ? parts.join(' ') : request.client_name || 'Not provided';\n    },\n\n    getClientFullAddress(request) {\n      if (!request) return null;\n      const parts = [\n        request.client_house_number,\n        request.client_street,\n        request.client_subdivision,\n        request.client_barangay,\n        request.client_city_municipality || request.client_city,\n        request.client_province\n      ].filter(Boolean);\n      return parts.length > 0 ? parts.join(', ') : (request.client_address || null);\n    },\n\n    formatGender(gender) {\n      if (!gender) {\n        return null;\n      }\n      return gender.charAt(0).toUpperCase() + gender.slice(1);\n    },\n\n    getCivilStatusName(statusId) {\n      const statuses = {\n        1: 'Single',\n        2: 'Married',\n        3: 'Divorced',\n        4: 'Widowed',\n        5: 'Separated'\n      };\n      return statuses[statusId] || null;\n    },\n\n    getResidencyDisplay(request) {\n      if (!request) return null;\n      const years = request.client_years_of_residency;\n      const months = request.client_months_of_residency;\n\n      if (!years && !months) return null; // Return null so the template can handle \"Not provided\"\n\n      const parts = [];\n      if (years) parts.push(`${years} year${years > 1 ? 's' : ''}`);\n      if (months) parts.push(`${months} month${months > 1 ? 's' : ''}`);\n\n      return parts.join(' and ');\n    },\n\n    formatTime(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    },\n\n    // Real-time features\n    async initializeRealTimeFeatures() {\n      console.log('Initializing real-time features for AdminRequests');\n\n      try {\n        // Initialize notification service\n        await notificationService.init('admin');\n\n        // Listen for request-related notifications\n        notificationService.on('notification', this.handleRealTimeNotification);\n        notificationService.on('request_status_changed', this.handleStatusChange);\n        notificationService.on('new_request', this.handleNewRequest);\n\n        // Start auto-refresh if enabled\n        if (this.autoRefreshEnabled) {\n          this.startAutoRefresh();\n        }\n      } catch (error) {\n        console.error('Failed to initialize real-time features:', error);\n      }\n    },\n\n    cleanupRealTimeFeatures() {\n      console.log('Cleaning up real-time features for AdminRequests');\n\n      // Remove notification listeners\n      notificationService.off('notification', this.handleRealTimeNotification);\n      notificationService.off('request_status_changed', this.handleStatusChange);\n      notificationService.off('new_request', this.handleNewRequest);\n\n      // Cleanup (simplified)\n      notificationService.cleanup();\n\n      // Stop auto-refresh\n      this.stopAutoRefresh();\n    },\n\n    startAutoRefresh() {\n      if (this.refreshInterval) {\n        clearInterval(this.refreshInterval);\n      }\n\n      this.refreshInterval = setInterval(() => {\n        if (this.autoRefreshEnabled && !this.loading) {\n          console.log('Auto-refreshing requests data...');\n          this.refreshRequestsData();\n        }\n      }, this.refreshRate);\n\n      console.log(`Auto-refresh started with ${this.refreshRate / 1000}s interval`);\n    },\n\n    stopAutoRefresh() {\n      if (this.refreshInterval) {\n        clearInterval(this.refreshInterval);\n        this.refreshInterval = null;\n        console.log('Auto-refresh stopped');\n      }\n    },\n\n    toggleAutoRefresh() {\n      this.autoRefreshEnabled = !this.autoRefreshEnabled;\n\n      if (this.autoRefreshEnabled) {\n        this.startAutoRefresh();\n      } else {\n        this.stopAutoRefresh();\n      }\n    },\n\n    async refreshRequestsData() {\n      try {\n        this.lastRefresh = new Date();\n\n        // Refresh requests list\n        await this.loadRequests();\n\n        // Refresh statistics\n        await this.loadDashboardStats();\n\n        // If request details modal is open, refresh it\n        if (this.showRequestDetails && this.currentRequest) {\n          await this.refreshRequestDetails();\n        }\n\n        console.log('Requests data refreshed successfully');\n      } catch (error) {\n        console.error('Failed to refresh requests data:', error);\n      }\n    },\n\n    handleRealTimeNotification(notification) {\n      console.log('Real-time notification received:', notification);\n\n      // Handle different notification types\n      switch (notification.type) {\n        case 'request_status_changed':\n          this.handleStatusChange(notification.data);\n          break;\n        case 'new_request':\n          this.handleNewRequest(notification.data);\n          break;\n        case 'request_updated':\n          this.handleRequestUpdate(notification.data);\n          break;\n        case 'unread_count_update':\n        case 'heartbeat':\n          // Polling system notifications - handled by notification service\n          break;\n        default:\n          // Only log unknown types, not system types\n          if (!['unread_count_update', 'heartbeat'].includes(notification.type)) {\n            console.log('Unhandled notification type:', notification.type);\n          }\n      }\n    },\n\n    handleStatusChange(data) {\n      console.log('Request status changed:', data);\n\n      // Update the request in the list if it exists\n      const requestIndex = this.requests.findIndex(req => req.id === data.request_id);\n      if (requestIndex !== -1) {\n        // Refresh the specific request or reload all requests\n        this.refreshRequestsData();\n      }\n\n      // Show toast notification\n      this.showToast('Request status updated', `Request #${data.request_id} status changed to ${data.new_status}`, 'info');\n    },\n\n    handleNewRequest(data) {\n      console.log('New request received:', data);\n\n      // Refresh requests to show the new request\n      this.refreshRequestsData();\n\n      // Show toast notification\n      this.showToast('New Request', `New ${data.document_type} request received`, 'success');\n    },\n\n    handleRequestUpdate(data) {\n      console.log('Request updated:', data);\n\n      // If the updated request is currently being viewed, refresh details\n      if (this.currentRequest && this.currentRequest.id === data.request_id) {\n        this.refreshRequestDetails();\n      }\n\n      // Refresh the requests list\n      this.refreshRequestsData();\n    },\n\n    showToast(title, message, type = 'info') {\n      // Log to console for debugging\n      console.log(`[${type.toUpperCase()}] ${title}: ${message}`);\n\n      // Create a simple toast notification\n      const toast = document.createElement('div');\n      toast.className = `toast-notification toast-${type}`;\n      toast.innerHTML = `\n        <div class=\"toast-header\">\n          <strong>${title}</strong>\n          <button type=\"button\" class=\"toast-close\" onclick=\"this.parentElement.parentElement.remove()\">×</button>\n        </div>\n        <div class=\"toast-body\">${message}</div>\n      `;\n\n      // Add toast styles if not already added\n      if (!document.getElementById('toast-styles')) {\n        const styles = document.createElement('style');\n        styles.id = 'toast-styles';\n        styles.textContent = `\n          .toast-notification {\n            position: fixed;\n            top: 20px;\n            right: 20px;\n            min-width: 300px;\n            background: white;\n            border-radius: 8px;\n            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n            z-index: 9999;\n            animation: slideIn 0.3s ease;\n          }\n          .toast-header {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            padding: 12px 16px 8px;\n            border-bottom: 1px solid #e9ecef;\n          }\n          .toast-body {\n            padding: 8px 16px 12px;\n            color: #6c757d;\n          }\n          .toast-close {\n            background: none;\n            border: none;\n            font-size: 18px;\n            cursor: pointer;\n            color: #6c757d;\n          }\n          .toast-success { border-left: 4px solid #28a745; }\n          .toast-error { border-left: 4px solid #dc3545; }\n          .toast-info { border-left: 4px solid #17a2b8; }\n          .toast-warning { border-left: 4px solid #ffc107; }\n          @keyframes slideIn {\n            from { transform: translateX(100%); opacity: 0; }\n            to { transform: translateX(0); opacity: 1; }\n          }\n        `;\n        document.head.appendChild(styles);\n      }\n\n      // Add toast to page\n      document.body.appendChild(toast);\n\n      // Auto-remove after 5 seconds\n      setTimeout(() => {\n        if (toast.parentElement) {\n          toast.style.animation = 'slideIn 0.3s ease reverse';\n          setTimeout(() => toast.remove(), 300);\n        }\n      }, 5000);\n    },\n\n    // Document handling methods\n    getDocumentTypeDisplayName(type) {\n      const displayNames = {\n        'government_id': 'Government ID',\n        'proof_of_residency': 'Proof of Residency',\n        'cedula': 'Community Tax Certificate (Cedula)',\n        'birth_certificate': 'Birth Certificate',\n        'marriage_certificate': 'Marriage Certificate',\n        'other': 'Other Document'\n      };\n      return displayNames[type] || type;\n    },\n\n    isImageFile(mimeType) {\n      return mimeType && (\n        mimeType.startsWith('image/') ||\n        ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'].includes(mimeType)\n      );\n    },\n\n    isPdfFile(mimeType) {\n      return mimeType === 'application/pdf';\n    },\n\n    async loadDocumentUrls(documents) {\n      // Filter documents that need loading (images only, not already loaded/loading/failed)\n      const documentsToLoad = documents.filter(doc =>\n        this.isImageFile(doc.mime_type) &&\n        !this.documentUrls[doc.id] &&\n        !this.loadingDocuments.has(doc.id) &&\n        !this.failedDocuments.has(doc.id)\n      );\n\n      if (documentsToLoad.length === 0) return;\n\n      // Load documents in parallel with concurrency limit\n      const CONCURRENT_LIMIT = 3;\n      const chunks = this.chunkArray(documentsToLoad, CONCURRENT_LIMIT);\n\n      for (const chunk of chunks) {\n        await Promise.allSettled(\n          chunk.map(document => this.loadSingleDocument(document))\n        );\n      }\n    },\n\n    async loadSingleDocument(document, isForModal = false) {\n      const docId = document.id;\n\n      try {\n        // Mark as loading\n        this.loadingDocuments.add(docId);\n        if (isForModal) this.imageLoadingInModal = true;\n\n        // Use authenticated API call to get the document\n        const response = await api.get(`/documents/view/${docId}`, {\n          responseType: 'blob',\n          timeout: 15000, // Increased timeout for large images\n          onDownloadProgress: (progressEvent) => {\n            // Optional: Could emit progress events here\n            if (progressEvent.lengthComputable) {\n              const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n              console.log(`Loading ${docId}: ${percentCompleted}%`);\n            }\n          }\n        });\n\n        // Validate response\n        if (!response.data || response.data.size === 0) {\n          throw new Error('Empty response received');\n        }\n\n        // Check file size and optimize if needed\n        const blob = response.data;\n        const optimizedBlob = await this.optimizeImageBlob(blob, document.mime_type, isForModal);\n\n        // Create blob URL using requestIdleCallback for better performance\n        await this.createBlobUrlWhenIdle(docId, optimizedBlob);\n\n        // Remove from failed set if it was there\n        this.failedDocuments.delete(docId);\n        if (isForModal) this.modalImageError = false;\n\n      } catch (error) {\n        console.warn(`Failed to load document ${docId}:`, error.message);\n        this.failedDocuments.add(docId);\n        if (isForModal) this.modalImageError = true;\n\n        // Optionally retry after a delay for network errors\n        if (error.code === 'NETWORK_ERROR' || error.code === 'ECONNABORTED') {\n          setTimeout(() => {\n            this.failedDocuments.delete(docId);\n          }, 30000); // Retry after 30 seconds\n        }\n      } finally {\n        // Remove from loading set\n        this.loadingDocuments.delete(docId);\n        if (isForModal) this.imageLoadingInModal = false;\n      }\n    },\n\n    chunkArray(array, size) {\n      const chunks = [];\n      for (let i = 0; i < array.length; i += size) {\n        chunks.push(array.slice(i, i + size));\n      }\n      return chunks;\n    },\n\n    async getDocumentUrl(document) {\n      // This method is now deprecated in favor of loadDocumentUrls\n      // Keeping for backward compatibility\n      if (this.documentUrls[document.id]) {\n        return this.documentUrls[document.id];\n      }\n      return null;\n    },\n\n    formatFileSize(bytes) {\n      if (!bytes) return '0 B';\n      const k = 1024;\n      const sizes = ['B', 'KB', 'MB', 'GB'];\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];\n    },\n\n    async openImageModal(document) {\n      // Prevent multiple rapid clicks\n      if (this.imageLoadingInModal) return;\n\n      // Don't open modal if document failed to load and we're not retrying\n      if (this.failedDocuments.has(document.id)) {\n        return;\n      }\n\n      // Set modal state immediately for responsiveness\n      this.selectedImage = document;\n      this.showImageModal = true;\n      this.modalImageError = false;\n\n      // Use nextTick to ensure DOM is updated before heavy operations\n      await this.$nextTick();\n\n      // If image isn't loaded yet, try to load it with modal optimization\n      if (!this.documentUrls[document.id] && !this.loadingDocuments.has(document.id)) {\n        await this.loadSingleDocument(document, true);\n      }\n    },\n\n    async retryLoadDocument(document) {\n      // Remove from failed set and retry loading\n      this.failedDocuments.delete(document.id);\n      this.modalImageError = false;\n      await this.loadSingleDocument(document, true);\n    },\n\n    onModalImageLoad() {\n      // Called when modal image finishes loading\n      this.imageLoadingInModal = false;\n    },\n\n    cleanupDocumentUrls() {\n      // Revoke all blob URLs to prevent memory leaks\n      Object.values(this.documentUrls).forEach(url => {\n        if (url) URL.revokeObjectURL(url);\n      });\n\n      // Clear all tracking sets and objects\n      this.documentUrls = {};\n      this.loadingDocuments.clear();\n      this.failedDocuments.clear();\n    },\n\n    preloadImage(document) {\n      // Preload image on hover for better UX\n      if (!this.documentUrls[document.id] &&\n          !this.loadingDocuments.has(document.id) &&\n          !this.failedDocuments.has(document.id)) {\n        this.loadSingleDocument(document, false);\n      }\n    },\n\n    async optimizeImageBlob(blob, mimeType, isForModal = false) {\n      // For very large images, we might want to resize them\n      const MAX_SIZE = isForModal ? 5 * 1024 * 1024 : 2 * 1024 * 1024; // 5MB for modal, 2MB for preview\n\n      if (blob.size <= MAX_SIZE) {\n        return blob; // No optimization needed\n      }\n\n      try {\n        // Create image element for resizing\n        const img = new Image();\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n\n        return new Promise((resolve) => {\n          img.onload = () => {\n            // Calculate new dimensions (maintain aspect ratio)\n            const maxDimension = isForModal ? 1920 : 800;\n            let { width, height } = img;\n\n            if (width > height && width > maxDimension) {\n              height = (height * maxDimension) / width;\n              width = maxDimension;\n            } else if (height > maxDimension) {\n              width = (width * maxDimension) / height;\n              height = maxDimension;\n            }\n\n            // Set canvas size and draw resized image\n            canvas.width = width;\n            canvas.height = height;\n            ctx.drawImage(img, 0, 0, width, height);\n\n            // Convert to blob with compression\n            canvas.toBlob(\n              (optimizedBlob) => {\n                resolve(optimizedBlob || blob); // Fallback to original if optimization fails\n              },\n              mimeType,\n              0.85 // 85% quality\n            );\n          };\n\n          img.onerror = () => resolve(blob); // Fallback to original\n          img.src = URL.createObjectURL(blob);\n        });\n      } catch (error) {\n        console.warn('Image optimization failed:', error);\n        return blob; // Fallback to original\n      }\n    },\n\n    async createBlobUrlWhenIdle(docId, blob) {\n      return new Promise((resolve) => {\n        const createUrl = () => {\n          this.documentUrls[docId] = URL.createObjectURL(blob);\n          resolve();\n        };\n\n        // Use requestIdleCallback if available, otherwise use setTimeout\n        if (window.requestIdleCallback) {\n          window.requestIdleCallback(createUrl, { timeout: 1000 });\n        } else {\n          setTimeout(createUrl, 0);\n        }\n      });\n    },\n\n    closeImageModal() {\n      // Prevent rapid clicking during image loading\n      if (this.imageLoadingInModal) return;\n\n      this.showImageModal = false;\n      this.selectedImage = null;\n      this.imageLoadingInModal = false;\n      this.modalImageError = false;\n    },\n\n    async downloadDocument(documentFile) {\n      try {\n        // Use authenticated API call to download the document\n        const response = await api.get(`/documents/download/${documentFile.id}`, {\n          responseType: 'blob'\n        });\n\n        // Create a download link\n        const blob = new Blob([response.data], { type: documentFile.mime_type });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = documentFile.document_name;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n      } catch (error) {\n        console.error('Failed to download document:', error);\n        this.showToast('Error', 'Failed to download document', 'error');\n      }\n    },\n\n    handleImageError(event) {\n      console.error('Failed to load image:', event.target.src);\n      // You could set a placeholder image here\n      event.target.style.display = 'none';\n\n      // Show error message\n      const errorDiv = document.createElement('div');\n      errorDiv.className = 'text-center text-muted p-3';\n      errorDiv.innerHTML = '<i class=\"fas fa-exclamation-triangle\"></i><br>Failed to load image';\n      event.target.parentNode.appendChild(errorDiv);\n    }\n  }\n};\n</script>\n\n<style scoped>\n@import './css/adminDashboard.css';\n\n/* Additional styles specific to AdminRequests */\n.admin-requests {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);\n}\n\n/* Ensure proper spacing for request statistics cards */\n.card.border-left-primary {\n  border-left: 4px solid #3b82f6 !important;\n}\n\n.card.border-left-warning {\n  border-left: 4px solid #f59e0b !important;\n}\n\n.card.border-left-success {\n  border-left: 4px solid #059669 !important;\n}\n\n.card.border-left-info {\n  border-left: 4px solid #06b6d4 !important;\n}\n\n/* Bootstrap utility classes for compatibility */\n.text-xs {\n  font-size: 0.75rem !important;\n}\n\n.text-gray-800 {\n  color: #1f2937 !important;\n}\n\n.text-gray-300 {\n  color: #d1d5db !important;\n}\n\n.text-muted {\n  color: #6c757d !important;\n}\n\n.fw-bold {\n  font-weight: 700 !important;\n}\n\n.g-0 {\n  --bs-gutter-x: 0;\n  --bs-gutter-y: 0;\n}\n\n.me-2 {\n  margin-right: 0.5rem !important;\n}\n\n/* Improve button spacing */\n.d-flex.gap-2 {\n  gap: 0.5rem !important;\n}\n\n/* Timeline Styles */\n.timeline {\n  position: relative;\n  padding-left: 2rem;\n}\n\n.timeline::before {\n  content: '';\n  position: absolute;\n  left: 1rem;\n  top: 0;\n  bottom: 0;\n  width: 2px;\n  background: #e3e6f0;\n}\n\n.timeline-item {\n  position: relative;\n  margin-bottom: 2rem;\n}\n\n.timeline-item:last-child {\n  margin-bottom: 0;\n}\n\n.timeline-item.timeline-item-last::after {\n  display: none;\n}\n\n.timeline-marker {\n  position: absolute;\n  left: -2rem;\n  top: 0.25rem;\n  width: 2rem;\n  height: 2rem;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 0.75rem;\n  z-index: 1;\n}\n\n.timeline-content {\n  background: #f8f9fc;\n  border-radius: 8px;\n  padding: 1rem;\n  border-left: 3px solid #e3e6f0;\n}\n\n.timeline-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.timeline-body p {\n  margin-bottom: 0.25rem;\n  font-size: 0.875rem;\n}\n\n.timeline-body p:last-child {\n  margin-bottom: 0;\n}\n\n/* Modal Styles */\n.modal-xl {\n  max-width: 1200px;\n}\n\n.modal-dialog-scrollable .modal-body {\n  max-height: 70vh;\n  overflow-y: auto;\n}\n\n/* Real-time status indicator styles */\n.real-time-status .badge {\n  font-size: 0.75rem;\n  padding: 0.375rem 0.75rem;\n  border-radius: 1rem;\n}\n\n.pulse {\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n/* Card View Styles */\n.requests-grid {\n  min-height: 400px;\n}\n\n.empty-state {\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 3rem 2rem;\n  margin: 2rem 0;\n}\n\n.empty-state-icon {\n  opacity: 0.5;\n}\n\n.request-card {\n  background: #ffffff;\n  border: 1px solid #e3e6f0;\n  border-radius: 12px;\n  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);\n  transition: all 0.3s ease;\n  overflow: hidden;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.request-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);\n  border-color: #5a5c69;\n}\n\n.request-card.selected {\n  border-color: #4e73df;\n  box-shadow: 0 0 0 2px rgba(78, 115, 223, 0.25);\n}\n\n.request-card-header {\n  padding: 1rem 1.25rem 0.5rem;\n  border-bottom: 1px solid #f1f1f1;\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\n}\n\n.request-card-body {\n  padding: 1.25rem;\n  flex-grow: 1;\n}\n\n.request-card-footer {\n  padding: 0.75rem 1.25rem 1.25rem;\n  background: #f8f9fa;\n  border-top: 1px solid #e3e6f0;\n}\n\n.client-avatar {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  color: white;\n  font-size: 1.2rem;\n}\n\n.client-info h6 {\n  color: #2c3e50;\n  font-weight: 600;\n}\n\n.document-type {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 0.75rem;\n  border-left: 4px solid #17a2b8;\n}\n\n.document-type .badge {\n  font-size: 0.875rem;\n  font-weight: 500;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n}\n\n.request-meta {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 0.75rem;\n}\n\n.meta-item {\n  text-align: center;\n}\n\n.meta-item small {\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.request-date {\n  padding-top: 0.75rem;\n  border-top: 1px solid #e9ecef;\n  margin-top: 0.75rem;\n}\n\n.request-actions .dropdown-toggle {\n  border: none;\n  background: transparent;\n  color: #6c757d;\n  padding: 0.25rem 0.5rem;\n  border-radius: 6px;\n}\n\n.request-actions .dropdown-toggle:hover {\n  background: #e9ecef;\n  color: #495057;\n}\n\n/* View Toggle Styles */\n.btn-check:checked + .btn-outline-primary {\n  background-color: #4e73df;\n  border-color: #4e73df;\n  color: white;\n}\n\n/* Badge Enhancements */\n.badge.bg-info-subtle {\n  background-color: #cff4fc !important;\n  color: #055160 !important;\n  border: 1px solid #b6effb;\n}\n\n/* Button Enhancements */\n.request-card-footer .btn {\n  border-radius: 6px;\n  font-weight: 500;\n  transition: all 0.2s ease;\n}\n\n.request-card-footer .btn:hover {\n  transform: translateY(-1px);\n}\n\n/* Modern Table Styles */\n.modern-table-container {\n  background: #ffffff;\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  overflow: visible;\n  border: 1px solid #e8ecef;\n}\n\n.modern-table-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.modern-table-header h5 {\n  color: white;\n  margin: 0;\n  font-weight: 600;\n}\n\n.table-actions .btn {\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  color: white;\n  backdrop-filter: blur(10px);\n}\n\n.table-actions .btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n  border-color: rgba(255, 255, 255, 0.3);\n  color: white;\n}\n\n.modern-table-empty {\n  padding: 4rem 2rem;\n  text-align: center;\n  background: #f8f9fa;\n}\n\n.empty-content {\n  max-width: 400px;\n  margin: 0 auto;\n}\n\n.empty-icon {\n  font-size: 4rem;\n  color: #6c757d;\n  margin-bottom: 1.5rem;\n  opacity: 0.5;\n}\n\n.empty-title {\n  color: #495057;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n}\n\n.empty-text {\n  color: #6c757d;\n  margin: 0;\n}\n\n/* Compact Table Styles */\n.compact-table-wrapper {\n  background: white;\n  border-radius: 12px;\n  overflow: visible;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  border: 1px solid #e8ecef;\n}\n\n.compact-table-header {\n  display: grid;\n  grid-template-columns: 40px 140px 1fr 140px 120px 100px 120px 120px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  font-weight: 600;\n  font-size: 0.875rem;\n  padding: 0.75rem 1rem;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  gap: 0.5rem;\n}\n\n.header-cell {\n  display: flex;\n  align-items: center;\n  padding: 0 0.5rem;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  font-size: 0.75rem;\n}\n\n.selection-header {\n  justify-content: center;\n}\n\n.compact-table-body {\n  background: white;\n  overflow: visible;\n}\n\n.compact-row {\n  display: grid;\n  grid-template-columns: 40px 140px 1fr 140px 120px 100px 120px 120px;\n  align-items: center;\n  padding: 0.5rem 1rem;\n  border-bottom: 1px solid #f1f3f4;\n  transition: all 0.15s ease;\n  position: relative;\n  min-height: 48px;\n  gap: 0.5rem;\n}\n\n.compact-row:hover {\n  background: #f8f9fa;\n  transform: translateX(2px);\n  box-shadow: 2px 0 0 #667eea;\n}\n\n.compact-row.selected {\n  background: #e3f2fd;\n  border-left: 3px solid #2196f3;\n}\n\n.compact-row:last-child {\n  border-bottom: none;\n}\n\n.row-cell {\n  display: flex;\n  align-items: center;\n  padding: 0 0.5rem;\n  font-size: 0.875rem;\n}\n\n/* Selection Cell */\n.selection-cell {\n  justify-content: center;\n}\n\n.selection-cell .form-check-input {\n  width: 18px;\n  height: 18px;\n  border-radius: 4px;\n  border: 2px solid #dee2e6;\n}\n\n.selection-cell .form-check-input:checked {\n  background-color: #667eea;\n  border-color: #667eea;\n}\n\n/* Request Number Cell */\n.request-number-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.request-number {\n  font-weight: 700;\n  color: #667eea;\n  font-size: 1rem;\n  letter-spacing: 0.5px;\n}\n\n.request-id {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-top: 2px;\n}\n\n/* Client Cell */\n.client-info {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.client-avatar-sm {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1rem;\n  flex-shrink: 0;\n}\n\n.client-details {\n  min-width: 0;\n  flex: 1;\n}\n\n.client-name {\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 0.95rem;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.client-email {\n  font-size: 0.8rem;\n  color: #6c757d;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-top: 2px;\n}\n\n/* Document Type Cell */\n.document-type-badge {\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.85rem;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);\n}\n\n/* Status Cell */\n.status-badge {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.85rem;\n  font-weight: 500;\n  text-transform: capitalize;\n}\n\n.status-indicator {\n  font-size: 0.6rem;\n  animation: pulse 2s infinite;\n}\n\n.status-success {\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);\n}\n\n.status-warning {\n  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);\n  color: #212529;\n  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);\n}\n\n.status-danger {\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);\n}\n\n.status-info {\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);\n}\n\n.status-secondary {\n  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);\n}\n\n/* Amount Cell */\n.amount-content {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.amount {\n  font-weight: 700;\n  color: #28a745;\n  font-size: 1.1rem;\n}\n\n.currency {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-top: 2px;\n}\n\n/* Date Cell */\n.date-content {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.date {\n  font-weight: 500;\n  color: #495057;\n  font-size: 0.9rem;\n}\n\n.time {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-top: 2px;\n}\n\n/* Actions Cell */\n.action-buttons {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  position: relative;\n}\n\n.action-btn {\n  width: 36px;\n  height: 36px;\n  border: none;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.875rem;\n  transition: all 0.2s ease;\n  cursor: pointer;\n}\n\n.view-btn {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.view-btn:hover {\n  background: #bbdefb;\n  transform: translateY(-2px);\n}\n\n.approve-btn {\n  background: #e8f5e8;\n  color: #2e7d32;\n}\n\n.approve-btn:hover {\n  background: #c8e6c9;\n  transform: translateY(-2px);\n}\n\n.reject-btn {\n  background: #ffebee;\n  color: #c62828;\n}\n\n.reject-btn:hover {\n  background: #ffcdd2;\n  transform: translateY(-2px);\n}\n\n.more-btn {\n  background: #f5f5f5;\n  color: #666;\n}\n\n.more-btn:hover {\n  background: #e0e0e0;\n  transform: translateY(-2px);\n}\n\n.more-btn::after {\n  display: none;\n}\n\n/* Dropdown positioning fixes */\n.modern-table {\n  overflow: visible;\n}\n\n.table-row {\n  overflow: visible;\n}\n\n.action-buttons .dropdown {\n  position: static;\n}\n\n.action-buttons .dropdown-menu {\n  position: absolute !important;\n  top: 100% !important;\n  right: 0 !important;\n  left: auto !important;\n  z-index: 1050 !important;\n  transform: none !important;\n  margin-top: 0.25rem;\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 8px;\n  background: white;\n  min-width: 160px;\n}\n\n.action-buttons .dropdown-menu.show {\n  display: block !important;\n}\n\n/* Ensure dropdown appears above other elements */\n.action-buttons .dropdown.show {\n  z-index: 1051;\n}\n\n/* Pagination Container */\n.pagination-container {\n  background: white;\n  border-radius: 0 0 16px 16px;\n  padding: 1.5rem 2rem;\n  border-top: 1px solid #f1f3f4;\n  margin-top: -1px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.pagination-container .pagination {\n  margin: 0;\n}\n\n.pagination-container .page-link {\n  border: 1px solid #e3e6f0;\n  color: #667eea;\n  padding: 0.5rem 0.75rem;\n  margin: 0 2px;\n  border-radius: 8px;\n  transition: all 0.2s ease;\n}\n\n.pagination-container .page-link:hover {\n  background: #667eea;\n  border-color: #667eea;\n  color: white;\n  transform: translateY(-1px);\n}\n\n.pagination-container .page-item.active .page-link {\n  background: #667eea;\n  border-color: #667eea;\n  color: white;\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\n}\n\n.pagination-container .page-item.disabled .page-link {\n  color: #6c757d;\n  background: #f8f9fa;\n  border-color: #e3e6f0;\n}\n\n/* Responsive improvements */\n@media (max-width: 768px) {\n  .d-flex.gap-2 {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .d-flex.gap-2 .btn {\n    margin-bottom: 0.5rem;\n  }\n\n  .modal-xl {\n    max-width: 95%;\n    margin: 1rem auto;\n  }\n\n  .timeline {\n    padding-left: 1.5rem;\n  }\n\n  .timeline-marker {\n    left: -1.5rem;\n    width: 1.5rem;\n    height: 1.5rem;\n    font-size: 0.625rem;\n  }\n\n  /* Compact table mobile adjustments */\n  .compact-table-header {\n    display: none;\n  }\n\n  .compact-row {\n    grid-template-columns: 1fr;\n    padding: 1rem;\n    gap: 0.75rem;\n    border-radius: 8px;\n    margin-bottom: 0.75rem;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  }\n\n  .row-cell {\n    min-height: auto;\n    flex-direction: column;\n    align-items: flex-start;\n    padding: 0.25rem 0;\n    border-bottom: 1px solid #f1f3f4;\n  }\n\n  .row-cell:last-child {\n    border-bottom: none;\n  }\n\n  .selection-cell {\n    flex-direction: row;\n    justify-content: flex-start;\n  }\n\n  .selection-cell {\n    flex-direction: row;\n    justify-content: flex-start;\n  }\n\n  .client-info {\n    width: 100%;\n  }\n\n  .client-details {\n    flex: 1;\n  }\n\n  .document-type-badge,\n  .status-badge {\n    align-self: flex-start;\n  }\n\n  .amount-content,\n  .date-content {\n    align-items: flex-start;\n  }\n\n  .action-buttons {\n    width: 100%;\n    justify-content: space-between;\n  }\n\n  .action-btn {\n    flex: 1;\n    max-width: 60px;\n  }\n\n  /* Mobile fixes for simple actions */\n  .actions-simple {\n    justify-content: center;\n    gap: 0.5rem;\n  }\n\n  .request-actions-simple {\n    justify-content: center;\n    gap: 0.5rem;\n  }\n\n  /* Card view mobile adjustments */\n  .request-card {\n    margin-bottom: 1rem;\n  }\n\n  .request-card-header,\n  .request-card-body,\n  .request-card-footer {\n    padding: 1rem;\n  }\n\n  .client-info .d-flex {\n    flex-direction: column;\n    text-align: center;\n    gap: 0.5rem;\n  }\n\n  .client-avatar {\n    align-self: center;\n  }\n\n  .request-meta .row {\n    text-align: center;\n  }\n\n  .request-card-footer .d-flex {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n\n  .request-card-footer .btn {\n    width: 100%;\n  }\n\n  /* View toggle mobile */\n  .btn-group {\n    width: 100%;\n  }\n\n  .btn-group .btn {\n    flex: 1;\n  }\n}\n\n@media (max-width: 576px) {\n  .empty-state {\n    padding: 2rem 1rem;\n  }\n\n  .empty-state-icon {\n    font-size: 3rem;\n  }\n\n  .request-card-body {\n    padding: 1rem;\n  }\n\n  .document-type,\n  .request-meta {\n    padding: 0.5rem;\n  }\n}\n\n/* Compact Table Additional Styles */\n.document-badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.25rem 0.75rem;\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\n  color: white;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 500;\n  box-shadow: 0 1px 4px rgba(23, 162, 184, 0.3);\n}\n\n.client-compact {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  width: 100%;\n}\n\n.client-avatar-tiny {\n  width: 28px;\n  height: 28px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 0.75rem;\n  flex-shrink: 0;\n}\n\n.client-info-compact {\n  flex: 1;\n  min-width: 0;\n}\n\n.client-name-compact {\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 0.8rem;\n  line-height: 1.2;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.client-email-compact {\n  font-size: 0.7rem;\n  color: #6c757d;\n  margin-top: 1px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.request-id-small {\n  font-size: 0.7rem;\n  color: #6c757d;\n  margin-top: 1px;\n}\n\n.status-compact {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.25rem 0.75rem;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 500;\n  text-transform: capitalize;\n}\n\n.status-compact i {\n  font-size: 0.5rem;\n}\n\n.amount-compact {\n  font-weight: 700;\n  color: #28a745;\n  font-size: 0.85rem;\n}\n\n.date-compact {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.date-main {\n  font-weight: 500;\n  color: #495057;\n  font-size: 0.8rem;\n  line-height: 1.2;\n}\n\n.time-small {\n  font-size: 0.7rem;\n  color: #6c757d;\n  margin-top: 1px;\n}\n\n.actions-compact {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n/* Fixed Actions Layout */\n.actions-compact-fixed {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  min-width: 120px;\n}\n\n.primary-actions {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n.dropdown-wrapper .dropdown {\n  position: static;\n}\n\n.compact-dropdown {\n  z-index: 1050 !important;\n}\n\n.compact-dropdown .dropdown-item {\n  padding: 0.5rem 1rem !important;\n  font-size: 0.875rem !important;\n  white-space: nowrap !important;\n  display: flex !important;\n  align-items: center !important;\n}\n\n.compact-dropdown .dropdown-item:hover {\n  background-color: #f8f9fa !important;\n}\n\n.compact-dropdown .dropdown-divider {\n  margin: 0.25rem 0 !important;\n}\n\n/* Ensure dropdown appears above table rows */\n.compact-row {\n  position: relative;\n  z-index: 1;\n}\n\n.compact-row:hover {\n  z-index: 2;\n}\n\n.dropdown-wrapper .dropdown.show {\n  z-index: 1051 !important;\n}\n\n.compact-dropdown.show {\n  display: block !important;\n}\n\n.action-btn-sm {\n  width: 28px;\n  height: 28px;\n  border: none;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.75rem;\n  transition: all 0.2s ease;\n  cursor: pointer;\n}\n\n.primary-btn-sm {\n  background: #007bff;\n  color: white;\n  border: 1px solid #007bff;\n}\n\n.primary-btn-sm:hover {\n  background: #0056b3;\n  border-color: #0056b3;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);\n}\n\n.view-btn-sm {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.view-btn-sm:hover {\n  background: #bbdefb;\n  transform: translateY(-1px);\n}\n\n.approve-btn-sm {\n  background: #e8f5e8;\n  color: #2e7d32;\n}\n\n.approve-btn-sm:hover {\n  background: #c8e6c9;\n  transform: translateY(-1px);\n}\n\n.reject-btn-sm {\n  background: #ffebee;\n  color: #c62828;\n}\n\n.reject-btn-sm:hover {\n  background: #ffcdd2;\n  transform: translateY(-1px);\n}\n\n.more-btn-sm {\n  background: #f5f5f5;\n  color: #666;\n}\n\n.more-btn-sm:hover {\n  background: #e0e0e0;\n  transform: translateY(-1px);\n}\n\n.more-btn-sm::after {\n  display: none;\n}\n\n/* Simple Actions Layout */\n.actions-simple {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  justify-content: center;\n}\n\n.request-actions-simple {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n/* Disabled button styles */\n.btn:disabled,\n.action-btn-sm:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  pointer-events: none;\n}\n\n.btn:disabled:hover,\n.action-btn-sm:disabled:hover {\n  transform: none;\n  box-shadow: none;\n}\n\n/* Status-based button styling */\n.btn-outline-success:disabled {\n  background-color: #f8f9fa;\n  border-color: #28a745;\n  color: #28a745;\n}\n\n.btn-outline-danger:disabled {\n  background-color: #f8f9fa;\n  border-color: #dc3545;\n  color: #dc3545;\n}\n\n/* Document Preview Styles */\n.document-preview-card {\n  border: 1px solid #e9ecef;\n  border-radius: 12px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  background: white;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.document-preview-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n}\n\n.document-preview-header {\n  padding: 0.75rem;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.document-type-badge {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.25rem 0.75rem;\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\n  color: white;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n.document-preview-content {\n  position: relative;\n  min-height: 200px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.image-preview {\n  position: relative;\n  width: 100%;\n  height: 200px;\n  overflow: hidden;\n  cursor: pointer;\n}\n\n.document-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.image-preview:hover .document-image {\n  transform: scale(1.05);\n}\n\n.image-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0,0,0,0.7);\n  color: white;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.image-preview:hover .image-overlay {\n  opacity: 1;\n}\n\n.pdf-preview, .file-preview {\n  text-align: center;\n  padding: 1rem;\n}\n\n.pdf-icon, .file-icon {\n  margin-bottom: 1rem;\n}\n\n.document-preview-footer {\n  padding: 0.75rem;\n  background: #f8f9fa;\n  border-top: 1px solid #e9ecef;\n  text-align: center;\n}\n\n.no-documents {\n  background: #f8f9fa;\n  border-radius: 8px;\n  margin: 1rem 0;\n}\n\n/* Image Modal Styles */\n.image-modal-container {\n  max-height: 70vh;\n  overflow: auto;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.modal-image {\n  max-width: 100%;\n  max-height: 70vh;\n  object-fit: contain;\n  border-radius: 8px;\n}\n\n.image-info {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.image-actions {\n  display: flex;\n  gap: 0.5rem;\n}\n\n/* Not Provided Styling */\n.not-provided {\n  font-style: italic;\n  color: #6c757d;\n}\n\n/* Client Details Styling */\n.client-details-grid {\n  font-size: 0.75rem;\n}\n\n.client-details-compact {\n  margin-top: 0.25rem;\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n  font-size: 0.7rem;\n}\n\n.detail-item {\n  display: inline-flex;\n  align-items: center;\n  color: #6c757d;\n  white-space: nowrap;\n}\n\n.detail-item i {\n  font-size: 0.65rem;\n  opacity: 0.7;\n}\n\n/* Loading Placeholder for Documents */\n.loading-placeholder {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #6c757d;\n  background: #f8f9fa;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n}\n\n.loading-placeholder:hover {\n  background: #e9ecef;\n}\n\n.loading-placeholder i {\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n}\n\n.loading-placeholder span {\n  font-size: 0.875rem;\n}\n\n/* Error Placeholder for Failed Documents */\n.error-placeholder {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #dc3545;\n  background: #f8d7da;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n}\n\n.error-placeholder:hover {\n  background: #f5c6cb;\n}\n\n.error-placeholder i {\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n}\n\n.error-placeholder span {\n  font-size: 0.875rem;\n}\n\n.error-placeholder small {\n  font-size: 0.75rem;\n  opacity: 0.8;\n  margin-top: 0.25rem;\n}\n\n/* Modal Loading and Error States */\n.modal-loading {\n  height: 400px;\n  background: rgba(0, 0, 0, 0.1);\n  color: #fff;\n  will-change: opacity;\n}\n\n.modal-error {\n  height: 400px;\n  background: rgba(220, 53, 69, 0.1);\n  color: #fff;\n}\n\n/* Optimized Modal Styles */\n.sticky-header {\n  position: sticky;\n  top: 0;\n  z-index: 1050;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n}\n\n.header-controls {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.modal-image {\n  max-width: 100%;\n  max-height: 80vh;\n  object-fit: contain;\n  will-change: transform;\n  transition: opacity 0.3s ease;\n}\n\n.loading-content, .error-text, .loading-text {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.loading-progress {\n  width: 200px;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 2px;\n  overflow: hidden;\n}\n\n.progress-bar {\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);\n  animation: loading-shimmer 1.5s infinite;\n}\n\n@keyframes loading-shimmer {\n  0% { transform: translateX(-100%); }\n  100% { transform: translateX(100%); }\n}\n\n/* Performance optimizations */\n.image-modal-container {\n  contain: layout style paint;\n  transform: translateZ(0); /* Force hardware acceleration */\n}\n\n.modal-content {\n  will-change: transform;\n}\n\n/* Responsive modal improvements */\n@media (max-width: 768px) {\n  .modal-xl {\n    max-width: 95vw;\n  }\n\n  .modal-image {\n    max-height: 70vh;\n  }\n\n  .header-controls .btn-sm {\n    padding: 0.25rem 0.5rem;\n  }\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .document-preview-card {\n    margin-bottom: 1rem;\n  }\n\n  .image-modal-container {\n    max-height: 60vh;\n  }\n\n  .modal-image {\n    max-height: 60vh;\n  }\n}\n\n/* Highlight section animation for notification focus */\n.highlight-section {\n  background: linear-gradient(90deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 123, 255, 0.05) 100%);\n  border-left: 4px solid #007bff;\n  padding: 1rem;\n  border-radius: 0.375rem;\n  transition: all 0.3s ease;\n  animation: highlightPulse 2s ease-in-out;\n}\n\n@keyframes highlightPulse {\n  0% {\n    background: rgba(0, 123, 255, 0.2);\n    transform: scale(1.02);\n  }\n  50% {\n    background: rgba(0, 123, 255, 0.1);\n    transform: scale(1.01);\n  }\n  100% {\n    background: rgba(0, 123, 255, 0.05);\n    transform: scale(1);\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;EAoBpBA,KAAK,EAAC;AAAqB;;;EAWRA,KAAK,EAAC,kDAAkD;EAACC,KAA0B,EAA1B;IAAA;EAAA;;;EAOjED,KAAK,EAAC;AAAsB;;;EAEbA,KAAK,EAAC,gDAAgD;EAACE,IAAI,EAAC;;;EAOhFF,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAQ;;EACZA,KAAK,EAAC;AAA6D;;EAEjEA,KAAK,EAAC;AAAiB;;;EACCA,KAAK,EAAC;;;EAM9BA,KAAK,EAAC;AAAiC;;EAErCA,KAAK,EAAC;AAAuB;;;EAE3BA,KAAK,EAAC;;;;EACNA,KAAK,EAAC;;;;;EA0BhBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAsC;;EAC1CA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAA2B;;EAO3CA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAsC;;EAC1CA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAA2B;;EAO3CA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAsC;;EAC1CA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAA2B;;EAO3CA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAA2B;;;EAU1BA,KAAK,EAAC;;;EAIvBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAe;;;EASrBA,KAAK,EAAC;AAAe;;EAQrBA,KAAK,EAAC;AAAe;;EAIrBA,KAAK,EAAC;AAAe;;EAIrBA,KAAK,EAAC;AAAsC;;EAC1CA,KAAK,EAAC;AAAoB;;;EAcCA,KAAK,EAAC;;;EACvCA,KAAK,EAAC;AAA6B;;EAClCA,KAAK,EAAC;AAAuB;;EAK9BA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAe;;;EAUrBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;;EAgB5BA,KAAK,EAAC;AAAwD;;EAC5DA,KAAK,EAAC;AAAiC;;EACrCA,KAAK,EAAC,WAAW;EAACE,IAAI,EAAC,OAAO;EAAC,YAAU,EAAC;;;EAY1CF,KAAK,EAAC;AAAiC;;EACpCA,KAAK,EAAC;AAAkB;;EAc7BA,KAAK,EAAC;AAAiC;;;EASdA,KAAK,EAAC;;;;EAEFA,KAAK,EAAC;;;EAS5BA,KAAK,EAAC;AAAS;;EAIhBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAkD;;EACtDA,KAAK,EAAC;AAAiC;;;EAOrCA,KAAK,EAAC;AAAgB;;EACnBA,KAAK,EAAC;AAAkB;;EAG7BA,KAAK,EAAC;AAAwB;;;EASlCA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAsC;;EAKzCA,KAAK,EAAC;AAAc;;EACjBA,KAAK,EAAC;AAAY;;EAKxBA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAAS;;;EACbA,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAW;;;EAErBA,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAW;;;EAErBA,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAW;;EAO3BA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAiC;;EAEpCA,KAAK,EAAC;AAAmD;;EAO9DA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAW;;EAOnBA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAW;;EAEdA,KAAK,EAAC;AAAsB;;EAOrCA,KAAK,EAAC;AAAc;;EAChBA,KAAK,EAAC;AAAY;;EAQxBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAQ;;;EAYjBA,KAAK,EAAC;AAAwB;;;EAGNA,KAAK,EAAC;;;EAW5BA,KAAK,EAAC;AAAuB;;EAElCA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAA8B;;;EAkBtCA,KAAK,EAAC;AAAoB;;EAMtBA,KAAK,EAAC;AAAyB;;;EAU/BA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAAoB;;EACvBA,KAAK,EAAC;AAAgB;;EACtBA,KAAK,EAAC;AAAkB;;EAK7BA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAgB;;EAIpBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAqB;;EAC3BA,KAAK,EAAC;AAAsB;;EAC5BA,KAAK,EAAC;AAAwB;;;EAC+BA,KAAK,EAAC;;;;EAG9BA,KAAK,EAAC;;;;EAGJA,KAAK,EAAC;;;EASnDA,KAAK,EAAC;AAAwB;;EAC3BA,KAAK,EAAC;AAAgB;;EAOzBA,KAAK,EAAC;AAAsB;;EAQ5BA,KAAK,EAAC;AAAsB;;EACzBA,KAAK,EAAC;AAAgB;;EAIzBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAc;;EACjBA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAY;;EAKvBA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAgB;;;;EAYCA,KAAK,EAAC;;;EACnC,YAAU,EAAC;AAAqB;;EAC/BA,KAAK,EAAC;AAAsD;;;;EAyBrBA,KAAK,EAAC,yBAAyB;EAACG,QAAQ,EAAC,IAAI;EAACF,KAA0C,EAA1C;IAAA;EAAA;;;EACxFD,KAAK,EAAC;AAA+C;;EACnDA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;EACnBA,KAAK,EAAC;AAAa;;EAMpBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAK;;EAETA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EACPA,KAAK,EAAC;AAAe;;EAG1BA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAGdA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAMZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAMZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAQpBA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;;;;;EAIAA,KAAK,EAAC;;;EAGlBA,KAAK,EAAC;AAAM;;;;EAIAA,KAAK,EAAC;;;EAGlBA,KAAK,EAAC;AAAM;;;;EAIAA,KAAK,EAAC;;;EAIpBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;;;;;EAIAA,KAAK,EAAC;;;EAOtBA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAQ;;EACZA,KAAK,EAAC;AAAM;;;;;;EAIAA,KAAK,EAAC;;;EAOtBA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;;;;;EAIAA,KAAK,EAAC;;;EAIpBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;;;;;EAIAA,KAAK,EAAC;;;EAS1BA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAW;;;;;EAEbA,KAAK,EAAC;AAAS;;EAEXA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAqB;;EAK7BA,KAAK,EAAC;AAA0B;;;;;EAuCYA,KAAK,EAAC;AAAa;;EAI3DA,KAAK,EAAC;AAAU;;EAChBA,KAAK,EAAC;AAAc;;EAChBA,KAAK,EAAC;AAAY;;;EAUjBA,KAAK,EAAC;AAAc;;EAIzBA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAc;;EAChBA,KAAK,EAAC;AAAY;;;EAU1BA,KAAK,EAAC;AAAyB;;EAC3BA,KAAK,EAAC;AAAY;;;EASvBA,KAAK,EAAC;;;EACXA,KAAK,EAAC;AAAkB;;EAGxBA,KAAK,EAAC;AAAiB;;;;;;;;EAe/BA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAM;;;EAOLI,KAAK,EAAC;AAAE;;;;EAOmCJ,KAAK,EAAC;;;EAOxDA,KAAK,EAAC;AAAQ;;;EAelBA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAMZA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAGdA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAGdA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAGdA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAA2B;;;EAMWA,KAAK,EAAC;;;EAKpDA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;;EAYdA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAYhBA,KAAK,EAAC;AAAQ;;;;;;;;;;EAiByBA,KAAK,EAAC;;;EAK7CA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;;EAUdA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EASdA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAWhBA,KAAK,EAAC;AAAQ;;;;;;;;;EAqBxBA,KAAK,EAAC;AAAM;;EAIVA,KAAK,EAAC;AAAW;;;EACkEA,KAAK,EAAC;;;EAUnFA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAiB;;EAInBA,KAAK,EAAC;AAAiB;;EAE3BA,KAAK,EAAC;AAAe;;EACrBA,KAAK,EAAC;AAAM;;;EAGmBA,KAAK,EAAC;;;;EAGRA,KAAK,EAAC;;;;EAOlCA,KAAK,EAAC;;;EAOnBA,KAAK,EAAC;AAAc;;;EAeyBA,KAAK,EAAC,yBAAyB;EAACG,QAAQ,EAAC,IAAI;EAACF,KAA0C,EAA1C;IAAA;EAAA;;;EAC/FD,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;EAOpBA,KAAK,EAAC;AAAY;;EAMhBA,KAAK,EAAC;AAAM;;EAEXA,KAAK,EAAC;AAAoB;;EAS7BA,KAAK,EAAC;AAAc;;;;;;;;;;;EAmB2BA,KAAK,EAAC,yBAAyB;EAACG,QAAQ,EAAC,IAAI;EAACF,KAA0C,EAA1C;IAAA;EAAA;;;EACjGD,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;EAOpBA,KAAK,EAAC;AAAY;;EAMhBA,KAAK,EAAC;AAAM;;EAEXA,KAAK,EAAC;AAAoB;;;EAOGA,KAAK,EAAC;;;EAKtCA,KAAK,EAAC;AAAc;;;;;;;;;;EAuB9BA,KAAK,EAAC;AAA6C;;EACjDA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA4B;;EACjCA,KAAK,EAAC;AAAa;;EAIlBA,KAAK,EAAC;AAAiB;;;EAkBzBA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAuB;;;EAwB0CA,KAAK,EAAC;AAA+B;;;EAkB9GA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAyD;;EAC7DA,KAAK,EAAC;AAAY;;EACfA,KAAK,EAAC;AAAoB;;EACzBA,KAAK,EAAC;AAAY;;EAKtBA,KAAK,EAAC;AAAe;;;;;6DA9tCtCK,mBAAA,CA6oCQ,OA7oCRC,UA6oCQ,GA5oCNC,YAAA,CAUEC,sBAAA;IATCC,QAAQ,EAAEC,KAAA,CAAAC,SAAS,EAAEC,UAAU;IAC/BC,gBAAgB,EAAEH,KAAA,CAAAG,gBAAgB;IAClCC,gBAAgB,EAAEJ,KAAA,CAAAI,gBAAgB;IAClCC,UAAU,EAAEC,QAAA,CAAAD,UAAU;IACtBE,eAAc,EAAED,QAAA,CAAAE,mBAAmB;IACnCC,oBAAoB,EAAEH,QAAA,CAAAI,wBAAwB;IAC9CC,YAAW,EAAEL,QAAA,CAAAM,gBAAgB;IAC7BC,kBAAkB,EAAEP,QAAA,CAAAQ,sBAAsB;IAC1CC,QAAM,EAAET,QAAA,CAAAU;4LAGXC,mBAAA,oBAAuB,EACvBtB,mBAAA,CAIO;IAHLL,KAAK,EAAA4B,eAAA,EAAC,gBAAgB;MAAAC,MAAA,GACHnB,KAAA,CAAAI,gBAAgB,IAAIJ,KAAA,CAAAoB;IAAQ;IAC9CC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEjB,QAAA,CAAAkB,kBAAA,IAAAlB,QAAA,CAAAkB,kBAAA,IAAAD,IAAA,CAAkB;2BAG5B5B,mBAAA,CAwnCQ,OAxnCR8B,UAwnCQ,GAvnCN5B,YAAA,CAME6B,uBAAA;IALCC,SAAS,EAAE3B,KAAA,CAAAI,gBAAgB;IAC3BC,UAAU,EAAEC,QAAA,CAAAD,UAAU;IACtBuB,YAAW,EAAEtB,QAAA,CAAAuB,gBAAgB;IAC7Bd,QAAM,EAAET,QAAA,CAAAU,YAAY;IACpBc,eAAc,EAAExB,QAAA,CAAAE;uGAGnBb,mBAAA,CA8mCS;IA9mCHL,KAAK,EAAA4B,eAAA,EAAC,cAAc;MAAA,qBAAgClB,KAAA,CAAAI;IAAgB;MACxEa,mBAAA,mBAAsB,EACXjB,KAAA,CAAA+B,OAAO,I,cAAlBC,mBAAA,CAIM,OAJNC,UAIM,EAAAX,MAAA,SAAAA,MAAA,QAHJ3B,mBAAA,CAEM;IAFDL,KAAK,EAAC,6BAA6B;IAACE,IAAI,EAAC;MAC5CG,mBAAA,CAA+C;IAAzCL,KAAK,EAAC;EAAiB,GAAC,YAAU,E,yCAK5C0C,mBAAA,CA4fQE,SAAA;IAAAC,GAAA;EAAA,IA7fRlB,mBAAA,kBAAqB,EACrBtB,mBAAA,CA4fQ,OA5fRyC,UA4fQ,GA3fNnB,mBAAA,mBAAsB,EACXjB,KAAA,CAAAqC,YAAY,I,cAAvBL,mBAAA,CAIM,OAJNM,UAIM,G,4BAHJ3C,mBAAA,CAAgD;IAA7CL,KAAK,EAAC;EAAkC,6B,iBAAK,GAChD,GAAAiD,gBAAA,CAAGvC,KAAA,CAAAqC,YAAY,IAAG,GAClB,iBAAA1C,mBAAA,CAA+F;IAAvF6C,IAAI,EAAC,QAAQ;IAAClD,KAAK,EAAC,WAAW;IAAE+B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAmB,MAAA,IAAEzC,KAAA,CAAAqC,YAAY;IAAO,YAAU,EAAC;6CAGhFpB,mBAAA,iBAAoB,EACpBtB,mBAAA,CAuCM,OAvCN+C,UAuCM,GAtCJ/C,mBAAA,CAqCM,OArCNgD,UAqCM,GApCJhD,mBAAA,CAmCM,OAnCNiD,UAmCM,GAlCJjD,mBAAA,CAOM,cANJA,mBAAA,CAKI,KALJkD,UAKI,GAJU7C,KAAA,CAAA8C,WAAW,I,cAAvBd,mBAAA,CAGO,QAHPe,WAGO,G,4BAFLpD,mBAAA,CAAuC;IAApCL,KAAK,EAAC;EAAyB,6B,iBAAK,iBACzB,GAAAiD,gBAAA,CAAGjC,QAAA,CAAA0C,UAAU,CAAChD,KAAA,CAAA8C,WAAW,kB,4CAI7CnD,mBAAA,CAyBM,OAzBNsD,WAyBM,GAxBJhC,mBAAA,gCAAmC,EACnCtB,mBAAA,CAMM,OANNuD,WAMM,GALJvD,mBAAA,CAIO;IAJDL,KAAK,EAAA4B,eAAA,EAAC,OAAO,EAASlB,KAAA,CAAAmD,kBAAkB;MACPnD,KAAA,CAAAmD,kBAAkB,I,cAAvDnB,mBAAA,CAA6D,KAA7DoB,WAA6D,M,cAC7DpB,mBAAA,CAAmC,KAAnCqB,WAAmC,I,iBAAA,GACnC,GAAAd,gBAAA,CAAGvC,KAAA,CAAAmD,kBAAkB,qC,oBAIzBxD,mBAAA,CAES;IAFDL,KAAK,EAAC,kCAAkC;IAAE+B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEjB,QAAA,CAAAgD,iBAAA,IAAAhD,QAAA,CAAAgD,iBAAA,IAAA/B,IAAA,CAAiB;IAAGgC,KAAK,EAAEvD,KAAA,CAAAmD,kBAAkB;MACpGxD,mBAAA,CAAwE;IAArEL,KAAK,EAAA4B,eAAA,EAAC,KAAK,EAASlB,KAAA,CAAAmD,kBAAkB;yDAE3CxD,mBAAA,CAGS;IAHDL,KAAK,EAAC,gCAAgC;IAAE+B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAmB,MAAA,IAAEzC,KAAA,CAAAwD,WAAW,IAAIxD,KAAA,CAAAwD,WAAW;kCAC/E7D,mBAAA,CAAkC;IAA/BL,KAAK,EAAC;EAAoB,6B,iBAAK,GAClC,GAAAiD,gBAAA,CAAGvC,KAAA,CAAAwD,WAAW,sBAAqB,WACrC,gB,GACAvC,mBAAA,wNAGa,EACbtB,mBAAA,CAGS;IAHDL,KAAK,EAAC,wBAAwB;IAAE+B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEjB,QAAA,CAAAmD,mBAAA,IAAAnD,QAAA,CAAAmD,mBAAA,IAAAlC,IAAA,CAAmB;IAAGmC,QAAQ,EAAE1D,KAAA,CAAA+B;MAC7EpC,mBAAA,CAAoE;IAAjEL,KAAK,EAAA4B,eAAA,EAAC,sBAAsB;MAAA,WAAsBlB,KAAA,CAAA+B;IAAO;wEAAQ,WAEtE,G,uCAMRd,mBAAA,wBAA2B,EAC3BtB,mBAAA,CAqDM,OArDNgE,WAqDM,GApDJhE,mBAAA,CAYM,OAZNiE,WAYM,GAXJjE,mBAAA,CAUM,OAVNkE,WAUM,GATJlE,mBAAA,CAQM,OARNmE,WAQM,GAPJnE,mBAAA,CAMM,OANNoE,WAMM,GALJpE,mBAAA,CAGM,OAHNqE,WAGM,G,4BAFJrE,mBAAA,CAAkF;IAA7EL,KAAK,EAAC;EAAkD,GAAC,gBAAc,sBAC5EK,mBAAA,CAA0E,OAA1EsE,WAA0E,EAAA1B,gBAAA,CAAhCvC,KAAA,CAAAkE,YAAY,CAACC,KAAK,sB,+BAE9DxE,mBAAA,CAAqD;IAAlDL,KAAK,EAAC;EAAuC,4B,SAKxDK,mBAAA,CAYM,OAZNyE,WAYM,GAXJzE,mBAAA,CAUM,OAVN0E,WAUM,GATJ1E,mBAAA,CAQM,OARN2E,WAQM,GAPJ3E,mBAAA,CAMM,OANN4E,WAMM,GALJ5E,mBAAA,CAGM,OAHN6E,WAGM,G,4BAFJ7E,mBAAA,CAA2E;IAAtEL,KAAK,EAAC;EAAkD,GAAC,SAAO,sBACrEK,mBAAA,CAA4E,OAA5E8E,WAA4E,EAAAlC,gBAAA,CAAlCvC,KAAA,CAAAkE,YAAY,CAACQ,OAAO,sB,+BAEhE/E,mBAAA,CAAkD;IAA/CL,KAAK,EAAC;EAAoC,4B,SAKrDK,mBAAA,CAYM,OAZNgF,WAYM,GAXJhF,mBAAA,CAUM,OAVNiF,WAUM,GATJjF,mBAAA,CAQM,OARNkF,WAQM,GAPJlF,mBAAA,CAMM,OANNmF,WAMM,GALJnF,mBAAA,CAGM,OAHNoF,WAGM,G,4BAFJpF,mBAAA,CAA6E;IAAxEL,KAAK,EAAC;EAAkD,GAAC,WAAS,sBACvEK,mBAAA,CAA8E,OAA9EqF,WAA8E,EAAAzC,gBAAA,CAApCvC,KAAA,CAAAkE,YAAY,CAACe,SAAS,sB,+BAElEtF,mBAAA,CAAyD;IAAtDL,KAAK,EAAC;EAA2C,4B,SAK5DK,mBAAA,CAYM,OAZNuF,WAYM,GAXJvF,mBAAA,CAUM,OAVNwF,WAUM,GATJxF,mBAAA,CAQM,OARNyF,WAQM,GAPJzF,mBAAA,CAMM,OANN0F,WAMM,GALJ1F,mBAAA,CAGM,OAHN2F,WAGM,G,4BAFJ3F,mBAAA,CAAyE;IAApEL,KAAK,EAAC;EAA+C,GAAC,UAAQ,sBACnEK,mBAAA,CAA6E,OAA7E4F,WAA6E,EAAAhD,gBAAA,CAAnCvC,KAAA,CAAAkE,YAAY,CAACsB,QAAQ,sB,+BAEjE7F,mBAAA,CAAsD;IAAnDL,KAAK,EAAC;EAAwC,4B,WAO3D2B,mBAAA,mBAAsB,EACXjB,KAAA,CAAAwD,WAAW,I,cAAtBxB,mBAAA,CAqDM,OArDNyD,WAqDM,G,4BApDJ9F,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAkB,IAC3BK,mBAAA,CAAyD;IAArDL,KAAK,EAAC;EAA0B,GAAC,iBAAe,E,sBAEtDK,mBAAA,CAgDM,OAhDN+F,WAgDM,GA/CJ/F,mBAAA,CA8CM,OA9CNgG,WA8CM,GA7CJhG,mBAAA,CASM,OATNiG,WASM,G,4BARJjG,mBAAA,CAAwC;IAAjCL,KAAK,EAAC;EAAY,GAAC,QAAM,sB,gBAChCK,mBAAA,CAMC;IALC6C,IAAI,EAAC,MAAM;IACXlD,KAAK,EAAC,cAAc;+DACXU,KAAA,CAAA6F,OAAO,CAACC,MAAM,GAAArD,MAAA;IACvBsD,WAAW,EAAC,0CAA0C;IACrDC,OAAK,EAAA1E,MAAA,QAAAA,MAAA,MAAA2E,SAAA,KAAA1E,IAAA,KAAQjB,QAAA,CAAA4F,YAAA,IAAA5F,QAAA,CAAA4F,YAAA,IAAA3E,IAAA,CAAY;iEAFjBvB,KAAA,CAAA6F,OAAO,CAACC,MAAM,E,KAK3BnG,mBAAA,CAQM,OARNwG,WAQM,G,4BAPJxG,mBAAA,CAAwC;IAAjCL,KAAK,EAAC;EAAY,GAAC,QAAM,sB,gBAChCK,mBAAA,CAKS;IALDL,KAAK,EAAC,aAAa;+DAAUU,KAAA,CAAA6F,OAAO,CAACO,MAAM,GAAA3D,MAAA;kCACjD9C,mBAAA,CAAsC;IAA9BD,KAAK,EAAC;EAAE,GAAC,cAAY,uB,kBAC7BsC,mBAAA,CAESE,SAAA,QAAAmE,WAAA,CAFgBrG,KAAA,CAAAsG,aAAa,EAAvBF,MAAM;yBAArBpE,mBAAA,CAES;MAFgCG,GAAG,EAAEiE,MAAM,CAACG,EAAE;MAAG7G,KAAK,EAAE0G,MAAM,CAACI;wBACnElG,QAAA,CAAAmG,YAAY,CAACL,MAAM,CAACI,WAAW,yBAAAE,WAAA;2EAHD1G,KAAA,CAAA6F,OAAO,CAACO,MAAM,E,KAOrDzG,mBAAA,CAOM,OAPNgH,WAOM,G,4BANJhH,mBAAA,CAA+C;IAAxCL,KAAK,EAAC;EAAY,GAAC,eAAa,sB,gBACvCK,mBAAA,CAIS;IAJDL,KAAK,EAAC,aAAa;+DAAUU,KAAA,CAAA6F,OAAO,CAACe,aAAa,GAAAnE,MAAA;kCACxD9C,mBAAA,CAAmC;IAA3BD,KAAK,EAAC;EAAE,GAAC,WAAS,qBAC1BC,mBAAA,CAA8D;IAAtDD,KAAK,EAAC;EAAoB,GAAC,oBAAkB,qBACrDC,mBAAA,CAAsC;IAA9BD,KAAK,EAAC;EAAQ,GAAC,QAAM,oB,2CAHMM,KAAA,CAAA6F,OAAO,CAACe,aAAa,E,KAM5DjH,mBAAA,CAGM,OAHNkH,WAGM,G,4BAFJlH,mBAAA,CAA2C;IAApCL,KAAK,EAAC;EAAY,GAAC,WAAS,sB,gBACnCK,mBAAA,CAAoE;IAA7D6C,IAAI,EAAC,MAAM;IAAClD,KAAK,EAAC,cAAc;+DAAUU,KAAA,CAAA6F,OAAO,CAACiB,SAAS,GAAArE,MAAA;iDAAjBzC,KAAA,CAAA6F,OAAO,CAACiB,SAAS,E,KAEpEnH,mBAAA,CAGM,OAHNoH,WAGM,G,4BAFJpH,mBAAA,CAAyC;IAAlCL,KAAK,EAAC;EAAY,GAAC,SAAO,sB,gBACjCK,mBAAA,CAAkE;IAA3D6C,IAAI,EAAC,MAAM;IAAClD,KAAK,EAAC,cAAc;iEAAUU,KAAA,CAAA6F,OAAO,CAACmB,OAAO,GAAAvE,MAAA;iDAAfzC,KAAA,CAAA6F,OAAO,CAACmB,OAAO,E,KAElErH,mBAAA,CASM,OATNsH,WASM,GARJtH,mBAAA,CAOM,OAPNuH,WAOM,GANJvH,mBAAA,CAES;IAFDL,KAAK,EAAC,wBAAwB;IAAE+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAA4F,YAAA,IAAA5F,QAAA,CAAA4F,YAAA,IAAA3E,IAAA,CAAY;kCACzD5B,mBAAA,CAA6B;IAA1BL,KAAK,EAAC;EAAe,2B,IAE1BK,mBAAA,CAES;IAFDL,KAAK,EAAC,kCAAkC;IAAE+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAA6G,YAAA,IAAA7G,QAAA,CAAA6G,YAAA,IAAA5F,IAAA,CAAY;kCACnE5B,mBAAA,CAA4B;IAAzBL,KAAK,EAAC;EAAc,2B,mDAQnC2B,mBAAA,wBAA2B,EAChBjB,KAAA,CAAAoH,gBAAgB,CAACC,MAAM,Q,cAAlCrF,mBAAA,CAiCM,OAjCNsF,WAiCM,GAhCJ3H,mBAAA,CAKM,OALN4H,WAKM,GAJJ5H,mBAAA,CAGK,MAHL6H,WAGK,G,4BAFH7H,mBAAA,CAAiC;IAA9BL,KAAK,EAAC;EAAmB,6B,iBAAK,iBACnB,GAAAiD,gBAAA,CAAGvC,KAAA,CAAAoH,gBAAgB,CAACC,MAAM,IAAG,aAC7C,gB,KAEF1H,mBAAA,CAyBM,OAzBN8H,WAyBM,GAxBJ9H,mBAAA,CAuBM,OAvBN+H,WAuBM,GAtBJ/H,mBAAA,CAQM,OARNgI,WAQM,G,4BAPJhI,mBAAA,CAAwC;IAAjCL,KAAK,EAAC;EAAY,GAAC,QAAM,sB,gBAChCK,mBAAA,CAKS;IALDL,KAAK,EAAC,aAAa;iEAAUU,KAAA,CAAA4H,UAAU,GAAAnF,MAAA;kCAC7C9C,mBAAA,CAAuC;IAA/BD,KAAK,EAAC;EAAE,GAAC,eAAa,uB,kBAC9BsC,mBAAA,CAESE,SAAA,QAAAmE,WAAA,CAFgBrG,KAAA,CAAAsG,aAAa,EAAvBF,MAAM;yBAArBpE,mBAAA,CAES;MAFgCG,GAAG,EAAEiE,MAAM,CAACG,EAAE;MAAG7G,KAAK,EAAE0G,MAAM,CAACG;OAAI,aAChE,GAAAhE,gBAAA,CAAGjC,QAAA,CAAAmG,YAAY,CAACL,MAAM,CAACI,WAAW,yBAAAqB,WAAA;2EAHX7H,KAAA,CAAA4H,UAAU,E,KAQjDjI,mBAAA,CAWM,OAXNmI,WAWM,GAVJnI,mBAAA,CASM,OATNoI,WASM,GARJpI,mBAAA,CAGS;IAHDL,KAAK,EAAC,iBAAiB;IAAE+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAA0H,iBAAA,IAAA1H,QAAA,CAAA0H,iBAAA,IAAAzG,IAAA,CAAiB;IAAGmC,QAAQ,GAAG1D,KAAA,CAAA4H;kCACrEjI,mBAAA,CAAgC;IAA7BL,KAAK,EAAC;EAAkB,4B,iBAAK,SAElC,E,gCACAK,mBAAA,CAGS;IAHDL,KAAK,EAAC,2BAA2B;IAAE+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAmB,MAAA,IAAEzC,KAAA,CAAAoH,gBAAgB;kCAChEzH,mBAAA,CAAiC;IAA9BL,KAAK,EAAC;EAAmB,4B,iBAAK,UAEnC,E,mDAOV2B,mBAAA,iBAAoB,EACpBtB,mBAAA,CAmCM,OAnCNsI,WAmCM,GAlCJtI,mBAAA,CA0BM,OA1BNuI,WA0BM,GAzBJvI,mBAAA,CAUM,OAVNwI,WAUM,G,gBATJxI,mBAAA,CAAuH;IAAhH6C,IAAI,EAAC,OAAO;IAAClD,KAAK,EAAC,WAAW;IAAC8I,IAAI,EAAC,UAAU;IAAC7B,EAAE,EAAC,UAAU;iEAAUvG,KAAA,CAAAqI,QAAQ,GAAA5F,MAAA;IAAE/C,KAAK,EAAC,MAAM;IAAC4I,YAAY,EAAC;kDAApCtI,KAAA,CAAAqI,QAAQ,E,+BACrF1I,mBAAA,CAEQ;IAFDL,KAAK,EAAC,gCAAgC;IAACiJ,GAAG,EAAC;MAChD5I,mBAAA,CAAoC;IAAjCL,KAAK,EAAC;EAAsB,I,iBAAK,QACtC,E,sCAEAK,mBAAA,CAAyH;IAAlH6C,IAAI,EAAC,OAAO;IAAClD,KAAK,EAAC,WAAW;IAAC8I,IAAI,EAAC,UAAU;IAAC7B,EAAE,EAAC,WAAW;iEAAUvG,KAAA,CAAAqI,QAAQ,GAAA5F,MAAA;IAAE/C,KAAK,EAAC,OAAO;IAAC4I,YAAY,EAAC;kDAArCtI,KAAA,CAAAqI,QAAQ,E,+BACtF1I,mBAAA,CAEQ;IAFDL,KAAK,EAAC,gCAAgC;IAACiJ,GAAG,EAAC;MAChD5I,mBAAA,CAAiC;IAA9BL,KAAK,EAAC;EAAmB,I,iBAAK,QACnC,E,wBAGFK,mBAAA,CAYM,OAZN6I,WAYM,GAXJ7I,mBAAA,CAIO,QAJP8I,WAIO,EAJwB,WACrB,GAAAlG,gBAAA,EAAKvC,KAAA,CAAA0I,UAAU,CAACC,WAAW,QAAQ3I,KAAA,CAAA0I,UAAU,CAACE,YAAY,QAAQ,KAC1E,GAAArG,gBAAA,CAAGsG,IAAI,CAACC,GAAG,CAAC9I,KAAA,CAAA0I,UAAU,CAACC,WAAW,GAAG3I,KAAA,CAAA0I,UAAU,CAACE,YAAY,EAAE5I,KAAA,CAAA0I,UAAU,CAACK,UAAU,KAAI,MACpF,GAAAxG,gBAAA,CAAGvC,KAAA,CAAA0I,UAAU,CAACK,UAAU,IAAG,YAChC,iB,gBACApJ,mBAAA,CAKS;IALDL,KAAK,EAAC,4BAA4B;IAACC,KAAoB,EAApB;MAAA;IAAA,CAAoB;iEAAUS,KAAA,CAAA0I,UAAU,CAACE,YAAY,GAAAnG,MAAA;IAAGuG,QAAM,EAAA1H,MAAA,SAAAA,MAAA,OAAAmB,MAAA,IAAEnC,QAAA,CAAA2I,kBAAkB,CAACjJ,KAAA,CAAA0I,UAAU,CAACE,YAAY;kCACnJjJ,mBAAA,CAAuC;IAA/BD,KAAK,EAAC;EAAI,GAAC,aAAW,qBAC9BC,mBAAA,CAAuC;IAA/BD,KAAK,EAAC;EAAI,GAAC,aAAW,qBAC9BC,mBAAA,CAAuC;IAA/BD,KAAK,EAAC;EAAI,GAAC,aAAW,qBAC9BC,mBAAA,CAAyC;IAAjCD,KAAK,EAAC;EAAK,GAAC,cAAY,oB,2DAJuCM,KAAA,CAAA0I,UAAU,CAACE,YAAY,E,OASpGjJ,mBAAA,CAKM,OALNuJ,WAKM,GAJ8ElJ,KAAA,CAAAmJ,QAAQ,CAAC9B,MAAM,Q,cAAjGrF,mBAAA,CAGS;;IAHD1C,KAAK,EAAC,kCAAkC;IAAE+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAA8I,iBAAA,IAAA9I,QAAA,CAAA8I,iBAAA,IAAA7H,IAAA,CAAiB;kCACxE5B,mBAAA,CAAwC;IAArCL,KAAK,EAAC;EAA0B,6B,iBAAK,GACxC,GAAAiD,gBAAA,CAAGvC,KAAA,CAAAoH,gBAAgB,CAACC,MAAM,KAAKrH,KAAA,CAAAmJ,QAAQ,CAAC9B,MAAM,iD,4CAKpDpG,mBAAA,eAAkB,EACPjB,KAAA,CAAAqI,QAAQ,e,cAAnBrG,mBAAA,CAuHM,OAvHNqH,WAuHM,GAtHJpI,mBAAA,iBAAoB,EACTjB,KAAA,CAAAmJ,QAAQ,CAAC9B,MAAM,U,cAA1BrF,mBAAA,CAMM,OANNsH,WAMM,EAAAhI,MAAA,SAAAA,MAAA,QALJ3B,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAuB,IAChCK,mBAAA,CAA6C;IAA1CL,KAAK,EAAC;EAA+B,G,qBAE1CK,mBAAA,CAA2D;IAAvDL,KAAK,EAAC;EAAiB,GAAC,4BAA0B,qBACtDK,mBAAA,CAAuF;IAApFL,KAAK,EAAC;EAAY,GAAC,+DAA6D,oB,qBAIrF0C,mBAAA,CA2GME,SAAA;IAAAC,GAAA;EAAA,IA5GNlB,mBAAA,mBAAsB,EACtBtB,mBAAA,CA2GM,OA3GN4J,WA2GM,I,kBA1GJvH,mBAAA,CAyGME,SAAA,QAAAmE,WAAA,CAzGiBrG,KAAA,CAAAmJ,QAAQ,EAAnBK,OAAO;yBAAnBxH,mBAAA,CAyGM;MAzG4BG,GAAG,EAAEqH,OAAO,CAACjD,EAAE;MAAEjH,KAAK,EAAC;QACvDK,mBAAA,CAuGM;MAvGDL,KAAK,EAAA4B,eAAA,EAAC,cAAc;QAAA,YAAuBlB,KAAA,CAAAoH,gBAAgB,CAACqC,QAAQ,CAACD,OAAO,CAACjD,EAAE;MAAA;QAClFtF,mBAAA,iBAAoB,EACpBtB,mBAAA,CAmBM,OAnBN+J,WAmBM,GAlBJ/J,mBAAA,CAiBM,OAjBNgK,WAiBM,GAhBJhK,mBAAA,CAUM,OAVNiK,WAUM,GATJjK,mBAAA,CAKC;MAJC6C,IAAI,EAAC,UAAU;MACflD,KAAK,EAAC,kBAAkB;MACvBuK,OAAO,EAAE7J,KAAA,CAAAoH,gBAAgB,CAACqC,QAAQ,CAACD,OAAO,CAACjD,EAAE;MAC7CyC,QAAM,EAAAvG,MAAA,IAAEnC,QAAA,CAAAwJ,sBAAsB,CAACN,OAAO,CAACjD,EAAE;2DAE5C5G,mBAAA,CAEM,OAFNoK,WAEM,GADJpK,mBAAA,CAAkE,QAAlEqK,WAAkE,EAAAzH,gBAAA,CAAhCiH,OAAO,CAACS,cAAc,iB,KAG5DtK,mBAAA,CAIM,OAJNuK,WAIM,GAHJvK,mBAAA,CAES;MAFDL,KAAK,EAAC,wBAAwB;MAAE+B,OAAK,EAAAoB,MAAA,IAAEnC,QAAA,CAAA6J,kBAAkB,CAACX,OAAO,CAACjD,EAAE;MAAGhD,KAAK,EAAC;yCACnF5D,mBAAA,CAAgC;MAA7BL,KAAK,EAAC;IAAkB,4B,iBAAK,SAClC,E,wCAKN2B,mBAAA,eAAkB,EAClBtB,mBAAA,CAqEM,OArENyK,WAqEM,GApEJnJ,mBAAA,iBAAoB,EACpBtB,mBAAA,CA4BM,OA5BN0K,WA4BM,GA3BJ1K,mBAAA,CAQM,OARN2K,WAQM,G,4BAPJ3K,mBAAA,CAEM;MAFDL,KAAK,EAAC;IAAe,IACxBK,mBAAA,CAAqD;MAAlDL,KAAK,EAAC;IAAuC,G,sBAElDK,mBAAA,CAGM,cAFJA,mBAAA,CAAuD,MAAvD4K,WAAuD,EAAAhI,gBAAA,CAA3BiH,OAAO,CAACgB,WAAW,kBAC/C7K,mBAAA,CAA4D,SAA5D8K,WAA4D,EAAAlI,gBAAA,CAA/BiH,OAAO,CAACkB,YAAY,iB,KAIrDzJ,mBAAA,+BAAkC,EAClCtB,mBAAA,CAeM,OAfNgL,WAeM,GAdJhL,mBAAA,CAaM,OAbNiL,WAaM,GAZqBtK,QAAA,CAAAuK,kBAAkB,CAACrB,OAAO,CAACsB,sBAAsB,K,cAA1E9I,mBAAA,CAGM,OAHN+I,WAGM,G,4BAFJpL,mBAAA,CAAsD;MAA/CL,KAAK,EAAC;IAAoB,GAAC,cAAY,sBAC9CK,mBAAA,CAAyF,SAAzFqL,WAAyF,EAAAzI,gBAAA,CAA7DjC,QAAA,CAAAuK,kBAAkB,CAACrB,OAAO,CAACsB,sBAAsB,kB,wCAEtDtB,OAAO,CAACyB,kBAAkB,I,cAAnDjJ,mBAAA,CAGM,OAHNkJ,WAGM,G,4BAFJvL,mBAAA,CAAqD;MAA9CL,KAAK,EAAC;IAAoB,GAAC,aAAW,sBAC7CK,mBAAA,CAAiE,SAAjEwL,WAAiE,EAAA5I,gBAAA,CAArCiH,OAAO,CAACyB,kBAAkB,iB,wCAE9B3K,QAAA,CAAA8K,mBAAmB,CAAC5B,OAAO,K,cAArDxH,mBAAA,CAGM,OAHNqJ,WAGM,G,4BAFJ1L,mBAAA,CAA4D;MAArDL,KAAK,EAAC;IAAoB,GAAC,oBAAkB,sBACpDK,mBAAA,CAAmE,SAAnE2L,WAAmE,EAAA/I,gBAAA,CAAvCjC,QAAA,CAAA8K,mBAAmB,CAAC5B,OAAO,kB,8CAM/DvI,mBAAA,mBAAsB,EACtBtB,mBAAA,CAOM,OAPN4L,WAOM,GANJ5L,mBAAA,CAKM,OALN6L,WAKM,G,4BAJJ7L,mBAAA,CAAyC;MAAtCL,KAAK,EAAC;IAA2B,6BACpCK,mBAAA,CAEO,QAFP8L,WAEO,EAAAlJ,gBAAA,CADFiH,OAAO,CAAC5C,aAAa,iB,KAK9B3F,mBAAA,uBAA0B,EAC1BtB,mBAAA,CAiBM,OAjBN+L,WAiBM,GAhBJ/L,mBAAA,CAeM,OAfNgM,WAeM,GAdJhM,mBAAA,CAOM,OAPNiM,WAOM,GANJjM,mBAAA,CAKM,OALNkM,WAKM,G,4BAJJlM,mBAAA,CAAgD;MAAzCL,KAAK,EAAC;IAAoB,GAAC,QAAM,sBACxCK,mBAAA,CAEO;MAFDL,KAAK,EAAA4B,eAAA,EAAC,OAAO,QAAeZ,QAAA,CAAAwL,cAAc,CAACtC,OAAO,CAAChD,WAAW;wBAC/DlG,QAAA,CAAAmG,YAAY,CAAC+C,OAAO,CAAChD,WAAW,yB,KAIzC7G,mBAAA,CAKM,OALNoM,YAKM,GAJJpM,mBAAA,CAGM,OAHNqM,YAGM,G,4BAFJrM,mBAAA,CAAgD;MAAzCL,KAAK,EAAC;IAAoB,GAAC,QAAM,sBACxCK,mBAAA,CAAiF,QAAjFsM,YAAiF,EAAA1J,gBAAA,CAA3CjC,QAAA,CAAA4L,cAAc,CAAC1C,OAAO,CAAC2C,SAAS,kB,SAM9ElL,mBAAA,UAAa,EACbtB,mBAAA,CAKM,OALNyM,YAKM,GAJJzM,mBAAA,CAGQ,SAHR0M,YAGQ,G,4BAFN1M,mBAAA,CAAwC;MAArCL,KAAK,EAAC;IAA0B,6B,iBAAK,aAC9B,GAAAiD,gBAAA,CAAGjC,QAAA,CAAAgM,UAAU,CAAC9C,OAAO,CAAC+C,YAAY,kB,OAKlDtL,mBAAA,iBAAoB,EACpBtB,mBAAA,CAMM,OANN6M,YAMM,GALJ7M,mBAAA,CAIM,OAJN8M,YAIM,GAHJ9M,mBAAA,CAES;MAFDL,KAAK,EAAC,wBAAwB;MAAE+B,OAAK,EAAAoB,MAAA,IAAEnC,QAAA,CAAA6J,kBAAkB,CAACX,OAAO,CAACjD,EAAE;yCAC1E5G,mBAAA,CAAgC;MAA7BL,KAAK,EAAC;IAAkB,4B,iBAAK,iBAClC,E;yGASZ0C,mBAAA,CA0HME,SAAA;IAAAC,GAAA;EAAA,IA3HNlB,mBAAA,gBAAmB,EACnBtB,mBAAA,CA0HM,OA1HN+M,YA0HM,GAxHJzL,mBAAA,iBAAoB,EACTjB,KAAA,CAAAmJ,QAAQ,CAAC9B,MAAM,U,cAA1BrF,mBAAA,CAQM,OARN2K,YAQM,EAAArL,MAAA,SAAAA,MAAA,Q,kXAGNU,mBAAA,CA2GME,SAAA;IAAAC,GAAA;EAAA,IA5GNlB,mBAAA,0BAA6B,EAC7BtB,mBAAA,CA2GM,OA3GNiN,YA2GM,GA1GJ3L,mBAAA,kBAAqB,EACrBtB,mBAAA,CAgBM,OAhBNkN,YAgBM,GAfJlN,mBAAA,CAOM,OAPNmN,YAOM,GANJnN,mBAAA,CAKC;IAJC6C,IAAI,EAAC,UAAU;IACflD,KAAK,EAAC,kBAAkB;IACvBuK,OAAO,EAAE7J,KAAA,CAAAoH,gBAAgB,CAACC,MAAM,KAAKrH,KAAA,CAAAmJ,QAAQ,CAAC9B,MAAM,IAAIrH,KAAA,CAAAmJ,QAAQ,CAAC9B,MAAM;IACvE2B,QAAM,EAAA1H,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAA8I,iBAAA,IAAA9I,QAAA,CAAA8I,iBAAA,IAAA7H,IAAA,CAAiB;4fAYhCN,mBAAA,gBAAmB,EACnBtB,mBAAA,CAqFM,OArFNoN,YAqFM,I,kBApFJ/K,mBAAA,CAmFME,SAAA,QAAAmE,WAAA,CAnFiBrG,KAAA,CAAAmJ,QAAQ,EAAnBK,OAAO;yBAAnBxH,mBAAA,CAmFM;MAnF4BG,GAAG,EAAEqH,OAAO,CAACjD,EAAE;MAC5CjH,KAAK,EAAA4B,eAAA,EAAC,aAAa;QAAA,YACGlB,KAAA,CAAAoH,gBAAgB,CAACqC,QAAQ,CAACD,OAAO,CAACjD,EAAE;MAAA;QAE7DtF,mBAAA,eAAkB,EAClBtB,mBAAA,CAOM,OAPNqN,YAOM,GANJrN,mBAAA,CAKC;MAJC6C,IAAI,EAAC,UAAU;MACflD,KAAK,EAAC,kBAAkB;MACvBuK,OAAO,EAAE7J,KAAA,CAAAoH,gBAAgB,CAACqC,QAAQ,CAACD,OAAO,CAACjD,EAAE;MAC7CyC,QAAM,EAAAvG,MAAA,IAAEnC,QAAA,CAAAwJ,sBAAsB,CAACN,OAAO,CAACjD,EAAE;8DAI9CtF,mBAAA,gBAAmB,EACnBtB,mBAAA,CAKM,OALNsN,YAKM,GAJJtN,mBAAA,CAGM,OAHNuN,YAGM,GAFJvN,mBAAA,CAAgE,QAAhEwN,YAAgE,EAAA5K,gBAAA,CAAhCiH,OAAO,CAACS,cAAc,kBACtDtK,mBAAA,CAAsD,QAAtDyN,YAAsD,EAAA7K,gBAAA,CAApBiH,OAAO,CAACjD,EAAE,iB,KAIhDtF,mBAAA,YAAe,EACftB,mBAAA,CAqBM,OArBN0N,YAqBM,GApBJ1N,mBAAA,CAmBM,OAnBN2N,YAmBM,G,4BAlBJ3N,mBAAA,CAEM;MAFDL,KAAK,EAAC;IAAoB,IAC7BK,mBAAA,CAA2B;MAAxBL,KAAK,EAAC;IAAa,G,sBAExBK,mBAAA,CAcM,OAdN4N,YAcM,GAbJ5N,mBAAA,CAAgE,OAAhE6N,YAAgE,EAAAjL,gBAAA,CAA5BiH,OAAO,CAACgB,WAAW,kBACvD7K,mBAAA,CAAkE,OAAlE8N,YAAkE,EAAAlL,gBAAA,CAA7BiH,OAAO,CAACkB,YAAY,kBACzD/K,mBAAA,CAUM,OAVN+N,YAUM,GATQpN,QAAA,CAAAuK,kBAAkB,CAACrB,OAAO,CAACsB,sBAAsB,K,cAA7D9I,mBAAA,CAEO,QAFP2L,YAEO,G,4BADLhO,mBAAA,CAAgC;MAA7BL,KAAK,EAAC;IAAkB,6B,kCAAQgB,QAAA,CAAAuK,kBAAkB,CAACrB,OAAO,CAACsB,sBAAsB,kB,wCAE1EtB,OAAO,CAACyB,kBAAkB,I,cAAtCjJ,mBAAA,CAEO,QAFP4L,YAEO,G,4BADLjO,mBAAA,CAAgC;MAA7BL,KAAK,EAAC;IAAkB,6B,kCAAQkK,OAAO,CAACyB,kBAAkB,iB,wCAEnD3K,QAAA,CAAA8K,mBAAmB,CAAC5B,OAAO,K,cAAvCxH,mBAAA,CAEO,QAFP6L,YAEO,G,4BADLlO,mBAAA,CAAgC;MAA7BL,KAAK,EAAC;IAAkB,6B,kCAAQgB,QAAA,CAAA8K,mBAAmB,CAAC5B,OAAO,kB,gDAOxEvI,mBAAA,mBAAsB,EACtBtB,mBAAA,CAKM,OALNmO,YAKM,GAJJnO,mBAAA,CAGO,QAHPoO,YAGO,G,8BAFLpO,mBAAA,CAA+B;MAA5BL,KAAK,EAAC;IAAiB,6B,iBAAK,GAC/B,GAAAiD,gBAAA,CAAGiH,OAAO,CAAC5C,aAAa,iB,KAI5B3F,mBAAA,YAAe,EACftB,mBAAA,CAKM,OALNqO,YAKM,GAJJrO,mBAAA,CAGO;MAHDL,KAAK,EAAA4B,eAAA,EAAC,gBAAgB,YAAmBZ,QAAA,CAAAwL,cAAc,CAACtC,OAAO,CAAChD,WAAW;sCAC/E7G,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,6B,iBAAK,GAC7B,GAAAiD,gBAAA,CAAGjC,QAAA,CAAAmG,YAAY,CAAC+C,OAAO,CAAChD,WAAW,kB,oBAIvCvF,mBAAA,YAAe,EACftB,mBAAA,CAEM,OAFNsO,YAEM,GADJtO,mBAAA,CAA2E,QAA3EuO,YAA2E,EAAA3L,gBAAA,CAA3CjC,QAAA,CAAA4L,cAAc,CAAC1C,OAAO,CAAC2C,SAAS,kB,GAGlElL,mBAAA,UAAa,EACbtB,mBAAA,CAKM,OALNwO,YAKM,GAJJxO,mBAAA,CAGM,OAHNyO,YAGM,GAFJzO,mBAAA,CAAqE,QAArE0O,YAAqE,EAAA9L,gBAAA,CAA1CjC,QAAA,CAAAgM,UAAU,CAAC9C,OAAO,CAAC+C,YAAY,mBAC1D5M,mBAAA,CAAsE,QAAtE2O,YAAsE,EAAA/L,gBAAA,CAA1CjC,QAAA,CAAA0C,UAAU,CAACwG,OAAO,CAAC+C,YAAY,kB,KAI/DtL,mBAAA,aAAgB,EAChBtB,mBAAA,CAMM,OANN4O,YAMM,GALJ5O,mBAAA,CAIM,OAJN6O,YAIM,GAHJ7O,mBAAA,CAES;MAFDL,KAAK,EAAC,8BAA8B;MAAE+B,OAAK,EAAAoB,MAAA,IAAEnC,QAAA,CAAA6J,kBAAkB,CAACX,OAAO,CAACjD,EAAE;MAAGhD,KAAK,EAAC;2CACzF5D,mBAAA,CAA2B;MAAxBL,KAAK,EAAC;IAAa,2B;4IASpC2B,mBAAA,gBAAmB,EACRjB,KAAA,CAAA0I,UAAU,CAAC+F,UAAU,Q,cAAhCzM,mBAAA,CAuBQ,OAvBR0M,YAuBQ,GAtBJ/O,mBAAA,CAqBM,OArBNgP,YAqBM,GApBJhP,mBAAA,CAmBK,MAnBLiP,YAmBK,GAlBHjP,mBAAA,CAIK;IAJDL,KAAK,EAAA4B,eAAA,EAAC,WAAW;MAAAwC,QAAA,EAAqB1D,KAAA,CAAA0I,UAAU,CAACC,WAAW;IAAA;MAC9DhJ,mBAAA,CAEI;IAFDL,KAAK,EAAC,WAAW;IAACuP,IAAI,EAAC,GAAG;IAAExN,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAwN,cAAA,CAAArM,MAAA,IAAUnC,QAAA,CAAAyO,UAAU,CAAC/O,KAAA,CAAA0I,UAAU,CAACC,WAAW;oCAC7EhJ,mBAAA,CAAmC;IAAhCL,KAAK,EAAC;EAAqB,2B,wCAGlC0C,mBAAA,CAOKE,SAAA,QAAAmE,WAAA,CANYwC,IAAI,CAACC,GAAG,CAAC9I,KAAA,CAAA0I,UAAU,CAAC+F,UAAU,OAAtCO,IAAI;yBADbhN,mBAAA,CAOK;MALFG,GAAG,EAAE6M,IAAI;MACV1P,KAAK,EAAA4B,eAAA,EAAC,WAAW;QAAAC,MAAA,EACC6N,IAAI,KAAKhP,KAAA,CAAA0I,UAAU,CAACC;MAAW;QAEjDhJ,mBAAA,CAA8E;MAA3EL,KAAK,EAAC,WAAW;MAACuP,IAAI,EAAC,GAAG;MAAExN,OAAK,EAAAyN,cAAA,CAAArM,MAAA,IAAUnC,QAAA,CAAAyO,UAAU,CAACC,IAAI;wBAAMA,IAAI,wBAAAC,YAAA,E;kCAEzEtP,mBAAA,CAIK;IAJDL,KAAK,EAAA4B,eAAA,EAAC,WAAW;MAAAwC,QAAA,EAAqB1D,KAAA,CAAA0I,UAAU,CAACC,WAAW,KAAK3I,KAAA,CAAA0I,UAAU,CAAC+F;IAAU;MACxF9O,mBAAA,CAEI;IAFDL,KAAK,EAAC,WAAW;IAACuP,IAAI,EAAC,GAAG;IAAExN,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAwN,cAAA,CAAArM,MAAA,IAAUnC,QAAA,CAAAyO,UAAU,CAAC/O,KAAA,CAAA0I,UAAU,CAACC,WAAW;oCAC7EhJ,mBAAA,CAAoC;IAAjCL,KAAK,EAAC;EAAsB,2B,oHAQ3C2B,mBAAA,2BAA8B,EACnBjB,KAAA,CAAAkP,kBAAkB,IAAIlP,KAAA,CAAAmP,cAAc,I,cAA/CnN,mBAAA,CAsgBM,OAtgBNoN,YAsgBM,GArgBJzP,mBAAA,CAogBM,OApgBN0P,YAogBM,GAngBJ1P,mBAAA,CAkgBM,OAlgBN2P,YAkgBM,GAjgBJ3P,mBAAA,CAMM,OANN4P,YAMM,GALJ5P,mBAAA,CAGK,MAHL6P,YAGK,G,8BAFH7P,mBAAA,CAAoC;IAAjCL,KAAK,EAAC;EAAsB,6B,iBAAK,qBAClB,GAAAiD,gBAAA,CAAGvC,KAAA,CAAAmP,cAAc,CAAClF,cAAc,iB,GAEpDtK,mBAAA,CAAqF;IAA7E6C,IAAI,EAAC,QAAQ;IAAClD,KAAK,EAAC,WAAW;IAAE+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAmB,MAAA,IAAEzC,KAAA,CAAAkP,kBAAkB;QAEpEvP,mBAAA,CA+eM,OA/eN8P,YA+eM,GA9eJ9P,mBAAA,CAicM,OAjcN+P,YAicM,GAhcJzO,mBAAA,uCAA0C,EAC1CtB,mBAAA,CA0PM,OA1PNgQ,YA0PM,GAzPJ1O,mBAAA,uBAA0B,EAC1BtB,mBAAA,CAsDM,OAtDNiQ,YAsDM,G,8BArDJjQ,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAa,IACtBK,mBAAA,CAAgF;IAA5EL,KAAK,EAAC;EAAM,IAACK,mBAAA,CAAuC;IAApCL,KAAK,EAAC;EAAyB,I,iBAAK,qBAAmB,E,wBAE7EK,mBAAA,CAiDM,OAjDNkQ,YAiDM,GAhDJlQ,mBAAA,CA+CM,OA/CNmQ,YA+CM,GA9CJnQ,mBAAA,CAmBM,OAnBNoQ,YAmBM,GAlBJpQ,mBAAA,CAGM,OAHNqQ,YAGM,G,8BAFJrQ,mBAAA,CAAwD;IAAjDL,KAAK,EAAC;EAAoB,GAAC,gBAAc,sBAChDK,mBAAA,CAAuD,KAAvDsQ,YAAuD,EAAA1N,gBAAA,CAApCvC,KAAA,CAAAmP,cAAc,CAAClF,cAAc,iB,GAElDtK,mBAAA,CAKM,OALNuQ,YAKM,G,8BAJJvQ,mBAAA,CAAuD;IAAhDL,KAAK,EAAC;EAAoB,GAAC,eAAa,sBAC/CK,mBAAA,CAEI,KAFJwQ,YAEI,GADFxQ,mBAAA,CAAqE,QAArEyQ,YAAqE,EAAA7N,gBAAA,CAAtCvC,KAAA,CAAAmP,cAAc,CAACvI,aAAa,iB,KAG/DjH,mBAAA,CAGM,OAHN0Q,YAGM,G,8BAFJ1Q,mBAAA,CAA0D;IAAnDL,KAAK,EAAC;EAAoB,GAAC,kBAAgB,sBAClDK,mBAAA,CAAyD,KAAzD2Q,YAAyD,EAAA/N,gBAAA,CAAtCvC,KAAA,CAAAmP,cAAc,CAACoB,gBAAgB,iB,GAEpD5Q,mBAAA,CAGM,OAHN6Q,YAGM,G,8BAFJ7Q,mBAAA,CAAyD;IAAlDL,KAAK,EAAC;EAAoB,GAAC,iBAAe,sBACjDK,mBAAA,CAA2E,KAA3E8Q,YAA2E,EAAAlO,gBAAA,CAAxDvC,KAAA,CAAAmP,cAAc,CAACuB,eAAe,oC,KAGrD/Q,mBAAA,CAyBM,OAzBNgR,YAyBM,GAxBJhR,mBAAA,CAOM,OAPNiR,YAOM,G,8BANJjR,mBAAA,CAAwD;IAAjDL,KAAK,EAAC;EAAoB,GAAC,gBAAc,sBAChDK,mBAAA,CAII,KAJJkR,YAII,GAHFlR,mBAAA,CAEO;IAFDL,KAAK,EAAA4B,eAAA,EAAC,OAAO,QAAeZ,QAAA,CAAAwL,cAAc,CAAC9L,KAAA,CAAAmP,cAAc,CAAC3I,WAAW;sBACtElG,QAAA,CAAAmG,YAAY,CAACzG,KAAA,CAAAmP,cAAc,CAAC3I,WAAW,yB,KAIhD7G,mBAAA,CAOM,OAPNmR,YAOM,G,8BANJnR,mBAAA,CAAkD;IAA3CL,KAAK,EAAC;EAAoB,GAAC,UAAQ,sBAC1CK,mBAAA,CAII,KAJJoR,YAII,GAHFpR,mBAAA,CAEO;IAFDL,KAAK,EAAA4B,eAAA,EAAC,OAAO,EAASlB,KAAA,CAAAmP,cAAc,CAAC6B,QAAQ,4BAA4BhR,KAAA,CAAAmP,cAAc,CAAC6B,QAAQ;sBACjGhR,KAAA,CAAAmP,cAAc,CAAC6B,QAAQ,oC,KAIhCrR,mBAAA,CAGM,OAHNsR,YAGM,G,8BAFJtR,mBAAA,CAAyD;IAAlDL,KAAK,EAAC;EAAoB,GAAC,iBAAe,sBACjDK,mBAAA,CAAoE,KAApEuR,YAAoE,EAAA3O,gBAAA,CAAjDvC,KAAA,CAAAmP,cAAc,CAACgC,eAAe,6B,GAEnDxR,mBAAA,CAGM,OAHNyR,YAGM,G,8BAFJzR,mBAAA,CAAwD;IAAjDL,KAAK,EAAC;EAAoB,GAAC,gBAAc,sBAChDK,mBAAA,CAAqE,KAArE0R,YAAqE,EAAA9O,gBAAA,CAAlDjC,QAAA,CAAAgR,cAAc,CAACtR,KAAA,CAAAmP,cAAc,CAAC5C,YAAY,kB,WAOvEtL,mBAAA,wBAA2B,EAC3BtB,mBAAA,CA4EM,OA5EN4R,YA4EM,G,8BA3EJ5R,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAa,IACtBK,mBAAA,CAAwE;IAApEL,KAAK,EAAC;EAAM,IAACK,mBAAA,CAAgC;IAA7BL,KAAK,EAAC;EAAkB,I,iBAAK,oBAAkB,E,wBAErEK,mBAAA,CAuEM,OAvEN6R,YAuEM,GAtEJvQ,mBAAA,uBAA0B,EAC1BtB,mBAAA,CAiCM,OAjCN8R,YAiCM,GAhCJ9R,mBAAA,CAsBM,OAtBN+R,YAsBM,GArBJ/R,mBAAA,CAMM,OANNgS,YAMM,G,8BALJhS,mBAAA,CAAmD;IAA5CL,KAAK,EAAC;EAAoB,GAAC,WAAS,sBAC3CK,mBAAA,CAGI;IAHDL,KAAK,EAAA4B,eAAA,EAAC,MAAM;MAAA,cAAyBZ,QAAA,CAAAsR,iBAAiB,CAAC5R,KAAA,CAAAmP,cAAc;IAAA;MAC1D7O,QAAA,CAAAsR,iBAAiB,CAAC5R,KAAA,CAAAmP,cAAc,wB,cAA5CnN,mBAAA,CAAgH,QAAA6P,YAAA,EAAAtP,gBAAA,CAA3CjC,QAAA,CAAAsR,iBAAiB,CAAC5R,KAAA,CAAAmP,cAAc,sB,cACrGnN,mBAAA,CAAqD,QAArD8P,YAAqD,EAAnB,cAAY,G,oBAGlDnS,mBAAA,CAMM,OANNoS,YAMM,G,8BALJpS,mBAAA,CAAuD;IAAhDL,KAAK,EAAC;EAAoB,GAAC,eAAa,sBAC/CK,mBAAA,CAGI;IAHDL,KAAK,EAAA4B,eAAA,EAAC,MAAM;MAAA,eAA0BlB,KAAA,CAAAmP,cAAc,CAACzE;IAAY;MACzD1K,KAAA,CAAAmP,cAAc,CAACzE,YAAY,I,cAApC1I,mBAAA,CAA2H;;IAApF6M,IAAI,YAAY7O,KAAA,CAAAmP,cAAc,CAACzE,YAAY;sBAAO1K,KAAA,CAAAmP,cAAc,CAACzE,YAAY,wBAAAsH,YAAA,M,cACpHhQ,mBAAA,CAAqD,QAArDiQ,YAAqD,EAAnB,cAAY,G,oBAGlDtS,mBAAA,CAMM,OANNuS,YAMM,G,8BALJvS,mBAAA,CAAsD;IAA/CL,KAAK,EAAC;EAAoB,GAAC,cAAY,sBAC9CK,mBAAA,CAGI;IAHDL,KAAK,EAAA4B,eAAA,EAAC,MAAM;MAAA,eAA0BlB,KAAA,CAAAmP,cAAc,CAACgD;IAAY;MACzDnS,KAAA,CAAAmP,cAAc,CAACgD,YAAY,I,cAApCnQ,mBAAA,CAAwH;;IAAjF6M,IAAI,SAAS7O,KAAA,CAAAmP,cAAc,CAACgD,YAAY;sBAAOnS,KAAA,CAAAmP,cAAc,CAACgD,YAAY,wBAAAC,YAAA,M,cACjHpQ,mBAAA,CAAqD,QAArDqQ,YAAqD,EAAnB,cAAY,G,sBAIpD1S,mBAAA,CAQM,OARN2S,YAQM,GAPJ3S,mBAAA,CAMM,OANN4S,YAMM,G,8BALJ5S,mBAAA,CAAsD;IAA/CL,KAAK,EAAC;EAAoB,GAAC,cAAY,sBAC9CK,mBAAA,CAGI;IAHDL,KAAK,EAAA4B,eAAA,EAAC,MAAM;MAAA,eAA0BlB,KAAA,CAAAmP,cAAc,CAACrE;IAAsB;MAChExK,QAAA,CAAAuK,kBAAkB,CAAC7K,KAAA,CAAAmP,cAAc,CAACrE,sBAAsB,K,cAApE9I,mBAAA,CAA6I,QAAAwQ,YAAA,EAAAjQ,gBAAA,CAAnEjC,QAAA,CAAAuK,kBAAkB,CAAC7K,KAAA,CAAAmP,cAAc,CAACrE,sBAAsB,sB,cAClI9I,mBAAA,CAAqD,QAArDyQ,YAAqD,EAAnB,cAAY,G,wBAMtDxR,mBAAA,yBAA4B,EAC5BtB,mBAAA,CAUM,OAVN+S,YAUM,GATJ/S,mBAAA,CAQM,OARNgT,YAQM,GAPJhT,mBAAA,CAMM,OANNiT,YAMM,G,8BALJjT,mBAAA,CAA0D;IAAnDL,KAAK,EAAC;EAAoB,GAAC,kBAAgB,sBAClDK,mBAAA,CAGI;IAHDL,KAAK,EAAA4B,eAAA,EAAC,MAAM;MAAA,eAA0BZ,QAAA,CAAAuS,oBAAoB,CAAC7S,KAAA,CAAAmP,cAAc;IAAA;MAC9D7O,QAAA,CAAAuS,oBAAoB,CAAC7S,KAAA,CAAAmP,cAAc,K,cAA/CnN,mBAAA,CAAmG,QAAA8Q,YAAA,EAAAvQ,gBAAA,CAA9CjC,QAAA,CAAAuS,oBAAoB,CAAC7S,KAAA,CAAAmP,cAAc,sB,cACxFnN,mBAAA,CAAqD,QAArD+Q,YAAqD,EAAnB,cAAY,G,wBAMtD9R,mBAAA,4BAA+B,EAC/BtB,mBAAA,CAmBM,OAnBNqT,YAmBM,GAlBJrT,mBAAA,CAQM,OARNsT,YAQM,GAPJtT,mBAAA,CAMM,OANNuT,YAMM,G,8BALJvT,mBAAA,CAAqD;IAA9CL,KAAK,EAAC;EAAoB,GAAC,aAAW,sBAC7CK,mBAAA,CAGI;IAHDL,KAAK,EAAA4B,eAAA,EAAC,MAAM;MAAA,eAA0BlB,KAAA,CAAAmP,cAAc,CAAClE;IAAkB;MAC5DjL,KAAA,CAAAmP,cAAc,CAAClE,kBAAkB,I,cAA7CjJ,mBAAA,CAA6F,QAAAmR,YAAA,EAAA5Q,gBAAA,CAA3CvC,KAAA,CAAAmP,cAAc,CAAClE,kBAAkB,qB,cACnFjJ,mBAAA,CAAqD,QAArDoR,YAAqD,EAAnB,cAAY,G,sBAIpDzT,mBAAA,CAQM,OARN0T,YAQM,GAPJ1T,mBAAA,CAMM,OANN2T,YAMM,G,8BALJ3T,mBAAA,CAA4D;IAArDL,KAAK,EAAC;EAAoB,GAAC,oBAAkB,sBACpDK,mBAAA,CAGI;IAHDL,KAAK,EAAA4B,eAAA,EAAC,MAAM;MAAA,eAA0BZ,QAAA,CAAA8K,mBAAmB,CAACpL,KAAA,CAAAmP,cAAc;IAAA;MAC7D7O,QAAA,CAAA8K,mBAAmB,CAACpL,KAAA,CAAAmP,cAAc,K,cAA9CnN,mBAAA,CAAiG,QAAAuR,YAAA,EAAAhR,gBAAA,CAA7CjC,QAAA,CAAA8K,mBAAmB,CAACpL,KAAA,CAAAmP,cAAc,sB,cACtFnN,mBAAA,CAAqD,QAArDwR,YAAqD,EAAnB,cAAY,G,4BAQ1DvS,mBAAA,wBAA2B,EAC3BtB,mBAAA,CA+GM,OA/GN8T,YA+GM,G,8BA9GJ9T,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAa,IACtBK,mBAAA,CAA6E;IAAzEL,KAAK,EAAC;EAAM,IAACK,mBAAA,CAAqC;IAAlCL,KAAK,EAAC;EAAuB,I,iBAAK,oBAAkB,E,wBAE1EK,mBAAA,CA0GM,OA1GN+T,YA0GM,GAzGO1T,KAAA,CAAAmP,cAAc,CAACwE,kBAAkB,IAAI3T,KAAA,CAAAmP,cAAc,CAACwE,kBAAkB,CAACtM,MAAM,Q,cAAxFrF,mBAAA,CA0FM,OAAA4R,YAAA,GAzFJjU,mBAAA,CAwFM,OAxFNkU,YAwFM,I,kBAvFJ7R,mBAAA,CAsFME,SAAA,QAAAmE,WAAA,CAtFkBrG,KAAA,CAAAmP,cAAc,CAACwE,kBAAkB,EAA7CG,QAAQ;yBAApB9R,mBAAA,CAsFM;MAtFsDG,GAAG,EAAE2R,QAAQ,CAACvN,EAAE;MAAEjH,KAAK,EAAC;QAClFK,mBAAA,CAoFM,OApFNoU,YAoFM,GAnFJpU,mBAAA,CAKM,OALNqU,YAKM,GAJJrU,mBAAA,CAGM,OAHNsU,YAGM,G,8BAFJtU,mBAAA,CAAoC;MAAjCL,KAAK,EAAC;IAAsB,6B,iBAAK,GACpC,GAAAiD,gBAAA,CAAGjC,QAAA,CAAA4T,0BAA0B,CAACJ,QAAQ,CAAClN,aAAa,kB,KAGxDjH,mBAAA,CAsEM,OAtENwU,YAsEM,GArEJlT,mBAAA,mBAAsB,EACXX,QAAA,CAAA8T,WAAW,CAACN,QAAQ,CAACO,SAAS,K,cAAzCrS,mBAAA,CAmCM;;MAlCD1C,KAAK,EAAC,eAAe;MACpB+B,OAAK,EAAAoB,MAAA,IAAEnC,QAAA,CAAAgU,cAAc,CAACR,QAAQ;MAC9BS,YAAU,EAAA9R,MAAA,IAAEnC,QAAA,CAAAkU,YAAY,CAACV,QAAQ;QACrC7S,mBAAA,+BAAkC,EAE1BjB,KAAA,CAAAyU,YAAY,CAACX,QAAQ,CAACvN,EAAE,K,cADhCvE,mBAAA,CAME;;MAJC0S,GAAG,EAAE1U,KAAA,CAAAyU,YAAY,CAACX,QAAQ,CAACvN,EAAE;MAC7BoO,GAAG,EAAEb,QAAQ,CAACc,aAAa;MAC5BtV,KAAK,EAAC,gBAAgB;MACrBuV,OAAK,EAAAvT,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAAwU,gBAAA,IAAAxU,QAAA,CAAAwU,gBAAA,IAAAvT,IAAA,CAAgB;8DAIVvB,KAAA,CAAA+U,gBAAgB,CAACC,GAAG,CAAClB,QAAQ,CAACvN,EAAE,K,cAAhDvE,mBAAA,CAGME,SAAA;MAAAC,GAAA;IAAA,IAJNlB,mBAAA,mBAAsB,E,8BACtBtB,mBAAA,CAGM;MAH6CL,KAAK,EAAC;IAAqB,IAC5EK,mBAAA,CAAsC;MAAnCL,KAAK,EAAC;IAAwB,IACjCK,mBAAA,CAA6B,cAAvB,kBAAgB,E,yEAIRK,KAAA,CAAAiV,eAAe,CAACD,GAAG,CAAClB,QAAQ,CAACvN,EAAE,K,cAA/CvE,mBAAA,CAIME,SAAA;MAAAC,GAAA;IAAA,IALNlB,mBAAA,oCAAuC,EACvCtB,mBAAA,CAIM;MAJ4CL,KAAK,EAAC,mBAAmB;MAAE+B,OAAK,EAAAyN,cAAA,CAAArM,MAAA,IAAOnC,QAAA,CAAA4U,iBAAiB,CAACpB,QAAQ;2CACjHnU,mBAAA,CAA2C;MAAxCL,KAAK,EAAC;IAA6B,4BACtCK,mBAAA,CAA2B,cAArB,gBAAc,qBACpBA,mBAAA,CAA6B,eAAtB,gBAAc,oB,qGAIvBqC,mBAAA,CAGME,SAAA;MAAAC,GAAA;IAAA,IAJNlB,mBAAA,uCAA0C,E,8BAC1CtB,mBAAA,CAGM;MAHML,KAAK,EAAC;IAAqB,IACrCK,mBAAA,CAA4B;MAAzBL,KAAK,EAAC;IAAc,IACvBK,mBAAA,CAA0B,cAApB,eAAa,E,sGAErBA,mBAAA,CAGM;MAHDL,KAAK,EAAC;IAAe,IACxBK,mBAAA,CAAkC;MAA/BL,KAAK,EAAC;IAAoB,IAC7BK,mBAAA,CAA0B,cAApB,eAAa,E,wEAIPW,QAAA,CAAA6U,SAAS,CAACrB,QAAQ,CAACO,SAAS,K,cAA5CrS,mBAAA,CAcME,SAAA;MAAAC,GAAA;IAAA,IAfNlB,mBAAA,iBAAoB,EACpBtB,mBAAA,CAcM,OAdNyV,YAcM,G,8BAbJzV,mBAAA,CAEM;MAFDL,KAAK,EAAC;IAAU,IACnBK,mBAAA,CAAiD;MAA9CL,KAAK,EAAC;IAAmC,G,sBAE9CK,mBAAA,CAGM,OAHN0V,YAGM,GAFJ1V,mBAAA,CAAwD,KAAxD2V,YAAwD,EAAA/S,gBAAA,CAA7BuR,QAAQ,CAACc,aAAa,kBACjDjV,mBAAA,CAA0E,SAA1E4V,YAA0E,EAAAhT,gBAAA,CAA7CjC,QAAA,CAAAkV,cAAc,CAAC1B,QAAQ,CAAC2B,SAAS,kB,GAEhE9V,mBAAA,CAKS;MAJPL,KAAK,EAAC,qCAAqC;MAC1C+B,OAAK,EAAAoB,MAAA,IAAEnC,QAAA,CAAAoV,gBAAgB,CAAC5B,QAAQ;2CAEjCnU,mBAAA,CAAoC;MAAjCL,KAAK,EAAC;IAAsB,4B,iBAAK,WACtC,E,uGAGF0C,mBAAA,CAcME,SAAA;MAAAC,GAAA;IAAA,IAfNlB,mBAAA,sBAAyB,EACzBtB,mBAAA,CAcM,OAdNgW,YAcM,G,8BAbJhW,mBAAA,CAEM;MAFDL,KAAK,EAAC;IAAW,IACpBK,mBAAA,CAAgD;MAA7CL,KAAK,EAAC;IAAkC,G,sBAE7CK,mBAAA,CAGM,OAHNiW,YAGM,GAFJjW,mBAAA,CAAwD,KAAxDkW,YAAwD,EAAAtT,gBAAA,CAA7BuR,QAAQ,CAACc,aAAa,kBACjDjV,mBAAA,CAA0E,SAA1EmW,YAA0E,EAAAvT,gBAAA,CAA7CjC,QAAA,CAAAkV,cAAc,CAAC1B,QAAQ,CAAC2B,SAAS,kB,GAEhE9V,mBAAA,CAKS;MAJPL,KAAK,EAAC,qCAAqC;MAC1C+B,OAAK,EAAAoB,MAAA,IAAEnC,QAAA,CAAAoV,gBAAgB,CAAC5B,QAAQ;2CAEjCnU,mBAAA,CAAoC;MAAjCL,KAAK,EAAC;IAAsB,4B,iBAAK,WACtC,E,yFAGJK,mBAAA,CAKM,OALNoW,YAKM,GAJJpW,mBAAA,CAGQ,SAHRqW,YAGQ,G,8BAFNrW,mBAAA,CAAiC;MAA9BL,KAAK,EAAC;IAAmB,6B,iBAAK,YACxB,GAAAiD,gBAAA,CAAGjC,QAAA,CAAAgM,UAAU,CAACwH,QAAQ,CAACmC,UAAU,kB;uDAOtDjU,mBAAA,CAaM,OAbNkU,YAaM,GAZJvW,mBAAA,CAWM,OAXNwW,YAWM,G,8BAVJxW,mBAAA,CAAwD;IAArDL,KAAK,EAAC;EAA0C,6B,8BACnDK,mBAAA,CAAiD;IAA7CL,KAAK,EAAC;EAAY,GAAC,uBAAqB,sBAC5CK,mBAAA,CAOI,KAPJyW,YAOI,GANUpW,KAAA,CAAAmP,cAAc,CAACvI,aAAa,iB,cAAxC5E,mBAAA,CAEO,QAAAqU,YAAA,EAFgD,iEAEvD,M,cACArU,mBAAA,CAEO,QAAAsU,YAAA,EAFM,4DAEb,G,cAQZrV,mBAAA,sCAAyC,EACzCtB,mBAAA,CAiMM,OAjMN4W,YAiMM,GAhMJtV,mBAAA,uBAA0B,EAC1BtB,mBAAA,CAsCM,OAtCN6W,YAsCM,G,8BArCJ7W,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAa,IACtBK,mBAAA,CAAwE;IAApEL,KAAK,EAAC;EAAM,IAACK,mBAAA,CAAiC;IAA9BL,KAAK,EAAC;EAAmB,I,iBAAK,mBAAiB,E,wBAErEK,mBAAA,CAiCM,OAjCN8W,YAiCM,GAhCJ9W,mBAAA,CAkBM,OAlBN+W,YAkBM,G,8BAjBJ/W,mBAAA,CAAuD;IAAhDL,KAAK,EAAC;EAAoB,GAAC,eAAa,sB,gBAC/CK,mBAAA,CAWS;IAVPL,KAAK,EAAC,aAAa;iEACVU,KAAA,CAAA2W,gBAAgB,CAACC,SAAS,GAAAnU,MAAA;IAClCiB,QAAQ,EAAEpD,QAAA,CAAAuW,yBAAyB,GAAGxP,MAAM;MAE7C1H,mBAAA,CAES,UAFTmX,YAES,EAAAvU,gBAAA,CADJjC,QAAA,CAAAuW,yBAAyB,GAAGxP,MAAM,+E,kBAEvCrF,mBAAA,CAESE,SAAA,QAAAmE,WAAA,CAFgB/F,QAAA,CAAAuW,yBAAyB,IAAnCzQ,MAAM;yBAArBpE,mBAAA,CAES;MAF8CG,GAAG,EAAEiE,MAAM,CAACG,EAAE;MAAG7G,KAAK,EAAE0G,MAAM,CAACG;wBACjFjG,QAAA,CAAAmG,YAAY,CAACL,MAAM,CAACI,WAAW,yBAAAuQ,YAAA;kFAP3B/W,KAAA,CAAA2W,gBAAgB,CAACC,SAAS,E,GAU1BtW,QAAA,CAAAuW,yBAAyB,GAAGxP,MAAM,U,cAA7CrF,mBAAA,CAGM,OAHNgV,YAGM,G,8BAFJrX,mBAAA,CAAuC;IAApCL,KAAK,EAAC;EAAyB,6B,iBAAK,0CACA,GAAAiD,gBAAA,CAAGjC,QAAA,CAAAmG,YAAY,CAACzG,KAAA,CAAAmP,cAAc,CAAC3I,WAAW,KAAI,IACvF,gB,0CAGFvF,mBAAA,0BAA6B,EAC7BtB,mBAAA,CAUM,OAVNsX,YAUM,GATJtX,mBAAA,CAQS;IAPPL,KAAK,EAAC,iBAAiB;IACtB+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAA4W,4BAAA,IAAA5W,QAAA,CAAA4W,4BAAA,IAAA3V,IAAA,CAA4B;IACnCmC,QAAQ,GAAG1D,KAAA,CAAA2W,gBAAgB,CAACC,SAAS,KAAKtW,QAAA,CAAA6W,mBAAmB,CAACnX,KAAA,CAAAmP,cAAc,CAAC3I,WAAW,EAAExG,KAAA,CAAA2W,gBAAgB,CAACC,SAAS;IACpHrT,KAAK,EAAEjD,QAAA,CAAA8W,oBAAoB;oCAE5BzX,mBAAA,CAAgC;IAA7BL,KAAK,EAAC;EAAkB,6B,iBAAK,GAChC,GAAAiD,gBAAA,CAAGjC,QAAA,CAAA+W,mBAAmB,mB,sCAM9BpW,mBAAA,yBAA4B,EAC5BtB,mBAAA,CAqJM,OArJN2X,YAqJM,G,8BApJJ3X,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAa,IACtBK,mBAAA,CAAgF;IAA5EL,KAAK,EAAC;EAAM,IAACK,mBAAA,CAAuC;IAApCL,KAAK,EAAC;EAAyB,I,iBAAK,qBAAmB,E,wBAE7EK,mBAAA,CAgJM,OAhJN4X,YAgJM,GA/IJ5X,mBAAA,CAGM,OAHN6X,YAGM,G,8BAFJ7X,mBAAA,CAAwD;IAAjDL,KAAK,EAAC;EAAoB,GAAC,gBAAc,sBAChDK,mBAAA,CAA0E,KAA1E8X,YAA0E,EAAAlV,gBAAA,CAAvDvC,KAAA,CAAAmP,cAAc,CAACuI,cAAc,oC,GAElD/X,mBAAA,CAOM,OAPNgY,YAOM,G,8BANJhY,mBAAA,CAAwD;IAAjDL,KAAK,EAAC;EAAoB,GAAC,gBAAc,sBAChDK,mBAAA,CAII,KAJJiY,YAII,GAHFjY,mBAAA,CAEO;IAFDL,KAAK,EAAA4B,eAAA,EAAC,OAAO,EAASZ,QAAA,CAAAuX,qBAAqB,CAAC7X,KAAA,CAAAmP,cAAc,CAAC2I,cAAc;sBAC1ExX,QAAA,CAAAyX,mBAAmB,CAAC/X,KAAA,CAAAmP,cAAc,CAAC2I,cAAc,yB,KAI1DnY,mBAAA,CAyBM,OAzBNqY,YAyBM,GAxBJrY,mBAAA,CAKM,OALNsY,YAKM,GAJJtY,mBAAA,CAGM,OAHNuY,YAGM,G,8BAFJvY,mBAAA,CAAwD;IAAjDL,KAAK,EAAC;EAA0B,GAAC,UAAQ,sBAChDK,mBAAA,CAAiE,KAAjEwY,YAAiE,EAAA5V,gBAAA,CAA9CjC,QAAA,CAAA4L,cAAc,CAAClM,KAAA,CAAAmP,cAAc,CAACiJ,QAAQ,kB,KAG7DzY,mBAAA,CAKM,OALN0Y,YAKM,GAJJ1Y,mBAAA,CAGM,OAHN2Y,YAGM,G,8BAFJ3Y,mBAAA,CAA+D;IAAxDL,KAAK,EAAC;EAA0B,GAAC,iBAAe,sBACvDK,mBAAA,CAAwE,KAAxE4Y,YAAwE,EAAAhW,gBAAA,CAArDjC,QAAA,CAAA4L,cAAc,CAAClM,KAAA,CAAAmP,cAAc,CAACqJ,eAAe,kB,KAGpE7Y,mBAAA,CAKM,OALN8Y,YAKM,GAJJ9Y,mBAAA,CAGM,OAHN+Y,YAGM,G,8BAFJ/Y,mBAAA,CAA8D;IAAvDL,KAAK,EAAC;EAA0B,GAAC,gBAAc,sBACtDK,mBAAA,CAAuE,KAAvEgZ,YAAuE,EAAApW,gBAAA,CAApDjC,QAAA,CAAA4L,cAAc,CAAClM,KAAA,CAAAmP,cAAc,CAACyJ,cAAc,kB,KAGnEjZ,mBAAA,CAKM,OALNkZ,YAKM,GAJJlZ,mBAAA,CAGM,OAHNmZ,YAGM,G,8BAFJnZ,mBAAA,CAA4D;IAArDL,KAAK,EAAC;EAA0B,GAAC,cAAY,sBACpDK,mBAAA,CAAuF,KAAvFoZ,YAAuF,EAAAxW,gBAAA,CAA/CjC,QAAA,CAAA4L,cAAc,CAAClM,KAAA,CAAAmP,cAAc,CAAChD,SAAS,kB,OAKrFlL,mBAAA,oCAAuC,EAC5BX,QAAA,CAAA0Y,wBAAwB,CAAChZ,KAAA,CAAAmP,cAAc,K,cAAlDnN,mBAAA,CA8CM,OA9CNiX,YA8CM,G,8BA7CJtZ,mBAAA,CAGK;IAHDL,KAAK,EAAC;EAAmB,IAC3BK,mBAAA,CAAsC;IAAnCL,KAAK,EAAC;EAAwB,I,iBAAK,4BAExC,E,sBACAK,mBAAA,CAyBM,OAzBNuZ,YAyBM,GAxBJvZ,mBAAA,CAYM,OAZNwZ,YAYM,GAXJxZ,mBAAA,CAUM,OAVNyZ,YAUM,G,8BATJzZ,mBAAA,CAA2D;IAApDL,KAAK,EAAC;EAAoB,GAAC,mBAAiB,sB,gBACnDK,mBAAA,CAOC;IANC6C,IAAI,EAAC,QAAQ;IACblD,KAAK,EAAC,cAAc;iEACXU,KAAA,CAAAqZ,uBAAuB,CAACC,eAAe,GAAA7W,MAAA;IAC/CqG,GAAG,EAAE9I,KAAA,CAAAmP,cAAc,CAAChD,SAAS;IAC9BoN,IAAI,EAAC,MAAM;IACXxT,WAAW,EAAC;wDAHH/F,KAAA,CAAAqZ,uBAAuB,CAACC,eAAe,E,OAOtD3Z,mBAAA,CAUM,OAVN6Z,YAUM,GATJ7Z,mBAAA,CAQM,OARN8Z,YAQM,G,8BAPJ9Z,mBAAA,CAAwD;IAAjDL,KAAK,EAAC;EAAoB,GAAC,gBAAc,sB,gBAChDK,mBAAA,CAKC;IAJC6C,IAAI,EAAC,MAAM;IACXlD,KAAK,EAAC,cAAc;iEACXU,KAAA,CAAAqZ,uBAAuB,CAACK,cAAc,GAAAjX,MAAA;IAC/CsD,WAAW,EAAC;iDADH/F,KAAA,CAAAqZ,uBAAuB,CAACK,cAAc,E,SAOvD/Z,mBAAA,CAaM,OAbNga,YAaM,GAZJha,mBAAA,CAWS;IAVPL,KAAK,EAAC,iBAAiB;IACtB+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAAsZ,qBAAA,IAAAtZ,QAAA,CAAAsZ,qBAAA,IAAArY,IAAA,CAAqB;IAC5BmC,QAAQ,GAAG1D,KAAA,CAAAqZ,uBAAuB,CAACC,eAAe,IAAItZ,KAAA,CAAAqZ,uBAAuB,CAACtX;oCAE/EpC,mBAAA,CAAwC;IAArCL,KAAK,EAAC;EAA0B,6BACvBU,KAAA,CAAAqZ,uBAAuB,CAACtX,OAAO,I,cAA3CC,mBAAA,CAGO,QAAA6X,YAAA,EAAAvY,MAAA,UAAAA,MAAA,SAFL3B,mBAAA,CAA2C;IAAxCL,KAAK,EAAC;EAA6B,4B,iBAAK,gBAE7C,E,qBACA0C,mBAAA,CAAkC,QAAA8X,YAAA,EAArB,gBAAc,G,yEAKjC7Y,mBAAA,uBAA0B,EACfX,QAAA,CAAAyZ,iBAAiB,CAAC/Z,KAAA,CAAAmP,cAAc,K,cAA3CnN,mBAAA,CAqDM,OArDNgY,YAqDM,G,8BApDJra,mBAAA,CAGK;IAHDL,KAAK,EAAC;EAAgB,IACxBK,mBAAA,CAAwC;IAArCL,KAAK,EAAC;EAA0B,I,iBAAK,+BAE1C,E,sBACAK,mBAAA,CAgCM,OAhCNsa,YAgCM,GA/BJta,mBAAA,CAUM,OAVNua,YAUM,GATJva,mBAAA,CAQM,OARNwa,YAQM,G,8BAPJxa,mBAAA,CAAgD;IAAzCL,KAAK,EAAC;EAAoB,GAAC,QAAM,sB,gBACxCK,mBAAA,CAKC;IAJC6C,IAAI,EAAC,MAAM;IACXlD,KAAK,EAAC,cAAc;iEACXU,KAAA,CAAAoa,kBAAkB,CAACC,cAAc,GAAA5X,MAAA;IACzCqG,GAAG,EAAExI,QAAA,CAAAga,eAAe;wDADZta,KAAA,CAAAoa,kBAAkB,CAACC,cAAc,E,OAKhD1a,mBAAA,CASM,OATN4a,YASM,GARJ5a,mBAAA,CAOM,OAPN6a,YAOM,G,8BANJ7a,mBAAA,CAAsD;IAA/CL,KAAK,EAAC;EAAoB,GAAC,cAAY,sB,gBAC9CK,mBAAA,CAIC;IAHC6C,IAAI,EAAC,MAAM;IACXlD,KAAK,EAAC,cAAc;iEACXU,KAAA,CAAAoa,kBAAkB,CAACK,oBAAoB,GAAAhY,MAAA;iDAAvCzC,KAAA,CAAAoa,kBAAkB,CAACK,oBAAoB,E,OAItD9a,mBAAA,CASM,OATN+a,YASM,GARJ/a,mBAAA,CAOM,OAPNgb,YAOM,G,8BANJhb,mBAAA,CAAoD;IAA7CL,KAAK,EAAC;EAAoB,GAAC,YAAU,sB,gBAC5CK,mBAAA,CAIC;IAHC6C,IAAI,EAAC,MAAM;IACXlD,KAAK,EAAC,cAAc;iEACXU,KAAA,CAAAoa,kBAAkB,CAACQ,kBAAkB,GAAAnY,MAAA;iDAArCzC,KAAA,CAAAoa,kBAAkB,CAACQ,kBAAkB,E,SAMtDjb,mBAAA,CAaM,OAbNkb,YAaM,GAZJlb,mBAAA,CAWS;IAVPL,KAAK,EAAC,cAAc;IACnB+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAAwa,cAAA,IAAAxa,QAAA,CAAAwa,cAAA,IAAAvZ,IAAA,CAAc;IACrBmC,QAAQ,GAAGpD,QAAA,CAAAya,iBAAiB,MAAM/a,KAAA,CAAAoa,kBAAkB,CAACrY;oCAEtDpC,mBAAA,CAA0C;IAAvCL,KAAK,EAAC;EAA4B,6BACzBU,KAAA,CAAAoa,kBAAkB,CAACrY,OAAO,I,cAAtCC,mBAAA,CAGO,QAAAgZ,YAAA,EAAA1Z,MAAA,UAAAA,MAAA,SAFL3B,mBAAA,CAA2C;IAAxCL,KAAK,EAAC;EAA6B,4B,iBAAK,iBAE7C,E,qBACA0C,mBAAA,CAAmC,QAAAiZ,YAAA,EAAtB,iBAAe,G,iFAS1Cha,mBAAA,6BAAgC,EAChCtB,mBAAA,CAyCM,OAzCNub,YAyCM,G,8BAxCJvb,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAa,IACtBK,mBAAA,CAAuE;IAAnEL,KAAK,EAAC;EAAM,IAACK,mBAAA,CAAmC;IAAhCL,KAAK,EAAC;EAAqB,I,iBAAK,gBAAc,E,wBAEpEK,mBAAA,CAoCM,OApCNwb,YAoCM,GAnCOnb,KAAA,CAAAmP,cAAc,CAACiM,cAAc,IAAIpb,KAAA,CAAAmP,cAAc,CAACiM,cAAc,CAAC/T,MAAM,Q,cAAhFrF,mBAAA,CA8BM,OA9BNqZ,YA8BM,I,kBA7BJrZ,mBAAA,CA4BME,SAAA,QAAAmE,WAAA,CA3BuBrG,KAAA,CAAAmP,cAAc,CAACiM,cAAc,GAAhDE,OAAO,EAAEC,KAAK;yBADxBvZ,mBAAA,CA4BM;MA1BHG,GAAG,EAAEmZ,OAAO,CAAC/U,EAAE;MAChBjH,KAAK,EAAA4B,eAAA,EAAC,eAAe;QAAA,sBACWqa,KAAK,KAAKvb,KAAA,CAAAmP,cAAc,CAACiM,cAAc,CAAC/T,MAAM;MAAA;QAE9E1H,mBAAA,CAEM;MAFDL,KAAK,EAAA4B,eAAA,EAAC,iBAAiB,QAAeZ,QAAA,CAAAwL,cAAc,CAACwP,OAAO,CAACE,eAAe;2CAC/E7b,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,2B,qBAE1BK,mBAAA,CAkBM,OAlBN8b,YAkBM,GAjBJ9b,mBAAA,CAKM,OALN+b,YAKM,GAJJ/b,mBAAA,CAEO;MAFDL,KAAK,EAAA4B,eAAA,EAAC,OAAO,QAAeZ,QAAA,CAAAwL,cAAc,CAACwP,OAAO,CAACE,eAAe;wBACnElb,QAAA,CAAAmG,YAAY,CAAC6U,OAAO,CAACE,eAAe,0BAEzC7b,mBAAA,CAA+E,SAA/Egc,YAA+E,EAAApZ,gBAAA,CAA7CjC,QAAA,CAAAgR,cAAc,CAACgK,OAAO,CAACM,UAAU,kB,GAErEjc,mBAAA,CAUM,OAVNkc,YAUM,GATJlc,mBAAA,CAEI,KAFJmc,YAEI,G,8BADFnc,mBAAA,CAA4B,gBAApB,aAAW,sB,iBAAS,GAAC,GAAA4C,gBAAA,CAAG+Y,OAAO,CAACS,eAAe,iB,GAEhDT,OAAO,CAACU,eAAe,I,cAAhCha,mBAAA,CAEI,KAFJia,YAEI,G,8BADFtc,mBAAA,CAAsB,gBAAd,OAAK,sB,iBAAS,GAAC,GAAA4C,gBAAA,CAAGjC,QAAA,CAAAmG,YAAY,CAAC6U,OAAO,CAACU,eAAe,kB,wCAEvDV,OAAO,CAACY,aAAa,I,cAA9Bla,mBAAA,CAEI,KAFJma,YAEI,G,8BADFxc,mBAAA,CAAwB,gBAAhB,SAAO,sB,iBAAS,GAAC,GAAA4C,gBAAA,CAAG+Y,OAAO,CAACY,aAAa,iB;qDAM3Dla,mBAAA,CAGM,OAHNoa,YAGM,EAAA9a,MAAA,UAAAA,MAAA,SAFJ3B,mBAAA,CAAyC;IAAtCL,KAAK,EAAC;EAA2B,4BACpCK,mBAAA,CAAkC,WAA/B,6BAA2B,oB,WAKtCA,mBAAA,CASM,OATN0c,YASM,GARJ1c,mBAAA,CAGS;IAHD6C,IAAI,EAAC,QAAQ;IAAClD,KAAK,EAAC,mBAAmB;IAAE+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAmB,MAAA,IAAEzC,KAAA,CAAAkP,kBAAkB;oCACxEvP,mBAAA,CAAiC;IAA9BL,KAAK,EAAC;EAAmB,4B,iBAAK,SAEnC,E,IACAK,mBAAA,CAGS;IAHD6C,IAAI,EAAC,QAAQ;IAAClD,KAAK,EAAC,iBAAiB;IAAE+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAAgc,qBAAA,IAAAhc,QAAA,CAAAgc,qBAAA,IAAA/a,IAAA,CAAqB;oCACzE5B,mBAAA,CAAoC;IAAjCL,KAAK,EAAC;EAAsB,4B,iBAAK,WAEtC,E,iDAMR2B,mBAAA,wBAA2B,EAChBjB,KAAA,CAAAuc,eAAe,IAAIvc,KAAA,CAAAwc,wBAAwB,I,cAAtDxa,mBAAA,CA2CM,OA3CNya,YA2CM,GA1CJ9c,mBAAA,CAyCM,OAzCN+c,YAyCM,GAxCJ/c,mBAAA,CAuCM,OAvCNgd,YAuCM,GAtCJhd,mBAAA,CAMM,OANNid,YAMM,G,8BALJjd,mBAAA,CAGK;IAHDL,KAAK,EAAC;EAAa,IACrBK,mBAAA,CAAoD;IAAjDL,KAAK,EAAC;EAAsC,I,iBAAK,kBAEtD,E,sBACAK,mBAAA,CAAgF;IAAxE6C,IAAI,EAAC,QAAQ;IAAClD,KAAK,EAAC,WAAW;IAAE+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAAuc,qBAAA,IAAAvc,QAAA,CAAAuc,qBAAA,IAAAtb,IAAA,CAAqB;QAEvE5B,mBAAA,CAgBM,OAhBNmd,YAgBM,G,8BAfJnd,mBAAA,CAGM;IAHDL,KAAK,EAAC;EAAqB,IAC9BK,mBAAA,CAAgD;IAA7CL,KAAK,EAAC;EAAkC,I,iBAAK,kGAElD,E,sBAEAK,mBAAA,CAOM,OAPNod,YAOM,G,8BANJpd,mBAAA,CAAiC,gBAAzB,kBAAgB,sBACxBA,mBAAA,CAIK,MAJLqd,YAIK,GAHHrd,mBAAA,CAAuF,a,8BAAnFA,mBAAA,CAAgC,gBAAxB,iBAAe,sB,iBAAS,GAAC,GAAA4C,gBAAA,CAAGvC,KAAA,CAAAwc,wBAAwB,CAACvS,cAAc,iB,GAC/EtK,mBAAA,CAAqF,a,8BAAjFA,mBAAA,CAA+B,gBAAvB,gBAAc,sB,iBAAS,GAAC,GAAA4C,gBAAA,CAAGvC,KAAA,CAAAwc,wBAAwB,CAAC5V,aAAa,iB,GAC7EjH,mBAAA,CAA4E,a,8BAAxEA,mBAAA,CAAwB,gBAAhB,SAAO,sB,iBAAS,GAAC,GAAA4C,gBAAA,CAAGvC,KAAA,CAAAwc,wBAAwB,CAAChS,WAAW,iB,SAM1E7K,mBAAA,CAaM,OAbNsd,YAaM,GAZJtd,mBAAA,CAGS;IAHD6C,IAAI,EAAC,QAAQ;IAAClD,KAAK,EAAC,mBAAmB;IAAE+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAAuc,qBAAA,IAAAvc,QAAA,CAAAuc,qBAAA,IAAAtb,IAAA,CAAqB;IAAGmC,QAAQ,EAAE1D,KAAA,CAAAkd,eAAe,CAACnb;oCACxGpC,mBAAA,CAAiC;IAA9BL,KAAK,EAAC;EAAmB,4B,iBAAK,UAEnC,E,iCACAK,mBAAA,CAOS;IAPD6C,IAAI,EAAC,QAAQ;IAAClD,KAAK,EAAC,gBAAgB;IAAE+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAA6c,kBAAA,IAAA7c,QAAA,CAAA6c,kBAAA,IAAA5b,IAAA,CAAkB;IAAGmC,QAAQ,EAAE1D,KAAA,CAAAkd,eAAe,CAACnb;oCAClGpC,mBAAA,CAAwC;IAArCL,KAAK,EAAC;EAA0B,6BACvBU,KAAA,CAAAkd,eAAe,CAACnb,OAAO,I,cAAnCC,mBAAA,CAGO,QAAAob,YAAA,EAAA9b,MAAA,UAAAA,MAAA,SAFL3B,mBAAA,CAA2C;IAAxCL,KAAK,EAAC;EAA6B,4B,iBAAK,gBAE7C,E,qBACA0C,mBAAA,CAAkC,QAAAqb,YAAA,EAArB,gBAAc,G,6EAOrCpc,mBAAA,yBAA4B,EACjBjB,KAAA,CAAAsd,gBAAgB,IAAItd,KAAA,CAAAud,yBAAyB,I,cAAxDvb,mBAAA,CA8CM,OA9CNwb,YA8CM,GA7CJ7d,mBAAA,CA4CM,OA5CN8d,YA4CM,GA3CJ9d,mBAAA,CA0CM,OA1CN+d,YA0CM,GAzCJ/d,mBAAA,CAMM,OANNge,YAMM,G,8BALJhe,mBAAA,CAGK;IAHDL,KAAK,EAAC;EAAa,IACrBK,mBAAA,CAAqD;IAAlDL,KAAK,EAAC;EAAuC,I,iBAAK,mBAEvD,E,sBACAK,mBAAA,CAAiF;IAAzE6C,IAAI,EAAC,QAAQ;IAAClD,KAAK,EAAC,WAAW;IAAE+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAAsd,sBAAA,IAAAtd,QAAA,CAAAsd,sBAAA,IAAArc,IAAA,CAAsB;QAExE5B,mBAAA,CAmBM,OAnBNke,YAmBM,G,8BAlBJle,mBAAA,CAGM;IAHDL,KAAK,EAAC;EAAkB,IAC3BK,mBAAA,CAAuC;IAApCL,KAAK,EAAC;EAAyB,I,iBAAK,qJAEzC,E,sBAEAK,mBAAA,CAOM,OAPNme,YAOM,G,8BANJne,mBAAA,CAAiC,gBAAzB,kBAAgB,sBACxBA,mBAAA,CAIK,MAJLoe,YAIK,GAHHpe,mBAAA,CAAwF,a,8BAApFA,mBAAA,CAAgC,gBAAxB,iBAAe,sB,iBAAS,GAAC,GAAA4C,gBAAA,CAAGvC,KAAA,CAAAud,yBAAyB,CAACtT,cAAc,iB,GAChFtK,mBAAA,CAAsF,a,8BAAlFA,mBAAA,CAA+B,gBAAvB,gBAAc,sB,iBAAS,GAAC,GAAA4C,gBAAA,CAAGvC,KAAA,CAAAud,yBAAyB,CAAC3W,aAAa,iB,GAC9EjH,mBAAA,CAA6E,a,8BAAzEA,mBAAA,CAAwB,gBAAhB,SAAO,sB,iBAAS,GAAC,GAAA4C,gBAAA,CAAGvC,KAAA,CAAAud,yBAAyB,CAAC/S,WAAW,iB,OAI9DxK,KAAA,CAAAge,gBAAgB,CAACC,KAAK,I,cAAjCjc,mBAAA,CAGM,OAHNkc,YAGM,G,8BAFJve,mBAAA,CAAgD;IAA7CL,KAAK,EAAC;EAAkC,6B,iBAAK,GAChD,GAAAiD,gBAAA,CAAGvC,KAAA,CAAAge,gBAAgB,CAACC,KAAK,iB,0CAG7Bte,mBAAA,CAaM,OAbNwe,YAaM,GAZJxe,mBAAA,CAGS;IAHD6C,IAAI,EAAC,QAAQ;IAAClD,KAAK,EAAC,mBAAmB;IAAE+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAAsd,sBAAA,IAAAtd,QAAA,CAAAsd,sBAAA,IAAArc,IAAA,CAAsB;IAAGmC,QAAQ,EAAE1D,KAAA,CAAAge,gBAAgB,CAACjc;oCAC1GpC,mBAAA,CAAiC;IAA9BL,KAAK,EAAC;EAAmB,4B,iBAAK,UAEnC,E,iCACAK,mBAAA,CAOS;IAPD6C,IAAI,EAAC,QAAQ;IAAClD,KAAK,EAAC,iBAAiB;IAAE+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAA8d,mBAAA,IAAA9d,QAAA,CAAA8d,mBAAA,IAAA7c,IAAA,CAAmB;IAAGmC,QAAQ,EAAE1D,KAAA,CAAAge,gBAAgB,CAACjc;oCACrGpC,mBAAA,CAAwC;IAArCL,KAAK,EAAC;EAA0B,6BACvBU,KAAA,CAAAge,gBAAgB,CAACjc,OAAO,I,cAApCC,mBAAA,CAGO,QAAAqc,YAAA,EAAA/c,MAAA,UAAAA,MAAA,SAFL3B,mBAAA,CAA2C;IAAxCL,KAAK,EAAC;EAA6B,4B,iBAAK,gBAE7C,E,qBACA0C,mBAAA,CAAmC,QAAAsc,YAAA,EAAtB,iBAAe,G,kGAU5Crd,mBAAA,iBAAoB,EACTjB,KAAA,CAAAue,cAAc,IAAIve,KAAA,CAAAwe,aAAa,I,cAA1Cxc,mBAAA,CA8FM;;IA9FsC1C,KAAK,EAAC,yBAAyB;IAACG,QAAQ,EAAC,IAAI;IAACF,KAA0C,EAA1C;MAAA;IAAA,CAA0C;IAAE8B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAwN,cAAA,KAAAvN,IAAA,KAAOjB,QAAA,CAAAme,eAAA,IAAAne,QAAA,CAAAme,eAAA,IAAAld,IAAA,CAAe;MAC/J5B,mBAAA,CA4FM,OA5FN+e,YA4FM,GA3FJ/e,mBAAA,CA0FM,OA1FNgf,YA0FM,GAzFJhf,mBAAA,CAsBM,OAtBNif,YAsBM,GArBJjf,mBAAA,CAGK,MAHLkf,YAGK,G,8BAFHlf,mBAAA,CAAiC;IAA9BL,KAAK,EAAC;EAAmB,6B,iBAAK,GACjC,GAAAiD,gBAAA,CAAGvC,KAAA,CAAAwe,aAAa,CAAC5J,aAAa,iB,GAEhCjV,mBAAA,CAgBM,OAhBNmf,YAgBM,GAfJnf,mBAAA,CAOS;IANP6C,IAAI,EAAC,QAAQ;IACblD,KAAK,EAAC,mCAAmC;IACxC+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAmB,MAAA,IAAEnC,QAAA,CAAAoV,gBAAgB,CAAC1V,KAAA,CAAAwe,aAAa;IACrC9a,QAAQ,GAAG1D,KAAA,CAAAyU,YAAY,CAACzU,KAAA,CAAAwe,aAAa,CAACjY,EAAE,KAAKvG,KAAA,CAAA+e,mBAAmB;IACjExb,KAAK,EAAC;oCACN5D,mBAAA,CAA+B;IAA5BL,KAAK,EAAC;EAAiB,2B,iCAE5BK,mBAAA,CAMS;IALP6C,IAAI,EAAC,QAAQ;IACblD,KAAK,EAAC,2BAA2B;IAChC+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAAme,eAAA,IAAAne,QAAA,CAAAme,eAAA,IAAAld,IAAA,CAAe;IACvB,YAAU,EAAC,OAAO;IAClBgC,KAAK,EAAC;UAIZ5D,mBAAA,CA0CM,OA1CNqf,YA0CM,GAzCJrf,mBAAA,CAwCM,OAxCNsf,YAwCM,GAvCJhe,mBAAA,+BAAkC,EAE1BjB,KAAA,CAAAyU,YAAY,CAACzU,KAAA,CAAAwe,aAAa,CAACjY,EAAE,MAAMvG,KAAA,CAAA+e,mBAAmB,I,cAD9D/c,mBAAA,CAQE;;IANC0S,GAAG,EAAE1U,KAAA,CAAAyU,YAAY,CAACzU,KAAA,CAAAwe,aAAa,CAACjY,EAAE;IAClCoO,GAAG,EAAE3U,KAAA,CAAAwe,aAAa,CAAC5J,aAAa;IACjCtV,KAAK,EAAC,aAAa;IAClBuV,OAAK,EAAAvT,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAAwU,gBAAA,IAAAxU,QAAA,CAAAwU,gBAAA,IAAAvT,IAAA,CAAgB;IACvB2d,MAAI,EAAA5d,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAA6e,gBAAA,IAAA7e,QAAA,CAAA6e,gBAAA,IAAA5d,IAAA,CAAgB;IACvBQ,OAAO,EAAC;4DAIM/B,KAAA,CAAA+e,mBAAmB,IAAI/e,KAAA,CAAA+U,gBAAgB,CAACC,GAAG,CAAChV,KAAA,CAAAwe,aAAa,CAACjY,EAAE,K,cAA5EvE,mBAAA,CAQME,SAAA;IAAAC,GAAA;EAAA,IATNlB,mBAAA,mBAAsB,E,6eAYNjB,KAAA,CAAAof,eAAe,IAAIpf,KAAA,CAAAiV,eAAe,CAACD,GAAG,CAAChV,KAAA,CAAAwe,aAAa,CAACjY,EAAE,K,cAAvEvE,mBAAA,CASME,SAAA;IAAAC,GAAA;EAAA,IAVNlB,mBAAA,kBAAqB,EACrBtB,mBAAA,CASM,OATN0f,YASM,G,8BARJ1f,mBAAA,CAAsD;IAAnDL,KAAK,EAAC;EAAwC,6B,8BACjDK,mBAAA,CAAoD;IAA9CL,KAAK,EAAC;EAAY,GAAC,sBAAoB,sBAC7CK,mBAAA,CAKS;IAJPL,KAAK,EAAC,4BAA4B;IACjC+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAmB,MAAA,IAAEnC,QAAA,CAAA4U,iBAAiB,CAAClV,KAAA,CAAAwe,aAAa;IACtC9a,QAAQ,EAAE1D,KAAA,CAAA+e;oCACXpf,mBAAA,CAAgC;IAA7BL,KAAK,EAAC;EAAkB,4B,iBAAK,QAClC,E,qGAIF0C,mBAAA,CAGME,SAAA;IAAAC,GAAA;EAAA,IAJNlB,mBAAA,cAAiB,E,8BACjBtB,mBAAA,CAGM;IAHML,KAAK,EAAC;EAAmC,IACnDK,mBAAA,CAAuC;IAApCL,KAAK,EAAC;EAAyB,IAClCK,mBAAA,CAAoD;IAA9CL,KAAK,EAAC;EAAc,GAAC,oBAAkB,E,4EAInDK,mBAAA,CAsBM,OAtBN2f,YAsBM,GArBJ3f,mBAAA,CAoBM,OApBN4f,YAoBM,GAnBJ5f,mBAAA,CAMM,OANN6f,YAMM,GALJ7f,mBAAA,CAAqG,QAArG8f,YAAqG,EAAAld,gBAAA,CAAjEjC,QAAA,CAAA4T,0BAA0B,CAAClU,KAAA,CAAAwe,aAAa,CAAC5X,aAAa,mBAC1FjH,mBAAA,CAGQ,SAHR+f,YAGQ,EAAAnd,gBAAA,CAFHjC,QAAA,CAAAkV,cAAc,CAACxV,KAAA,CAAAwe,aAAa,CAAC/I,SAAS,KAAI,cACpC,GAAAlT,gBAAA,CAAGjC,QAAA,CAAAgM,UAAU,CAACtM,KAAA,CAAAwe,aAAa,CAACvI,UAAU,kB,GAGnDtW,mBAAA,CAWM,OAXNggB,YAWM,GAVJhgB,mBAAA,CAMS;IALP6C,IAAI,EAAC,QAAQ;IACblD,KAAK,EAAC,8BAA8B;IACnC+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAmB,MAAA,IAAEnC,QAAA,CAAAoV,gBAAgB,CAAC1V,KAAA,CAAAwe,aAAa;IACrC9a,QAAQ,GAAG1D,KAAA,CAAAyU,YAAY,CAACzU,KAAA,CAAAwe,aAAa,CAACjY,EAAE,KAAKvG,KAAA,CAAA+e;oCAC9Cpf,mBAAA,CAAoC;IAAjCL,KAAK,EAAC;EAAsB,4B,iBAAK,WACtC,E,iCACAK,mBAAA,CAES;IAFD6C,IAAI,EAAC,QAAQ;IAAClD,KAAK,EAAC,mBAAmB;IAAE+B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEjB,QAAA,CAAAme,eAAA,IAAAne,QAAA,CAAAme,eAAA,IAAAld,IAAA,CAAe;oCACrE5B,mBAAA,CAAiC;IAA9BL,KAAK,EAAC;EAAmB,4B,iBAAK,QACnC,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}