{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport documentRequestService from '@/services/documentRequestService';\nimport clientAuthService from '@/services/clientAuthService';\nexport default {\n  name: 'BarangayClearanceRequest',\n  data() {\n    return {\n      currentStep: 1,\n      submitting: false,\n      purposeCategories: [],\n      paymentMethods: [],\n      baseFee: 0.00,\n      totalFee: 0.00,\n      formData: {\n        document_type_id: 2,\n        // Barangay Clearance\n        purpose_category_id: '',\n        purpose_details: '',\n        has_pending_cases: false,\n        pending_cases_details: '',\n        payment_method_id: '',\n        agree_to_terms: false\n      },\n      uploadedFiles: {\n        government_id: null,\n        proof_of_residency: null,\n        cedula: null\n      },\n      uploadErrors: {},\n      maxFileSize: 5 * 1024 * 1024,\n      // 5MB\n      allowedFileTypes: ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'],\n      clientData: null // Fresh profile data\n    };\n  },\n  computed: {\n    // Keep the old method as fallback\n    cachedClientData() {\n      return clientAuthService.getCurrentUser();\n    }\n  },\n  async mounted() {\n    await this.loadFormData();\n  },\n  methods: {\n    async loadFormData() {\n      try {\n        // Load fresh profile data first\n        console.log('Loading fresh profile data...');\n        const profileResponse = await clientAuthService.getProfile();\n        if (profileResponse.success) {\n          this.clientData = profileResponse.data;\n          console.log('Fresh profile data loaded:', this.clientData);\n        } else {\n          // Fallback to cached data\n          this.clientData = this.cachedClientData;\n          console.log('Using cached profile data:', this.clientData);\n        }\n        const [purposeResponse, paymentResponse] = await Promise.all([documentRequestService.getPurposeCategories(), documentRequestService.getPaymentMethods()]);\n        this.purposeCategories = purposeResponse.data || [];\n        this.paymentMethods = paymentResponse.data || [];\n      } catch (error) {\n        console.error('Error loading form data:', error);\n        // Fallback to cached data on error\n        this.clientData = this.cachedClientData;\n        this.$toast?.error('Failed to load some form data');\n      }\n    },\n    getFullName() {\n      // Try fresh data first, then fallback to cached data structure\n      const profile = this.clientData || this.clientData?.profile;\n      if (!profile) return 'N/A';\n      return `${profile.first_name || ''} ${profile.middle_name || ''} ${profile.last_name || ''}`.trim();\n    },\n    getFullAddress() {\n      // Try fresh data first, then fallback to cached data structure\n      const profile = this.clientData || this.clientData?.profile;\n      if (!profile) return 'Not provided';\n      const parts = [profile.house_number, profile.street, profile.subdivision, profile.barangay, profile.city_municipality || profile.city, profile.province].filter(Boolean);\n      return parts.length > 0 ? parts.join(', ') : 'Not provided';\n    },\n    getCivilStatusName(statusId) {\n      const statuses = {\n        1: 'Single',\n        2: 'Married',\n        3: 'Divorced',\n        4: 'Widowed',\n        5: 'Separated'\n      };\n      return statuses[statusId] || 'Not provided';\n    },\n    getResidencyDisplay() {\n      const profile = this.clientData || this.clientData?.profile;\n      if (!profile) return 'Not provided';\n      const years = profile.years_of_residency;\n      const months = profile.months_of_residency;\n      if (!years && !months) return 'Not provided';\n      const parts = [];\n      if (years) parts.push(`${years} year${years > 1 ? 's' : ''}`);\n      if (months) parts.push(`${months} month${months > 1 ? 's' : ''}`);\n      return parts.join(' and ');\n    },\n    formatDate(dateString) {\n      if (!dateString) return 'Not provided';\n      return new Date(dateString).toLocaleDateString();\n    },\n    formatCurrency(amount) {\n      return parseFloat(amount).toFixed(2);\n    },\n    canProceedToNextStep() {\n      switch (this.currentStep) {\n        case 1:\n          // Step 1: Required documents must be uploaded\n          return this.uploadedFiles.government_id && this.uploadedFiles.proof_of_residency;\n        case 2:\n          return this.formData.purpose_category_id && this.formData.purpose_details && this.formData.has_pending_cases !== null;\n        case 3:\n          return this.formData.payment_method_id;\n        default:\n          return true;\n      }\n    },\n    nextStep() {\n      if (this.canProceedToNextStep() && this.currentStep < 4) {\n        this.currentStep++;\n      }\n    },\n    previousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--;\n      }\n    },\n    onPurposeChange() {\n      // Could implement dynamic fee calculation based on purpose\n    },\n    redirectToCedula() {\n      // Redirect to Cedula application page\n      this.$router.push('/client/cedula-request');\n    },\n    selectPaymentMethod(methodId) {\n      this.formData.payment_method_id = methodId;\n    },\n    getPaymentIcon(methodCode) {\n      const icons = {\n        'CASH': 'fas fa-money-bill',\n        'PAYMONGO_CARD': 'fas fa-credit-card',\n        'PAYMONGO_GCASH': 'fab fa-google-pay',\n        'PAYMONGO_GRABPAY': 'fas fa-mobile-alt',\n        'PAYMONGO_PAYMAYA': 'fas fa-wallet'\n      };\n      return icons[methodCode] || 'fas fa-credit-card';\n    },\n    getPurposeCategoryName() {\n      const category = this.purposeCategories.find(c => c.id === this.formData.purpose_category_id);\n      return category?.category_name || '';\n    },\n    getPaymentMethodName() {\n      const method = this.paymentMethods.find(m => m.id === this.formData.payment_method_id);\n      return method?.method_name || '';\n    },\n    async handleSubmit() {\n      if (!this.formData.agree_to_terms) return;\n      try {\n        this.submitting = true;\n\n        // Prepare request data with proper validation\n        const requestData = {\n          document_type_id: parseInt(this.formData.document_type_id) || 2,\n          purpose_category_id: parseInt(this.formData.purpose_category_id) || 1,\n          purpose_details: this.formData.purpose_details && this.formData.purpose_details.length >= 10 ? this.formData.purpose_details : 'Barangay Clearance request for official purposes',\n          payment_method_id: parseInt(this.formData.payment_method_id) || null,\n          delivery_method: 'pickup',\n          priority: 'normal',\n          // Barangay Clearance specific fields (legally required)\n          has_pending_cases: Boolean(this.formData.has_pending_cases),\n          pending_cases_details: this.formData.pending_cases_details || null,\n          cedula_number: this.formData.cedula_number || null,\n          cedula_date_issued: this.formData.cedula_date_issued || null,\n          cedula_place_issued: this.formData.cedula_place_issued || null,\n          total_fee: this.totalFee || 150.00\n        };\n        console.log('Submitting request data:', requestData);\n        const response = await documentRequestService.submitRequest(requestData);\n        const requestId = response.data.id;\n        console.log('Request created with ID:', requestId);\n\n        // Upload documents if any are selected\n        const hasDocuments = this.uploadedFiles.government_id || this.uploadedFiles.proof_of_residency || this.uploadedFiles.cedula;\n        if (hasDocuments) {\n          console.log('Uploading documents...');\n          await this.uploadDocumentsToRequest(requestId);\n        }\n        this.$toast?.success('Request submitted successfully!');\n        this.$router.push({\n          name: 'RequestDetails',\n          params: {\n            id: requestId\n          }\n        });\n      } catch (error) {\n        console.error('Error submitting request:', error);\n        console.error('Error details:', {\n          status: error.response?.status,\n          data: error.response?.data,\n          message: error.message\n        });\n        let errorMessage = 'Failed to submit request';\n        if (error.response?.data?.message) {\n          errorMessage = error.response.data.message;\n        } else if (error.response?.data?.errors) {\n          errorMessage = error.response.data.errors.map(e => e.msg).join(', ');\n        } else if (error.message) {\n          errorMessage = error.message;\n        }\n        this.$toast?.error(errorMessage);\n      } finally {\n        this.submitting = false;\n      }\n    },\n    async uploadDocumentsToRequest(requestId) {\n      try {\n        const filesToUpload = [];\n\n        // Collect files to upload\n        Object.entries(this.uploadedFiles).forEach(([type, file]) => {\n          if (file) {\n            filesToUpload.push({\n              type,\n              file\n            });\n          }\n        });\n        if (filesToUpload.length === 0) {\n          return;\n        }\n\n        // Upload documents using the service\n        const uploadResponse = await documentRequestService.uploadDocuments(requestId, filesToUpload);\n        if (uploadResponse.success) {\n          console.log('Documents uploaded successfully:', uploadResponse.data);\n          this.$toast?.success(`${uploadResponse.data.total_uploaded} document(s) uploaded successfully`);\n        } else {\n          console.error('Document upload failed:', uploadResponse);\n          this.$toast?.warning('Request submitted but some documents failed to upload');\n        }\n      } catch (error) {\n        console.error('Document upload error:', error);\n        this.$toast?.warning('Request submitted but document upload failed. You can upload documents later.');\n      }\n    },\n    goBack() {\n      this.$router.push({\n        name: 'NewDocumentRequest'\n      });\n    },\n    updateProfile() {\n      // TODO: Navigate to profile update page\n      console.log('Update profile');\n    },\n    // File handling methods\n    triggerFileInput(fileType) {\n      // Convert snake_case to camelCase for ref names\n      const refNameMap = {\n        'government_id': 'governmentIdInput',\n        'proof_of_residency': 'proofOfResidencyInput',\n        'cedula': 'cedulaInput'\n      };\n      const inputRef = refNameMap[fileType];\n      if (!inputRef) {\n        console.error(`Unknown file type: ${fileType}`);\n        return;\n      }\n\n      // Add safety check for ref existence\n      if (this.$refs[inputRef]) {\n        this.$refs[inputRef].click();\n      } else {\n        console.warn(`File input ref '${inputRef}' not found`);\n        // Try again after next tick\n        this.$nextTick(() => {\n          if (this.$refs[inputRef]) {\n            this.$refs[inputRef].click();\n          } else {\n            console.error(`File input ref '${inputRef}' still not found after nextTick`);\n          }\n        });\n      }\n    },\n    handleFileSelect(event, fileType) {\n      const file = event.target.files[0];\n      if (file) {\n        this.validateAndSetFile(file, fileType);\n      }\n    },\n    handleFileDrop(event, fileType) {\n      const file = event.dataTransfer.files[0];\n      if (file) {\n        this.validateAndSetFile(file, fileType);\n      }\n    },\n    validateAndSetFile(file, fileType) {\n      // Clear previous errors (Vue 3 compatible)\n      delete this.uploadErrors[fileType];\n\n      // Validate file size\n      if (file.size > this.maxFileSize) {\n        this.uploadErrors[fileType] = 'File size must be less than 5MB';\n        this.$toast?.error(`File size must be less than 5MB`);\n        return;\n      }\n\n      // Validate file type\n      if (!this.allowedFileTypes.includes(file.type)) {\n        this.uploadErrors[fileType] = 'Only JPG, PNG, and PDF files are allowed';\n        this.$toast?.error('Only JPG, PNG, and PDF files are allowed');\n        return;\n      }\n\n      // Set the file (Vue 3 compatible)\n      this.uploadedFiles[fileType] = file;\n      this.$toast?.success(`${file.name} uploaded successfully`);\n    },\n    removeFile(fileType) {\n      // Vue 3 compatible reactive updates\n      this.uploadedFiles[fileType] = null;\n      delete this.uploadErrors[fileType];\n\n      // Convert snake_case to camelCase for ref names\n      const refNameMap = {\n        'government_id': 'governmentIdInput',\n        'proof_of_residency': 'proofOfResidencyInput',\n        'cedula': 'cedulaInput'\n      };\n      const inputRef = refNameMap[fileType];\n\n      // Clear the input with safety check\n      if (inputRef && this.$refs[inputRef]) {\n        this.$refs[inputRef].value = '';\n      } else {\n        console.warn(`File input ref '${inputRef}' not found during removal`);\n      }\n    },\n    showTerms() {\n      // TODO: Show terms and conditions modal\n      console.log('Show terms');\n    }\n  }\n};", "map": {"version": 3, "names": ["documentRequestService", "clientAuthService", "name", "data", "currentStep", "submitting", "purposeCategories", "paymentMethods", "baseFee", "totalFee", "formData", "document_type_id", "purpose_category_id", "purpose_details", "has_pending_cases", "pending_cases_details", "payment_method_id", "agree_to_terms", "uploadedFiles", "government_id", "proof_of_residency", "cedula", "uploadErrors", "maxFileSize", "allowedFileTypes", "clientData", "computed", "cachedClientData", "getCurrentUser", "mounted", "loadFormData", "methods", "console", "log", "profileResponse", "getProfile", "success", "purposeResponse", "paymentResponse", "Promise", "all", "getPurposeCategories", "getPaymentMethods", "error", "$toast", "getFullName", "profile", "first_name", "middle_name", "last_name", "trim", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parts", "house_number", "street", "subdivision", "barangay", "city_municipality", "city", "province", "filter", "Boolean", "length", "join", "getCivilStatusName", "statusId", "statuses", "getResidencyDisplay", "years", "years_of_residency", "months", "months_of_residency", "push", "formatDate", "dateString", "Date", "toLocaleDateString", "formatCurrency", "amount", "parseFloat", "toFixed", "canProceedToNextStep", "nextStep", "previousStep", "onPurposeChange", "redirectToCedula", "$router", "selectPaymentMethod", "methodId", "getPaymentIcon", "methodCode", "icons", "getPurposeCategoryName", "category", "find", "c", "id", "category_name", "getPaymentMethodName", "method", "m", "method_name", "handleSubmit", "requestData", "parseInt", "delivery_method", "priority", "cedula_number", "cedula_date_issued", "cedula_place_issued", "total_fee", "response", "submitRequest", "requestId", "hasDocuments", "uploadDocumentsToRequest", "params", "status", "message", "errorMessage", "errors", "map", "e", "msg", "filesToUpload", "Object", "entries", "for<PERSON>ach", "type", "file", "uploadResponse", "uploadDocuments", "total_uploaded", "warning", "goBack", "updateProfile", "triggerFileInput", "fileType", "refNameMap", "inputRef", "$refs", "click", "warn", "$nextTick", "handleFileSelect", "event", "target", "files", "validateAndSetFile", "handleFileDrop", "dataTransfer", "size", "includes", "removeFile", "value", "showTerms"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\BarangayClearanceRequest.vue"], "sourcesContent": ["<template>\n  <div class=\"barangay-clearance-request\">\n    <!-- Header -->\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <div class=\"header-main\">\n          <h1 class=\"page-title\">\n            <i class=\"fas fa-certificate\"></i>\n            Barangay Clearance Request\n          </h1>\n          <p class=\"page-description\">\n            Apply for your Barangay Clearance certificate online\n          </p>\n        </div>\n        <button class=\"back-btn\" @click=\"goBack\">\n          <i class=\"fas fa-arrow-left\"></i>\n          Back\n        </button>\n      </div>\n    </div>\n\n    <!-- Progress Steps -->\n    <div class=\"progress-steps\">\n      <div class=\"step\" :class=\"{ active: currentStep >= 1, completed: currentStep > 1 }\">\n        <div class=\"step-number\">1</div>\n        <span class=\"step-label\">Upload Documents</span>\n      </div>\n      <div class=\"step\" :class=\"{ active: currentStep >= 2, completed: currentStep > 2 }\">\n        <div class=\"step-number\">2</div>\n        <span class=\"step-label\">Purpose & Details</span>\n      </div>\n      <div class=\"step\" :class=\"{ active: currentStep >= 3, completed: currentStep > 3 }\">\n        <div class=\"step-number\">3</div>\n        <span class=\"step-label\">Payment</span>\n      </div>\n      <div class=\"step\" :class=\"{ active: currentStep >= 4 }\">\n        <div class=\"step-number\">4</div>\n        <span class=\"step-label\">Review & Submit</span>\n      </div>\n    </div>\n\n    <!-- Form Container -->\n    <div class=\"form-container\">\n      <form @submit.prevent=\"handleSubmit\">\n        \n        <!-- Step 1: Personal Information -->\n        <div v-if=\"currentStep === 1\" class=\"form-step\">\n          <div class=\"step-header\">\n            <h2>Personal Information</h2>\n            <p>Your profile information will be used for this request</p>\n          </div>\n\n          <div class=\"profile-preview\">\n            <div class=\"profile-card\">\n              <div class=\"profile-info\">\n                <h3>{{ getFullName() }}</h3>\n                <div class=\"info-grid\">\n                  <div class=\"info-item\">\n                    <label>Email:</label>\n                    <span>{{ (clientData?.email || clientData?.profile?.email) || 'Not provided' }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Phone:</label>\n                    <span>{{ (clientData?.phone_number || clientData?.profile?.phone_number) || 'Not provided' }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Address:</label>\n                    <span>{{ getFullAddress() }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Date of Birth:</label>\n                    <span>{{ formatDate(clientData?.birth_date || clientData?.profile?.birth_date) }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Gender:</label>\n                    <span>{{ (clientData?.gender || clientData?.profile?.gender) || 'Not provided' }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Civil Status:</label>\n                    <span>{{ getCivilStatusName(clientData?.civil_status_id || clientData?.profile?.civil_status_id) }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Nationality:</label>\n                    <span>{{ (clientData?.nationality || clientData?.profile?.nationality) || 'Not provided' }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Years of Residency:</label>\n                    <span>{{ getResidencyDisplay() }}</span>\n                  </div>\n                </div>\n              </div>\n              <div class=\"profile-actions\">\n                <button type=\"button\" class=\"update-profile-btn\" @click=\"updateProfile\">\n                  <i class=\"fas fa-edit\"></i>\n                  Update Profile\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <!-- Required Documents Upload -->\n          <div class=\"form-section\">\n            <h3><i class=\"fas fa-upload\"></i> Required Documents</h3>\n            <p class=\"section-description\">Please upload the following required documents as per government regulations:</p>\n\n            <!-- Valid Government ID -->\n            <div class=\"document-upload-group\">\n              <label class=\"document-label\">\n                <i class=\"fas fa-id-card\"></i>\n                Valid Government ID *\n                <span class=\"document-info\">(Driver's License, Voter's ID, Passport, etc.)</span>\n              </label>\n              <div class=\"file-upload-area\" @click=\"triggerFileInput('government_id')\" @dragover.prevent @drop.prevent=\"handleFileDrop($event, 'government_id')\">\n                <input\n                  ref=\"governmentIdInput\"\n                  type=\"file\"\n                  accept=\"image/*,.pdf\"\n                  @change=\"handleFileSelect($event, 'government_id')\"\n                  style=\"display: none\"\n                  required\n                />\n                <div v-if=\"!uploadedFiles.government_id\" class=\"upload-placeholder\">\n                  <i class=\"fas fa-cloud-upload-alt\"></i>\n                  <p>Click to upload or drag and drop</p>\n                  <small>JPG, PNG, PDF (Max 5MB)</small>\n                </div>\n                <div v-else class=\"uploaded-file\">\n                  <i class=\"fas fa-file-check\"></i>\n                  <span>{{ uploadedFiles.government_id.name }}</span>\n                  <button type=\"button\" @click.stop=\"removeFile('government_id')\" class=\"remove-file\">\n                    <i class=\"fas fa-times\"></i>\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <!-- Proof of Residency -->\n            <div class=\"document-upload-group\">\n              <label class=\"document-label\">\n                <i class=\"fas fa-home\"></i>\n                Proof of Residency *\n                <span class=\"document-info\">(Utility Bill, Lease Agreement, Barangay Certificate)</span>\n              </label>\n              <div class=\"file-upload-area\" @click=\"triggerFileInput('proof_of_residency')\" @dragover.prevent @drop.prevent=\"handleFileDrop($event, 'proof_of_residency')\">\n                <input\n                  ref=\"proofOfResidencyInput\"\n                  type=\"file\"\n                  accept=\"image/*,.pdf\"\n                  @change=\"handleFileSelect($event, 'proof_of_residency')\"\n                  style=\"display: none\"\n                  required\n                />\n                <div v-if=\"!uploadedFiles.proof_of_residency\" class=\"upload-placeholder\">\n                  <i class=\"fas fa-cloud-upload-alt\"></i>\n                  <p>Click to upload or drag and drop</p>\n                  <small>JPG, PNG, PDF (Max 5MB)</small>\n                </div>\n                <div v-else class=\"uploaded-file\">\n                  <i class=\"fas fa-file-check\"></i>\n                  <span>{{ uploadedFiles.proof_of_residency.name }}</span>\n                  <button type=\"button\" @click.stop=\"removeFile('proof_of_residency')\" class=\"remove-file\">\n                    <i class=\"fas fa-times\"></i>\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <!-- Community Tax Certificate (Optional) -->\n            <div class=\"document-upload-group\">\n              <label class=\"document-label\">\n                <i class=\"fas fa-certificate\"></i>\n                Community Tax Certificate (Cedula)\n                <span class=\"document-info optional\">(Optional - if available)</span>\n              </label>\n              <div class=\"file-upload-area\" @click=\"triggerFileInput('cedula')\" @dragover.prevent @drop.prevent=\"handleFileDrop($event, 'cedula')\">\n                <input\n                  ref=\"cedulaInput\"\n                  type=\"file\"\n                  accept=\"image/*,.pdf\"\n                  @change=\"handleFileSelect($event, 'cedula')\"\n                  style=\"display: none\"\n                />\n                <div v-if=\"!uploadedFiles.cedula\" class=\"upload-placeholder\">\n                  <i class=\"fas fa-cloud-upload-alt\"></i>\n                  <p>Click to upload or drag and drop</p>\n                  <small>JPG, PNG, PDF (Max 5MB)</small>\n                </div>\n                <div v-else class=\"uploaded-file\">\n                  <i class=\"fas fa-file-check\"></i>\n                  <span>{{ uploadedFiles.cedula.name }}</span>\n                  <button type=\"button\" @click.stop=\"removeFile('cedula')\" class=\"remove-file\">\n                    <i class=\"fas fa-times\"></i>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Legal Notice -->\n          <div class=\"form-section\">\n            <div class=\"legal-notice\">\n              <h3><i class=\"fas fa-info-circle\"></i> Important Notice</h3>\n              <p>This barangay clearance certifies that you are a resident in good standing with no pending legal cases or disputes within the barangay. Only information required by law is collected.</p>\n              <div class=\"data-privacy-note\">\n                <small><i class=\"fas fa-shield-alt\"></i> Your personal information is protected under the Data Privacy Act of 2012.</small>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Step 2: Purpose and Details -->\n        <div v-if=\"currentStep === 2\" class=\"form-step\">\n          <div class=\"step-header\">\n            <h2>Purpose and Additional Details</h2>\n            <p>Please provide the purpose and any additional information</p>\n          </div>\n\n          <div class=\"form-section\">\n            <div class=\"form-group\">\n              <label for=\"purpose_category\">Purpose Category *</label>\n              <select\n                id=\"purpose_category\"\n                v-model=\"formData.purpose_category_id\"\n                required\n                @change=\"onPurposeChange\"\n              >\n                <option value=\"\">Select purpose</option>\n                <option\n                  v-for=\"category in purposeCategories\"\n                  :key=\"category.id\"\n                  :value=\"category.id\"\n                >\n                  {{ category.category_name }}\n                </option>\n              </select>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"purpose_details\">Purpose Details *</label>\n              <textarea\n                id=\"purpose_details\"\n                v-model=\"formData.purpose_details\"\n                rows=\"3\"\n                required\n                placeholder=\"Please provide specific details about the purpose of this clearance\"\n              ></textarea>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"pending_cases\">Pending Cases Declaration *</label>\n              <div class=\"radio-group\">\n                <label class=\"radio-option\">\n                  <input\n                    type=\"radio\"\n                    v-model=\"formData.has_pending_cases\"\n                    :value=\"false\"\n                    required\n                  />\n                  <span class=\"radio-custom\"></span>\n                  No pending cases\n                </label>\n                <label class=\"radio-option\">\n                  <input\n                    type=\"radio\"\n                    v-model=\"formData.has_pending_cases\"\n                    :value=\"true\"\n                    required\n                  />\n                  <span class=\"radio-custom\"></span>\n                  Has pending cases\n                </label>\n              </div>\n            </div>\n\n            <div v-if=\"formData.has_pending_cases\" class=\"form-group\">\n              <label for=\"pending_cases_details\">Pending Cases Details *</label>\n              <textarea\n                id=\"pending_cases_details\"\n                v-model=\"formData.pending_cases_details\"\n                rows=\"3\"\n                required\n                placeholder=\"Please provide details about pending cases\"\n              ></textarea>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"voter_registration\">Voter Registration Status</label>\n              <div class=\"radio-group\">\n                <label class=\"radio-option\">\n                  <input\n                    type=\"radio\"\n                    v-model=\"formData.is_registered_voter\"\n                    :value=\"true\"\n                  />\n                  <span class=\"radio-custom\"></span>\n                  Registered voter\n                </label>\n                <label class=\"radio-option\">\n                  <input\n                    type=\"radio\"\n                    v-model=\"formData.is_registered_voter\"\n                    :value=\"false\"\n                  />\n                  <span class=\"radio-custom\"></span>\n                  Not registered\n                </label>\n              </div>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"additional_notes\">Additional Notes</label>\n              <textarea\n                id=\"additional_notes\"\n                v-model=\"formData.additional_notes\"\n                rows=\"2\"\n                placeholder=\"Any additional information or special requests\"\n              ></textarea>\n            </div>\n          </div>\n        </div>\n\n        <!-- Step 3: Payment Method -->\n        <div v-if=\"currentStep === 3\" class=\"form-step\">\n          <div class=\"step-header\">\n            <h2>Payment Information</h2>\n            <p>Choose your preferred payment method</p>\n          </div>\n\n          <!-- Fee Summary -->\n          <div class=\"fee-summary\">\n            <div class=\"fee-card\">\n              <h3>Fee Breakdown</h3>\n              <div class=\"fee-items\">\n                <div class=\"fee-item\">\n                  <span>Barangay Clearance Fee</span>\n                  <span>₱{{ formatCurrency(baseFee) }}</span>\n                </div>\n                <div class=\"fee-item total\">\n                  <span>Total Amount</span>\n                  <span>₱{{ formatCurrency(totalFee) }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Payment Methods -->\n          <div class=\"form-section\">\n            <h3>Select Payment Method</h3>\n            <div class=\"payment-methods\">\n              <div\n                v-for=\"method in paymentMethods\"\n                :key=\"method.id\"\n                class=\"payment-option\"\n                :class=\"{ selected: formData.payment_method_id === method.id }\"\n                @click=\"selectPaymentMethod(method.id)\"\n              >\n                <div class=\"payment-icon\">\n                  <i :class=\"getPaymentIcon(method.method_code)\"></i>\n                </div>\n                <div class=\"payment-info\">\n                  <h4>{{ method.method_name }}</h4>\n                  <p v-if=\"method.description\">{{ method.description }}</p>\n                </div>\n                <div class=\"payment-radio\">\n                  <input\n                    type=\"radio\"\n                    :value=\"method.id\"\n                    v-model=\"formData.payment_method_id\"\n                    required\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Step 4: Review and Submit -->\n        <div v-if=\"currentStep === 4\" class=\"form-step\">\n          <div class=\"step-header\">\n            <h2>Review Your Request</h2>\n            <p>Please review all information before submitting</p>\n          </div>\n\n          <div class=\"review-sections\">\n            <!-- Personal Information Review -->\n            <div class=\"review-section\">\n              <h3>Personal Information</h3>\n              <div class=\"review-grid\">\n                <div class=\"review-item\">\n                  <label>Full Name:</label>\n                  <span>{{ getFullName() }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Address:</label>\n                  <span>{{ getFullAddress() }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Phone:</label>\n                  <span>{{ (clientData?.phone_number || clientData?.profile?.phone_number) || 'Not provided' }}</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- Cedula Information Review -->\n            <div class=\"review-section\">\n              <h3>Cedula Information</h3>\n              <div class=\"review-grid\">\n                <div class=\"review-item\">\n                  <label>Cedula Number:</label>\n                  <span>{{ formData.cedula_number }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Date Issued:</label>\n                  <span>{{ formData.cedula_date_issued }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Place Issued:</label>\n                  <span>{{ formData.cedula_place_issued }}</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- Purpose Review -->\n            <div class=\"review-section\">\n              <h3>Purpose & Details</h3>\n              <div class=\"review-grid\">\n                <div class=\"review-item\">\n                  <label>Purpose:</label>\n                  <span>{{ getPurposeCategoryName() }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Details:</label>\n                  <span>{{ formData.purpose_details }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Pending Cases:</label>\n                  <span>{{ formData.has_pending_cases ? 'Yes' : 'No' }}</span>\n                </div>\n                <div v-if=\"formData.has_pending_cases && formData.pending_cases_details\" class=\"review-item\">\n                  <label>Case Details:</label>\n                  <span>{{ formData.pending_cases_details }}</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- Payment Review -->\n            <div class=\"review-section\">\n              <h3>Payment Information</h3>\n              <div class=\"review-grid\">\n                <div class=\"review-item\">\n                  <label>Payment Method:</label>\n                  <span>{{ getPaymentMethodName() }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Total Amount:</label>\n                  <span class=\"amount\">₱{{ formatCurrency(totalFee) }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Terms and Conditions -->\n          <div class=\"terms-section\">\n            <label class=\"checkbox-option\">\n              <input\n                type=\"checkbox\"\n                v-model=\"formData.agree_to_terms\"\n                required\n              />\n              <span class=\"checkbox-custom\"></span>\n              I agree to the <a href=\"#\" @click.prevent=\"showTerms\">terms and conditions</a> and certify that all information provided is true and accurate.\n            </label>\n          </div>\n        </div>\n\n        <!-- Form Actions -->\n        <div class=\"form-actions\">\n          <button\n            v-if=\"currentStep > 1\"\n            type=\"button\"\n            class=\"btn-secondary\"\n            @click=\"previousStep\"\n          >\n            <i class=\"fas fa-chevron-left\"></i>\n            Previous\n          </button>\n          \n          <button\n            v-if=\"currentStep < 4\"\n            type=\"button\"\n            class=\"btn-primary\"\n            @click=\"nextStep\"\n            :disabled=\"!canProceedToNextStep()\"\n          >\n            Next\n            <i class=\"fas fa-chevron-right\"></i>\n          </button>\n          \n          <button\n            v-if=\"currentStep === 4\"\n            type=\"submit\"\n            class=\"btn-submit\"\n            :disabled=\"submitting || !formData.agree_to_terms\"\n          >\n            <template v-if=\"submitting\">\n              <i class=\"fas fa-spinner fa-spin\"></i>\n              Submitting...\n            </template>\n            <template v-else>\n              <i class=\"fas fa-paper-plane\"></i>\n              Submit Request\n            </template>\n          </button>\n        </div>\n      </form>\n    </div>\n  </div>\n</template>\n\n<script>\nimport documentRequestService from '@/services/documentRequestService';\nimport clientAuthService from '@/services/clientAuthService';\n\nexport default {\n  name: 'BarangayClearanceRequest',\n  data() {\n    return {\n      currentStep: 1,\n      submitting: false,\n      purposeCategories: [],\n      paymentMethods: [],\n      baseFee: 0.00,\n      totalFee: 0.00,\n      formData: {\n        document_type_id: 2, // Barangay Clearance\n        purpose_category_id: '',\n        purpose_details: '',\n        has_pending_cases: false,\n        pending_cases_details: '',\n        payment_method_id: '',\n        agree_to_terms: false\n      },\n      uploadedFiles: {\n        government_id: null,\n        proof_of_residency: null,\n        cedula: null\n      },\n      uploadErrors: {},\n      maxFileSize: 5 * 1024 * 1024, // 5MB\n      allowedFileTypes: ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'],\n      clientData: null, // Fresh profile data\n    };\n  },\n  computed: {\n    // Keep the old method as fallback\n    cachedClientData() {\n      return clientAuthService.getCurrentUser();\n    }\n  },\n  async mounted() {\n    await this.loadFormData();\n  },\n  methods: {\n    async loadFormData() {\n      try {\n        // Load fresh profile data first\n        console.log('Loading fresh profile data...');\n        const profileResponse = await clientAuthService.getProfile();\n        if (profileResponse.success) {\n          this.clientData = profileResponse.data;\n          console.log('Fresh profile data loaded:', this.clientData);\n        } else {\n          // Fallback to cached data\n          this.clientData = this.cachedClientData;\n          console.log('Using cached profile data:', this.clientData);\n        }\n\n        const [purposeResponse, paymentResponse] = await Promise.all([\n          documentRequestService.getPurposeCategories(),\n          documentRequestService.getPaymentMethods()\n        ]);\n\n        this.purposeCategories = purposeResponse.data || [];\n        this.paymentMethods = paymentResponse.data || [];\n\n      } catch (error) {\n        console.error('Error loading form data:', error);\n        // Fallback to cached data on error\n        this.clientData = this.cachedClientData;\n        this.$toast?.error('Failed to load some form data');\n      }\n    },\n\n    getFullName() {\n      // Try fresh data first, then fallback to cached data structure\n      const profile = this.clientData || this.clientData?.profile;\n      if (!profile) return 'N/A';\n      return `${profile.first_name || ''} ${profile.middle_name || ''} ${profile.last_name || ''}`.trim();\n    },\n\n    getFullAddress() {\n      // Try fresh data first, then fallback to cached data structure\n      const profile = this.clientData || this.clientData?.profile;\n      if (!profile) return 'Not provided';\n\n      const parts = [\n        profile.house_number,\n        profile.street,\n        profile.subdivision,\n        profile.barangay,\n        profile.city_municipality || profile.city,\n        profile.province\n      ].filter(Boolean);\n\n      return parts.length > 0 ? parts.join(', ') : 'Not provided';\n    },\n\n    getCivilStatusName(statusId) {\n      const statuses = {\n        1: 'Single',\n        2: 'Married',\n        3: 'Divorced',\n        4: 'Widowed',\n        5: 'Separated'\n      };\n      return statuses[statusId] || 'Not provided';\n    },\n\n    getResidencyDisplay() {\n      const profile = this.clientData || this.clientData?.profile;\n      if (!profile) return 'Not provided';\n\n      const years = profile.years_of_residency;\n      const months = profile.months_of_residency;\n\n      if (!years && !months) return 'Not provided';\n\n      const parts = [];\n      if (years) parts.push(`${years} year${years > 1 ? 's' : ''}`);\n      if (months) parts.push(`${months} month${months > 1 ? 's' : ''}`);\n\n      return parts.join(' and ');\n    },\n\n    formatDate(dateString) {\n      if (!dateString) return 'Not provided';\n      return new Date(dateString).toLocaleDateString();\n    },\n\n    formatCurrency(amount) {\n      return parseFloat(amount).toFixed(2);\n    },\n\n    canProceedToNextStep() {\n      switch (this.currentStep) {\n        case 1:\n          // Step 1: Required documents must be uploaded\n          return this.uploadedFiles.government_id && this.uploadedFiles.proof_of_residency;\n        case 2:\n          return this.formData.purpose_category_id &&\n                 this.formData.purpose_details &&\n                 this.formData.has_pending_cases !== null;\n        case 3:\n          return this.formData.payment_method_id;\n        default:\n          return true;\n      }\n    },\n\n    nextStep() {\n      if (this.canProceedToNextStep() && this.currentStep < 4) {\n        this.currentStep++;\n      }\n    },\n\n    previousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--;\n      }\n    },\n\n    onPurposeChange() {\n      // Could implement dynamic fee calculation based on purpose\n    },\n\n    redirectToCedula() {\n      // Redirect to Cedula application page\n      this.$router.push('/client/cedula-request');\n    },\n\n    selectPaymentMethod(methodId) {\n      this.formData.payment_method_id = methodId;\n    },\n\n    getPaymentIcon(methodCode) {\n      const icons = {\n        'CASH': 'fas fa-money-bill',\n        'PAYMONGO_CARD': 'fas fa-credit-card',\n        'PAYMONGO_GCASH': 'fab fa-google-pay',\n        'PAYMONGO_GRABPAY': 'fas fa-mobile-alt',\n        'PAYMONGO_PAYMAYA': 'fas fa-wallet'\n      };\n      return icons[methodCode] || 'fas fa-credit-card';\n    },\n\n    getPurposeCategoryName() {\n      const category = this.purposeCategories.find(c => c.id === this.formData.purpose_category_id);\n      return category?.category_name || '';\n    },\n\n    getPaymentMethodName() {\n      const method = this.paymentMethods.find(m => m.id === this.formData.payment_method_id);\n      return method?.method_name || '';\n    },\n\n    async handleSubmit() {\n      if (!this.formData.agree_to_terms) return;\n\n      try {\n        this.submitting = true;\n\n        // Prepare request data with proper validation\n        const requestData = {\n          document_type_id: parseInt(this.formData.document_type_id) || 2,\n          purpose_category_id: parseInt(this.formData.purpose_category_id) || 1,\n          purpose_details: this.formData.purpose_details && this.formData.purpose_details.length >= 10\n            ? this.formData.purpose_details\n            : 'Barangay Clearance request for official purposes',\n          payment_method_id: parseInt(this.formData.payment_method_id) || null,\n          delivery_method: 'pickup',\n          priority: 'normal',\n          // Barangay Clearance specific fields (legally required)\n          has_pending_cases: Boolean(this.formData.has_pending_cases),\n          pending_cases_details: this.formData.pending_cases_details || null,\n          cedula_number: this.formData.cedula_number || null,\n          cedula_date_issued: this.formData.cedula_date_issued || null,\n          cedula_place_issued: this.formData.cedula_place_issued || null,\n          total_fee: this.totalFee || 150.00\n        };\n\n        console.log('Submitting request data:', requestData);\n\n        const response = await documentRequestService.submitRequest(requestData);\n\n        const requestId = response.data.id;\n        console.log('Request created with ID:', requestId);\n\n        // Upload documents if any are selected\n        const hasDocuments = this.uploadedFiles.government_id ||\n                            this.uploadedFiles.proof_of_residency ||\n                            this.uploadedFiles.cedula;\n\n        if (hasDocuments) {\n          console.log('Uploading documents...');\n          await this.uploadDocumentsToRequest(requestId);\n        }\n\n        this.$toast?.success('Request submitted successfully!');\n        this.$router.push({\n          name: 'RequestDetails',\n          params: { id: requestId }\n        });\n\n      } catch (error) {\n        console.error('Error submitting request:', error);\n        console.error('Error details:', {\n          status: error.response?.status,\n          data: error.response?.data,\n          message: error.message\n        });\n\n        let errorMessage = 'Failed to submit request';\n        if (error.response?.data?.message) {\n          errorMessage = error.response.data.message;\n        } else if (error.response?.data?.errors) {\n          errorMessage = error.response.data.errors.map(e => e.msg).join(', ');\n        } else if (error.message) {\n          errorMessage = error.message;\n        }\n\n        this.$toast?.error(errorMessage);\n      } finally {\n        this.submitting = false;\n      }\n    },\n\n    async uploadDocumentsToRequest(requestId) {\n      try {\n        const filesToUpload = [];\n\n        // Collect files to upload\n        Object.entries(this.uploadedFiles).forEach(([type, file]) => {\n          if (file) {\n            filesToUpload.push({ type, file });\n          }\n        });\n\n        if (filesToUpload.length === 0) {\n          return;\n        }\n\n        // Upload documents using the service\n        const uploadResponse = await documentRequestService.uploadDocuments(requestId, filesToUpload);\n\n        if (uploadResponse.success) {\n          console.log('Documents uploaded successfully:', uploadResponse.data);\n          this.$toast?.success(`${uploadResponse.data.total_uploaded} document(s) uploaded successfully`);\n        } else {\n          console.error('Document upload failed:', uploadResponse);\n          this.$toast?.warning('Request submitted but some documents failed to upload');\n        }\n\n      } catch (error) {\n        console.error('Document upload error:', error);\n        this.$toast?.warning('Request submitted but document upload failed. You can upload documents later.');\n      }\n    },\n\n    goBack() {\n      this.$router.push({ name: 'NewDocumentRequest' });\n    },\n\n    updateProfile() {\n      // TODO: Navigate to profile update page\n      console.log('Update profile');\n    },\n\n    // File handling methods\n    triggerFileInput(fileType) {\n      // Convert snake_case to camelCase for ref names\n      const refNameMap = {\n        'government_id': 'governmentIdInput',\n        'proof_of_residency': 'proofOfResidencyInput',\n        'cedula': 'cedulaInput'\n      };\n\n      const inputRef = refNameMap[fileType];\n\n      if (!inputRef) {\n        console.error(`Unknown file type: ${fileType}`);\n        return;\n      }\n\n      // Add safety check for ref existence\n      if (this.$refs[inputRef]) {\n        this.$refs[inputRef].click();\n      } else {\n        console.warn(`File input ref '${inputRef}' not found`);\n        // Try again after next tick\n        this.$nextTick(() => {\n          if (this.$refs[inputRef]) {\n            this.$refs[inputRef].click();\n          } else {\n            console.error(`File input ref '${inputRef}' still not found after nextTick`);\n          }\n        });\n      }\n    },\n\n    handleFileSelect(event, fileType) {\n      const file = event.target.files[0];\n      if (file) {\n        this.validateAndSetFile(file, fileType);\n      }\n    },\n\n    handleFileDrop(event, fileType) {\n      const file = event.dataTransfer.files[0];\n      if (file) {\n        this.validateAndSetFile(file, fileType);\n      }\n    },\n\n    validateAndSetFile(file, fileType) {\n      // Clear previous errors (Vue 3 compatible)\n      delete this.uploadErrors[fileType];\n\n      // Validate file size\n      if (file.size > this.maxFileSize) {\n        this.uploadErrors[fileType] = 'File size must be less than 5MB';\n        this.$toast?.error(`File size must be less than 5MB`);\n        return;\n      }\n\n      // Validate file type\n      if (!this.allowedFileTypes.includes(file.type)) {\n        this.uploadErrors[fileType] = 'Only JPG, PNG, and PDF files are allowed';\n        this.$toast?.error('Only JPG, PNG, and PDF files are allowed');\n        return;\n      }\n\n      // Set the file (Vue 3 compatible)\n      this.uploadedFiles[fileType] = file;\n      this.$toast?.success(`${file.name} uploaded successfully`);\n    },\n\n    removeFile(fileType) {\n      // Vue 3 compatible reactive updates\n      this.uploadedFiles[fileType] = null;\n      delete this.uploadErrors[fileType];\n\n      // Convert snake_case to camelCase for ref names\n      const refNameMap = {\n        'government_id': 'governmentIdInput',\n        'proof_of_residency': 'proofOfResidencyInput',\n        'cedula': 'cedulaInput'\n      };\n\n      const inputRef = refNameMap[fileType];\n\n      // Clear the input with safety check\n      if (inputRef && this.$refs[inputRef]) {\n        this.$refs[inputRef].value = '';\n      } else {\n        console.warn(`File input ref '${inputRef}' not found during removal`);\n      }\n    },\n\n    showTerms() {\n      // TODO: Show terms and conditions modal\n      console.log('Show terms');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.barangay-clearance-request {\n  padding: 2rem;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n\n.page-header {\n  margin-bottom: 2rem;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: 2rem;\n}\n\n.page-title {\n  font-size: 1.75rem;\n  font-weight: 700;\n  color: #1a365d;\n  margin-bottom: 0.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.page-title i {\n  color: #3182ce;\n}\n\n.page-description {\n  font-size: 1rem;\n  color: #4a5568;\n  margin: 0;\n}\n\n.back-btn {\n  background: #e2e8f0;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.5rem;\n  color: #4a5568;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.back-btn:hover {\n  background: #cbd5e0;\n  color: #2d3748;\n}\n\n.progress-steps {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 3rem;\n  position: relative;\n}\n\n.progress-steps::before {\n  content: '';\n  position: absolute;\n  top: 1.5rem;\n  left: 25%;\n  right: 25%;\n  height: 2px;\n  background: #e2e8f0;\n  z-index: 1;\n}\n\n.step {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  flex: 1;\n  position: relative;\n  z-index: 2;\n}\n\n.step-number {\n  width: 3rem;\n  height: 3rem;\n  border-radius: 50%;\n  background: #e2e8f0;\n  color: #a0aec0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  transition: all 0.3s;\n}\n\n.step.active .step-number {\n  background: #3182ce;\n  color: white;\n}\n\n.step.completed .step-number {\n  background: #38a169;\n  color: white;\n}\n\n.step-label {\n  font-size: 0.875rem;\n  color: #718096;\n  text-align: center;\n}\n\n.step.active .step-label {\n  color: #3182ce;\n  font-weight: 500;\n}\n\n.form-container {\n  background: white;\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\n}\n\n.form-step {\n  min-height: 400px;\n}\n\n.step-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.step-header h2 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 0.5rem;\n}\n\n.step-header p {\n  color: #4a5568;\n  margin: 0;\n}\n\n.profile-preview {\n  margin-bottom: 2rem;\n}\n\n.profile-card {\n  background: #f7fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n}\n\n.profile-info h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n\n.info-item {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.info-item label {\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: #718096;\n}\n\n.info-item span {\n  color: #2d3748;\n}\n\n.update-profile-btn {\n  background: #3182ce;\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.5rem;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  transition: all 0.2s;\n}\n\n.update-profile-btn:hover {\n  background: #2c5aa0;\n}\n\n.form-section {\n  margin-bottom: 2rem;\n}\n\n.form-section h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.form-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.form-group label {\n  font-weight: 500;\n  color: #2d3748;\n  font-size: 0.875rem;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  padding: 0.75rem;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  transition: all 0.2s;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #3182ce;\n  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);\n}\n\n.radio-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.radio-option {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  cursor: pointer;\n  padding: 0.5rem;\n  border-radius: 0.5rem;\n  transition: all 0.2s;\n}\n\n.radio-option:hover {\n  background: #f7fafc;\n}\n\n.radio-option input[type=\"radio\"] {\n  display: none;\n}\n\n.radio-custom {\n  width: 1.25rem;\n  height: 1.25rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 50%;\n  position: relative;\n  transition: all 0.2s;\n}\n\n.radio-option input[type=\"radio\"]:checked + .radio-custom {\n  border-color: #3182ce;\n}\n\n.radio-option input[type=\"radio\"]:checked + .radio-custom::after {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 0.5rem;\n  height: 0.5rem;\n  background: #3182ce;\n  border-radius: 50%;\n}\n\n.fee-summary {\n  margin-bottom: 2rem;\n}\n\n.fee-card {\n  background: #f7fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n}\n\n.fee-card h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.fee-items {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.fee-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem 0;\n}\n\n.fee-item.total {\n  border-top: 1px solid #e2e8f0;\n  padding-top: 1rem;\n  font-weight: 600;\n  font-size: 1.125rem;\n  color: #1a365d;\n}\n\n.payment-methods {\n  display: grid;\n  gap: 1rem;\n}\n\n.payment-option {\n  border: 2px solid #e2e8f0;\n  border-radius: 0.75rem;\n  padding: 1rem;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.payment-option:hover {\n  border-color: #cbd5e0;\n}\n\n.payment-option.selected {\n  border-color: #3182ce;\n  background: #ebf8ff;\n}\n\n.payment-icon {\n  width: 3rem;\n  height: 3rem;\n  background: #f7fafc;\n  border-radius: 0.5rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.25rem;\n  color: #4a5568;\n}\n\n.payment-option.selected .payment-icon {\n  background: #3182ce;\n  color: white;\n}\n\n.payment-info {\n  flex: 1;\n}\n\n.payment-info h4 {\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 0.25rem;\n}\n\n.payment-info p {\n  color: #718096;\n  font-size: 0.875rem;\n  margin: 0;\n}\n\n.payment-radio input {\n  width: 1.25rem;\n  height: 1.25rem;\n}\n\n.review-sections {\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n  margin-bottom: 2rem;\n}\n\n.review-section {\n  background: #f7fafc;\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n}\n\n.review-section h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.review-grid {\n  display: grid;\n  gap: 1rem;\n}\n\n.review-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  padding: 0.5rem 0;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.review-item:last-child {\n  border-bottom: none;\n}\n\n.review-item label {\n  font-weight: 500;\n  color: #4a5568;\n  min-width: 120px;\n}\n\n.review-item span {\n  color: #2d3748;\n  text-align: right;\n  flex: 1;\n}\n\n.review-item .amount {\n  font-weight: 600;\n  color: #38a169;\n  font-size: 1.125rem;\n}\n\n.terms-section {\n  margin-bottom: 2rem;\n  padding: 1rem;\n  background: #fffaf0;\n  border: 1px solid #fed7aa;\n  border-radius: 0.5rem;\n}\n\n.checkbox-option {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  cursor: pointer;\n  line-height: 1.5;\n}\n\n.checkbox-option input[type=\"checkbox\"] {\n  display: none;\n}\n\n.checkbox-custom {\n  width: 1.25rem;\n  height: 1.25rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 0.25rem;\n  position: relative;\n  flex-shrink: 0;\n  margin-top: 0.125rem;\n  transition: all 0.2s;\n}\n\n.checkbox-option input[type=\"checkbox\"]:checked + .checkbox-custom {\n  border-color: #3182ce;\n  background: #3182ce;\n}\n\n.checkbox-option input[type=\"checkbox\"]:checked + .checkbox-custom::after {\n  content: '✓';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: white;\n  font-size: 0.875rem;\n  font-weight: bold;\n}\n\n.checkbox-option a {\n  color: #3182ce;\n  text-decoration: underline;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding-top: 2rem;\n  border-top: 1px solid #e2e8f0;\n}\n\n.btn-secondary,\n.btn-primary,\n.btn-submit {\n  padding: 0.75rem 2rem;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  border: none;\n}\n\n.btn-secondary {\n  background: #e2e8f0;\n  color: #4a5568;\n}\n\n.btn-secondary:hover {\n  background: #cbd5e0;\n}\n\n.btn-primary {\n  background: #3182ce;\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background: #2c5aa0;\n}\n\n.btn-submit {\n  background: #38a169;\n  color: white;\n}\n\n.btn-submit:hover:not(:disabled) {\n  background: #2f855a;\n}\n\n.btn-primary:disabled,\n.btn-submit:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n@media (max-width: 768px) {\n  .barangay-clearance-request {\n    padding: 1rem;\n  }\n\n  .header-content {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .progress-steps {\n    flex-wrap: wrap;\n    gap: 1rem;\n  }\n\n  .progress-steps::before {\n    display: none;\n  }\n\n  .form-container {\n    padding: 1.5rem;\n  }\n\n  .form-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .info-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .profile-card {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .form-actions {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .review-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.25rem;\n  }\n\n  .review-item span {\n    text-align: left;\n  }\n}\n\n/* Legal Notice Styles */\n.legal-notice {\n  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);\n  border: 1px solid #2196f3;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 1rem;\n}\n\n.legal-notice h3 {\n  color: #1976d2;\n  margin-bottom: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.legal-notice p {\n  color: #424242;\n  line-height: 1.6;\n  margin-bottom: 1rem;\n}\n\n.data-privacy-note {\n  background: rgba(76, 175, 80, 0.1);\n  border-left: 4px solid #4caf50;\n  padding: 0.75rem;\n  border-radius: 4px;\n}\n\n.data-privacy-note small {\n  color: #2e7d32;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n/* Document Upload Styles */\n.document-upload-group {\n  margin-bottom: 1.5rem;\n}\n\n.document-label {\n  display: block;\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 0.5rem;\n  font-size: 1rem;\n}\n\n.document-label i {\n  margin-right: 0.5rem;\n  color: #3498db;\n}\n\n.document-info {\n  font-weight: 400;\n  color: #7f8c8d;\n  font-size: 0.875rem;\n  display: block;\n  margin-top: 0.25rem;\n}\n\n.document-info.optional {\n  color: #27ae60;\n}\n\n.file-upload-area {\n  border: 2px dashed #bdc3c7;\n  border-radius: 8px;\n  padding: 2rem;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: #f8f9fa;\n}\n\n.file-upload-area:hover {\n  border-color: #3498db;\n  background: #e3f2fd;\n}\n\n.file-upload-area.dragover {\n  border-color: #2ecc71;\n  background: #e8f5e8;\n}\n\n.upload-placeholder i {\n  font-size: 2rem;\n  color: #95a5a6;\n  margin-bottom: 0.5rem;\n}\n\n.upload-placeholder p {\n  margin: 0.5rem 0;\n  color: #2c3e50;\n  font-weight: 500;\n}\n\n.upload-placeholder small {\n  color: #7f8c8d;\n}\n\n.uploaded-file {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  color: #27ae60;\n  font-weight: 500;\n}\n\n.uploaded-file i {\n  color: #27ae60;\n}\n\n.remove-file {\n  background: #e74c3c;\n  color: white;\n  border: none;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  margin-left: 0.5rem;\n  transition: background 0.3s ease;\n}\n\n.remove-file:hover {\n  background: #c0392b;\n}\n\n.upload-error {\n  color: #e74c3c;\n  font-size: 0.875rem;\n  margin-top: 0.5rem;\n}\n\n@media (max-width: 768px) {\n  .file-upload-area {\n    padding: 1.5rem 1rem;\n  }\n\n  .upload-placeholder i {\n    font-size: 1.5rem;\n  }\n}\n</style>\n"], "mappings": ";;;;;AAwgBA,OAAOA,sBAAqB,MAAO,mCAAmC;AACtE,OAAOC,iBAAgB,MAAO,8BAA8B;AAE5D,eAAe;EACbC,IAAI,EAAE,0BAA0B;EAChCC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,KAAK;MACjBC,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAClBC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;QACRC,gBAAgB,EAAE,CAAC;QAAE;QACrBC,mBAAmB,EAAE,EAAE;QACvBC,eAAe,EAAE,EAAE;QACnBC,iBAAiB,EAAE,KAAK;QACxBC,qBAAqB,EAAE,EAAE;QACzBC,iBAAiB,EAAE,EAAE;QACrBC,cAAc,EAAE;MAClB,CAAC;MACDC,aAAa,EAAE;QACbC,aAAa,EAAE,IAAI;QACnBC,kBAAkB,EAAE,IAAI;QACxBC,MAAM,EAAE;MACV,CAAC;MACDC,YAAY,EAAE,CAAC,CAAC;MAChBC,WAAW,EAAE,IAAI,IAAG,GAAI,IAAI;MAAE;MAC9BC,gBAAgB,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,iBAAiB,CAAC;MAC7EC,UAAU,EAAE,IAAI,CAAE;IACpB,CAAC;EACH,CAAC;EACDC,QAAQ,EAAE;IACR;IACAC,gBAAgBA,CAAA,EAAG;MACjB,OAAO1B,iBAAiB,CAAC2B,cAAc,CAAC,CAAC;IAC3C;EACF,CAAC;EACD,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,YAAY,CAAC,CAAC;EAC3B,CAAC;EACDC,OAAO,EAAE;IACP,MAAMD,YAAYA,CAAA,EAAG;MACnB,IAAI;QACF;QACAE,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C,MAAMC,eAAc,GAAI,MAAMjC,iBAAiB,CAACkC,UAAU,CAAC,CAAC;QAC5D,IAAID,eAAe,CAACE,OAAO,EAAE;UAC3B,IAAI,CAACX,UAAS,GAAIS,eAAe,CAAC/B,IAAI;UACtC6B,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACR,UAAU,CAAC;QAC5D,OAAO;UACL;UACA,IAAI,CAACA,UAAS,GAAI,IAAI,CAACE,gBAAgB;UACvCK,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACR,UAAU,CAAC;QAC5D;QAEA,MAAM,CAACY,eAAe,EAAEC,eAAe,IAAI,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC3DxC,sBAAsB,CAACyC,oBAAoB,CAAC,CAAC,EAC7CzC,sBAAsB,CAAC0C,iBAAiB,CAAC,EAC1C,CAAC;QAEF,IAAI,CAACpC,iBAAgB,GAAI+B,eAAe,CAAClC,IAAG,IAAK,EAAE;QACnD,IAAI,CAACI,cAAa,GAAI+B,eAAe,CAACnC,IAAG,IAAK,EAAE;MAElD,EAAE,OAAOwC,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD;QACA,IAAI,CAAClB,UAAS,GAAI,IAAI,CAACE,gBAAgB;QACvC,IAAI,CAACiB,MAAM,EAAED,KAAK,CAAC,+BAA+B,CAAC;MACrD;IACF,CAAC;IAEDE,WAAWA,CAAA,EAAG;MACZ;MACA,MAAMC,OAAM,GAAI,IAAI,CAACrB,UAAS,IAAK,IAAI,CAACA,UAAU,EAAEqB,OAAO;MAC3D,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;MAC1B,OAAO,GAAGA,OAAO,CAACC,UAAS,IAAK,EAAE,IAAID,OAAO,CAACE,WAAU,IAAK,EAAE,IAAIF,OAAO,CAACG,SAAQ,IAAK,EAAE,EAAE,CAACC,IAAI,CAAC,CAAC;IACrG,CAAC;IAEDC,cAAcA,CAAA,EAAG;MACf;MACA,MAAML,OAAM,GAAI,IAAI,CAACrB,UAAS,IAAK,IAAI,CAACA,UAAU,EAAEqB,OAAO;MAC3D,IAAI,CAACA,OAAO,EAAE,OAAO,cAAc;MAEnC,MAAMM,KAAI,GAAI,CACZN,OAAO,CAACO,YAAY,EACpBP,OAAO,CAACQ,MAAM,EACdR,OAAO,CAACS,WAAW,EACnBT,OAAO,CAACU,QAAQ,EAChBV,OAAO,CAACW,iBAAgB,IAAKX,OAAO,CAACY,IAAI,EACzCZ,OAAO,CAACa,QAAO,CAChB,CAACC,MAAM,CAACC,OAAO,CAAC;MAEjB,OAAOT,KAAK,CAACU,MAAK,GAAI,IAAIV,KAAK,CAACW,IAAI,CAAC,IAAI,IAAI,cAAc;IAC7D,CAAC;IAEDC,kBAAkBA,CAACC,QAAQ,EAAE;MAC3B,MAAMC,QAAO,GAAI;QACf,CAAC,EAAE,QAAQ;QACX,CAAC,EAAE,SAAS;QACZ,CAAC,EAAE,UAAU;QACb,CAAC,EAAE,SAAS;QACZ,CAAC,EAAE;MACL,CAAC;MACD,OAAOA,QAAQ,CAACD,QAAQ,KAAK,cAAc;IAC7C,CAAC;IAEDE,mBAAmBA,CAAA,EAAG;MACpB,MAAMrB,OAAM,GAAI,IAAI,CAACrB,UAAS,IAAK,IAAI,CAACA,UAAU,EAAEqB,OAAO;MAC3D,IAAI,CAACA,OAAO,EAAE,OAAO,cAAc;MAEnC,MAAMsB,KAAI,GAAItB,OAAO,CAACuB,kBAAkB;MACxC,MAAMC,MAAK,GAAIxB,OAAO,CAACyB,mBAAmB;MAE1C,IAAI,CAACH,KAAI,IAAK,CAACE,MAAM,EAAE,OAAO,cAAc;MAE5C,MAAMlB,KAAI,GAAI,EAAE;MAChB,IAAIgB,KAAK,EAAEhB,KAAK,CAACoB,IAAI,CAAC,GAAGJ,KAAK,QAAQA,KAAI,GAAI,IAAI,GAAE,GAAI,EAAE,EAAE,CAAC;MAC7D,IAAIE,MAAM,EAAElB,KAAK,CAACoB,IAAI,CAAC,GAAGF,MAAM,SAASA,MAAK,GAAI,IAAI,GAAE,GAAI,EAAE,EAAE,CAAC;MAEjE,OAAOlB,KAAK,CAACW,IAAI,CAAC,OAAO,CAAC;IAC5B,CAAC;IAEDU,UAAUA,CAACC,UAAU,EAAE;MACrB,IAAI,CAACA,UAAU,EAAE,OAAO,cAAc;MACtC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;IAClD,CAAC;IAEDC,cAAcA,CAACC,MAAM,EAAE;MACrB,OAAOC,UAAU,CAACD,MAAM,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC;IACtC,CAAC;IAEDC,oBAAoBA,CAAA,EAAG;MACrB,QAAQ,IAAI,CAAC7E,WAAW;QACtB,KAAK,CAAC;UACJ;UACA,OAAO,IAAI,CAACc,aAAa,CAACC,aAAY,IAAK,IAAI,CAACD,aAAa,CAACE,kBAAkB;QAClF,KAAK,CAAC;UACJ,OAAO,IAAI,CAACV,QAAQ,CAACE,mBAAkB,IAChC,IAAI,CAACF,QAAQ,CAACG,eAAc,IAC5B,IAAI,CAACH,QAAQ,CAACI,iBAAgB,KAAM,IAAI;QACjD,KAAK,CAAC;UACJ,OAAO,IAAI,CAACJ,QAAQ,CAACM,iBAAiB;QACxC;UACE,OAAO,IAAI;MACf;IACF,CAAC;IAEDkE,QAAQA,CAAA,EAAG;MACT,IAAI,IAAI,CAACD,oBAAoB,CAAC,KAAK,IAAI,CAAC7E,WAAU,GAAI,CAAC,EAAE;QACvD,IAAI,CAACA,WAAW,EAAE;MACpB;IACF,CAAC;IAED+E,YAAYA,CAAA,EAAG;MACb,IAAI,IAAI,CAAC/E,WAAU,GAAI,CAAC,EAAE;QACxB,IAAI,CAACA,WAAW,EAAE;MACpB;IACF,CAAC;IAEDgF,eAAeA,CAAA,EAAG;MAChB;IAAA,CACD;IAEDC,gBAAgBA,CAAA,EAAG;MACjB;MACA,IAAI,CAACC,OAAO,CAACd,IAAI,CAAC,wBAAwB,CAAC;IAC7C,CAAC;IAEDe,mBAAmBA,CAACC,QAAQ,EAAE;MAC5B,IAAI,CAAC9E,QAAQ,CAACM,iBAAgB,GAAIwE,QAAQ;IAC5C,CAAC;IAEDC,cAAcA,CAACC,UAAU,EAAE;MACzB,MAAMC,KAAI,GAAI;QACZ,MAAM,EAAE,mBAAmB;QAC3B,eAAe,EAAE,oBAAoB;QACrC,gBAAgB,EAAE,mBAAmB;QACrC,kBAAkB,EAAE,mBAAmB;QACvC,kBAAkB,EAAE;MACtB,CAAC;MACD,OAAOA,KAAK,CAACD,UAAU,KAAK,oBAAoB;IAClD,CAAC;IAEDE,sBAAsBA,CAAA,EAAG;MACvB,MAAMC,QAAO,GAAI,IAAI,CAACvF,iBAAiB,CAACwF,IAAI,CAACC,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,IAAI,CAACtF,QAAQ,CAACE,mBAAmB,CAAC;MAC7F,OAAOiF,QAAQ,EAAEI,aAAY,IAAK,EAAE;IACtC,CAAC;IAEDC,oBAAoBA,CAAA,EAAG;MACrB,MAAMC,MAAK,GAAI,IAAI,CAAC5F,cAAc,CAACuF,IAAI,CAACM,CAAA,IAAKA,CAAC,CAACJ,EAAC,KAAM,IAAI,CAACtF,QAAQ,CAACM,iBAAiB,CAAC;MACtF,OAAOmF,MAAM,EAAEE,WAAU,IAAK,EAAE;IAClC,CAAC;IAED,MAAMC,YAAYA,CAAA,EAAG;MACnB,IAAI,CAAC,IAAI,CAAC5F,QAAQ,CAACO,cAAc,EAAE;MAEnC,IAAI;QACF,IAAI,CAACZ,UAAS,GAAI,IAAI;;QAEtB;QACA,MAAMkG,WAAU,GAAI;UAClB5F,gBAAgB,EAAE6F,QAAQ,CAAC,IAAI,CAAC9F,QAAQ,CAACC,gBAAgB,KAAK,CAAC;UAC/DC,mBAAmB,EAAE4F,QAAQ,CAAC,IAAI,CAAC9F,QAAQ,CAACE,mBAAmB,KAAK,CAAC;UACrEC,eAAe,EAAE,IAAI,CAACH,QAAQ,CAACG,eAAc,IAAK,IAAI,CAACH,QAAQ,CAACG,eAAe,CAACiD,MAAK,IAAK,EAAC,GACvF,IAAI,CAACpD,QAAQ,CAACG,eAAc,GAC5B,kDAAkD;UACtDG,iBAAiB,EAAEwF,QAAQ,CAAC,IAAI,CAAC9F,QAAQ,CAACM,iBAAiB,KAAK,IAAI;UACpEyF,eAAe,EAAE,QAAQ;UACzBC,QAAQ,EAAE,QAAQ;UAClB;UACA5F,iBAAiB,EAAE+C,OAAO,CAAC,IAAI,CAACnD,QAAQ,CAACI,iBAAiB,CAAC;UAC3DC,qBAAqB,EAAE,IAAI,CAACL,QAAQ,CAACK,qBAAoB,IAAK,IAAI;UAClE4F,aAAa,EAAE,IAAI,CAACjG,QAAQ,CAACiG,aAAY,IAAK,IAAI;UAClDC,kBAAkB,EAAE,IAAI,CAAClG,QAAQ,CAACkG,kBAAiB,IAAK,IAAI;UAC5DC,mBAAmB,EAAE,IAAI,CAACnG,QAAQ,CAACmG,mBAAkB,IAAK,IAAI;UAC9DC,SAAS,EAAE,IAAI,CAACrG,QAAO,IAAK;QAC9B,CAAC;QAEDuB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEsE,WAAW,CAAC;QAEpD,MAAMQ,QAAO,GAAI,MAAM/G,sBAAsB,CAACgH,aAAa,CAACT,WAAW,CAAC;QAExE,MAAMU,SAAQ,GAAIF,QAAQ,CAAC5G,IAAI,CAAC6F,EAAE;QAClChE,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEgF,SAAS,CAAC;;QAElD;QACA,MAAMC,YAAW,GAAI,IAAI,CAAChG,aAAa,CAACC,aAAY,IAChC,IAAI,CAACD,aAAa,CAACE,kBAAiB,IACpC,IAAI,CAACF,aAAa,CAACG,MAAM;QAE7C,IAAI6F,YAAY,EAAE;UAChBlF,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;UACrC,MAAM,IAAI,CAACkF,wBAAwB,CAACF,SAAS,CAAC;QAChD;QAEA,IAAI,CAACrE,MAAM,EAAER,OAAO,CAAC,iCAAiC,CAAC;QACvD,IAAI,CAACkD,OAAO,CAACd,IAAI,CAAC;UAChBtE,IAAI,EAAE,gBAAgB;UACtBkH,MAAM,EAAE;YAAEpB,EAAE,EAAEiB;UAAU;QAC1B,CAAC,CAAC;MAEJ,EAAE,OAAOtE,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDX,OAAO,CAACW,KAAK,CAAC,gBAAgB,EAAE;UAC9B0E,MAAM,EAAE1E,KAAK,CAACoE,QAAQ,EAAEM,MAAM;UAC9BlH,IAAI,EAAEwC,KAAK,CAACoE,QAAQ,EAAE5G,IAAI;UAC1BmH,OAAO,EAAE3E,KAAK,CAAC2E;QACjB,CAAC,CAAC;QAEF,IAAIC,YAAW,GAAI,0BAA0B;QAC7C,IAAI5E,KAAK,CAACoE,QAAQ,EAAE5G,IAAI,EAAEmH,OAAO,EAAE;UACjCC,YAAW,GAAI5E,KAAK,CAACoE,QAAQ,CAAC5G,IAAI,CAACmH,OAAO;QAC5C,OAAO,IAAI3E,KAAK,CAACoE,QAAQ,EAAE5G,IAAI,EAAEqH,MAAM,EAAE;UACvCD,YAAW,GAAI5E,KAAK,CAACoE,QAAQ,CAAC5G,IAAI,CAACqH,MAAM,CAACC,GAAG,CAACC,CAAA,IAAKA,CAAC,CAACC,GAAG,CAAC,CAAC5D,IAAI,CAAC,IAAI,CAAC;QACtE,OAAO,IAAIpB,KAAK,CAAC2E,OAAO,EAAE;UACxBC,YAAW,GAAI5E,KAAK,CAAC2E,OAAO;QAC9B;QAEA,IAAI,CAAC1E,MAAM,EAAED,KAAK,CAAC4E,YAAY,CAAC;MAClC,UAAU;QACR,IAAI,CAAClH,UAAS,GAAI,KAAK;MACzB;IACF,CAAC;IAED,MAAM8G,wBAAwBA,CAACF,SAAS,EAAE;MACxC,IAAI;QACF,MAAMW,aAAY,GAAI,EAAE;;QAExB;QACAC,MAAM,CAACC,OAAO,CAAC,IAAI,CAAC5G,aAAa,CAAC,CAAC6G,OAAO,CAAC,CAAC,CAACC,IAAI,EAAEC,IAAI,CAAC,KAAK;UAC3D,IAAIA,IAAI,EAAE;YACRL,aAAa,CAACpD,IAAI,CAAC;cAAEwD,IAAI;cAAEC;YAAK,CAAC,CAAC;UACpC;QACF,CAAC,CAAC;QAEF,IAAIL,aAAa,CAAC9D,MAAK,KAAM,CAAC,EAAE;UAC9B;QACF;;QAEA;QACA,MAAMoE,cAAa,GAAI,MAAMlI,sBAAsB,CAACmI,eAAe,CAAClB,SAAS,EAAEW,aAAa,CAAC;QAE7F,IAAIM,cAAc,CAAC9F,OAAO,EAAE;UAC1BJ,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEiG,cAAc,CAAC/H,IAAI,CAAC;UACpE,IAAI,CAACyC,MAAM,EAAER,OAAO,CAAC,GAAG8F,cAAc,CAAC/H,IAAI,CAACiI,cAAc,oCAAoC,CAAC;QACjG,OAAO;UACLpG,OAAO,CAACW,KAAK,CAAC,yBAAyB,EAAEuF,cAAc,CAAC;UACxD,IAAI,CAACtF,MAAM,EAAEyF,OAAO,CAAC,uDAAuD,CAAC;QAC/E;MAEF,EAAE,OAAO1F,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACC,MAAM,EAAEyF,OAAO,CAAC,+EAA+E,CAAC;MACvG;IACF,CAAC;IAEDC,MAAMA,CAAA,EAAG;MACP,IAAI,CAAChD,OAAO,CAACd,IAAI,CAAC;QAAEtE,IAAI,EAAE;MAAqB,CAAC,CAAC;IACnD,CAAC;IAEDqI,aAAaA,CAAA,EAAG;MACd;MACAvG,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAED;IACAuG,gBAAgBA,CAACC,QAAQ,EAAE;MACzB;MACA,MAAMC,UAAS,GAAI;QACjB,eAAe,EAAE,mBAAmB;QACpC,oBAAoB,EAAE,uBAAuB;QAC7C,QAAQ,EAAE;MACZ,CAAC;MAED,MAAMC,QAAO,GAAID,UAAU,CAACD,QAAQ,CAAC;MAErC,IAAI,CAACE,QAAQ,EAAE;QACb3G,OAAO,CAACW,KAAK,CAAC,sBAAsB8F,QAAQ,EAAE,CAAC;QAC/C;MACF;;MAEA;MACA,IAAI,IAAI,CAACG,KAAK,CAACD,QAAQ,CAAC,EAAE;QACxB,IAAI,CAACC,KAAK,CAACD,QAAQ,CAAC,CAACE,KAAK,CAAC,CAAC;MAC9B,OAAO;QACL7G,OAAO,CAAC8G,IAAI,CAAC,mBAAmBH,QAAQ,aAAa,CAAC;QACtD;QACA,IAAI,CAACI,SAAS,CAAC,MAAM;UACnB,IAAI,IAAI,CAACH,KAAK,CAACD,QAAQ,CAAC,EAAE;YACxB,IAAI,CAACC,KAAK,CAACD,QAAQ,CAAC,CAACE,KAAK,CAAC,CAAC;UAC9B,OAAO;YACL7G,OAAO,CAACW,KAAK,CAAC,mBAAmBgG,QAAQ,kCAAkC,CAAC;UAC9E;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IAEDK,gBAAgBA,CAACC,KAAK,EAAER,QAAQ,EAAE;MAChC,MAAMR,IAAG,GAAIgB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;MAClC,IAAIlB,IAAI,EAAE;QACR,IAAI,CAACmB,kBAAkB,CAACnB,IAAI,EAAEQ,QAAQ,CAAC;MACzC;IACF,CAAC;IAEDY,cAAcA,CAACJ,KAAK,EAAER,QAAQ,EAAE;MAC9B,MAAMR,IAAG,GAAIgB,KAAK,CAACK,YAAY,CAACH,KAAK,CAAC,CAAC,CAAC;MACxC,IAAIlB,IAAI,EAAE;QACR,IAAI,CAACmB,kBAAkB,CAACnB,IAAI,EAAEQ,QAAQ,CAAC;MACzC;IACF,CAAC;IAEDW,kBAAkBA,CAACnB,IAAI,EAAEQ,QAAQ,EAAE;MACjC;MACA,OAAO,IAAI,CAACnH,YAAY,CAACmH,QAAQ,CAAC;;MAElC;MACA,IAAIR,IAAI,CAACsB,IAAG,GAAI,IAAI,CAAChI,WAAW,EAAE;QAChC,IAAI,CAACD,YAAY,CAACmH,QAAQ,IAAI,iCAAiC;QAC/D,IAAI,CAAC7F,MAAM,EAAED,KAAK,CAAC,iCAAiC,CAAC;QACrD;MACF;;MAEA;MACA,IAAI,CAAC,IAAI,CAACnB,gBAAgB,CAACgI,QAAQ,CAACvB,IAAI,CAACD,IAAI,CAAC,EAAE;QAC9C,IAAI,CAAC1G,YAAY,CAACmH,QAAQ,IAAI,0CAA0C;QACxE,IAAI,CAAC7F,MAAM,EAAED,KAAK,CAAC,0CAA0C,CAAC;QAC9D;MACF;;MAEA;MACA,IAAI,CAACzB,aAAa,CAACuH,QAAQ,IAAIR,IAAI;MACnC,IAAI,CAACrF,MAAM,EAAER,OAAO,CAAC,GAAG6F,IAAI,CAAC/H,IAAI,wBAAwB,CAAC;IAC5D,CAAC;IAEDuJ,UAAUA,CAAChB,QAAQ,EAAE;MACnB;MACA,IAAI,CAACvH,aAAa,CAACuH,QAAQ,IAAI,IAAI;MACnC,OAAO,IAAI,CAACnH,YAAY,CAACmH,QAAQ,CAAC;;MAElC;MACA,MAAMC,UAAS,GAAI;QACjB,eAAe,EAAE,mBAAmB;QACpC,oBAAoB,EAAE,uBAAuB;QAC7C,QAAQ,EAAE;MACZ,CAAC;MAED,MAAMC,QAAO,GAAID,UAAU,CAACD,QAAQ,CAAC;;MAErC;MACA,IAAIE,QAAO,IAAK,IAAI,CAACC,KAAK,CAACD,QAAQ,CAAC,EAAE;QACpC,IAAI,CAACC,KAAK,CAACD,QAAQ,CAAC,CAACe,KAAI,GAAI,EAAE;MACjC,OAAO;QACL1H,OAAO,CAAC8G,IAAI,CAAC,mBAAmBH,QAAQ,4BAA4B,CAAC;MACvE;IACF,CAAC;IAEDgB,SAASA,CAAA,EAAG;MACV;MACA3H,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;IAC3B;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}