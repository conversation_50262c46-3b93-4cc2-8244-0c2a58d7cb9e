{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass, withModifiers as _withModifiers } from \"vue\";\nconst _hoisted_1 = {\n  class: \"admin-notifications\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"notification-badge\"\n};\nconst _hoisted_3 = {\n  class: \"notification-header\"\n};\nconst _hoisted_4 = {\n  class: \"notification-actions\"\n};\nconst _hoisted_5 = [\"disabled\"];\nconst _hoisted_6 = {\n  class: \"notification-body\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"text-center p-3\"\n};\nconst _hoisted_8 = {\n  key: 1,\n  class: \"text-center p-4 text-muted\"\n};\nconst _hoisted_9 = {\n  key: 2,\n  class: \"notification-list\"\n};\nconst _hoisted_10 = [\"data-notification-id\", \"onClick\"];\nconst _hoisted_11 = {\n  class: \"notification-icon\"\n};\nconst _hoisted_12 = {\n  class: \"notification-content\"\n};\nconst _hoisted_13 = {\n  class: \"notification-title\"\n};\nconst _hoisted_14 = {\n  class: \"notification-message\"\n};\nconst _hoisted_15 = {\n  class: \"notification-time\"\n};\nconst _hoisted_16 = {\n  key: 0,\n  class: \"notification-priority\"\n};\nconst _hoisted_17 = {\n  key: 3,\n  class: \"notification-footer\"\n};\nconst _hoisted_18 = [\"disabled\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" Notification Bell Icon \"), _createElementVNode(\"div\", {\n    class: \"notification-bell\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.toggleNotificationPanel && $options.toggleNotificationPanel(...args))\n  }, [_cache[6] || (_cache[6] = _createElementVNode(\"i\", {\n    class: \"fas fa-bell\"\n  }, null, -1 /* HOISTED */)), $data.unreadCount > 0 ? (_openBlock(), _createElementBlock(\"span\", _hoisted_2, _toDisplayString($data.unreadCount > 99 ? '99+' : $data.unreadCount), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" Notification Panel \"), $data.showPanel ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"notification-panel\",\n    onClick: _cache[4] || (_cache[4] = _withModifiers(() => {}, [\"stop\"]))\n  }, [_createElementVNode(\"div\", _hoisted_3, [_cache[9] || (_cache[9] = _createElementVNode(\"h5\", null, \"Notifications\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_4, [$data.unreadCount > 0 ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.markAllAsRead && $options.markAllAsRead(...args)),\n    class: \"btn btn-sm btn-outline-primary\",\n    disabled: $data.markingAllRead\n  }, [_cache[7] || (_cache[7] = _createElementVNode(\"i\", {\n    class: \"fas fa-check-double\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.markingAllRead ? 'Marking...' : 'Mark All Read'), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_5)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"button\", {\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.toggleNotificationPanel && $options.toggleNotificationPanel(...args)),\n    class: \"btn btn-sm btn-outline-secondary\"\n  }, _cache[8] || (_cache[8] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times\"\n  }, null, -1 /* HOISTED */)]))])]), _createElementVNode(\"div\", _hoisted_6, [$data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, _cache[10] || (_cache[10] = [_createElementVNode(\"div\", {\n    class: \"spinner-border spinner-border-sm\",\n    role: \"status\"\n  }, [_createElementVNode(\"span\", {\n    class: \"visually-hidden\"\n  }, \"Loading...\")], -1 /* HOISTED */), _createElementVNode(\"p\", {\n    class: \"mt-2 mb-0\"\n  }, \"Loading notifications...\", -1 /* HOISTED */)]))) : $data.notifications.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, _cache[11] || (_cache[11] = [_createElementVNode(\"i\", {\n    class: \"fas fa-bell-slash fa-2x mb-2\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"p\", {\n    class: \"mb-0\"\n  }, \"No notifications\", -1 /* HOISTED */)]))) : (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.notifications, notification => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: notification.id,\n      \"data-notification-id\": notification.id,\n      class: _normalizeClass([\"notification-item\", {\n        'unread': !notification.is_read,\n        'priority-high': notification.priority === 'high' || notification.priority === 'urgent',\n        'clickable': true\n      }]),\n      onClick: $event => $options.handleNotificationClick(notification)\n    }, [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"i\", {\n      class: _normalizeClass($options.getNotificationIcon(notification.type))\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, _toDisplayString(notification.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_14, _toDisplayString(notification.message), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_15, _toDisplayString($options.formatTime(notification.created_at)), 1 /* TEXT */)]), notification.priority === 'high' || notification.priority === 'urgent' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [...(_cache[12] || (_cache[12] = [_createElementVNode(\"i\", {\n      class: \"fas fa-exclamation-triangle text-warning\"\n    }, null, -1 /* HOISTED */)]))])) : _createCommentVNode(\"v-if\", true)], 10 /* CLASS, PROPS */, _hoisted_10);\n  }), 128 /* KEYED_FRAGMENT */))])), $data.hasMore ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_createElementVNode(\"button\", {\n    onClick: _cache[3] || (_cache[3] = (...args) => $options.loadMore && $options.loadMore(...args)),\n    class: \"btn btn-sm btn-outline-primary w-100\",\n    disabled: $data.loadingMore\n  }, [_cache[13] || (_cache[13] = _createElementVNode(\"i\", {\n    class: \"fas fa-chevron-down\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.loadingMore ? 'Loading...' : 'Load More'), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_18)])) : _createCommentVNode(\"v-if\", true)])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Overlay \"), $data.showPanel ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 1,\n    class: \"notification-overlay\",\n    onClick: _cache[5] || (_cache[5] = (...args) => $options.toggleNotificationPanel && $options.toggleNotificationPanel(...args))\n  })) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "onClick", "_cache", "args", "$options", "toggleNotificationPanel", "$data", "unreadCount", "_hoisted_2", "_toDisplayString", "showPanel", "_withModifiers", "_hoisted_3", "_hoisted_4", "markAllAsRead", "disabled", "markingAllRead", "_hoisted_6", "loading", "_hoisted_7", "role", "notifications", "length", "_hoisted_8", "_hoisted_9", "_Fragment", "_renderList", "notification", "key", "id", "_normalizeClass", "is_read", "priority", "$event", "handleNotificationClick", "_hoisted_11", "getNotificationIcon", "type", "_hoisted_12", "_hoisted_13", "title", "_hoisted_14", "message", "_hoisted_15", "formatTime", "created_at", "_hoisted_16", "hasMore", "_hoisted_17", "loadMore", "loadingMore"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminNotifications.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-notifications\">\n    <!-- Notification Bell Icon -->\n    <div class=\"notification-bell\" @click=\"toggleNotificationPanel\">\n      <i class=\"fas fa-bell\"></i>\n      <span v-if=\"unreadCount > 0\" class=\"notification-badge\">{{ unreadCount > 99 ? '99+' : unreadCount }}</span>\n    </div>\n\n    <!-- Notification Panel -->\n    <div v-if=\"showPanel\" class=\"notification-panel\" @click.stop>\n      <div class=\"notification-header\">\n        <h5>Notifications</h5>\n        <div class=\"notification-actions\">\n          <button \n            v-if=\"unreadCount > 0\" \n            @click=\"markAllAsRead\" \n            class=\"btn btn-sm btn-outline-primary\"\n            :disabled=\"markingAllRead\"\n          >\n            <i class=\"fas fa-check-double\"></i>\n            {{ markingAllRead ? 'Marking...' : 'Mark All Read' }}\n          </button>\n          <button @click=\"toggleNotificationPanel\" class=\"btn btn-sm btn-outline-secondary\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n      </div>\n\n      <div class=\"notification-body\">\n        <div v-if=\"loading\" class=\"text-center p-3\">\n          <div class=\"spinner-border spinner-border-sm\" role=\"status\">\n            <span class=\"visually-hidden\">Loading...</span>\n          </div>\n          <p class=\"mt-2 mb-0\">Loading notifications...</p>\n        </div>\n\n        <div v-else-if=\"notifications.length === 0\" class=\"text-center p-4 text-muted\">\n          <i class=\"fas fa-bell-slash fa-2x mb-2\"></i>\n          <p class=\"mb-0\">No notifications</p>\n        </div>\n\n        <div v-else class=\"notification-list\">\n          <div\n            v-for=\"notification in notifications\"\n            :key=\"notification.id\"\n            :data-notification-id=\"notification.id\"\n            class=\"notification-item\"\n            :class=\"{\n              'unread': !notification.is_read,\n              'priority-high': notification.priority === 'high' || notification.priority === 'urgent',\n              'clickable': true\n            }\"\n            @click=\"handleNotificationClick(notification)\"\n          >\n            <div class=\"notification-icon\">\n              <i :class=\"getNotificationIcon(notification.type)\"></i>\n            </div>\n            <div class=\"notification-content\">\n              <div class=\"notification-title\">{{ notification.title }}</div>\n              <div class=\"notification-message\">{{ notification.message }}</div>\n              <div class=\"notification-time\">{{ formatTime(notification.created_at) }}</div>\n            </div>\n            <div class=\"notification-priority\" v-if=\"notification.priority === 'high' || notification.priority === 'urgent'\">\n              <i class=\"fas fa-exclamation-triangle text-warning\"></i>\n            </div>\n          </div>\n        </div>\n\n        <div v-if=\"hasMore\" class=\"notification-footer\">\n          <button \n            @click=\"loadMore\" \n            class=\"btn btn-sm btn-outline-primary w-100\"\n            :disabled=\"loadingMore\"\n          >\n            <i class=\"fas fa-chevron-down\"></i>\n            {{ loadingMore ? 'Loading...' : 'Load More' }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Overlay -->\n    <div v-if=\"showPanel\" class=\"notification-overlay\" @click=\"toggleNotificationPanel\"></div>\n  </div>\n</template>\n\n<script>\nimport { getAdminNotificationService } from '../../services/notificationService';\nimport notificationNavigationService from '../../services/notificationNavigationService';\n\nexport default {\n  name: 'AdminNotifications',\n  data() {\n    return {\n      showPanel: false,\n      notifications: [],\n      unreadCount: 0,\n      loading: false,\n      loadingMore: false,\n      markingAllRead: false,\n      currentPage: 1,\n      hasMore: true,\n      limit: 10,\n      notificationService: null // Store the admin-specific service instance\n    };\n  },\n  mounted() {\n    this.initializeNotifications();\n  },\n  beforeUnmount() {\n    this.cleanup();\n  },\n  methods: {\n    async initializeNotifications() {\n      try {\n        console.log('🚀 AdminNotifications: Initializing admin notification service');\n\n        // Get the admin-specific notification service\n        this.notificationService = getAdminNotificationService();\n\n        // Request notification permission\n        await this.notificationService.requestNotificationPermission();\n\n        // Initialize the admin notification service\n        await this.notificationService.init();\n\n        // Set up event listeners\n        this.notificationService.on('notification', this.handleNewNotification);\n        this.notificationService.on('connected', this.onConnected);\n        this.notificationService.on('error', this.onError);\n\n        // Load initial data\n        await this.loadUnreadCount();\n\n        console.log('✅ AdminNotifications: Admin notification service initialized successfully');\n\n      } catch (error) {\n        console.error('❌ AdminNotifications: Failed to initialize notifications:', error);\n        this.error = error.message || 'Failed to initialize notifications';\n      }\n    },\n\n    cleanup() {\n      if (this.notificationService) {\n        this.notificationService.off('notification', this.handleNewNotification);\n        this.notificationService.off('connected', this.onConnected);\n        this.notificationService.off('error', this.onError);\n      }\n    },\n\n    async toggleNotificationPanel() {\n      this.showPanel = !this.showPanel;\n      \n      if (this.showPanel && this.notifications.length === 0) {\n        await this.loadNotifications();\n      }\n    },\n\n    async loadNotifications(page = 1) {\n      try {\n        if (page === 1) {\n          this.loading = true;\n          this.notifications = [];\n          this.currentPage = 1;\n        } else {\n          this.loadingMore = true;\n        }\n\n        console.log('🔍 AdminNotifications: Loading notifications, page:', page, 'limit:', this.limit);\n        const response = await this.notificationService.getNotifications(page, this.limit);\n        console.log('📨 AdminNotifications: Raw API response:', response);\n\n        // Handle the correct response structure from backend\n        let notifications = [];\n        let pagination = {};\n\n        if (response.data && response.data.notifications) {\n          // Backend returns: { success: true, data: { notifications: [...], pagination: {...} } }\n          if (Array.isArray(response.data.notifications)) {\n            notifications = response.data.notifications;\n            pagination = response.data.pagination || {};\n            console.log('✅ AdminNotifications: Parsed notifications:', notifications.length, 'items');\n            console.log('📊 AdminNotifications: Pagination:', pagination);\n          } else {\n            console.warn('⚠️ AdminNotifications: Invalid response structure - notifications is not an array');\n            console.log('📊 AdminNotifications: response.data.notifications:', response.data.notifications);\n          }\n        } else {\n          console.warn('⚠️ AdminNotifications: Invalid response structure - no data.notifications found');\n          console.log('📊 AdminNotifications: response.data:', response.data);\n        }\n\n        if (page === 1) {\n          this.notifications = notifications;\n        } else {\n          this.notifications.push(...notifications);\n        }\n\n        this.hasMore = pagination.page < pagination.pages;\n        this.currentPage = page;\n\n        console.log('📋 AdminNotifications: Final notifications array:', this.notifications);\n        console.log('📄 AdminNotifications: Has more pages:', this.hasMore);\n\n      } catch (error) {\n        console.error('❌ AdminNotifications: Failed to load notifications:', error);\n        this.$emit('error', 'Failed to load notifications');\n      } finally {\n        this.loading = false;\n        this.loadingMore = false;\n      }\n    },\n\n    async loadMore() {\n      if (this.hasMore && !this.loadingMore) {\n        await this.loadNotifications(this.currentPage + 1);\n      }\n    },\n\n    async loadUnreadCount() {\n      try {\n        this.unreadCount = await this.notificationService.getUnreadCount();\n      } catch (error) {\n        console.error('Failed to load unread count:', error);\n      }\n    },\n\n    async markAllAsRead() {\n      try {\n        this.markingAllRead = true;\n        await this.notificationService.markAllAsRead();\n        \n        // Update local state\n        this.notifications.forEach(notification => {\n          notification.is_read = true;\n        });\n        this.unreadCount = 0;\n        \n        this.$emit('notifications-read');\n        \n      } catch (error) {\n        console.error('Failed to mark all as read:', error);\n        this.$emit('error', 'Failed to mark notifications as read');\n      } finally {\n        this.markingAllRead = false;\n      }\n    },\n\n    async handleNotificationClick(notification) {\n      console.log('🔔 Admin notification clicked:', notification);\n\n      // Show loading state\n      this.showLoadingState(notification);\n\n      try {\n        // Mark as read if not already read\n        if (!notification.is_read) {\n          await this.notificationService.markAsRead(notification.id);\n          notification.is_read = true;\n          this.unreadCount = Math.max(0, this.unreadCount - 1);\n          this.$emit('notification-read', notification);\n        }\n\n        // Navigate based on notification type and data\n        await this.navigateToRelevantPage(notification);\n\n        // Close notification panel after successful navigation\n        this.showPanel = false;\n\n      } catch (error) {\n        console.error('❌ Failed to handle notification click:', error);\n        this.showErrorToast('Failed to process notification');\n      } finally {\n        this.hideLoadingState(notification);\n      }\n\n      // Emit click event for parent components to handle\n      this.$emit('notification-click', notification);\n    },\n\n    /**\n     * Navigate to the relevant page based on notification type and data\n     */\n    async navigateToRelevantPage(notification) {\n      console.log('🧭 Determining navigation for notification:', notification.type, notification.data);\n\n      try {\n        const notificationData = typeof notification.data === 'string'\n          ? JSON.parse(notification.data)\n          : notification.data || {};\n\n        let targetRoute = null;\n\n        switch (notification.type) {\n          case 'new_request':\n            targetRoute = await this.handleNewRequestNavigation(notificationData);\n            break;\n\n          case 'status_change':\n          case 'request_update':\n            targetRoute = await this.handleRequestUpdateNavigation(notificationData);\n            break;\n\n          case 'request_cancelled':\n            targetRoute = await this.handleRequestCancellationNavigation(notificationData);\n            break;\n\n          case 'payment_confirmed':\n            targetRoute = await this.handlePaymentConfirmationNavigation(notificationData);\n            break;\n\n          case 'payment_update':\n            targetRoute = await this.handlePaymentNavigation(notificationData);\n            break;\n\n          case 'system_alert':\n          case 'urgent_request':\n            targetRoute = await this.handleSystemAlertNavigation(notificationData);\n            break;\n\n          case 'user_registration':\n          case 'new_user':\n            targetRoute = await this.handleUserNavigation(notificationData);\n            break;\n\n          default:\n            console.log('🤷 Unknown notification type, using default navigation');\n            targetRoute = await this.handleDefaultNavigation(notificationData);\n        }\n\n        if (targetRoute) {\n          console.log('🚀 Navigating to:', targetRoute);\n\n          // Add timeout to prevent hanging navigation\n          const navigationPromise = this.$router.push(targetRoute);\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Navigation timeout')), 5000);\n          });\n\n          await Promise.race([navigationPromise, timeoutPromise]);\n        } else {\n          console.log('ℹ️ No navigation target determined for notification');\n        }\n\n      } catch (error) {\n        console.error('❌ Navigation error:', error);\n        throw new Error('Failed to navigate to notification target');\n      }\n    },\n\n    /**\n     * Handle navigation for new request notifications\n     */\n    async handleNewRequestNavigation(data) {\n      if (data.request_id) {\n        // Check if request still exists\n        const exists = await this.checkRequestExists(data.request_id);\n        if (exists) {\n          // Open request details modal directly for better UX\n          return await this.openRequestDetailsModal(data.request_id);\n        } else {\n          this.showErrorToast('Request no longer exists');\n          return { name: 'AdminRequests' };\n        }\n      }\n      return { name: 'AdminRequests' };\n    },\n\n    /**\n     * Handle navigation for request update notifications\n     */\n    async handleRequestUpdateNavigation(data) {\n      if (data.request_id) {\n        const exists = await this.checkRequestExists(data.request_id);\n        if (exists) {\n          // Open request details modal directly for better UX\n          return await this.openRequestDetailsModal(data.request_id);\n        } else {\n          this.showErrorToast('Request no longer exists');\n          return { name: 'AdminRequests' };\n        }\n      }\n      return { name: 'AdminRequests' };\n    },\n\n    /**\n     * Handle navigation for request cancellation notifications\n     */\n    async handleRequestCancellationNavigation(data) {\n      if (data.request_id) {\n        const exists = await this.checkRequestExists(data.request_id);\n        if (exists) {\n          // Open request details modal to show the cancelled request\n          // Focus on status section to highlight the cancellation\n          return await this.openRequestDetailsModal(data.request_id, 'status');\n        } else {\n          this.showErrorToast('Request no longer exists');\n          return { name: 'AdminRequests' };\n        }\n      }\n      // If no specific request, show cancelled requests filter\n      return {\n        name: 'AdminRequests',\n        query: { filter: 'cancelled' }\n      };\n    },\n\n    /**\n     * Handle navigation for payment-related notifications\n     */\n    async handlePaymentNavigation(data) {\n      if (data.request_id) {\n        const exists = await this.checkRequestExists(data.request_id);\n        if (exists) {\n          // Open request details modal with focus on payment section\n          return await this.openRequestDetailsModal(data.request_id, 'payment');\n        }\n      }\n      // Fallback to reports page for payment overview\n      return { name: 'AdminReports', query: { section: 'revenue' } };\n    },\n\n    /**\n     * Handle navigation for system alerts and urgent requests\n     */\n    async handleSystemAlertNavigation(data) {\n      if (data.request_id) {\n        return {\n          name: 'AdminRequests',\n          query: {\n            highlight: data.request_id,\n            filter: 'urgent'\n          }\n        };\n      }\n      // For general system alerts, go to dashboard\n      return { name: 'AdminDashboard' };\n    },\n\n    /**\n     * Handle navigation for user-related notifications\n     */\n    async handleUserNavigation(data) {\n      if (data.user_id || data.client_id) {\n        const userId = data.user_id || data.client_id;\n        const exists = await this.checkUserExists(userId);\n        if (exists) {\n          return {\n            name: 'AdminUsers',\n            query: {\n              highlight: userId,\n              type: data.user_type || 'client'\n            }\n          };\n        } else {\n          this.showErrorToast('User no longer exists');\n          return { name: 'AdminUsers' };\n        }\n      }\n      return { name: 'AdminUsers' };\n    },\n\n    /**\n     * Handle default navigation when type is unknown\n     */\n    async handleDefaultNavigation(data) {\n      // Priority order: request_id > user_id > dashboard\n      if (data.request_id) {\n        const exists = await this.checkRequestExists(data.request_id);\n        if (exists) {\n          // Open modal for request-related notifications for better UX\n          return await this.openRequestDetailsModal(data.request_id);\n        }\n      }\n\n      if (data.user_id || data.client_id) {\n        const userId = data.user_id || data.client_id;\n        const exists = await this.checkUserExists(userId);\n        if (exists) {\n          return {\n            name: 'AdminUsers',\n            query: {\n              highlight: userId,\n              type: data.user_type || 'client'\n            }\n          };\n        } else {\n          this.showErrorToast('User no longer exists');\n          return { name: 'AdminUsers' };\n        }\n      }\n\n      return { name: 'AdminDashboard' };\n    },\n\n    /**\n     * Check if a request still exists in the system\n     */\n    async checkRequestExists(requestId) {\n      return await notificationNavigationService.checkRequestExists(requestId, 'admin');\n    },\n\n    /**\n     * Check if a user still exists in the system\n     */\n    async checkUserExists(userId) {\n      return await notificationNavigationService.checkUserExists(userId);\n    },\n\n    /**\n     * Show loading state for a notification\n     */\n    showLoadingState(notification) {\n      // Add loading class to notification item\n      const notificationElement = document.querySelector(`[data-notification-id=\"${notification.id}\"]`);\n      if (notificationElement) {\n        notificationElement.classList.add('loading');\n      }\n    },\n\n    /**\n     * Hide loading state for a notification\n     */\n    hideLoadingState(notification) {\n      const notificationElement = document.querySelector(`[data-notification-id=\"${notification.id}\"]`);\n      if (notificationElement) {\n        notificationElement.classList.remove('loading');\n      }\n    },\n\n    /**\n     * Show error toast message\n     */\n    showErrorToast(message) {\n      notificationNavigationService.showNavigationError(message, this.$emit.bind(this));\n    },\n\n    /**\n     * Open request details modal by communicating with parent component\n     * This provides better UX by keeping context and avoiding page navigation\n     */\n    async openRequestDetailsModal(requestId, focusTab = null) {\n      try {\n        console.log('🔔 Opening request details modal for ID:', requestId, 'Focus tab:', focusTab);\n\n        // Emit event to parent component to open the modal\n        // The parent (AdminRequests or AdminHeader) will handle the modal opening\n        this.$emit('open-request-modal', {\n          requestId: requestId,\n          focusTab: focusTab\n        });\n\n        // Return null to indicate no navigation is needed\n        // The modal will be opened by the parent component\n        return null;\n\n      } catch (error) {\n        console.error('❌ Error opening request modal:', error);\n        // Fallback to page navigation if modal opening fails\n        return {\n          name: 'AdminRequests',\n          query: {\n            highlight: requestId,\n            tab: focusTab\n          }\n        };\n      }\n    },\n\n    handleNewNotification(notification, context = null) {\n      // Validate this is an admin notification\n      if (context && context.userType && context.userType !== 'admin') {\n        console.log('🚫 AdminNotifications: Ignoring non-admin notification:', context.userType);\n        return;\n      }\n\n      // Additional validation: check notification recipient type\n      if (notification.recipient_type && notification.recipient_type !== 'admin') {\n        console.log('🚫 AdminNotifications: Ignoring notification for:', notification.recipient_type);\n        return;\n      }\n\n      console.log('📢 AdminNotifications: Processing admin notification:', notification);\n\n      // Handle unread count updates from polling\n      if (notification.type === 'unread_count_update') {\n        this.unreadCount = notification.count || 0;\n        return;\n      }\n\n      // Handle notification read status updates\n      if (notification.type === 'notification_read') {\n        const notificationIndex = this.notifications.findIndex(n => n.id === notification.notification_id);\n        if (notificationIndex !== -1) {\n          this.notifications[notificationIndex].is_read = true;\n          this.unreadCount = Math.max(0, this.unreadCount - 1);\n        }\n        return;\n      }\n\n      // Handle all notifications marked as read\n      if (notification.type === 'all_notifications_read') {\n        this.notifications.forEach(n => n.is_read = true);\n        this.unreadCount = 0;\n        return;\n      }\n\n      // Add to beginning of list if panel is open\n      if (this.showPanel) {\n        this.notifications.unshift(notification);\n      }\n\n      // Update unread count for new notifications\n      if (!notification.is_read) {\n        this.unreadCount++;\n      }\n\n      // Emit event for parent components\n      this.$emit('new-notification', notification);\n    },\n\n    onConnected() {\n      console.log('Connected to notification stream');\n      this.$emit('connected');\n    },\n\n    onError(error) {\n      console.error('Notification stream error:', error);\n      this.$emit('error', 'Connection to notification stream failed');\n    },\n\n    getNotificationIcon(type) {\n      const icons = {\n        'status_change': 'fas fa-sync-alt text-info',\n        'new_request': 'fas fa-file-alt text-success',\n        'request_update': 'fas fa-edit text-warning',\n        'system_alert': 'fas fa-exclamation-triangle text-danger',\n        'test': 'fas fa-vial text-secondary',\n        'connection': 'fas fa-plug text-success'\n      };\n      return icons[type] || 'fas fa-bell text-primary';\n    },\n\n    formatTime(timestamp) {\n      const date = new Date(timestamp);\n      const now = new Date();\n      const diffInMinutes = Math.floor((now - date) / (1000 * 60));\n      \n      if (diffInMinutes < 1) return 'Just now';\n      if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n      if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;\n      return date.toLocaleDateString();\n    }\n  }\n};\n</script>\n\n<style scoped>\n.admin-notifications {\n  position: relative;\n}\n\n.notification-bell {\n  position: relative;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 50%;\n  transition: background-color 0.2s;\n}\n\n.notification-bell:hover {\n  background-color: rgba(0, 0, 0, 0.1);\n}\n\n.notification-bell i {\n  font-size: 1.2rem;\n  color: #6c757d;\n}\n\n.notification-badge {\n  position: absolute;\n  top: 0;\n  right: 0;\n  background: #dc3545;\n  color: white;\n  border-radius: 50%;\n  padding: 2px 6px;\n  font-size: 0.75rem;\n  font-weight: bold;\n  min-width: 18px;\n  text-align: center;\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.1); }\n  100% { transform: scale(1); }\n}\n\n.notification-panel {\n  position: absolute;\n  top: 100%;\n  right: 0;\n  width: 400px;\n  max-height: 500px;\n  background: white;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  z-index: 1050;\n  overflow: hidden;\n}\n\n.notification-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem;\n  border-bottom: 1px solid #dee2e6;\n  background: #f8f9fa;\n}\n\n.notification-header h5 {\n  margin: 0;\n  font-weight: 600;\n}\n\n.notification-actions {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.notification-body {\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.notification-list {\n  padding: 0;\n}\n\n.notification-item {\n  display: flex;\n  align-items: flex-start;\n  padding: 1rem;\n  border-bottom: 1px solid #f1f3f4;\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n\n.notification-item:hover {\n  background-color: #f8f9fa;\n}\n\n.notification-item.unread {\n  background-color: #e3f2fd;\n  border-left: 4px solid #2196f3;\n}\n\n.notification-item.priority-high {\n  border-left: 4px solid #ff9800;\n}\n\n.notification-item.clickable {\n  cursor: pointer;\n  position: relative;\n}\n\n.notification-item.clickable:hover {\n  background-color: #f8f9fa;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.notification-item.loading {\n  opacity: 0.7;\n  pointer-events: none;\n}\n\n.notification-item.loading::after {\n  content: '';\n  position: absolute;\n  top: 50%;\n  right: 1rem;\n  width: 16px;\n  height: 16px;\n  border: 2px solid #f3f3f3;\n  border-top: 2px solid #007bff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.notification-icon {\n  margin-right: 0.75rem;\n  margin-top: 0.25rem;\n}\n\n.notification-content {\n  flex: 1;\n}\n\n.notification-title {\n  font-weight: 600;\n  margin-bottom: 0.25rem;\n  color: #212529;\n}\n\n.notification-message {\n  font-size: 0.875rem;\n  color: #6c757d;\n  margin-bottom: 0.25rem;\n  line-height: 1.4;\n}\n\n.notification-time {\n  font-size: 0.75rem;\n  color: #adb5bd;\n}\n\n.notification-priority {\n  margin-left: 0.5rem;\n  margin-top: 0.25rem;\n}\n\n.notification-footer {\n  padding: 0.75rem;\n  border-top: 1px solid #dee2e6;\n  background: #f8f9fa;\n}\n\n.notification-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1040;\n}\n\n@media (max-width: 768px) {\n  .notification-panel {\n    width: 320px;\n    right: -50px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;;EAICA,KAAK,EAAC;;;EAK9BA,KAAK,EAAC;AAAqB;;EAEzBA,KAAK,EAAC;AAAsB;;;EAgB9BA,KAAK,EAAC;AAAmB;;;EACRA,KAAK,EAAC;;;;EAOkBA,KAAK,EAAC;;;;EAKtCA,KAAK,EAAC;;;;EAaTA,KAAK,EAAC;AAAmB;;EAGzBA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAoB;;EAC1BA,KAAK,EAAC;AAAsB;;EAC5BA,KAAK,EAAC;AAAmB;;;EAE3BA,KAAK,EAAC;;;;EAMKA,KAAK,EAAC;;;;uBAnEhCC,mBAAA,CAkFM,OAlFNC,UAkFM,GAjFJC,mBAAA,4BAA+B,EAC/BC,mBAAA,CAGM;IAHDJ,KAAK,EAAC,mBAAmB;IAAEK,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,uBAAA,IAAAD,QAAA,CAAAC,uBAAA,IAAAF,IAAA,CAAuB;gCAC5DH,mBAAA,CAA2B;IAAxBJ,KAAK,EAAC;EAAa,6BACVU,KAAA,CAAAC,WAAW,Q,cAAvBV,mBAAA,CAA2G,QAA3GW,UAA2G,EAAAC,gBAAA,CAAhDH,KAAA,CAAAC,WAAW,gBAAgBD,KAAA,CAAAC,WAAW,oB,qCAGnGR,mBAAA,wBAA2B,EAChBO,KAAA,CAAAI,SAAS,I,cAApBb,mBAAA,CAsEM;;IAtEgBD,KAAK,EAAC,oBAAoB;IAAEK,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAS,cAAA,CAAN,QAAW;MAC1DX,mBAAA,CAgBM,OAhBNY,UAgBM,G,0BAfJZ,mBAAA,CAAsB,YAAlB,eAAa,sBACjBA,mBAAA,CAaM,OAbNa,UAaM,GAXIP,KAAA,CAAAC,WAAW,Q,cADnBV,mBAAA,CAQS;;IANNI,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAU,aAAA,IAAAV,QAAA,CAAAU,aAAA,IAAAX,IAAA,CAAa;IACrBP,KAAK,EAAC,gCAAgC;IACrCmB,QAAQ,EAAET,KAAA,CAAAU;gCAEXhB,mBAAA,CAAmC;IAAhCJ,KAAK,EAAC;EAAqB,6B,iBAAK,GACnC,GAAAa,gBAAA,CAAGH,KAAA,CAAAU,cAAc,kD,mEAEnBhB,mBAAA,CAES;IAFAC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,uBAAA,IAAAD,QAAA,CAAAC,uBAAA,IAAAF,IAAA,CAAuB;IAAEP,KAAK,EAAC;gCAC7CI,mBAAA,CAA4B;IAAzBJ,KAAK,EAAC;EAAc,2B,QAK7BI,mBAAA,CAkDM,OAlDNiB,UAkDM,GAjDOX,KAAA,CAAAY,OAAO,I,cAAlBrB,mBAAA,CAKM,OALNsB,UAKM,EAAAjB,MAAA,SAAAA,MAAA,QAJJF,mBAAA,CAEM;IAFDJ,KAAK,EAAC,kCAAkC;IAACwB,IAAI,EAAC;MACjDpB,mBAAA,CAA+C;IAAzCJ,KAAK,EAAC;EAAiB,GAAC,YAAU,E,qBAE1CI,mBAAA,CAAiD;IAA9CJ,KAAK,EAAC;EAAW,GAAC,0BAAwB,oB,MAG/BU,KAAA,CAAAe,aAAa,CAACC,MAAM,U,cAApCzB,mBAAA,CAGM,OAHN0B,UAGM,EAAArB,MAAA,SAAAA,MAAA,QAFJF,mBAAA,CAA4C;IAAzCJ,KAAK,EAAC;EAA8B,4BACvCI,mBAAA,CAAoC;IAAjCJ,KAAK,EAAC;EAAM,GAAC,kBAAgB,oB,qBAGlCC,mBAAA,CAyBM,OAzBN2B,UAyBM,I,kBAxBJ3B,mBAAA,CAuBM4B,SAAA,QAAAC,WAAA,CAtBmBpB,KAAA,CAAAe,aAAa,EAA7BM,YAAY;yBADrB9B,mBAAA,CAuBM;MArBH+B,GAAG,EAAED,YAAY,CAACE,EAAE;MACpB,sBAAoB,EAAEF,YAAY,CAACE,EAAE;MACtCjC,KAAK,EAAAkC,eAAA,EAAC,mBAAmB;mBACUH,YAAY,CAACI,OAAO;yBAAiCJ,YAAY,CAACK,QAAQ,eAAeL,YAAY,CAACK,QAAQ;;;MAKhJ/B,OAAK,EAAAgC,MAAA,IAAE7B,QAAA,CAAA8B,uBAAuB,CAACP,YAAY;QAE5C3B,mBAAA,CAEM,OAFNmC,WAEM,GADJnC,mBAAA,CAAuD;MAAnDJ,KAAK,EAAAkC,eAAA,CAAE1B,QAAA,CAAAgC,mBAAmB,CAACT,YAAY,CAACU,IAAI;+BAElDrC,mBAAA,CAIM,OAJNsC,WAIM,GAHJtC,mBAAA,CAA8D,OAA9DuC,WAA8D,EAAA9B,gBAAA,CAA3BkB,YAAY,CAACa,KAAK,kBACrDxC,mBAAA,CAAkE,OAAlEyC,WAAkE,EAAAhC,gBAAA,CAA7BkB,YAAY,CAACe,OAAO,kBACzD1C,mBAAA,CAA8E,OAA9E2C,WAA8E,EAAAlC,gBAAA,CAA5CL,QAAA,CAAAwC,UAAU,CAACjB,YAAY,CAACkB,UAAU,kB,GAE7BlB,YAAY,CAACK,QAAQ,eAAeL,YAAY,CAACK,QAAQ,iB,cAAlGnC,mBAAA,CAEM,OAFNiD,WAEM,OAAA5C,MAAA,SAAAA,MAAA,QADJF,mBAAA,CAAwD;MAArDJ,KAAK,EAAC;IAA0C,2B;qCAK9CU,KAAA,CAAAyC,OAAO,I,cAAlBlD,mBAAA,CASM,OATNmD,WASM,GARJhD,mBAAA,CAOS;IANNC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAA6C,QAAA,IAAA7C,QAAA,CAAA6C,QAAA,IAAA9C,IAAA,CAAQ;IAChBP,KAAK,EAAC,sCAAsC;IAC3CmB,QAAQ,EAAET,KAAA,CAAA4C;kCAEXlD,mBAAA,CAAmC;IAAhCJ,KAAK,EAAC;EAAqB,6B,iBAAK,GACnC,GAAAa,gBAAA,CAAGH,KAAA,CAAA4C,WAAW,8C,+GAMtBnD,mBAAA,aAAgB,EACLO,KAAA,CAAAI,SAAS,I,cAApBb,mBAAA,CAA0F;;IAApED,KAAK,EAAC,sBAAsB;IAAEK,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,uBAAA,IAAAD,QAAA,CAAAC,uBAAA,IAAAF,IAAA,CAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}