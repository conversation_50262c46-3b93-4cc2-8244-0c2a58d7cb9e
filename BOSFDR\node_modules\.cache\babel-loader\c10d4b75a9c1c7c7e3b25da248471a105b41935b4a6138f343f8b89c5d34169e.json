{"ast": null, "code": "import api from './api';\n\n/**\n * Service for handling notification navigation and validation\n */\nclass NotificationNavigationService {\n  /**\n   * Check if a request exists and is accessible to the current user\n   * @param {number} requestId - The request ID to check\n   * @param {string} userType - 'admin' or 'client'\n   * @returns {Promise<boolean>} - True if request exists and is accessible\n   */\n  async checkRequestExists(requestId, userType = 'client') {\n    try {\n      console.log(`🔍 Checking if request ${requestId} exists for ${userType}`);\n      const endpoint = userType === 'admin' ? `/admin/documents/requests/${requestId}/exists` : `/client/document-requests/${requestId}/exists`;\n      const response = await api.get(endpoint);\n      if (response.data && response.data.success) {\n        console.log(`✅ Request ${requestId} exists and is accessible`);\n        return true;\n      } else {\n        console.log(`❌ Request ${requestId} does not exist or is not accessible`);\n        return false;\n      }\n    } catch (error) {\n      console.error(`❌ Error checking request existence:`, error);\n\n      // If it's a 404, the request doesn't exist\n      if (error.response && error.response.status === 404) {\n        return false;\n      }\n\n      // For other errors, assume it exists to avoid blocking navigation\n      // The actual page will handle the error appropriately\n      console.log(`⚠️ Assuming request exists due to error`);\n      return true;\n    }\n  }\n\n  /**\n   * Check if a user exists and is accessible to the current admin\n   * @param {number} userId - The user ID to check\n   * @returns {Promise<boolean>} - True if user exists and is accessible\n   */\n  async checkUserExists(userId) {\n    try {\n      console.log(`🔍 Checking if user ${userId} exists`);\n      const response = await api.get(`/admin/users/${userId}/exists`);\n      if (response.data && response.data.success) {\n        console.log(`✅ User ${userId} exists and is accessible`);\n        return true;\n      } else {\n        console.log(`❌ User ${userId} does not exist or is not accessible`);\n        return false;\n      }\n    } catch (error) {\n      console.error(`❌ Error checking user existence:`, error);\n\n      // If it's a 404, the user doesn't exist\n      if (error.response && error.response.status === 404) {\n        return false;\n      }\n\n      // For other errors, assume it exists\n      console.log(`⚠️ Assuming user exists due to error`);\n      return true;\n    }\n  }\n\n  /**\n   * Validate notification data and extract relevant IDs\n   * @param {Object} notification - The notification object\n   * @returns {Object} - Parsed and validated notification data\n   */\n  parseNotificationData(notification) {\n    try {\n      let data = {};\n      if (notification.data) {\n        if (typeof notification.data === 'string') {\n          data = JSON.parse(notification.data);\n        } else if (typeof notification.data === 'object') {\n          data = notification.data;\n        }\n      }\n      return {\n        requestId: data.request_id || null,\n        userId: data.user_id || data.client_id || null,\n        userType: data.user_type || null,\n        requestNumber: data.request_number || null,\n        documentType: data.document_type || null,\n        priority: data.priority || null,\n        ...data // Include all other data\n      };\n    } catch (error) {\n      console.error('❌ Error parsing notification data:', error);\n      return {};\n    }\n  }\n\n  /**\n   * Show a toast notification for navigation errors\n   * @param {string} message - Error message to display\n   * @param {Function} emitFn - Function to emit error events\n   */\n  showNavigationError(message, emitFn) {\n    console.error('🚨 Navigation Error:', message);\n    if (typeof emitFn === 'function') {\n      emitFn('error', message);\n    }\n\n    // You could also integrate with a toast notification system here\n    // For example: this.$toast.error(message);\n  }\n\n  /**\n   * Get the appropriate icon for a notification type\n   * @param {string} type - Notification type\n   * @param {string} userType - 'admin' or 'client'\n   * @returns {string} - CSS classes for the icon\n   */\n  getNotificationIcon(type, userType = 'client') {\n    const adminIcons = {\n      'new_request': 'fas fa-file-plus text-success',\n      'status_change': 'fas fa-sync-alt text-info',\n      'request_update': 'fas fa-edit text-warning',\n      'request_cancelled': 'fas fa-times-circle text-danger',\n      'payment_confirmed': 'fas fa-check-circle text-success',\n      'payment_update': 'fas fa-money-bill text-info',\n      'system_alert': 'fas fa-exclamation-triangle text-danger',\n      'urgent_request': 'fas fa-exclamation-circle text-danger',\n      'user_registration': 'fas fa-user-plus text-info',\n      'new_user': 'fas fa-user-check text-success',\n      'test': 'fas fa-vial text-secondary',\n      'connection': 'fas fa-plug text-success'\n    };\n    const clientIcons = {\n      'status_change': 'fas fa-sync-alt text-info',\n      'payment_confirmed': 'fas fa-credit-card text-success',\n      'payment_required': 'fas fa-money-bill text-warning',\n      'payment_update': 'fas fa-money-bill text-info',\n      'document_ready': 'fas fa-file-check text-success',\n      'ready_for_pickup': 'fas fa-hand-paper text-success',\n      'request_approved': 'fas fa-check-circle text-success',\n      'request_rejected': 'fas fa-times-circle text-danger',\n      'request_update': 'fas fa-edit text-warning',\n      'system_alert': 'fas fa-exclamation-triangle text-danger',\n      'maintenance_notice': 'fas fa-tools text-warning',\n      'test': 'fas fa-vial text-secondary',\n      'connection': 'fas fa-plug text-success'\n    };\n    const icons = userType === 'admin' ? adminIcons : clientIcons;\n    return icons[type] || 'fas fa-bell text-primary';\n  }\n\n  /**\n   * Format notification time in a human-readable way\n   * @param {string} timestamp - ISO timestamp\n   * @returns {string} - Formatted time string\n   */\n  formatNotificationTime(timestamp) {\n    if (!timestamp) return '';\n    try {\n      const date = new Date(timestamp);\n      const now = new Date();\n      const diffInMinutes = Math.floor((now - date) / (1000 * 60));\n      if (diffInMinutes < 1) return 'Just now';\n      if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n      const diffInHours = Math.floor(diffInMinutes / 60);\n      if (diffInHours < 24) return `${diffInHours}h ago`;\n      const diffInDays = Math.floor(diffInHours / 24);\n      if (diffInDays < 7) return `${diffInDays}d ago`;\n      return date.toLocaleDateString();\n    } catch (error) {\n      console.error('Error formatting notification time:', error);\n      return '';\n    }\n  }\n}\n\n// Export a singleton instance\nexport default new NotificationNavigationService();", "map": {"version": 3, "names": ["api", "NotificationNavigationService", "checkRequestExists", "requestId", "userType", "console", "log", "endpoint", "response", "get", "data", "success", "error", "status", "checkUserExists", "userId", "parseNotificationData", "notification", "JSON", "parse", "request_id", "user_id", "client_id", "user_type", "requestNumber", "request_number", "documentType", "document_type", "priority", "showNavigationError", "message", "emitFn", "getNotificationIcon", "type", "adminIcons", "clientIcons", "icons", "formatNotificationTime", "timestamp", "date", "Date", "now", "diffInMinutes", "Math", "floor", "diffInHours", "diffInDays", "toLocaleDateString"], "sources": ["D:/cap2_rhai_front_and_back/BOSFDR/src/services/notificationNavigationService.js"], "sourcesContent": ["import api from './api';\n\n/**\n * Service for handling notification navigation and validation\n */\nclass NotificationNavigationService {\n  /**\n   * Check if a request exists and is accessible to the current user\n   * @param {number} requestId - The request ID to check\n   * @param {string} userType - 'admin' or 'client'\n   * @returns {Promise<boolean>} - True if request exists and is accessible\n   */\n  async checkRequestExists(requestId, userType = 'client') {\n    try {\n      console.log(`🔍 Checking if request ${requestId} exists for ${userType}`);\n      \n      const endpoint = userType === 'admin' \n        ? `/admin/documents/requests/${requestId}/exists`\n        : `/client/document-requests/${requestId}/exists`;\n      \n      const response = await api.get(endpoint);\n      \n      if (response.data && response.data.success) {\n        console.log(`✅ Request ${requestId} exists and is accessible`);\n        return true;\n      } else {\n        console.log(`❌ Request ${requestId} does not exist or is not accessible`);\n        return false;\n      }\n    } catch (error) {\n      console.error(`❌ Error checking request existence:`, error);\n      \n      // If it's a 404, the request doesn't exist\n      if (error.response && error.response.status === 404) {\n        return false;\n      }\n      \n      // For other errors, assume it exists to avoid blocking navigation\n      // The actual page will handle the error appropriately\n      console.log(`⚠️ Assuming request exists due to error`);\n      return true;\n    }\n  }\n\n  /**\n   * Check if a user exists and is accessible to the current admin\n   * @param {number} userId - The user ID to check\n   * @returns {Promise<boolean>} - True if user exists and is accessible\n   */\n  async checkUserExists(userId) {\n    try {\n      console.log(`🔍 Checking if user ${userId} exists`);\n      \n      const response = await api.get(`/admin/users/${userId}/exists`);\n      \n      if (response.data && response.data.success) {\n        console.log(`✅ User ${userId} exists and is accessible`);\n        return true;\n      } else {\n        console.log(`❌ User ${userId} does not exist or is not accessible`);\n        return false;\n      }\n    } catch (error) {\n      console.error(`❌ Error checking user existence:`, error);\n      \n      // If it's a 404, the user doesn't exist\n      if (error.response && error.response.status === 404) {\n        return false;\n      }\n      \n      // For other errors, assume it exists\n      console.log(`⚠️ Assuming user exists due to error`);\n      return true;\n    }\n  }\n\n  /**\n   * Validate notification data and extract relevant IDs\n   * @param {Object} notification - The notification object\n   * @returns {Object} - Parsed and validated notification data\n   */\n  parseNotificationData(notification) {\n    try {\n      let data = {};\n      \n      if (notification.data) {\n        if (typeof notification.data === 'string') {\n          data = JSON.parse(notification.data);\n        } else if (typeof notification.data === 'object') {\n          data = notification.data;\n        }\n      }\n      \n      return {\n        requestId: data.request_id || null,\n        userId: data.user_id || data.client_id || null,\n        userType: data.user_type || null,\n        requestNumber: data.request_number || null,\n        documentType: data.document_type || null,\n        priority: data.priority || null,\n        ...data // Include all other data\n      };\n    } catch (error) {\n      console.error('❌ Error parsing notification data:', error);\n      return {};\n    }\n  }\n\n  /**\n   * Show a toast notification for navigation errors\n   * @param {string} message - Error message to display\n   * @param {Function} emitFn - Function to emit error events\n   */\n  showNavigationError(message, emitFn) {\n    console.error('🚨 Navigation Error:', message);\n    \n    if (typeof emitFn === 'function') {\n      emitFn('error', message);\n    }\n    \n    // You could also integrate with a toast notification system here\n    // For example: this.$toast.error(message);\n  }\n\n  /**\n   * Get the appropriate icon for a notification type\n   * @param {string} type - Notification type\n   * @param {string} userType - 'admin' or 'client'\n   * @returns {string} - CSS classes for the icon\n   */\n  getNotificationIcon(type, userType = 'client') {\n    const adminIcons = {\n      'new_request': 'fas fa-file-plus text-success',\n      'status_change': 'fas fa-sync-alt text-info',\n      'request_update': 'fas fa-edit text-warning',\n      'request_cancelled': 'fas fa-times-circle text-danger',\n      'payment_confirmed': 'fas fa-check-circle text-success',\n      'payment_update': 'fas fa-money-bill text-info',\n      'system_alert': 'fas fa-exclamation-triangle text-danger',\n      'urgent_request': 'fas fa-exclamation-circle text-danger',\n      'user_registration': 'fas fa-user-plus text-info',\n      'new_user': 'fas fa-user-check text-success',\n      'test': 'fas fa-vial text-secondary',\n      'connection': 'fas fa-plug text-success'\n    };\n\n    const clientIcons = {\n      'status_change': 'fas fa-sync-alt text-info',\n      'payment_confirmed': 'fas fa-credit-card text-success',\n      'payment_required': 'fas fa-money-bill text-warning',\n      'payment_update': 'fas fa-money-bill text-info',\n      'document_ready': 'fas fa-file-check text-success',\n      'ready_for_pickup': 'fas fa-hand-paper text-success',\n      'request_approved': 'fas fa-check-circle text-success',\n      'request_rejected': 'fas fa-times-circle text-danger',\n      'request_update': 'fas fa-edit text-warning',\n      'system_alert': 'fas fa-exclamation-triangle text-danger',\n      'maintenance_notice': 'fas fa-tools text-warning',\n      'test': 'fas fa-vial text-secondary',\n      'connection': 'fas fa-plug text-success'\n    };\n\n    const icons = userType === 'admin' ? adminIcons : clientIcons;\n    return icons[type] || 'fas fa-bell text-primary';\n  }\n\n  /**\n   * Format notification time in a human-readable way\n   * @param {string} timestamp - ISO timestamp\n   * @returns {string} - Formatted time string\n   */\n  formatNotificationTime(timestamp) {\n    if (!timestamp) return '';\n    \n    try {\n      const date = new Date(timestamp);\n      const now = new Date();\n      const diffInMinutes = Math.floor((now - date) / (1000 * 60));\n      \n      if (diffInMinutes < 1) return 'Just now';\n      if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n      \n      const diffInHours = Math.floor(diffInMinutes / 60);\n      if (diffInHours < 24) return `${diffInHours}h ago`;\n      \n      const diffInDays = Math.floor(diffInHours / 24);\n      if (diffInDays < 7) return `${diffInDays}d ago`;\n      \n      return date.toLocaleDateString();\n    } catch (error) {\n      console.error('Error formatting notification time:', error);\n      return '';\n    }\n  }\n}\n\n// Export a singleton instance\nexport default new NotificationNavigationService();\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;;AAEvB;AACA;AACA;AACA,MAAMC,6BAA6B,CAAC;EAClC;AACF;AACA;AACA;AACA;AACA;EACE,MAAMC,kBAAkBA,CAACC,SAAS,EAAEC,QAAQ,GAAG,QAAQ,EAAE;IACvD,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,0BAA0BH,SAAS,eAAeC,QAAQ,EAAE,CAAC;MAEzE,MAAMG,QAAQ,GAAGH,QAAQ,KAAK,OAAO,GACjC,6BAA6BD,SAAS,SAAS,GAC/C,6BAA6BA,SAAS,SAAS;MAEnD,MAAMK,QAAQ,GAAG,MAAMR,GAAG,CAACS,GAAG,CAACF,QAAQ,CAAC;MAExC,IAAIC,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAC1CN,OAAO,CAACC,GAAG,CAAC,aAAaH,SAAS,2BAA2B,CAAC;QAC9D,OAAO,IAAI;MACb,CAAC,MAAM;QACLE,OAAO,CAACC,GAAG,CAAC,aAAaH,SAAS,sCAAsC,CAAC;QACzE,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;;MAE3D;MACA,IAAIA,KAAK,CAACJ,QAAQ,IAAII,KAAK,CAACJ,QAAQ,CAACK,MAAM,KAAK,GAAG,EAAE;QACnD,OAAO,KAAK;MACd;;MAEA;MACA;MACAR,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMQ,eAAeA,CAACC,MAAM,EAAE;IAC5B,IAAI;MACFV,OAAO,CAACC,GAAG,CAAC,uBAAuBS,MAAM,SAAS,CAAC;MAEnD,MAAMP,QAAQ,GAAG,MAAMR,GAAG,CAACS,GAAG,CAAC,gBAAgBM,MAAM,SAAS,CAAC;MAE/D,IAAIP,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAC1CN,OAAO,CAACC,GAAG,CAAC,UAAUS,MAAM,2BAA2B,CAAC;QACxD,OAAO,IAAI;MACb,CAAC,MAAM;QACLV,OAAO,CAACC,GAAG,CAAC,UAAUS,MAAM,sCAAsC,CAAC;QACnE,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIA,KAAK,CAACJ,QAAQ,IAAII,KAAK,CAACJ,QAAQ,CAACK,MAAM,KAAK,GAAG,EAAE;QACnD,OAAO,KAAK;MACd;;MAEA;MACAR,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEU,qBAAqBA,CAACC,YAAY,EAAE;IAClC,IAAI;MACF,IAAIP,IAAI,GAAG,CAAC,CAAC;MAEb,IAAIO,YAAY,CAACP,IAAI,EAAE;QACrB,IAAI,OAAOO,YAAY,CAACP,IAAI,KAAK,QAAQ,EAAE;UACzCA,IAAI,GAAGQ,IAAI,CAACC,KAAK,CAACF,YAAY,CAACP,IAAI,CAAC;QACtC,CAAC,MAAM,IAAI,OAAOO,YAAY,CAACP,IAAI,KAAK,QAAQ,EAAE;UAChDA,IAAI,GAAGO,YAAY,CAACP,IAAI;QAC1B;MACF;MAEA,OAAO;QACLP,SAAS,EAAEO,IAAI,CAACU,UAAU,IAAI,IAAI;QAClCL,MAAM,EAAEL,IAAI,CAACW,OAAO,IAAIX,IAAI,CAACY,SAAS,IAAI,IAAI;QAC9ClB,QAAQ,EAAEM,IAAI,CAACa,SAAS,IAAI,IAAI;QAChCC,aAAa,EAAEd,IAAI,CAACe,cAAc,IAAI,IAAI;QAC1CC,YAAY,EAAEhB,IAAI,CAACiB,aAAa,IAAI,IAAI;QACxCC,QAAQ,EAAElB,IAAI,CAACkB,QAAQ,IAAI,IAAI;QAC/B,GAAGlB,IAAI,CAAC;MACV,CAAC;IACH,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,CAAC,CAAC;IACX;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEiB,mBAAmBA,CAACC,OAAO,EAAEC,MAAM,EAAE;IACnC1B,OAAO,CAACO,KAAK,CAAC,sBAAsB,EAAEkB,OAAO,CAAC;IAE9C,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;MAChCA,MAAM,CAAC,OAAO,EAAED,OAAO,CAAC;IAC1B;;IAEA;IACA;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEE,mBAAmBA,CAACC,IAAI,EAAE7B,QAAQ,GAAG,QAAQ,EAAE;IAC7C,MAAM8B,UAAU,GAAG;MACjB,aAAa,EAAE,+BAA+B;MAC9C,eAAe,EAAE,2BAA2B;MAC5C,gBAAgB,EAAE,0BAA0B;MAC5C,mBAAmB,EAAE,iCAAiC;MACtD,mBAAmB,EAAE,kCAAkC;MACvD,gBAAgB,EAAE,6BAA6B;MAC/C,cAAc,EAAE,yCAAyC;MACzD,gBAAgB,EAAE,uCAAuC;MACzD,mBAAmB,EAAE,4BAA4B;MACjD,UAAU,EAAE,gCAAgC;MAC5C,MAAM,EAAE,4BAA4B;MACpC,YAAY,EAAE;IAChB,CAAC;IAED,MAAMC,WAAW,GAAG;MAClB,eAAe,EAAE,2BAA2B;MAC5C,mBAAmB,EAAE,iCAAiC;MACtD,kBAAkB,EAAE,gCAAgC;MACpD,gBAAgB,EAAE,6BAA6B;MAC/C,gBAAgB,EAAE,gCAAgC;MAClD,kBAAkB,EAAE,gCAAgC;MACpD,kBAAkB,EAAE,kCAAkC;MACtD,kBAAkB,EAAE,iCAAiC;MACrD,gBAAgB,EAAE,0BAA0B;MAC5C,cAAc,EAAE,yCAAyC;MACzD,oBAAoB,EAAE,2BAA2B;MACjD,MAAM,EAAE,4BAA4B;MACpC,YAAY,EAAE;IAChB,CAAC;IAED,MAAMC,KAAK,GAAGhC,QAAQ,KAAK,OAAO,GAAG8B,UAAU,GAAGC,WAAW;IAC7D,OAAOC,KAAK,CAACH,IAAI,CAAC,IAAI,0BAA0B;EAClD;;EAEA;AACF;AACA;AACA;AACA;EACEI,sBAAsBA,CAACC,SAAS,EAAE;IAChC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,IAAI;MACF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;MAChC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,MAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,GAAGF,IAAI,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;MAE5D,IAAIG,aAAa,GAAG,CAAC,EAAE,OAAO,UAAU;MACxC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,OAAO;MAEtD,MAAMG,WAAW,GAAGF,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC;MAClD,IAAIG,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,OAAO;MAElD,MAAMC,UAAU,GAAGH,IAAI,CAACC,KAAK,CAACC,WAAW,GAAG,EAAE,CAAC;MAC/C,IAAIC,UAAU,GAAG,CAAC,EAAE,OAAO,GAAGA,UAAU,OAAO;MAE/C,OAAOP,IAAI,CAACQ,kBAAkB,CAAC,CAAC;IAClC,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO,EAAE;IACX;EACF;AACF;;AAEA;AACA,eAAe,IAAIX,6BAA6B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}