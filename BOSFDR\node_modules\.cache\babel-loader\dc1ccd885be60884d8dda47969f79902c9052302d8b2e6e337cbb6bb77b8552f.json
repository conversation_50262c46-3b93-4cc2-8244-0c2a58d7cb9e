{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { getAdminNotificationService } from '../../services/notificationService';\nimport notificationNavigationService from '../../services/notificationNavigationService';\nexport default {\n  name: 'AdminNotifications',\n  data() {\n    return {\n      showPanel: false,\n      notifications: [],\n      unreadCount: 0,\n      loading: false,\n      loadingMore: false,\n      markingAllRead: false,\n      currentPage: 1,\n      hasMore: true,\n      limit: 10,\n      notificationService: null // Store the admin-specific service instance\n    };\n  },\n  mounted() {\n    this.initializeNotifications();\n  },\n  beforeUnmount() {\n    this.cleanup();\n  },\n  methods: {\n    async initializeNotifications() {\n      try {\n        console.log('🚀 AdminNotifications: Initializing admin notification service');\n\n        // Get the admin-specific notification service\n        this.notificationService = getAdminNotificationService();\n\n        // Request notification permission\n        await this.notificationService.requestNotificationPermission();\n\n        // Initialize the admin notification service\n        await this.notificationService.init();\n\n        // Set up event listeners\n        this.notificationService.on('notification', this.handleNewNotification);\n        this.notificationService.on('connected', this.onConnected);\n        this.notificationService.on('error', this.onError);\n\n        // Load initial data\n        await this.loadUnreadCount();\n        console.log('✅ AdminNotifications: Admin notification service initialized successfully');\n      } catch (error) {\n        console.error('❌ AdminNotifications: Failed to initialize notifications:', error);\n        this.error = error.message || 'Failed to initialize notifications';\n      }\n    },\n    cleanup() {\n      if (this.notificationService) {\n        this.notificationService.off('notification', this.handleNewNotification);\n        this.notificationService.off('connected', this.onConnected);\n        this.notificationService.off('error', this.onError);\n      }\n    },\n    async toggleNotificationPanel() {\n      this.showPanel = !this.showPanel;\n      if (this.showPanel && this.notifications.length === 0) {\n        await this.loadNotifications();\n      }\n    },\n    async loadNotifications(page = 1) {\n      try {\n        if (page === 1) {\n          this.loading = true;\n          this.notifications = [];\n          this.currentPage = 1;\n        } else {\n          this.loadingMore = true;\n        }\n        console.log('🔍 AdminNotifications: Loading notifications, page:', page, 'limit:', this.limit);\n        const response = await this.notificationService.getNotifications(page, this.limit);\n        console.log('📨 AdminNotifications: Raw API response:', response);\n\n        // Handle the correct response structure from backend\n        let notifications = [];\n        let pagination = {};\n        if (response.data && response.data.notifications) {\n          // Backend returns: { success: true, data: { notifications: [...], pagination: {...} } }\n          if (Array.isArray(response.data.notifications)) {\n            notifications = response.data.notifications;\n            pagination = response.data.pagination || {};\n            console.log('✅ AdminNotifications: Parsed notifications:', notifications.length, 'items');\n            console.log('📊 AdminNotifications: Pagination:', pagination);\n          } else {\n            console.warn('⚠️ AdminNotifications: Invalid response structure - notifications is not an array');\n            console.log('📊 AdminNotifications: response.data.notifications:', response.data.notifications);\n          }\n        } else {\n          console.warn('⚠️ AdminNotifications: Invalid response structure - no data.notifications found');\n          console.log('📊 AdminNotifications: response.data:', response.data);\n        }\n        if (page === 1) {\n          this.notifications = notifications;\n        } else {\n          this.notifications.push(...notifications);\n        }\n        this.hasMore = pagination.page < pagination.pages;\n        this.currentPage = page;\n        console.log('📋 AdminNotifications: Final notifications array:', this.notifications);\n        console.log('📄 AdminNotifications: Has more pages:', this.hasMore);\n      } catch (error) {\n        console.error('❌ AdminNotifications: Failed to load notifications:', error);\n        this.$emit('error', 'Failed to load notifications');\n      } finally {\n        this.loading = false;\n        this.loadingMore = false;\n      }\n    },\n    async loadMore() {\n      if (this.hasMore && !this.loadingMore) {\n        await this.loadNotifications(this.currentPage + 1);\n      }\n    },\n    async loadUnreadCount() {\n      try {\n        this.unreadCount = await this.notificationService.getUnreadCount();\n      } catch (error) {\n        console.error('Failed to load unread count:', error);\n      }\n    },\n    async markAllAsRead() {\n      try {\n        this.markingAllRead = true;\n        await this.notificationService.markAllAsRead();\n\n        // Update local state\n        this.notifications.forEach(notification => {\n          notification.is_read = true;\n        });\n        this.unreadCount = 0;\n        this.$emit('notifications-read');\n      } catch (error) {\n        console.error('Failed to mark all as read:', error);\n        this.$emit('error', 'Failed to mark notifications as read');\n      } finally {\n        this.markingAllRead = false;\n      }\n    },\n    async handleNotificationClick(notification) {\n      console.log('🔔 Admin notification clicked:', notification);\n\n      // Show loading state\n      this.showLoadingState(notification);\n      try {\n        // Mark as read if not already read\n        if (!notification.is_read) {\n          await this.notificationService.markAsRead(notification.id);\n          notification.is_read = true;\n          this.unreadCount = Math.max(0, this.unreadCount - 1);\n          this.$emit('notification-read', notification);\n        }\n\n        // Navigate based on notification type and data\n        await this.navigateToRelevantPage(notification);\n\n        // Close notification panel after successful navigation\n        this.showPanel = false;\n      } catch (error) {\n        console.error('❌ Failed to handle notification click:', error);\n        this.showErrorToast('Failed to process notification');\n      } finally {\n        this.hideLoadingState(notification);\n      }\n\n      // Emit click event for parent components to handle\n      this.$emit('notification-click', notification);\n    },\n    /**\n     * Navigate to the relevant page based on notification type and data\n     */\n    async navigateToRelevantPage(notification) {\n      console.log('🧭 Determining navigation for notification:', notification.type, notification.data);\n      try {\n        const notificationData = typeof notification.data === 'string' ? JSON.parse(notification.data) : notification.data || {};\n        let targetRoute = null;\n        switch (notification.type) {\n          case 'new_request':\n            targetRoute = await this.handleNewRequestNavigation(notificationData);\n            break;\n          case 'status_change':\n          case 'request_update':\n            targetRoute = await this.handleRequestUpdateNavigation(notificationData);\n            break;\n          case 'request_cancelled':\n            targetRoute = await this.handleRequestCancellationNavigation(notificationData);\n            break;\n          case 'payment_confirmed':\n            targetRoute = await this.handlePaymentConfirmationNavigation(notificationData);\n            break;\n          case 'payment_update':\n            targetRoute = await this.handlePaymentNavigation(notificationData);\n            break;\n          case 'system_alert':\n          case 'urgent_request':\n            targetRoute = await this.handleSystemAlertNavigation(notificationData);\n            break;\n          case 'user_registration':\n          case 'new_user':\n            targetRoute = await this.handleUserNavigation(notificationData);\n            break;\n          default:\n            console.log('🤷 Unknown notification type, using default navigation');\n            targetRoute = await this.handleDefaultNavigation(notificationData);\n        }\n        if (targetRoute) {\n          console.log('🚀 Navigating to:', targetRoute);\n\n          // Add timeout to prevent hanging navigation\n          const navigationPromise = this.$router.push(targetRoute);\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Navigation timeout')), 5000);\n          });\n          await Promise.race([navigationPromise, timeoutPromise]);\n        } else {\n          console.log('ℹ️ No navigation target determined for notification');\n        }\n      } catch (error) {\n        console.error('❌ Navigation error:', error);\n        throw new Error('Failed to navigate to notification target');\n      }\n    },\n    /**\n     * Handle navigation for new request notifications\n     */\n    async handleNewRequestNavigation(data) {\n      if (data.request_id) {\n        // Check if request still exists\n        const exists = await this.checkRequestExists(data.request_id);\n        if (exists) {\n          // Open request details modal directly for better UX\n          return await this.openRequestDetailsModal(data.request_id);\n        } else {\n          this.showErrorToast('Request no longer exists');\n          return {\n            name: 'AdminRequests'\n          };\n        }\n      }\n      return {\n        name: 'AdminRequests'\n      };\n    },\n    /**\n     * Handle navigation for request update notifications\n     */\n    async handleRequestUpdateNavigation(data) {\n      if (data.request_id) {\n        const exists = await this.checkRequestExists(data.request_id);\n        if (exists) {\n          // Open request details modal directly for better UX\n          return await this.openRequestDetailsModal(data.request_id);\n        } else {\n          this.showErrorToast('Request no longer exists');\n          return {\n            name: 'AdminRequests'\n          };\n        }\n      }\n      return {\n        name: 'AdminRequests'\n      };\n    },\n    /**\n     * Handle navigation for request cancellation notifications\n     */\n    async handleRequestCancellationNavigation(data) {\n      if (data.request_id) {\n        const exists = await this.checkRequestExists(data.request_id);\n        if (exists) {\n          // Open request details modal to show the cancelled request\n          // Focus on status section to highlight the cancellation\n          return await this.openRequestDetailsModal(data.request_id, 'status');\n        } else {\n          this.showErrorToast('Request no longer exists');\n          return {\n            name: 'AdminRequests'\n          };\n        }\n      }\n      // If no specific request, show cancelled requests filter\n      return {\n        name: 'AdminRequests',\n        query: {\n          filter: 'cancelled'\n        }\n      };\n    },\n    /**\n     * Handle navigation for payment confirmation notifications\n     */\n    async handlePaymentConfirmationNavigation(data) {\n      if (data.request_id) {\n        const exists = await this.checkRequestExists(data.request_id);\n        if (exists) {\n          // Open request details modal to show the payment confirmation\n          // Focus on payment section to highlight the confirmation\n          return await this.openRequestDetailsModal(data.request_id, 'payment');\n        } else {\n          this.showErrorToast('Request no longer exists');\n          return {\n            name: 'AdminRequests'\n          };\n        }\n      }\n      // If no specific request, show payment confirmed requests filter\n      return {\n        name: 'AdminRequests',\n        query: {\n          filter: 'payment_confirmed'\n        }\n      };\n    },\n    /**\n     * Handle navigation for payment-related notifications\n     */\n    async handlePaymentNavigation(data) {\n      if (data.request_id) {\n        const exists = await this.checkRequestExists(data.request_id);\n        if (exists) {\n          // Open request details modal with focus on payment section\n          return await this.openRequestDetailsModal(data.request_id, 'payment');\n        }\n      }\n      // Fallback to reports page for payment overview\n      return {\n        name: 'AdminReports',\n        query: {\n          section: 'revenue'\n        }\n      };\n    },\n    /**\n     * Handle navigation for system alerts and urgent requests\n     */\n    async handleSystemAlertNavigation(data) {\n      if (data.request_id) {\n        return {\n          name: 'AdminRequests',\n          query: {\n            highlight: data.request_id,\n            filter: 'urgent'\n          }\n        };\n      }\n      // For general system alerts, go to dashboard\n      return {\n        name: 'AdminDashboard'\n      };\n    },\n    /**\n     * Handle navigation for user-related notifications\n     */\n    async handleUserNavigation(data) {\n      if (data.user_id || data.client_id) {\n        const userId = data.user_id || data.client_id;\n        const exists = await this.checkUserExists(userId);\n        if (exists) {\n          return {\n            name: 'AdminUsers',\n            query: {\n              highlight: userId,\n              type: data.user_type || 'client'\n            }\n          };\n        } else {\n          this.showErrorToast('User no longer exists');\n          return {\n            name: 'AdminUsers'\n          };\n        }\n      }\n      return {\n        name: 'AdminUsers'\n      };\n    },\n    /**\n     * Handle default navigation when type is unknown\n     */\n    async handleDefaultNavigation(data) {\n      // Priority order: request_id > user_id > dashboard\n      if (data.request_id) {\n        const exists = await this.checkRequestExists(data.request_id);\n        if (exists) {\n          // Open modal for request-related notifications for better UX\n          return await this.openRequestDetailsModal(data.request_id);\n        }\n      }\n      if (data.user_id || data.client_id) {\n        const userId = data.user_id || data.client_id;\n        const exists = await this.checkUserExists(userId);\n        if (exists) {\n          return {\n            name: 'AdminUsers',\n            query: {\n              highlight: userId,\n              type: data.user_type || 'client'\n            }\n          };\n        } else {\n          this.showErrorToast('User no longer exists');\n          return {\n            name: 'AdminUsers'\n          };\n        }\n      }\n      return {\n        name: 'AdminDashboard'\n      };\n    },\n    /**\n     * Check if a request still exists in the system\n     */\n    async checkRequestExists(requestId) {\n      return await notificationNavigationService.checkRequestExists(requestId, 'admin');\n    },\n    /**\n     * Check if a user still exists in the system\n     */\n    async checkUserExists(userId) {\n      return await notificationNavigationService.checkUserExists(userId);\n    },\n    /**\n     * Show loading state for a notification\n     */\n    showLoadingState(notification) {\n      // Add loading class to notification item\n      const notificationElement = document.querySelector(`[data-notification-id=\"${notification.id}\"]`);\n      if (notificationElement) {\n        notificationElement.classList.add('loading');\n      }\n    },\n    /**\n     * Hide loading state for a notification\n     */\n    hideLoadingState(notification) {\n      const notificationElement = document.querySelector(`[data-notification-id=\"${notification.id}\"]`);\n      if (notificationElement) {\n        notificationElement.classList.remove('loading');\n      }\n    },\n    /**\n     * Show error toast message\n     */\n    showErrorToast(message) {\n      notificationNavigationService.showNavigationError(message, this.$emit.bind(this));\n    },\n    /**\n     * Open request details modal by communicating with parent component\n     * This provides better UX by keeping context and avoiding page navigation\n     */\n    async openRequestDetailsModal(requestId, focusTab = null) {\n      try {\n        console.log('🔔 Opening request details modal for ID:', requestId, 'Focus tab:', focusTab);\n\n        // Emit event to parent component to open the modal\n        // The parent (AdminRequests or AdminHeader) will handle the modal opening\n        this.$emit('open-request-modal', {\n          requestId: requestId,\n          focusTab: focusTab\n        });\n\n        // Return null to indicate no navigation is needed\n        // The modal will be opened by the parent component\n        return null;\n      } catch (error) {\n        console.error('❌ Error opening request modal:', error);\n        // Fallback to page navigation if modal opening fails\n        return {\n          name: 'AdminRequests',\n          query: {\n            highlight: requestId,\n            tab: focusTab\n          }\n        };\n      }\n    },\n    handleNewNotification(notification, context = null) {\n      // Validate this is an admin notification\n      if (context && context.userType && context.userType !== 'admin') {\n        console.log('🚫 AdminNotifications: Ignoring non-admin notification:', context.userType);\n        return;\n      }\n\n      // Additional validation: check notification recipient type\n      if (notification.recipient_type && notification.recipient_type !== 'admin') {\n        console.log('🚫 AdminNotifications: Ignoring notification for:', notification.recipient_type);\n        return;\n      }\n      console.log('📢 AdminNotifications: Processing admin notification:', notification);\n\n      // Handle unread count updates from polling\n      if (notification.type === 'unread_count_update') {\n        this.unreadCount = notification.count || 0;\n        return;\n      }\n\n      // Handle notification read status updates\n      if (notification.type === 'notification_read') {\n        const notificationIndex = this.notifications.findIndex(n => n.id === notification.notification_id);\n        if (notificationIndex !== -1) {\n          this.notifications[notificationIndex].is_read = true;\n          this.unreadCount = Math.max(0, this.unreadCount - 1);\n        }\n        return;\n      }\n\n      // Handle all notifications marked as read\n      if (notification.type === 'all_notifications_read') {\n        this.notifications.forEach(n => n.is_read = true);\n        this.unreadCount = 0;\n        return;\n      }\n\n      // Add to beginning of list if panel is open\n      if (this.showPanel) {\n        this.notifications.unshift(notification);\n      }\n\n      // Update unread count for new notifications\n      if (!notification.is_read) {\n        this.unreadCount++;\n      }\n\n      // Emit event for parent components\n      this.$emit('new-notification', notification);\n    },\n    onConnected() {\n      console.log('Connected to notification stream');\n      this.$emit('connected');\n    },\n    onError(error) {\n      console.error('Notification stream error:', error);\n      this.$emit('error', 'Connection to notification stream failed');\n    },\n    getNotificationIcon(type) {\n      const icons = {\n        'status_change': 'fas fa-sync-alt text-info',\n        'new_request': 'fas fa-file-alt text-success',\n        'request_update': 'fas fa-edit text-warning',\n        'system_alert': 'fas fa-exclamation-triangle text-danger',\n        'test': 'fas fa-vial text-secondary',\n        'connection': 'fas fa-plug text-success'\n      };\n      return icons[type] || 'fas fa-bell text-primary';\n    },\n    formatTime(timestamp) {\n      const date = new Date(timestamp);\n      const now = new Date();\n      const diffInMinutes = Math.floor((now - date) / (1000 * 60));\n      if (diffInMinutes < 1) return 'Just now';\n      if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n      if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;\n      return date.toLocaleDateString();\n    }\n  }\n};", "map": {"version": 3, "names": ["getAdminNotificationService", "notificationNavigationService", "name", "data", "showPanel", "notifications", "unreadCount", "loading", "loadingMore", "markingAllRead", "currentPage", "hasMore", "limit", "notificationService", "mounted", "initializeNotifications", "beforeUnmount", "cleanup", "methods", "console", "log", "requestNotificationPermission", "init", "on", "handleNewNotification", "onConnected", "onError", "loadUnreadCount", "error", "message", "off", "toggleNotificationPanel", "length", "loadNotifications", "page", "response", "getNotifications", "pagination", "Array", "isArray", "warn", "push", "pages", "$emit", "loadMore", "getUnreadCount", "markAllAsRead", "for<PERSON>ach", "notification", "is_read", "handleNotificationClick", "showLoadingState", "mark<PERSON><PERSON><PERSON>", "id", "Math", "max", "navigateToRelevantPage", "showErrorToast", "hideLoadingState", "type", "notificationData", "JSON", "parse", "targetRoute", "handleNewRequestNavigation", "handleRequestUpdateNavigation", "handleRequestCancellationNavigation", "handlePaymentConfirmationNavigation", "handlePaymentNavigation", "handleSystemAlertNavigation", "handleUserNavigation", "handleDefaultNavigation", "navigationPromise", "$router", "timeoutPromise", "Promise", "_", "reject", "setTimeout", "Error", "race", "request_id", "exists", "checkRequestExists", "openRequestDetailsModal", "query", "filter", "section", "highlight", "user_id", "client_id", "userId", "checkUserExists", "user_type", "requestId", "notificationElement", "document", "querySelector", "classList", "add", "remove", "showNavigationError", "bind", "focusTab", "tab", "context", "userType", "recipient_type", "count", "notificationIndex", "findIndex", "n", "notification_id", "unshift", "getNotificationIcon", "icons", "formatTime", "timestamp", "date", "Date", "now", "diffInMinutes", "floor", "toLocaleDateString"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminNotifications.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-notifications\">\n    <!-- Notification Bell Icon -->\n    <div class=\"notification-bell\" @click=\"toggleNotificationPanel\">\n      <i class=\"fas fa-bell\"></i>\n      <span v-if=\"unreadCount > 0\" class=\"notification-badge\">{{ unreadCount > 99 ? '99+' : unreadCount }}</span>\n    </div>\n\n    <!-- Notification Panel -->\n    <div v-if=\"showPanel\" class=\"notification-panel\" @click.stop>\n      <div class=\"notification-header\">\n        <h5>Notifications</h5>\n        <div class=\"notification-actions\">\n          <button \n            v-if=\"unreadCount > 0\" \n            @click=\"markAllAsRead\" \n            class=\"btn btn-sm btn-outline-primary\"\n            :disabled=\"markingAllRead\"\n          >\n            <i class=\"fas fa-check-double\"></i>\n            {{ markingAllRead ? 'Marking...' : 'Mark All Read' }}\n          </button>\n          <button @click=\"toggleNotificationPanel\" class=\"btn btn-sm btn-outline-secondary\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n      </div>\n\n      <div class=\"notification-body\">\n        <div v-if=\"loading\" class=\"text-center p-3\">\n          <div class=\"spinner-border spinner-border-sm\" role=\"status\">\n            <span class=\"visually-hidden\">Loading...</span>\n          </div>\n          <p class=\"mt-2 mb-0\">Loading notifications...</p>\n        </div>\n\n        <div v-else-if=\"notifications.length === 0\" class=\"text-center p-4 text-muted\">\n          <i class=\"fas fa-bell-slash fa-2x mb-2\"></i>\n          <p class=\"mb-0\">No notifications</p>\n        </div>\n\n        <div v-else class=\"notification-list\">\n          <div\n            v-for=\"notification in notifications\"\n            :key=\"notification.id\"\n            :data-notification-id=\"notification.id\"\n            class=\"notification-item\"\n            :class=\"{\n              'unread': !notification.is_read,\n              'priority-high': notification.priority === 'high' || notification.priority === 'urgent',\n              'clickable': true\n            }\"\n            @click=\"handleNotificationClick(notification)\"\n          >\n            <div class=\"notification-icon\">\n              <i :class=\"getNotificationIcon(notification.type)\"></i>\n            </div>\n            <div class=\"notification-content\">\n              <div class=\"notification-title\">{{ notification.title }}</div>\n              <div class=\"notification-message\">{{ notification.message }}</div>\n              <div class=\"notification-time\">{{ formatTime(notification.created_at) }}</div>\n            </div>\n            <div class=\"notification-priority\" v-if=\"notification.priority === 'high' || notification.priority === 'urgent'\">\n              <i class=\"fas fa-exclamation-triangle text-warning\"></i>\n            </div>\n          </div>\n        </div>\n\n        <div v-if=\"hasMore\" class=\"notification-footer\">\n          <button \n            @click=\"loadMore\" \n            class=\"btn btn-sm btn-outline-primary w-100\"\n            :disabled=\"loadingMore\"\n          >\n            <i class=\"fas fa-chevron-down\"></i>\n            {{ loadingMore ? 'Loading...' : 'Load More' }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Overlay -->\n    <div v-if=\"showPanel\" class=\"notification-overlay\" @click=\"toggleNotificationPanel\"></div>\n  </div>\n</template>\n\n<script>\nimport { getAdminNotificationService } from '../../services/notificationService';\nimport notificationNavigationService from '../../services/notificationNavigationService';\n\nexport default {\n  name: 'AdminNotifications',\n  data() {\n    return {\n      showPanel: false,\n      notifications: [],\n      unreadCount: 0,\n      loading: false,\n      loadingMore: false,\n      markingAllRead: false,\n      currentPage: 1,\n      hasMore: true,\n      limit: 10,\n      notificationService: null // Store the admin-specific service instance\n    };\n  },\n  mounted() {\n    this.initializeNotifications();\n  },\n  beforeUnmount() {\n    this.cleanup();\n  },\n  methods: {\n    async initializeNotifications() {\n      try {\n        console.log('🚀 AdminNotifications: Initializing admin notification service');\n\n        // Get the admin-specific notification service\n        this.notificationService = getAdminNotificationService();\n\n        // Request notification permission\n        await this.notificationService.requestNotificationPermission();\n\n        // Initialize the admin notification service\n        await this.notificationService.init();\n\n        // Set up event listeners\n        this.notificationService.on('notification', this.handleNewNotification);\n        this.notificationService.on('connected', this.onConnected);\n        this.notificationService.on('error', this.onError);\n\n        // Load initial data\n        await this.loadUnreadCount();\n\n        console.log('✅ AdminNotifications: Admin notification service initialized successfully');\n\n      } catch (error) {\n        console.error('❌ AdminNotifications: Failed to initialize notifications:', error);\n        this.error = error.message || 'Failed to initialize notifications';\n      }\n    },\n\n    cleanup() {\n      if (this.notificationService) {\n        this.notificationService.off('notification', this.handleNewNotification);\n        this.notificationService.off('connected', this.onConnected);\n        this.notificationService.off('error', this.onError);\n      }\n    },\n\n    async toggleNotificationPanel() {\n      this.showPanel = !this.showPanel;\n      \n      if (this.showPanel && this.notifications.length === 0) {\n        await this.loadNotifications();\n      }\n    },\n\n    async loadNotifications(page = 1) {\n      try {\n        if (page === 1) {\n          this.loading = true;\n          this.notifications = [];\n          this.currentPage = 1;\n        } else {\n          this.loadingMore = true;\n        }\n\n        console.log('🔍 AdminNotifications: Loading notifications, page:', page, 'limit:', this.limit);\n        const response = await this.notificationService.getNotifications(page, this.limit);\n        console.log('📨 AdminNotifications: Raw API response:', response);\n\n        // Handle the correct response structure from backend\n        let notifications = [];\n        let pagination = {};\n\n        if (response.data && response.data.notifications) {\n          // Backend returns: { success: true, data: { notifications: [...], pagination: {...} } }\n          if (Array.isArray(response.data.notifications)) {\n            notifications = response.data.notifications;\n            pagination = response.data.pagination || {};\n            console.log('✅ AdminNotifications: Parsed notifications:', notifications.length, 'items');\n            console.log('📊 AdminNotifications: Pagination:', pagination);\n          } else {\n            console.warn('⚠️ AdminNotifications: Invalid response structure - notifications is not an array');\n            console.log('📊 AdminNotifications: response.data.notifications:', response.data.notifications);\n          }\n        } else {\n          console.warn('⚠️ AdminNotifications: Invalid response structure - no data.notifications found');\n          console.log('📊 AdminNotifications: response.data:', response.data);\n        }\n\n        if (page === 1) {\n          this.notifications = notifications;\n        } else {\n          this.notifications.push(...notifications);\n        }\n\n        this.hasMore = pagination.page < pagination.pages;\n        this.currentPage = page;\n\n        console.log('📋 AdminNotifications: Final notifications array:', this.notifications);\n        console.log('📄 AdminNotifications: Has more pages:', this.hasMore);\n\n      } catch (error) {\n        console.error('❌ AdminNotifications: Failed to load notifications:', error);\n        this.$emit('error', 'Failed to load notifications');\n      } finally {\n        this.loading = false;\n        this.loadingMore = false;\n      }\n    },\n\n    async loadMore() {\n      if (this.hasMore && !this.loadingMore) {\n        await this.loadNotifications(this.currentPage + 1);\n      }\n    },\n\n    async loadUnreadCount() {\n      try {\n        this.unreadCount = await this.notificationService.getUnreadCount();\n      } catch (error) {\n        console.error('Failed to load unread count:', error);\n      }\n    },\n\n    async markAllAsRead() {\n      try {\n        this.markingAllRead = true;\n        await this.notificationService.markAllAsRead();\n        \n        // Update local state\n        this.notifications.forEach(notification => {\n          notification.is_read = true;\n        });\n        this.unreadCount = 0;\n        \n        this.$emit('notifications-read');\n        \n      } catch (error) {\n        console.error('Failed to mark all as read:', error);\n        this.$emit('error', 'Failed to mark notifications as read');\n      } finally {\n        this.markingAllRead = false;\n      }\n    },\n\n    async handleNotificationClick(notification) {\n      console.log('🔔 Admin notification clicked:', notification);\n\n      // Show loading state\n      this.showLoadingState(notification);\n\n      try {\n        // Mark as read if not already read\n        if (!notification.is_read) {\n          await this.notificationService.markAsRead(notification.id);\n          notification.is_read = true;\n          this.unreadCount = Math.max(0, this.unreadCount - 1);\n          this.$emit('notification-read', notification);\n        }\n\n        // Navigate based on notification type and data\n        await this.navigateToRelevantPage(notification);\n\n        // Close notification panel after successful navigation\n        this.showPanel = false;\n\n      } catch (error) {\n        console.error('❌ Failed to handle notification click:', error);\n        this.showErrorToast('Failed to process notification');\n      } finally {\n        this.hideLoadingState(notification);\n      }\n\n      // Emit click event for parent components to handle\n      this.$emit('notification-click', notification);\n    },\n\n    /**\n     * Navigate to the relevant page based on notification type and data\n     */\n    async navigateToRelevantPage(notification) {\n      console.log('🧭 Determining navigation for notification:', notification.type, notification.data);\n\n      try {\n        const notificationData = typeof notification.data === 'string'\n          ? JSON.parse(notification.data)\n          : notification.data || {};\n\n        let targetRoute = null;\n\n        switch (notification.type) {\n          case 'new_request':\n            targetRoute = await this.handleNewRequestNavigation(notificationData);\n            break;\n\n          case 'status_change':\n          case 'request_update':\n            targetRoute = await this.handleRequestUpdateNavigation(notificationData);\n            break;\n\n          case 'request_cancelled':\n            targetRoute = await this.handleRequestCancellationNavigation(notificationData);\n            break;\n\n          case 'payment_confirmed':\n            targetRoute = await this.handlePaymentConfirmationNavigation(notificationData);\n            break;\n\n          case 'payment_update':\n            targetRoute = await this.handlePaymentNavigation(notificationData);\n            break;\n\n          case 'system_alert':\n          case 'urgent_request':\n            targetRoute = await this.handleSystemAlertNavigation(notificationData);\n            break;\n\n          case 'user_registration':\n          case 'new_user':\n            targetRoute = await this.handleUserNavigation(notificationData);\n            break;\n\n          default:\n            console.log('🤷 Unknown notification type, using default navigation');\n            targetRoute = await this.handleDefaultNavigation(notificationData);\n        }\n\n        if (targetRoute) {\n          console.log('🚀 Navigating to:', targetRoute);\n\n          // Add timeout to prevent hanging navigation\n          const navigationPromise = this.$router.push(targetRoute);\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Navigation timeout')), 5000);\n          });\n\n          await Promise.race([navigationPromise, timeoutPromise]);\n        } else {\n          console.log('ℹ️ No navigation target determined for notification');\n        }\n\n      } catch (error) {\n        console.error('❌ Navigation error:', error);\n        throw new Error('Failed to navigate to notification target');\n      }\n    },\n\n    /**\n     * Handle navigation for new request notifications\n     */\n    async handleNewRequestNavigation(data) {\n      if (data.request_id) {\n        // Check if request still exists\n        const exists = await this.checkRequestExists(data.request_id);\n        if (exists) {\n          // Open request details modal directly for better UX\n          return await this.openRequestDetailsModal(data.request_id);\n        } else {\n          this.showErrorToast('Request no longer exists');\n          return { name: 'AdminRequests' };\n        }\n      }\n      return { name: 'AdminRequests' };\n    },\n\n    /**\n     * Handle navigation for request update notifications\n     */\n    async handleRequestUpdateNavigation(data) {\n      if (data.request_id) {\n        const exists = await this.checkRequestExists(data.request_id);\n        if (exists) {\n          // Open request details modal directly for better UX\n          return await this.openRequestDetailsModal(data.request_id);\n        } else {\n          this.showErrorToast('Request no longer exists');\n          return { name: 'AdminRequests' };\n        }\n      }\n      return { name: 'AdminRequests' };\n    },\n\n    /**\n     * Handle navigation for request cancellation notifications\n     */\n    async handleRequestCancellationNavigation(data) {\n      if (data.request_id) {\n        const exists = await this.checkRequestExists(data.request_id);\n        if (exists) {\n          // Open request details modal to show the cancelled request\n          // Focus on status section to highlight the cancellation\n          return await this.openRequestDetailsModal(data.request_id, 'status');\n        } else {\n          this.showErrorToast('Request no longer exists');\n          return { name: 'AdminRequests' };\n        }\n      }\n      // If no specific request, show cancelled requests filter\n      return {\n        name: 'AdminRequests',\n        query: { filter: 'cancelled' }\n      };\n    },\n\n    /**\n     * Handle navigation for payment confirmation notifications\n     */\n    async handlePaymentConfirmationNavigation(data) {\n      if (data.request_id) {\n        const exists = await this.checkRequestExists(data.request_id);\n        if (exists) {\n          // Open request details modal to show the payment confirmation\n          // Focus on payment section to highlight the confirmation\n          return await this.openRequestDetailsModal(data.request_id, 'payment');\n        } else {\n          this.showErrorToast('Request no longer exists');\n          return { name: 'AdminRequests' };\n        }\n      }\n      // If no specific request, show payment confirmed requests filter\n      return {\n        name: 'AdminRequests',\n        query: { filter: 'payment_confirmed' }\n      };\n    },\n\n    /**\n     * Handle navigation for payment-related notifications\n     */\n    async handlePaymentNavigation(data) {\n      if (data.request_id) {\n        const exists = await this.checkRequestExists(data.request_id);\n        if (exists) {\n          // Open request details modal with focus on payment section\n          return await this.openRequestDetailsModal(data.request_id, 'payment');\n        }\n      }\n      // Fallback to reports page for payment overview\n      return { name: 'AdminReports', query: { section: 'revenue' } };\n    },\n\n    /**\n     * Handle navigation for system alerts and urgent requests\n     */\n    async handleSystemAlertNavigation(data) {\n      if (data.request_id) {\n        return {\n          name: 'AdminRequests',\n          query: {\n            highlight: data.request_id,\n            filter: 'urgent'\n          }\n        };\n      }\n      // For general system alerts, go to dashboard\n      return { name: 'AdminDashboard' };\n    },\n\n    /**\n     * Handle navigation for user-related notifications\n     */\n    async handleUserNavigation(data) {\n      if (data.user_id || data.client_id) {\n        const userId = data.user_id || data.client_id;\n        const exists = await this.checkUserExists(userId);\n        if (exists) {\n          return {\n            name: 'AdminUsers',\n            query: {\n              highlight: userId,\n              type: data.user_type || 'client'\n            }\n          };\n        } else {\n          this.showErrorToast('User no longer exists');\n          return { name: 'AdminUsers' };\n        }\n      }\n      return { name: 'AdminUsers' };\n    },\n\n    /**\n     * Handle default navigation when type is unknown\n     */\n    async handleDefaultNavigation(data) {\n      // Priority order: request_id > user_id > dashboard\n      if (data.request_id) {\n        const exists = await this.checkRequestExists(data.request_id);\n        if (exists) {\n          // Open modal for request-related notifications for better UX\n          return await this.openRequestDetailsModal(data.request_id);\n        }\n      }\n\n      if (data.user_id || data.client_id) {\n        const userId = data.user_id || data.client_id;\n        const exists = await this.checkUserExists(userId);\n        if (exists) {\n          return {\n            name: 'AdminUsers',\n            query: {\n              highlight: userId,\n              type: data.user_type || 'client'\n            }\n          };\n        } else {\n          this.showErrorToast('User no longer exists');\n          return { name: 'AdminUsers' };\n        }\n      }\n\n      return { name: 'AdminDashboard' };\n    },\n\n    /**\n     * Check if a request still exists in the system\n     */\n    async checkRequestExists(requestId) {\n      return await notificationNavigationService.checkRequestExists(requestId, 'admin');\n    },\n\n    /**\n     * Check if a user still exists in the system\n     */\n    async checkUserExists(userId) {\n      return await notificationNavigationService.checkUserExists(userId);\n    },\n\n    /**\n     * Show loading state for a notification\n     */\n    showLoadingState(notification) {\n      // Add loading class to notification item\n      const notificationElement = document.querySelector(`[data-notification-id=\"${notification.id}\"]`);\n      if (notificationElement) {\n        notificationElement.classList.add('loading');\n      }\n    },\n\n    /**\n     * Hide loading state for a notification\n     */\n    hideLoadingState(notification) {\n      const notificationElement = document.querySelector(`[data-notification-id=\"${notification.id}\"]`);\n      if (notificationElement) {\n        notificationElement.classList.remove('loading');\n      }\n    },\n\n    /**\n     * Show error toast message\n     */\n    showErrorToast(message) {\n      notificationNavigationService.showNavigationError(message, this.$emit.bind(this));\n    },\n\n    /**\n     * Open request details modal by communicating with parent component\n     * This provides better UX by keeping context and avoiding page navigation\n     */\n    async openRequestDetailsModal(requestId, focusTab = null) {\n      try {\n        console.log('🔔 Opening request details modal for ID:', requestId, 'Focus tab:', focusTab);\n\n        // Emit event to parent component to open the modal\n        // The parent (AdminRequests or AdminHeader) will handle the modal opening\n        this.$emit('open-request-modal', {\n          requestId: requestId,\n          focusTab: focusTab\n        });\n\n        // Return null to indicate no navigation is needed\n        // The modal will be opened by the parent component\n        return null;\n\n      } catch (error) {\n        console.error('❌ Error opening request modal:', error);\n        // Fallback to page navigation if modal opening fails\n        return {\n          name: 'AdminRequests',\n          query: {\n            highlight: requestId,\n            tab: focusTab\n          }\n        };\n      }\n    },\n\n    handleNewNotification(notification, context = null) {\n      // Validate this is an admin notification\n      if (context && context.userType && context.userType !== 'admin') {\n        console.log('🚫 AdminNotifications: Ignoring non-admin notification:', context.userType);\n        return;\n      }\n\n      // Additional validation: check notification recipient type\n      if (notification.recipient_type && notification.recipient_type !== 'admin') {\n        console.log('🚫 AdminNotifications: Ignoring notification for:', notification.recipient_type);\n        return;\n      }\n\n      console.log('📢 AdminNotifications: Processing admin notification:', notification);\n\n      // Handle unread count updates from polling\n      if (notification.type === 'unread_count_update') {\n        this.unreadCount = notification.count || 0;\n        return;\n      }\n\n      // Handle notification read status updates\n      if (notification.type === 'notification_read') {\n        const notificationIndex = this.notifications.findIndex(n => n.id === notification.notification_id);\n        if (notificationIndex !== -1) {\n          this.notifications[notificationIndex].is_read = true;\n          this.unreadCount = Math.max(0, this.unreadCount - 1);\n        }\n        return;\n      }\n\n      // Handle all notifications marked as read\n      if (notification.type === 'all_notifications_read') {\n        this.notifications.forEach(n => n.is_read = true);\n        this.unreadCount = 0;\n        return;\n      }\n\n      // Add to beginning of list if panel is open\n      if (this.showPanel) {\n        this.notifications.unshift(notification);\n      }\n\n      // Update unread count for new notifications\n      if (!notification.is_read) {\n        this.unreadCount++;\n      }\n\n      // Emit event for parent components\n      this.$emit('new-notification', notification);\n    },\n\n    onConnected() {\n      console.log('Connected to notification stream');\n      this.$emit('connected');\n    },\n\n    onError(error) {\n      console.error('Notification stream error:', error);\n      this.$emit('error', 'Connection to notification stream failed');\n    },\n\n    getNotificationIcon(type) {\n      const icons = {\n        'status_change': 'fas fa-sync-alt text-info',\n        'new_request': 'fas fa-file-alt text-success',\n        'request_update': 'fas fa-edit text-warning',\n        'system_alert': 'fas fa-exclamation-triangle text-danger',\n        'test': 'fas fa-vial text-secondary',\n        'connection': 'fas fa-plug text-success'\n      };\n      return icons[type] || 'fas fa-bell text-primary';\n    },\n\n    formatTime(timestamp) {\n      const date = new Date(timestamp);\n      const now = new Date();\n      const diffInMinutes = Math.floor((now - date) / (1000 * 60));\n      \n      if (diffInMinutes < 1) return 'Just now';\n      if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n      if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;\n      return date.toLocaleDateString();\n    }\n  }\n};\n</script>\n\n<style scoped>\n.admin-notifications {\n  position: relative;\n}\n\n.notification-bell {\n  position: relative;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 50%;\n  transition: background-color 0.2s;\n}\n\n.notification-bell:hover {\n  background-color: rgba(0, 0, 0, 0.1);\n}\n\n.notification-bell i {\n  font-size: 1.2rem;\n  color: #6c757d;\n}\n\n.notification-badge {\n  position: absolute;\n  top: 0;\n  right: 0;\n  background: #dc3545;\n  color: white;\n  border-radius: 50%;\n  padding: 2px 6px;\n  font-size: 0.75rem;\n  font-weight: bold;\n  min-width: 18px;\n  text-align: center;\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.1); }\n  100% { transform: scale(1); }\n}\n\n.notification-panel {\n  position: absolute;\n  top: 100%;\n  right: 0;\n  width: 400px;\n  max-height: 500px;\n  background: white;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  z-index: 1050;\n  overflow: hidden;\n}\n\n.notification-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem;\n  border-bottom: 1px solid #dee2e6;\n  background: #f8f9fa;\n}\n\n.notification-header h5 {\n  margin: 0;\n  font-weight: 600;\n}\n\n.notification-actions {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.notification-body {\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.notification-list {\n  padding: 0;\n}\n\n.notification-item {\n  display: flex;\n  align-items: flex-start;\n  padding: 1rem;\n  border-bottom: 1px solid #f1f3f4;\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n\n.notification-item:hover {\n  background-color: #f8f9fa;\n}\n\n.notification-item.unread {\n  background-color: #e3f2fd;\n  border-left: 4px solid #2196f3;\n}\n\n.notification-item.priority-high {\n  border-left: 4px solid #ff9800;\n}\n\n.notification-item.clickable {\n  cursor: pointer;\n  position: relative;\n}\n\n.notification-item.clickable:hover {\n  background-color: #f8f9fa;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.notification-item.loading {\n  opacity: 0.7;\n  pointer-events: none;\n}\n\n.notification-item.loading::after {\n  content: '';\n  position: absolute;\n  top: 50%;\n  right: 1rem;\n  width: 16px;\n  height: 16px;\n  border: 2px solid #f3f3f3;\n  border-top: 2px solid #007bff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.notification-icon {\n  margin-right: 0.75rem;\n  margin-top: 0.25rem;\n}\n\n.notification-content {\n  flex: 1;\n}\n\n.notification-title {\n  font-weight: 600;\n  margin-bottom: 0.25rem;\n  color: #212529;\n}\n\n.notification-message {\n  font-size: 0.875rem;\n  color: #6c757d;\n  margin-bottom: 0.25rem;\n  line-height: 1.4;\n}\n\n.notification-time {\n  font-size: 0.75rem;\n  color: #adb5bd;\n}\n\n.notification-priority {\n  margin-left: 0.5rem;\n  margin-top: 0.25rem;\n}\n\n.notification-footer {\n  padding: 0.75rem;\n  border-top: 1px solid #dee2e6;\n  background: #f8f9fa;\n}\n\n.notification-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1040;\n}\n\n@media (max-width: 768px) {\n  .notification-panel {\n    width: 320px;\n    right: -50px;\n  }\n}\n</style>\n"], "mappings": ";;;AAuFA,SAASA,2BAA0B,QAAS,oCAAoC;AAChF,OAAOC,6BAA4B,MAAO,8CAA8C;AAExF,eAAe;EACbC,IAAI,EAAE,oBAAoB;EAC1BC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,KAAK;MACdC,WAAW,EAAE,KAAK;MAClBC,cAAc,EAAE,KAAK;MACrBC,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,EAAE;MACTC,mBAAmB,EAAE,IAAG,CAAE;IAC5B,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,uBAAuB,CAAC,CAAC;EAChC,CAAC;EACDC,aAAaA,CAAA,EAAG;IACd,IAAI,CAACC,OAAO,CAAC,CAAC;EAChB,CAAC;EACDC,OAAO,EAAE;IACP,MAAMH,uBAAuBA,CAAA,EAAG;MAC9B,IAAI;QACFI,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;;QAE7E;QACA,IAAI,CAACP,mBAAkB,GAAIb,2BAA2B,CAAC,CAAC;;QAExD;QACA,MAAM,IAAI,CAACa,mBAAmB,CAACQ,6BAA6B,CAAC,CAAC;;QAE9D;QACA,MAAM,IAAI,CAACR,mBAAmB,CAACS,IAAI,CAAC,CAAC;;QAErC;QACA,IAAI,CAACT,mBAAmB,CAACU,EAAE,CAAC,cAAc,EAAE,IAAI,CAACC,qBAAqB,CAAC;QACvE,IAAI,CAACX,mBAAmB,CAACU,EAAE,CAAC,WAAW,EAAE,IAAI,CAACE,WAAW,CAAC;QAC1D,IAAI,CAACZ,mBAAmB,CAACU,EAAE,CAAC,OAAO,EAAE,IAAI,CAACG,OAAO,CAAC;;QAElD;QACA,MAAM,IAAI,CAACC,eAAe,CAAC,CAAC;QAE5BR,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;MAE1F,EAAE,OAAOQ,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,2DAA2D,EAAEA,KAAK,CAAC;QACjF,IAAI,CAACA,KAAI,GAAIA,KAAK,CAACC,OAAM,IAAK,oCAAoC;MACpE;IACF,CAAC;IAEDZ,OAAOA,CAAA,EAAG;MACR,IAAI,IAAI,CAACJ,mBAAmB,EAAE;QAC5B,IAAI,CAACA,mBAAmB,CAACiB,GAAG,CAAC,cAAc,EAAE,IAAI,CAACN,qBAAqB,CAAC;QACxE,IAAI,CAACX,mBAAmB,CAACiB,GAAG,CAAC,WAAW,EAAE,IAAI,CAACL,WAAW,CAAC;QAC3D,IAAI,CAACZ,mBAAmB,CAACiB,GAAG,CAAC,OAAO,EAAE,IAAI,CAACJ,OAAO,CAAC;MACrD;IACF,CAAC;IAED,MAAMK,uBAAuBA,CAAA,EAAG;MAC9B,IAAI,CAAC3B,SAAQ,GAAI,CAAC,IAAI,CAACA,SAAS;MAEhC,IAAI,IAAI,CAACA,SAAQ,IAAK,IAAI,CAACC,aAAa,CAAC2B,MAAK,KAAM,CAAC,EAAE;QACrD,MAAM,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAChC;IACF,CAAC;IAED,MAAMA,iBAAiBA,CAACC,IAAG,GAAI,CAAC,EAAE;MAChC,IAAI;QACF,IAAIA,IAAG,KAAM,CAAC,EAAE;UACd,IAAI,CAAC3B,OAAM,GAAI,IAAI;UACnB,IAAI,CAACF,aAAY,GAAI,EAAE;UACvB,IAAI,CAACK,WAAU,GAAI,CAAC;QACtB,OAAO;UACL,IAAI,CAACF,WAAU,GAAI,IAAI;QACzB;QAEAW,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEc,IAAI,EAAE,QAAQ,EAAE,IAAI,CAACtB,KAAK,CAAC;QAC9F,MAAMuB,QAAO,GAAI,MAAM,IAAI,CAACtB,mBAAmB,CAACuB,gBAAgB,CAACF,IAAI,EAAE,IAAI,CAACtB,KAAK,CAAC;QAClFO,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEe,QAAQ,CAAC;;QAEjE;QACA,IAAI9B,aAAY,GAAI,EAAE;QACtB,IAAIgC,UAAS,GAAI,CAAC,CAAC;QAEnB,IAAIF,QAAQ,CAAChC,IAAG,IAAKgC,QAAQ,CAAChC,IAAI,CAACE,aAAa,EAAE;UAChD;UACA,IAAIiC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAChC,IAAI,CAACE,aAAa,CAAC,EAAE;YAC9CA,aAAY,GAAI8B,QAAQ,CAAChC,IAAI,CAACE,aAAa;YAC3CgC,UAAS,GAAIF,QAAQ,CAAChC,IAAI,CAACkC,UAAS,IAAK,CAAC,CAAC;YAC3ClB,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEf,aAAa,CAAC2B,MAAM,EAAE,OAAO,CAAC;YACzFb,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEiB,UAAU,CAAC;UAC/D,OAAO;YACLlB,OAAO,CAACqB,IAAI,CAAC,mFAAmF,CAAC;YACjGrB,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEe,QAAQ,CAAChC,IAAI,CAACE,aAAa,CAAC;UACjG;QACF,OAAO;UACLc,OAAO,CAACqB,IAAI,CAAC,iFAAiF,CAAC;UAC/FrB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEe,QAAQ,CAAChC,IAAI,CAAC;QACrE;QAEA,IAAI+B,IAAG,KAAM,CAAC,EAAE;UACd,IAAI,CAAC7B,aAAY,GAAIA,aAAa;QACpC,OAAO;UACL,IAAI,CAACA,aAAa,CAACoC,IAAI,CAAC,GAAGpC,aAAa,CAAC;QAC3C;QAEA,IAAI,CAACM,OAAM,GAAI0B,UAAU,CAACH,IAAG,GAAIG,UAAU,CAACK,KAAK;QACjD,IAAI,CAAChC,WAAU,GAAIwB,IAAI;QAEvBf,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE,IAAI,CAACf,aAAa,CAAC;QACpFc,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAACT,OAAO,CAAC;MAErE,EAAE,OAAOiB,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;QAC3E,IAAI,CAACe,KAAK,CAAC,OAAO,EAAE,8BAA8B,CAAC;MACrD,UAAU;QACR,IAAI,CAACpC,OAAM,GAAI,KAAK;QACpB,IAAI,CAACC,WAAU,GAAI,KAAK;MAC1B;IACF,CAAC;IAED,MAAMoC,QAAQA,CAAA,EAAG;MACf,IAAI,IAAI,CAACjC,OAAM,IAAK,CAAC,IAAI,CAACH,WAAW,EAAE;QACrC,MAAM,IAAI,CAACyB,iBAAiB,CAAC,IAAI,CAACvB,WAAU,GAAI,CAAC,CAAC;MACpD;IACF,CAAC;IAED,MAAMiB,eAAeA,CAAA,EAAG;MACtB,IAAI;QACF,IAAI,CAACrB,WAAU,GAAI,MAAM,IAAI,CAACO,mBAAmB,CAACgC,cAAc,CAAC,CAAC;MACpE,EAAE,OAAOjB,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAED,MAAMkB,aAAaA,CAAA,EAAG;MACpB,IAAI;QACF,IAAI,CAACrC,cAAa,GAAI,IAAI;QAC1B,MAAM,IAAI,CAACI,mBAAmB,CAACiC,aAAa,CAAC,CAAC;;QAE9C;QACA,IAAI,CAACzC,aAAa,CAAC0C,OAAO,CAACC,YAAW,IAAK;UACzCA,YAAY,CAACC,OAAM,GAAI,IAAI;QAC7B,CAAC,CAAC;QACF,IAAI,CAAC3C,WAAU,GAAI,CAAC;QAEpB,IAAI,CAACqC,KAAK,CAAC,oBAAoB,CAAC;MAElC,EAAE,OAAOf,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACe,KAAK,CAAC,OAAO,EAAE,sCAAsC,CAAC;MAC7D,UAAU;QACR,IAAI,CAAClC,cAAa,GAAI,KAAK;MAC7B;IACF,CAAC;IAED,MAAMyC,uBAAuBA,CAACF,YAAY,EAAE;MAC1C7B,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE4B,YAAY,CAAC;;MAE3D;MACA,IAAI,CAACG,gBAAgB,CAACH,YAAY,CAAC;MAEnC,IAAI;QACF;QACA,IAAI,CAACA,YAAY,CAACC,OAAO,EAAE;UACzB,MAAM,IAAI,CAACpC,mBAAmB,CAACuC,UAAU,CAACJ,YAAY,CAACK,EAAE,CAAC;UAC1DL,YAAY,CAACC,OAAM,GAAI,IAAI;UAC3B,IAAI,CAAC3C,WAAU,GAAIgD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACjD,WAAU,GAAI,CAAC,CAAC;UACpD,IAAI,CAACqC,KAAK,CAAC,mBAAmB,EAAEK,YAAY,CAAC;QAC/C;;QAEA;QACA,MAAM,IAAI,CAACQ,sBAAsB,CAACR,YAAY,CAAC;;QAE/C;QACA,IAAI,CAAC5C,SAAQ,GAAI,KAAK;MAExB,EAAE,OAAOwB,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAAC6B,cAAc,CAAC,gCAAgC,CAAC;MACvD,UAAU;QACR,IAAI,CAACC,gBAAgB,CAACV,YAAY,CAAC;MACrC;;MAEA;MACA,IAAI,CAACL,KAAK,CAAC,oBAAoB,EAAEK,YAAY,CAAC;IAChD,CAAC;IAED;;;IAGA,MAAMQ,sBAAsBA,CAACR,YAAY,EAAE;MACzC7B,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE4B,YAAY,CAACW,IAAI,EAAEX,YAAY,CAAC7C,IAAI,CAAC;MAEhG,IAAI;QACF,MAAMyD,gBAAe,GAAI,OAAOZ,YAAY,CAAC7C,IAAG,KAAM,QAAO,GACzD0D,IAAI,CAACC,KAAK,CAACd,YAAY,CAAC7C,IAAI,IAC5B6C,YAAY,CAAC7C,IAAG,IAAK,CAAC,CAAC;QAE3B,IAAI4D,WAAU,GAAI,IAAI;QAEtB,QAAQf,YAAY,CAACW,IAAI;UACvB,KAAK,aAAa;YAChBI,WAAU,GAAI,MAAM,IAAI,CAACC,0BAA0B,CAACJ,gBAAgB,CAAC;YACrE;UAEF,KAAK,eAAe;UACpB,KAAK,gBAAgB;YACnBG,WAAU,GAAI,MAAM,IAAI,CAACE,6BAA6B,CAACL,gBAAgB,CAAC;YACxE;UAEF,KAAK,mBAAmB;YACtBG,WAAU,GAAI,MAAM,IAAI,CAACG,mCAAmC,CAACN,gBAAgB,CAAC;YAC9E;UAEF,KAAK,mBAAmB;YACtBG,WAAU,GAAI,MAAM,IAAI,CAACI,mCAAmC,CAACP,gBAAgB,CAAC;YAC9E;UAEF,KAAK,gBAAgB;YACnBG,WAAU,GAAI,MAAM,IAAI,CAACK,uBAAuB,CAACR,gBAAgB,CAAC;YAClE;UAEF,KAAK,cAAc;UACnB,KAAK,gBAAgB;YACnBG,WAAU,GAAI,MAAM,IAAI,CAACM,2BAA2B,CAACT,gBAAgB,CAAC;YACtE;UAEF,KAAK,mBAAmB;UACxB,KAAK,UAAU;YACbG,WAAU,GAAI,MAAM,IAAI,CAACO,oBAAoB,CAACV,gBAAgB,CAAC;YAC/D;UAEF;YACEzC,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;YACrE2C,WAAU,GAAI,MAAM,IAAI,CAACQ,uBAAuB,CAACX,gBAAgB,CAAC;QACtE;QAEA,IAAIG,WAAW,EAAE;UACf5C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE2C,WAAW,CAAC;;UAE7C;UACA,MAAMS,iBAAgB,GAAI,IAAI,CAACC,OAAO,CAAChC,IAAI,CAACsB,WAAW,CAAC;UACxD,MAAMW,cAAa,GAAI,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChDC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAIE,KAAK,CAAC,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC;UACjE,CAAC,CAAC;UAEF,MAAMJ,OAAO,CAACK,IAAI,CAAC,CAACR,iBAAiB,EAAEE,cAAc,CAAC,CAAC;QACzD,OAAO;UACLvD,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;QACpE;MAEF,EAAE,OAAOQ,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,MAAM,IAAImD,KAAK,CAAC,2CAA2C,CAAC;MAC9D;IACF,CAAC;IAED;;;IAGA,MAAMf,0BAA0BA,CAAC7D,IAAI,EAAE;MACrC,IAAIA,IAAI,CAAC8E,UAAU,EAAE;QACnB;QACA,MAAMC,MAAK,GAAI,MAAM,IAAI,CAACC,kBAAkB,CAAChF,IAAI,CAAC8E,UAAU,CAAC;QAC7D,IAAIC,MAAM,EAAE;UACV;UACA,OAAO,MAAM,IAAI,CAACE,uBAAuB,CAACjF,IAAI,CAAC8E,UAAU,CAAC;QAC5D,OAAO;UACL,IAAI,CAACxB,cAAc,CAAC,0BAA0B,CAAC;UAC/C,OAAO;YAAEvD,IAAI,EAAE;UAAgB,CAAC;QAClC;MACF;MACA,OAAO;QAAEA,IAAI,EAAE;MAAgB,CAAC;IAClC,CAAC;IAED;;;IAGA,MAAM+D,6BAA6BA,CAAC9D,IAAI,EAAE;MACxC,IAAIA,IAAI,CAAC8E,UAAU,EAAE;QACnB,MAAMC,MAAK,GAAI,MAAM,IAAI,CAACC,kBAAkB,CAAChF,IAAI,CAAC8E,UAAU,CAAC;QAC7D,IAAIC,MAAM,EAAE;UACV;UACA,OAAO,MAAM,IAAI,CAACE,uBAAuB,CAACjF,IAAI,CAAC8E,UAAU,CAAC;QAC5D,OAAO;UACL,IAAI,CAACxB,cAAc,CAAC,0BAA0B,CAAC;UAC/C,OAAO;YAAEvD,IAAI,EAAE;UAAgB,CAAC;QAClC;MACF;MACA,OAAO;QAAEA,IAAI,EAAE;MAAgB,CAAC;IAClC,CAAC;IAED;;;IAGA,MAAMgE,mCAAmCA,CAAC/D,IAAI,EAAE;MAC9C,IAAIA,IAAI,CAAC8E,UAAU,EAAE;QACnB,MAAMC,MAAK,GAAI,MAAM,IAAI,CAACC,kBAAkB,CAAChF,IAAI,CAAC8E,UAAU,CAAC;QAC7D,IAAIC,MAAM,EAAE;UACV;UACA;UACA,OAAO,MAAM,IAAI,CAACE,uBAAuB,CAACjF,IAAI,CAAC8E,UAAU,EAAE,QAAQ,CAAC;QACtE,OAAO;UACL,IAAI,CAACxB,cAAc,CAAC,0BAA0B,CAAC;UAC/C,OAAO;YAAEvD,IAAI,EAAE;UAAgB,CAAC;QAClC;MACF;MACA;MACA,OAAO;QACLA,IAAI,EAAE,eAAe;QACrBmF,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAY;MAC/B,CAAC;IACH,CAAC;IAED;;;IAGA,MAAMnB,mCAAmCA,CAAChE,IAAI,EAAE;MAC9C,IAAIA,IAAI,CAAC8E,UAAU,EAAE;QACnB,MAAMC,MAAK,GAAI,MAAM,IAAI,CAACC,kBAAkB,CAAChF,IAAI,CAAC8E,UAAU,CAAC;QAC7D,IAAIC,MAAM,EAAE;UACV;UACA;UACA,OAAO,MAAM,IAAI,CAACE,uBAAuB,CAACjF,IAAI,CAAC8E,UAAU,EAAE,SAAS,CAAC;QACvE,OAAO;UACL,IAAI,CAACxB,cAAc,CAAC,0BAA0B,CAAC;UAC/C,OAAO;YAAEvD,IAAI,EAAE;UAAgB,CAAC;QAClC;MACF;MACA;MACA,OAAO;QACLA,IAAI,EAAE,eAAe;QACrBmF,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAoB;MACvC,CAAC;IACH,CAAC;IAED;;;IAGA,MAAMlB,uBAAuBA,CAACjE,IAAI,EAAE;MAClC,IAAIA,IAAI,CAAC8E,UAAU,EAAE;QACnB,MAAMC,MAAK,GAAI,MAAM,IAAI,CAACC,kBAAkB,CAAChF,IAAI,CAAC8E,UAAU,CAAC;QAC7D,IAAIC,MAAM,EAAE;UACV;UACA,OAAO,MAAM,IAAI,CAACE,uBAAuB,CAACjF,IAAI,CAAC8E,UAAU,EAAE,SAAS,CAAC;QACvE;MACF;MACA;MACA,OAAO;QAAE/E,IAAI,EAAE,cAAc;QAAEmF,KAAK,EAAE;UAAEE,OAAO,EAAE;QAAU;MAAE,CAAC;IAChE,CAAC;IAED;;;IAGA,MAAMlB,2BAA2BA,CAAClE,IAAI,EAAE;MACtC,IAAIA,IAAI,CAAC8E,UAAU,EAAE;QACnB,OAAO;UACL/E,IAAI,EAAE,eAAe;UACrBmF,KAAK,EAAE;YACLG,SAAS,EAAErF,IAAI,CAAC8E,UAAU;YAC1BK,MAAM,EAAE;UACV;QACF,CAAC;MACH;MACA;MACA,OAAO;QAAEpF,IAAI,EAAE;MAAiB,CAAC;IACnC,CAAC;IAED;;;IAGA,MAAMoE,oBAAoBA,CAACnE,IAAI,EAAE;MAC/B,IAAIA,IAAI,CAACsF,OAAM,IAAKtF,IAAI,CAACuF,SAAS,EAAE;QAClC,MAAMC,MAAK,GAAIxF,IAAI,CAACsF,OAAM,IAAKtF,IAAI,CAACuF,SAAS;QAC7C,MAAMR,MAAK,GAAI,MAAM,IAAI,CAACU,eAAe,CAACD,MAAM,CAAC;QACjD,IAAIT,MAAM,EAAE;UACV,OAAO;YACLhF,IAAI,EAAE,YAAY;YAClBmF,KAAK,EAAE;cACLG,SAAS,EAAEG,MAAM;cACjBhC,IAAI,EAAExD,IAAI,CAAC0F,SAAQ,IAAK;YAC1B;UACF,CAAC;QACH,OAAO;UACL,IAAI,CAACpC,cAAc,CAAC,uBAAuB,CAAC;UAC5C,OAAO;YAAEvD,IAAI,EAAE;UAAa,CAAC;QAC/B;MACF;MACA,OAAO;QAAEA,IAAI,EAAE;MAAa,CAAC;IAC/B,CAAC;IAED;;;IAGA,MAAMqE,uBAAuBA,CAACpE,IAAI,EAAE;MAClC;MACA,IAAIA,IAAI,CAAC8E,UAAU,EAAE;QACnB,MAAMC,MAAK,GAAI,MAAM,IAAI,CAACC,kBAAkB,CAAChF,IAAI,CAAC8E,UAAU,CAAC;QAC7D,IAAIC,MAAM,EAAE;UACV;UACA,OAAO,MAAM,IAAI,CAACE,uBAAuB,CAACjF,IAAI,CAAC8E,UAAU,CAAC;QAC5D;MACF;MAEA,IAAI9E,IAAI,CAACsF,OAAM,IAAKtF,IAAI,CAACuF,SAAS,EAAE;QAClC,MAAMC,MAAK,GAAIxF,IAAI,CAACsF,OAAM,IAAKtF,IAAI,CAACuF,SAAS;QAC7C,MAAMR,MAAK,GAAI,MAAM,IAAI,CAACU,eAAe,CAACD,MAAM,CAAC;QACjD,IAAIT,MAAM,EAAE;UACV,OAAO;YACLhF,IAAI,EAAE,YAAY;YAClBmF,KAAK,EAAE;cACLG,SAAS,EAAEG,MAAM;cACjBhC,IAAI,EAAExD,IAAI,CAAC0F,SAAQ,IAAK;YAC1B;UACF,CAAC;QACH,OAAO;UACL,IAAI,CAACpC,cAAc,CAAC,uBAAuB,CAAC;UAC5C,OAAO;YAAEvD,IAAI,EAAE;UAAa,CAAC;QAC/B;MACF;MAEA,OAAO;QAAEA,IAAI,EAAE;MAAiB,CAAC;IACnC,CAAC;IAED;;;IAGA,MAAMiF,kBAAkBA,CAACW,SAAS,EAAE;MAClC,OAAO,MAAM7F,6BAA6B,CAACkF,kBAAkB,CAACW,SAAS,EAAE,OAAO,CAAC;IACnF,CAAC;IAED;;;IAGA,MAAMF,eAAeA,CAACD,MAAM,EAAE;MAC5B,OAAO,MAAM1F,6BAA6B,CAAC2F,eAAe,CAACD,MAAM,CAAC;IACpE,CAAC;IAED;;;IAGAxC,gBAAgBA,CAACH,YAAY,EAAE;MAC7B;MACA,MAAM+C,mBAAkB,GAAIC,QAAQ,CAACC,aAAa,CAAC,0BAA0BjD,YAAY,CAACK,EAAE,IAAI,CAAC;MACjG,IAAI0C,mBAAmB,EAAE;QACvBA,mBAAmB,CAACG,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;MAC9C;IACF,CAAC;IAED;;;IAGAzC,gBAAgBA,CAACV,YAAY,EAAE;MAC7B,MAAM+C,mBAAkB,GAAIC,QAAQ,CAACC,aAAa,CAAC,0BAA0BjD,YAAY,CAACK,EAAE,IAAI,CAAC;MACjG,IAAI0C,mBAAmB,EAAE;QACvBA,mBAAmB,CAACG,SAAS,CAACE,MAAM,CAAC,SAAS,CAAC;MACjD;IACF,CAAC;IAED;;;IAGA3C,cAAcA,CAAC5B,OAAO,EAAE;MACtB5B,6BAA6B,CAACoG,mBAAmB,CAACxE,OAAO,EAAE,IAAI,CAACc,KAAK,CAAC2D,IAAI,CAAC,IAAI,CAAC,CAAC;IACnF,CAAC;IAED;;;;IAIA,MAAMlB,uBAAuBA,CAACU,SAAS,EAAES,QAAO,GAAI,IAAI,EAAE;MACxD,IAAI;QACFpF,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE0E,SAAS,EAAE,YAAY,EAAES,QAAQ,CAAC;;QAE1F;QACA;QACA,IAAI,CAAC5D,KAAK,CAAC,oBAAoB,EAAE;UAC/BmD,SAAS,EAAEA,SAAS;UACpBS,QAAQ,EAAEA;QACZ,CAAC,CAAC;;QAEF;QACA;QACA,OAAO,IAAI;MAEb,EAAE,OAAO3E,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD;QACA,OAAO;UACL1B,IAAI,EAAE,eAAe;UACrBmF,KAAK,EAAE;YACLG,SAAS,EAAEM,SAAS;YACpBU,GAAG,EAAED;UACP;QACF,CAAC;MACH;IACF,CAAC;IAED/E,qBAAqBA,CAACwB,YAAY,EAAEyD,OAAM,GAAI,IAAI,EAAE;MAClD;MACA,IAAIA,OAAM,IAAKA,OAAO,CAACC,QAAO,IAAKD,OAAO,CAACC,QAAO,KAAM,OAAO,EAAE;QAC/DvF,OAAO,CAACC,GAAG,CAAC,yDAAyD,EAAEqF,OAAO,CAACC,QAAQ,CAAC;QACxF;MACF;;MAEA;MACA,IAAI1D,YAAY,CAAC2D,cAAa,IAAK3D,YAAY,CAAC2D,cAAa,KAAM,OAAO,EAAE;QAC1ExF,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE4B,YAAY,CAAC2D,cAAc,CAAC;QAC7F;MACF;MAEAxF,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE4B,YAAY,CAAC;;MAElF;MACA,IAAIA,YAAY,CAACW,IAAG,KAAM,qBAAqB,EAAE;QAC/C,IAAI,CAACrD,WAAU,GAAI0C,YAAY,CAAC4D,KAAI,IAAK,CAAC;QAC1C;MACF;;MAEA;MACA,IAAI5D,YAAY,CAACW,IAAG,KAAM,mBAAmB,EAAE;QAC7C,MAAMkD,iBAAgB,GAAI,IAAI,CAACxG,aAAa,CAACyG,SAAS,CAACC,CAAA,IAAKA,CAAC,CAAC1D,EAAC,KAAML,YAAY,CAACgE,eAAe,CAAC;QAClG,IAAIH,iBAAgB,KAAM,CAAC,CAAC,EAAE;UAC5B,IAAI,CAACxG,aAAa,CAACwG,iBAAiB,CAAC,CAAC5D,OAAM,GAAI,IAAI;UACpD,IAAI,CAAC3C,WAAU,GAAIgD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACjD,WAAU,GAAI,CAAC,CAAC;QACtD;QACA;MACF;;MAEA;MACA,IAAI0C,YAAY,CAACW,IAAG,KAAM,wBAAwB,EAAE;QAClD,IAAI,CAACtD,aAAa,CAAC0C,OAAO,CAACgE,CAAA,IAAKA,CAAC,CAAC9D,OAAM,GAAI,IAAI,CAAC;QACjD,IAAI,CAAC3C,WAAU,GAAI,CAAC;QACpB;MACF;;MAEA;MACA,IAAI,IAAI,CAACF,SAAS,EAAE;QAClB,IAAI,CAACC,aAAa,CAAC4G,OAAO,CAACjE,YAAY,CAAC;MAC1C;;MAEA;MACA,IAAI,CAACA,YAAY,CAACC,OAAO,EAAE;QACzB,IAAI,CAAC3C,WAAW,EAAE;MACpB;;MAEA;MACA,IAAI,CAACqC,KAAK,CAAC,kBAAkB,EAAEK,YAAY,CAAC;IAC9C,CAAC;IAEDvB,WAAWA,CAAA,EAAG;MACZN,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/C,IAAI,CAACuB,KAAK,CAAC,WAAW,CAAC;IACzB,CAAC;IAEDjB,OAAOA,CAACE,KAAK,EAAE;MACbT,OAAO,CAACS,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,IAAI,CAACe,KAAK,CAAC,OAAO,EAAE,0CAA0C,CAAC;IACjE,CAAC;IAEDuE,mBAAmBA,CAACvD,IAAI,EAAE;MACxB,MAAMwD,KAAI,GAAI;QACZ,eAAe,EAAE,2BAA2B;QAC5C,aAAa,EAAE,8BAA8B;QAC7C,gBAAgB,EAAE,0BAA0B;QAC5C,cAAc,EAAE,yCAAyC;QACzD,MAAM,EAAE,4BAA4B;QACpC,YAAY,EAAE;MAChB,CAAC;MACD,OAAOA,KAAK,CAACxD,IAAI,KAAK,0BAA0B;IAClD,CAAC;IAEDyD,UAAUA,CAACC,SAAS,EAAE;MACpB,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,SAAS,CAAC;MAChC,MAAMG,GAAE,GAAI,IAAID,IAAI,CAAC,CAAC;MACtB,MAAME,aAAY,GAAInE,IAAI,CAACoE,KAAK,CAAC,CAACF,GAAE,GAAIF,IAAI,KAAK,IAAG,GAAI,EAAE,CAAC,CAAC;MAE5D,IAAIG,aAAY,GAAI,CAAC,EAAE,OAAO,UAAU;MACxC,IAAIA,aAAY,GAAI,EAAE,EAAE,OAAO,GAAGA,aAAa,OAAO;MACtD,IAAIA,aAAY,GAAI,IAAI,EAAE,OAAO,GAAGnE,IAAI,CAACoE,KAAK,CAACD,aAAY,GAAI,EAAE,CAAC,OAAO;MACzE,OAAOH,IAAI,CAACK,kBAAkB,CAAC,CAAC;IAClC;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}