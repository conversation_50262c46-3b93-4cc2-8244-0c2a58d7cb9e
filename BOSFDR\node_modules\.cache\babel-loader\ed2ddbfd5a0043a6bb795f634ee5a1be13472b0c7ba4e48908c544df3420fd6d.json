{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\nimport adminAuthService from '@/services/adminAuthService';\nimport adminDocumentService from '@/services/adminDocumentService';\nimport api from '@/services/api';\nimport notificationService from '@/services/notificationService';\nexport default {\n  name: 'AdminRequests',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n  data() {\n    return {\n      // UI State\n      loading: true,\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      errorMessage: '',\n      viewMode: 'table',\n      // 'card' or 'table' - default to table view\n\n      // Request Management Data\n      requests: [],\n      selectedRequests: [],\n      currentRequest: null,\n      statusOptions: [],\n      // Pagination\n      pagination: {\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n      },\n      // Filters\n      filters: {\n        status: '',\n        document_type: '',\n        priority: '',\n        search: '',\n        date_from: '',\n        date_to: ''\n      },\n      // Statistics\n      requestStats: {\n        total: 0,\n        pending: 0,\n        approved: 0,\n        completed: 0,\n        thisMonth: 0\n      },\n      // UI State\n      showFilters: false,\n      showBulkActions: false,\n      showRequestDetails: false,\n      showRejectForm: false,\n      showQuickReject: false,\n      showQuickApprove: false,\n      showImageModal: false,\n      selectedImage: null,\n      bulkAction: '',\n      documentUrls: {},\n      // Store blob URLs for documents\n      loadingDocuments: new Set(),\n      // Track which documents are currently loading\n      failedDocuments: new Set(),\n      // Track which documents failed to load\n      imageLoadingInModal: false,\n      // Track if modal image is loading\n      modalImageError: false,\n      // Track if modal image failed\n\n      // Status Update Forms\n      statusUpdateForm: {\n        status_id: ''\n      },\n      rejectForm: {\n        reason: ''\n      },\n      quickRejectForm: {\n        loading: false,\n        error: ''\n      },\n      selectedRequestForReject: null,\n      quickApproveForm: {\n        loading: false,\n        error: ''\n      },\n      selectedRequestForApprove: null,\n      // Payment verification form\n      paymentVerificationForm: {\n        amount_received: '',\n        receipt_number: '',\n        loading: false,\n        error: ''\n      },\n      // Pickup scheduling form\n      pickupScheduleForm: {\n        scheduled_date: '',\n        scheduled_time_start: '',\n        scheduled_time_end: '',\n        loading: false,\n        error: ''\n      },\n      // Real-time features\n      refreshInterval: null,\n      autoRefreshEnabled: true,\n      refreshRate: 30000,\n      // 30 seconds\n      lastRefresh: null\n    };\n  },\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Load component data\n    await this.loadComponentData();\n\n    // Initialize real-time features\n    this.initializeRealTimeFeatures();\n  },\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n\n    // Clean up real-time features\n    this.cleanupRealTimeFeatures();\n\n    // Clean up blob URLs to prevent memory leaks\n    this.cleanupDocumentUrls();\n  },\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    }\n  },\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n    // Handle opening request modal from notifications\n    async handleOpenRequestModal(modalData) {\n      console.log('🔔 AdminRequests: Opening request modal from notification:', modalData);\n      try {\n        const {\n          requestId,\n          focusTab\n        } = modalData;\n        if (!requestId) {\n          console.error('❌ No request ID provided for modal');\n          return;\n        }\n\n        // Use the existing viewRequestDetails method to open the modal\n        await this.viewRequestDetails(requestId);\n\n        // If a specific tab should be focused, handle that after modal opens\n        if (focusTab) {\n          // Wait a bit for the modal to fully render\n          setTimeout(() => {\n            this.focusModalTab(focusTab);\n          }, 300);\n        }\n        console.log('✅ Request modal opened successfully');\n      } catch (error) {\n        console.error('❌ Error opening request modal:', error);\n        // Show error message to user\n        this.showErrorMessage('Failed to open request details');\n      }\n    },\n    // Focus on a specific tab in the request details modal\n    focusModalTab(tabName) {\n      try {\n        console.log('🎯 Focusing on modal tab:', tabName);\n\n        // Map tab names to actual tab elements or actions\n        const tabMappings = {\n          'payment': () => {\n            // Focus on payment section in the modal\n            const paymentSection = document.querySelector('#requestDetailsModal .payment-section');\n            if (paymentSection) {\n              paymentSection.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n              });\n              paymentSection.classList.add('highlight-section');\n              setTimeout(() => paymentSection.classList.remove('highlight-section'), 2000);\n            }\n          },\n          'status': () => {\n            // Focus on status section\n            const statusSection = document.querySelector('#requestDetailsModal .status-section');\n            if (statusSection) {\n              statusSection.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n              });\n              statusSection.classList.add('highlight-section');\n              setTimeout(() => statusSection.classList.remove('highlight-section'), 2000);\n            }\n          },\n          'documents': () => {\n            // Focus on documents section\n            const documentsSection = document.querySelector('#requestDetailsModal .documents-section');\n            if (documentsSection) {\n              documentsSection.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n              });\n              documentsSection.classList.add('highlight-section');\n              setTimeout(() => documentsSection.classList.remove('highlight-section'), 2000);\n            }\n          }\n        };\n        const focusAction = tabMappings[tabName];\n        if (focusAction) {\n          focusAction();\n        } else {\n          console.log('⚠️ Unknown tab name:', tabName);\n        }\n      } catch (error) {\n        console.error('❌ Error focusing modal tab:', error);\n      }\n    },\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n    // Load admin profile\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin profile:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n    // Load all component data\n    async loadComponentData() {\n      this.loading = true;\n      try {\n        await Promise.all([this.loadAdminProfile(), this.loadStatusOptions(), this.loadRequests(), this.loadDashboardStats()]);\n      } catch (error) {\n        console.error('Failed to load component data:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load request data';\n        if (errorData.status === 401) {\n          adminAuthService.logout();\n          this.$router.push('/admin/login');\n        }\n      } finally {\n        this.loading = false;\n      }\n    },\n    // Load status options\n    async loadStatusOptions() {\n      try {\n        console.log('🔄 Loading status options...');\n        const response = await adminDocumentService.getStatusOptions();\n        console.log('📋 Status options response:', response);\n        if (response.success) {\n          this.statusOptions = response.data || [];\n          console.log('✅ Status options loaded:', this.statusOptions);\n        } else {\n          console.error('❌ Failed to load status options:', response.message);\n          this.statusOptions = [];\n        }\n      } catch (error) {\n        console.error('❌ Error loading status options:', error);\n        this.statusOptions = [];\n        this.showToast('Error', 'Failed to load status options', 'error');\n      }\n    },\n    // Load dashboard statistics\n    async loadDashboardStats() {\n      try {\n        console.log('🔄 Loading dashboard stats...');\n        const response = await adminDocumentService.getDashboardStats();\n        console.log('📊 Dashboard stats response:', response);\n        if (response.success) {\n          // Map the backend response structure to frontend expectations\n          const data = response.data;\n          this.requestStats = {\n            total: data.overview?.total_requests || 0,\n            pending: data.overview?.pending_requests || 0,\n            approved: data.overview?.approved_requests || 0,\n            completed: data.overview?.completed_requests || 0,\n            thisMonth: data.time_based?.today_requests || 0\n          };\n          console.log('✅ Request stats updated:', this.requestStats);\n        } else {\n          console.error('❌ Failed to load dashboard stats:', response.message);\n        }\n      } catch (error) {\n        console.error('❌ Error loading dashboard stats:', error);\n        // Set default values on error\n        this.requestStats = {\n          total: 0,\n          pending: 0,\n          approved: 0,\n          completed: 0,\n          thisMonth: 0\n        };\n      }\n    },\n    // Load requests with current filters and pagination\n    async loadRequests() {\n      try {\n        const params = {\n          page: this.pagination.currentPage,\n          limit: this.pagination.itemsPerPage,\n          ...this.filters\n        };\n        const response = await adminDocumentService.getAllRequests(params);\n        if (response.success) {\n          this.requests = response.data.requests || [];\n          this.pagination = {\n            currentPage: response.data.pagination?.current_page || 1,\n            totalPages: response.data.pagination?.total_pages || 1,\n            totalItems: response.data.pagination?.total_records || 0,\n            itemsPerPage: response.data.pagination?.per_page || 10\n          };\n        }\n      } catch (error) {\n        console.error('Failed to load requests:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load requests';\n        this.requests = [];\n      }\n    },\n    // Filter and search methods\n    applyFilters() {\n      this.pagination.currentPage = 1;\n      this.loadRequests();\n    },\n    clearFilters() {\n      this.filters = {\n        status: '',\n        document_type: '',\n        priority: '',\n        search: '',\n        date_from: '',\n        date_to: ''\n      };\n      this.applyFilters();\n    },\n    // Pagination methods\n    changePage(page) {\n      if (page >= 1 && page <= this.pagination.totalPages) {\n        this.pagination.currentPage = page;\n        this.loadRequests();\n      }\n    },\n    changeItemsPerPage(itemsPerPage) {\n      this.pagination.itemsPerPage = itemsPerPage;\n      this.pagination.currentPage = 1;\n      this.loadRequests();\n    },\n    goBack() {\n      this.$router.push('/admin/dashboard');\n    },\n    // Request selection methods\n    toggleRequestSelection(requestId) {\n      const index = this.selectedRequests.indexOf(requestId);\n      if (index > -1) {\n        this.selectedRequests.splice(index, 1);\n      } else {\n        this.selectedRequests.push(requestId);\n      }\n    },\n    selectAllRequests() {\n      if (this.selectedRequests.length === this.requests.length) {\n        this.selectedRequests = [];\n      } else {\n        this.selectedRequests = this.requests.map(r => r.id);\n      }\n    },\n    // Request details\n    async viewRequestDetails(requestId) {\n      console.log('🚀 View details clicked for request ID:', requestId);\n      try {\n        const response = await adminDocumentService.getRequestDetails(requestId);\n        console.log('📋 API Response received:', response);\n        if (response.success) {\n          console.log('✅ Response successful, data:', response.data);\n\n          // Debug client profile fields\n          const data = response.data;\n          console.log('🎯 COMPLETE RESPONSE DATA:', data);\n          console.log('🎯 ALL DATA KEYS:', Object.keys(data));\n          console.log('🎯 CLIENT PROFILE FIELDS DEBUG:');\n          console.log('   Birth Date:', data.client_birth_date);\n          console.log('   Gender:', data.client_gender);\n          console.log('   Civil Status ID:', data.client_civil_status_id);\n          console.log('   Nationality:', data.client_nationality);\n          console.log('   Years of Residency:', data.client_years_of_residency);\n          console.log('   Months of Residency:', data.client_months_of_residency);\n\n          // Check if fields exist with different names\n          console.log('🔍 SEARCHING FOR SIMILAR FIELDS:');\n          Object.keys(data).forEach(key => {\n            if (key.includes('birth') || key.includes('gender') || key.includes('civil') || key.includes('nationality') || key.includes('residency')) {\n              console.log(`   Found: ${key} = ${data[key]}`);\n            }\n          });\n          this.currentRequest = response.data;\n          this.showRequestDetails = true;\n          // Reset forms\n          this.statusUpdateForm = {\n            status_id: ''\n          };\n          this.rejectForm = {\n            reason: ''\n          };\n          this.showRejectForm = false;\n          console.log('📋 Request details loaded:', response.data);\n\n          // Load document URLs for images\n          if (response.data.uploaded_documents && response.data.uploaded_documents.length > 0) {\n            this.loadDocumentUrls(response.data.uploaded_documents);\n          }\n          this.showToast('Success', `Request details loaded for ${response.data.request_number}`, 'success');\n        }\n      } catch (error) {\n        console.error('Failed to load request details:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load request details';\n        this.showToast('Error', 'Failed to load request details', 'error');\n      }\n    },\n    // Refresh request details\n    async refreshRequestDetails() {\n      if (this.currentRequest) {\n        await this.viewRequestDetails(this.currentRequest.id);\n      }\n    },\n    // Update request status from modal\n    async updateRequestStatusFromModal() {\n      console.log('🔄 Updating request status...');\n      console.log('📋 Status form data:', this.statusUpdateForm);\n      console.log('📋 Current request:', this.currentRequest);\n      if (!this.statusUpdateForm.status_id || !this.currentRequest) {\n        console.error('❌ Missing required data for status update');\n        this.showToast('Error', 'Please select a status to update', 'error');\n        return;\n      }\n\n      // Enhanced debugging for status validation\n      const currentStatus = this.currentRequest.status_name;\n      const newStatusId = this.statusUpdateForm.status_id;\n      const newStatus = this.statusOptions.find(s => s.id == newStatusId);\n      console.log('🔍 Status validation debug:');\n      console.log('   Current status:', currentStatus);\n      console.log('   New status ID:', newStatusId, '(type:', typeof newStatusId, ')');\n      console.log('   New status object:', newStatus);\n      console.log('   Available transitions:', this.getAllowedStatusTransitions(currentStatus.toLowerCase()));\n      console.log('   Available status options:', this.getAvailableStatusOptions());\n      console.log('   All status options:', this.statusOptions);\n      if (!this.isValidStatusChange(currentStatus, newStatusId)) {\n        console.error('❌ Invalid status change attempted');\n        console.error('   From:', currentStatus, 'To:', newStatus?.status_name);\n        this.showToast('Error', 'This status change is not allowed', 'error');\n        return;\n      }\n      try {\n        const updateData = {\n          status_id: parseInt(this.statusUpdateForm.status_id)\n        };\n        console.log('📤 Sending status update:', updateData);\n        const response = await adminDocumentService.updateRequestStatus(this.currentRequest.id, updateData);\n        console.log('📥 Status update response:', response);\n        if (response.success) {\n          // Refresh the request details\n          await this.refreshRequestDetails();\n          // Refresh the main requests list\n          await this.loadRequests();\n          // Reset form\n          this.statusUpdateForm = {\n            status_id: ''\n          };\n\n          // Show success message\n          this.errorMessage = '';\n          this.showToast('Success', 'Request status updated successfully', 'success');\n        } else {\n          console.error('❌ Status update failed:', response.message);\n          this.showToast('Error', response.message || 'Failed to update request status', 'error');\n        }\n      } catch (error) {\n        console.error('❌ Error updating request status:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to update request status';\n        this.showToast('Error', errorData.message || 'Failed to update request status', 'error');\n      }\n    },\n    // Reject request from modal\n    async rejectRequestFromModal() {\n      if (!this.currentRequest || !this.rejectForm.reason.trim()) return;\n      try {\n        const response = await adminDocumentService.rejectRequest(this.currentRequest.id, {\n          reason: this.rejectForm.reason\n        });\n        if (response.success) {\n          await this.refreshRequestDetails();\n          await this.loadRequests();\n          this.rejectForm = {\n            reason: ''\n          };\n          this.showRejectForm = false;\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to reject request';\n      }\n    },\n    // Status update methods\n    async updateRequestStatus(requestId, statusId, reason = '') {\n      try {\n        const response = await adminDocumentService.updateRequestStatus(requestId, {\n          status_id: statusId,\n          reason: reason\n        });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to update request status:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to update request status';\n      }\n    },\n    async approveRequest(requestId, reason = '') {\n      try {\n        const response = await adminDocumentService.approveRequest(requestId, {\n          reason\n        });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to approve request';\n      }\n    },\n    async rejectRequest(requestId, reason) {\n      if (!reason || reason.trim() === '') {\n        this.errorMessage = 'Rejection reason is required';\n        return;\n      }\n      try {\n        const response = await adminDocumentService.rejectRequest(requestId, {\n          reason\n        });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to reject request';\n      }\n    },\n    // Quick approval/rejection methods\n    canApprove(request) {\n      // Can approve if 'approved' is in allowed transitions\n      const allowedTransitions = this.getAllowedStatusTransitions(request.status_name.toLowerCase());\n      return allowedTransitions.includes('approved');\n    },\n    canReject(request) {\n      // Can reject if 'rejected' is in allowed transitions\n      const allowedTransitions = this.getAllowedStatusTransitions(request.status_name.toLowerCase());\n      return allowedTransitions.includes('rejected');\n    },\n    // Helper method to get status explanation for disabled buttons\n    getStatusExplanation(request, action) {\n      const status = request.status_name.toLowerCase();\n      const allowedTransitions = this.getAllowedStatusTransitions(status);\n      if (action === 'approve') {\n        if (allowedTransitions.includes('approved')) {\n          return 'Click to approve this request';\n        } else if (status === 'approved') {\n          return 'This request has already been approved';\n        } else if (status === 'rejected') {\n          return 'Rejected requests can be resubmitted, not directly approved';\n        } else if (status === 'completed') {\n          return 'This request has already been completed';\n        } else {\n          return `Cannot approve from ${this.formatStatus(status)} status`;\n        }\n      } else if (action === 'reject') {\n        if (allowedTransitions.includes('rejected')) {\n          return 'Click to reject this request';\n        } else if (status === 'rejected') {\n          return 'This request has already been rejected';\n        } else if (status === 'completed') {\n          return 'Cannot reject a completed request';\n        } else {\n          return `Cannot reject from ${this.formatStatus(status)} status`;\n        }\n      }\n      return `Request status: ${this.formatStatus(status)}`;\n    },\n    // Check if status change is valid\n    isValidStatusChange(currentStatus, newStatusId) {\n      if (!currentStatus || !newStatusId) return false;\n\n      // Find the new status name\n      const newStatus = this.statusOptions.find(s => s.id == newStatusId);\n      if (!newStatus) return false;\n      const currentStatusName = currentStatus.toLowerCase();\n      const newStatusName = newStatus.status_name.toLowerCase();\n\n      // Same status - no change needed\n      if (currentStatusName === newStatusName) {\n        return false;\n      }\n\n      // Check if transition is allowed based on workflow rules\n      const allowedTransitions = this.getAllowedStatusTransitions(currentStatusName);\n      return allowedTransitions.includes(newStatusName);\n    },\n    // Check if request needs payment verification\n    needsPaymentVerification(request) {\n      return request.status_name === 'payment_pending' && request.payment_method && !request.payment_method.includes('PayMongo') && request.payment_status !== 'paid';\n    },\n    // Check if pickup can be scheduled\n    canSchedulePickup(request) {\n      return request.status_name === 'ready_for_pickup';\n    },\n    // Get payment status color\n    getPaymentStatusColor(status) {\n      const colors = {\n        'pending': 'bg-warning',\n        'processing': 'bg-info',\n        'paid': 'bg-success',\n        'failed': 'bg-danger',\n        'refunded': 'bg-secondary',\n        'cancelled': 'bg-dark'\n      };\n      return colors[status] || 'bg-secondary';\n    },\n    // Format payment status\n    formatPaymentStatus(status) {\n      const statuses = {\n        'pending': 'Pending',\n        'processing': 'Processing',\n        'paid': 'Paid',\n        'failed': 'Failed',\n        'refunded': 'Refunded',\n        'cancelled': 'Cancelled'\n      };\n      return statuses[status] || 'Unknown';\n    },\n    // Get tomorrow's date for pickup scheduling\n    getTomorrowDate() {\n      const tomorrow = new Date();\n      tomorrow.setDate(tomorrow.getDate() + 1);\n      return tomorrow.toISOString().split('T')[0];\n    },\n    // Validate pickup form\n    isPickupFormValid() {\n      return this.pickupScheduleForm.scheduled_date && this.pickupScheduleForm.scheduled_time_start && this.pickupScheduleForm.scheduled_time_end && this.pickupScheduleForm.scheduled_time_start < this.pickupScheduleForm.scheduled_time_end;\n    },\n    // Get filtered status options based on current status\n    getAvailableStatusOptions() {\n      if (!this.currentRequest || !this.statusOptions) return [];\n      const currentStatus = this.currentRequest.status_name.toLowerCase();\n\n      // Only these states are truly final (cannot be changed)\n      const finalStates = ['completed', 'cancelled'];\n\n      // If current status is final, no changes allowed\n      if (finalStates.includes(currentStatus)) {\n        return [];\n      }\n\n      // Define allowed transitions based on current status\n      const allowedTransitions = this.getAllowedStatusTransitions(currentStatus);\n\n      // Return only allowed status options\n      return this.statusOptions.filter(status => allowedTransitions.includes(status.status_name.toLowerCase()));\n    },\n    // Define allowed status transitions based on government workflow best practices\n    // This must match the backend validateStatusTransition logic exactly\n    getAllowedStatusTransitions(currentStatus) {\n      const transitions = {\n        'pending': ['under_review', 'approved', 'cancelled', 'rejected'],\n        'under_review': ['approved', 'rejected', 'cancelled'],\n        // Removed additional_info_required\n        // Removed 'additional_info_required' status entirely\n        'approved': ['payment_pending', 'cancelled'],\n        // Updated to match backend: approved must go to payment_pending first\n        'payment_pending': ['payment_confirmed', 'payment_failed', 'cancelled'],\n        'payment_confirmed': ['processing'],\n        // Automatic transition after payment\n        'payment_failed': ['payment_pending', 'cancelled'],\n        'processing': ['ready_for_pickup'],\n        // Processing can only complete successfully\n        'ready_for_pickup': ['pickup_scheduled', 'completed', 'cancelled'],\n        'pickup_scheduled': ['completed', 'ready_for_pickup', 'cancelled'],\n        // Can reschedule\n        'rejected': ['pending', 'under_review'],\n        // Allow resubmission after corrections\n        // Final states - no transitions allowed\n        'completed': [],\n        'cancelled': []\n      };\n      return transitions[currentStatus] || [];\n    },\n    // Get title for update button based on validation state\n    getUpdateButtonTitle() {\n      if (!this.statusUpdateForm.status_id) {\n        return 'Please select a new status';\n      }\n      if (!this.isValidStatusChange(this.currentRequest.status_name, this.statusUpdateForm.status_id)) {\n        return 'Invalid status change';\n      }\n      return 'Update request status';\n    },\n    // Get dynamic button text based on selected status\n    getActionButtonText() {\n      if (!this.statusUpdateForm.status_id) {\n        return 'Update Status';\n      }\n      const selectedStatus = this.statusOptions.find(s => s.id === parseInt(this.statusUpdateForm.status_id));\n      if (!selectedStatus) {\n        return 'Update Status';\n      }\n      const statusName = selectedStatus.status_name.toLowerCase();\n\n      // Special button text for common actions\n      switch (statusName) {\n        case 'approved':\n          return 'Approve Request';\n        case 'rejected':\n          return 'Reject Request';\n        case 'under_review':\n          return 'Move to Review';\n        case 'processing':\n          return 'Start Processing';\n        case 'ready_for_pickup':\n          return 'Mark Ready for Pickup';\n        case 'completed':\n          return 'Complete Request';\n        default:\n          return `Update to ${selectedStatus.status_name}`;\n      }\n    },\n    async quickApprove(request) {\n      console.log('🚀 Quick approve clicked for request:', request);\n      try {\n        this.loading = true;\n        const response = await adminDocumentService.approveRequest(request.id, {\n          reason: 'Quick approval from admin interface'\n        });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.showToast('Success', `Request ${request.request_number} approved successfully`, 'success');\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.showToast('Error', errorData.message || 'Failed to approve request', 'error');\n      } finally {\n        this.loading = false;\n      }\n    },\n    showQuickRejectModal(request) {\n      console.log('🚀 Quick reject clicked for request:', request);\n      this.selectedRequestForReject = request;\n      this.quickRejectForm = {\n        loading: false,\n        error: ''\n      };\n      this.showQuickReject = true;\n    },\n    closeQuickRejectModal() {\n      this.showQuickReject = false;\n      this.selectedRequestForReject = null;\n      this.quickRejectForm = {\n        loading: false,\n        error: ''\n      };\n    },\n    async confirmQuickReject() {\n      this.quickRejectForm.loading = true;\n      this.quickRejectForm.error = '';\n      try {\n        const response = await adminDocumentService.rejectRequest(this.selectedRequestForReject.id, {\n          reason: 'Request rejected by admin'\n        });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.showToast('Success', `Request ${this.selectedRequestForReject.request_number} rejected successfully`, 'success');\n          this.closeQuickRejectModal();\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.quickRejectForm.error = errorData.message || 'Failed to reject request';\n      } finally {\n        this.quickRejectForm.loading = false;\n      }\n    },\n    showQuickApproveModal(request) {\n      console.log('🚀 Quick approve clicked for request:', request);\n      this.selectedRequestForApprove = request;\n      this.quickApproveForm = {\n        loading: false,\n        error: ''\n      };\n      this.showQuickApprove = true;\n    },\n    closeQuickApproveModal() {\n      this.showQuickApprove = false;\n      this.selectedRequestForApprove = null;\n      this.quickApproveForm = {\n        loading: false,\n        error: ''\n      };\n    },\n    async confirmQuickApprove() {\n      this.quickApproveForm.loading = true;\n      this.quickApproveForm.error = '';\n      try {\n        const response = await adminDocumentService.approveRequest(this.selectedRequestForApprove.id, {\n          reason: 'Quick approval from admin interface'\n        });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.showToast('Success', `Request ${this.selectedRequestForApprove.request_number} approved successfully`, 'success');\n          this.closeQuickApproveModal();\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.quickApproveForm.error = errorData.message || 'Failed to approve request';\n      } finally {\n        this.quickApproveForm.loading = false;\n      }\n    },\n    // Bulk operations\n    async performBulkAction() {\n      if (this.selectedRequests.length === 0) {\n        this.errorMessage = 'Please select at least one request';\n        return;\n      }\n      if (!this.bulkAction) {\n        this.errorMessage = 'Please select a bulk action';\n        return;\n      }\n      try {\n        const response = await adminDocumentService.bulkUpdateRequests({\n          request_ids: this.selectedRequests,\n          status_id: parseInt(this.bulkAction)\n        });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.selectedRequests = [];\n          this.bulkAction = '';\n          this.showBulkActions = false;\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to perform bulk action:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to perform bulk action';\n      }\n    },\n    // Export functionality\n    async exportRequests() {\n      try {\n        const csvData = await adminDocumentService.exportRequests(this.filters);\n        const filename = `document_requests_${new Date().toISOString().split('T')[0]}.csv`;\n        adminDocumentService.downloadCSV(csvData, filename);\n      } catch (error) {\n        console.error('Failed to export requests:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to export requests';\n      }\n    },\n    // Verify in-person payment\n    async verifyInPersonPayment() {\n      if (!this.paymentVerificationForm.amount_received || !this.currentRequest) {\n        this.showToast('Error', 'Please enter the amount received', 'error');\n        return;\n      }\n      const totalFee = parseFloat(this.currentRequest.total_fee);\n      const amountReceived = parseFloat(this.paymentVerificationForm.amount_received);\n      if (amountReceived < totalFee) {\n        this.showToast('Error', `Insufficient payment. Required: ${this.formatCurrency(totalFee)}`, 'error');\n        return;\n      }\n      this.paymentVerificationForm.loading = true;\n      this.paymentVerificationForm.error = '';\n      try {\n        const paymentData = {\n          amount_received: amountReceived,\n          payment_method_id: this.currentRequest.payment_method_id || 1,\n          // Default to cash\n          receipt_number: this.paymentVerificationForm.receipt_number\n        };\n        const response = await adminDocumentService.verifyInPersonPayment(this.currentRequest.id, paymentData);\n        if (response.success) {\n          await this.refreshRequestDetails();\n          await this.loadRequests();\n\n          // Reset form\n          this.paymentVerificationForm = {\n            amount_received: '',\n            receipt_number: '',\n            loading: false,\n            error: ''\n          };\n          this.showToast('Success', 'Payment verified successfully', 'success');\n        }\n      } catch (error) {\n        console.error('Failed to verify payment:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.paymentVerificationForm.error = errorData.message || 'Failed to verify payment';\n        this.showToast('Error', errorData.message || 'Failed to verify payment', 'error');\n      } finally {\n        this.paymentVerificationForm.loading = false;\n      }\n    },\n    // Schedule pickup appointment\n    async schedulePickup() {\n      if (!this.isPickupFormValid() || !this.currentRequest) {\n        this.showToast('Error', 'Please fill in all required fields', 'error');\n        return;\n      }\n      this.pickupScheduleForm.loading = true;\n      this.pickupScheduleForm.error = '';\n      try {\n        const scheduleData = {\n          scheduled_date: this.pickupScheduleForm.scheduled_date,\n          scheduled_time_start: this.pickupScheduleForm.scheduled_time_start,\n          scheduled_time_end: this.pickupScheduleForm.scheduled_time_end\n        };\n        const response = await adminDocumentService.schedulePickup(this.currentRequest.id, scheduleData);\n        if (response.success) {\n          await this.refreshRequestDetails();\n          await this.loadRequests();\n\n          // Reset form\n          this.pickupScheduleForm = {\n            scheduled_date: '',\n            scheduled_time_start: '',\n            scheduled_time_end: '',\n            loading: false,\n            error: ''\n          };\n          this.showToast('Success', 'Pickup scheduled successfully', 'success');\n        }\n      } catch (error) {\n        console.error('Failed to schedule pickup:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.pickupScheduleForm.error = errorData.message || 'Failed to schedule pickup';\n        this.showToast('Error', errorData.message || 'Failed to schedule pickup', 'error');\n      } finally {\n        this.pickupScheduleForm.loading = false;\n      }\n    },\n    // Utility methods\n    formatStatus(status) {\n      return adminDocumentService.formatStatus(status);\n    },\n    getStatusColor(status) {\n      return adminDocumentService.getStatusColor(status);\n    },\n    formatDate(dateString) {\n      console.log('🗓️ formatDate called with:', dateString);\n      if (!dateString) {\n        console.log('🗓️ formatDate: No date provided, returning null');\n        return null;\n      }\n      const date = new Date(dateString);\n      const formatted = date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n      console.log('🗓️ formatDate result:', formatted);\n      return formatted;\n    },\n    formatCurrency(amount) {\n      return new Intl.NumberFormat('en-PH', {\n        style: 'currency',\n        currency: 'PHP'\n      }).format(amount || 0);\n    },\n    formatDateTime(dateString) {\n      if (!dateString) return 'N/A';\n      return new Date(dateString).toLocaleString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n    // New helper methods for complete client information\n    getClientFullName(request) {\n      if (!request) return 'Not provided';\n      const parts = [request.client_first_name, request.client_middle_name, request.client_last_name, request.client_suffix].filter(Boolean);\n      return parts.length > 0 ? parts.join(' ') : request.client_name || 'Not provided';\n    },\n    getClientFullAddress(request) {\n      if (!request) return null;\n      const parts = [request.client_house_number, request.client_street, request.client_subdivision, request.client_barangay, request.client_city_municipality || request.client_city, request.client_province].filter(Boolean);\n      return parts.length > 0 ? parts.join(', ') : request.client_address || null;\n    },\n    formatGender(gender) {\n      if (!gender) {\n        return null;\n      }\n      return gender.charAt(0).toUpperCase() + gender.slice(1);\n    },\n    getCivilStatusName(statusId) {\n      const statuses = {\n        1: 'Single',\n        2: 'Married',\n        3: 'Divorced',\n        4: 'Widowed',\n        5: 'Separated'\n      };\n      return statuses[statusId] || null;\n    },\n    getResidencyDisplay(request) {\n      if (!request) return null;\n      const years = request.client_years_of_residency;\n      const months = request.client_months_of_residency;\n      if (!years && !months) return null; // Return null so the template can handle \"Not provided\"\n\n      const parts = [];\n      if (years) parts.push(`${years} year${years > 1 ? 's' : ''}`);\n      if (months) parts.push(`${months} month${months > 1 ? 's' : ''}`);\n      return parts.join(' and ');\n    },\n    formatTime(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    },\n    // Real-time features\n    async initializeRealTimeFeatures() {\n      console.log('Initializing real-time features for AdminRequests');\n      try {\n        // Initialize notification service\n        await notificationService.init('admin');\n\n        // Listen for request-related notifications\n        notificationService.on('notification', this.handleRealTimeNotification);\n        notificationService.on('request_status_changed', this.handleStatusChange);\n        notificationService.on('new_request', this.handleNewRequest);\n\n        // Start auto-refresh if enabled\n        if (this.autoRefreshEnabled) {\n          this.startAutoRefresh();\n        }\n      } catch (error) {\n        console.error('Failed to initialize real-time features:', error);\n      }\n    },\n    cleanupRealTimeFeatures() {\n      console.log('Cleaning up real-time features for AdminRequests');\n\n      // Remove notification listeners\n      notificationService.off('notification', this.handleRealTimeNotification);\n      notificationService.off('request_status_changed', this.handleStatusChange);\n      notificationService.off('new_request', this.handleNewRequest);\n\n      // Cleanup (simplified)\n      notificationService.cleanup();\n\n      // Stop auto-refresh\n      this.stopAutoRefresh();\n    },\n    startAutoRefresh() {\n      if (this.refreshInterval) {\n        clearInterval(this.refreshInterval);\n      }\n      this.refreshInterval = setInterval(() => {\n        if (this.autoRefreshEnabled && !this.loading) {\n          console.log('Auto-refreshing requests data...');\n          this.refreshRequestsData();\n        }\n      }, this.refreshRate);\n      console.log(`Auto-refresh started with ${this.refreshRate / 1000}s interval`);\n    },\n    stopAutoRefresh() {\n      if (this.refreshInterval) {\n        clearInterval(this.refreshInterval);\n        this.refreshInterval = null;\n        console.log('Auto-refresh stopped');\n      }\n    },\n    toggleAutoRefresh() {\n      this.autoRefreshEnabled = !this.autoRefreshEnabled;\n      if (this.autoRefreshEnabled) {\n        this.startAutoRefresh();\n      } else {\n        this.stopAutoRefresh();\n      }\n    },\n    async refreshRequestsData() {\n      try {\n        this.lastRefresh = new Date();\n\n        // Refresh requests list\n        await this.loadRequests();\n\n        // Refresh statistics\n        await this.loadDashboardStats();\n\n        // If request details modal is open, refresh it\n        if (this.showRequestDetails && this.currentRequest) {\n          await this.refreshRequestDetails();\n        }\n        console.log('Requests data refreshed successfully');\n      } catch (error) {\n        console.error('Failed to refresh requests data:', error);\n      }\n    },\n    handleRealTimeNotification(notification) {\n      console.log('Real-time notification received:', notification);\n\n      // Handle different notification types\n      switch (notification.type) {\n        case 'request_status_changed':\n          this.handleStatusChange(notification.data);\n          break;\n        case 'new_request':\n          this.handleNewRequest(notification.data);\n          break;\n        case 'request_updated':\n          this.handleRequestUpdate(notification.data);\n          break;\n        case 'unread_count_update':\n        case 'heartbeat':\n          // Polling system notifications - handled by notification service\n          break;\n        default:\n          // Only log unknown types, not system types\n          if (!['unread_count_update', 'heartbeat'].includes(notification.type)) {\n            console.log('Unhandled notification type:', notification.type);\n          }\n      }\n    },\n    handleStatusChange(data) {\n      console.log('Request status changed:', data);\n\n      // Update the request in the list if it exists\n      const requestIndex = this.requests.findIndex(req => req.id === data.request_id);\n      if (requestIndex !== -1) {\n        // Refresh the specific request or reload all requests\n        this.refreshRequestsData();\n      }\n\n      // Show toast notification\n      this.showToast('Request status updated', `Request #${data.request_id} status changed to ${data.new_status}`, 'info');\n    },\n    handleNewRequest(data) {\n      console.log('New request received:', data);\n\n      // Refresh requests to show the new request\n      this.refreshRequestsData();\n\n      // Show toast notification\n      this.showToast('New Request', `New ${data.document_type} request received`, 'success');\n    },\n    handleRequestUpdate(data) {\n      console.log('Request updated:', data);\n\n      // If the updated request is currently being viewed, refresh details\n      if (this.currentRequest && this.currentRequest.id === data.request_id) {\n        this.refreshRequestDetails();\n      }\n\n      // Refresh the requests list\n      this.refreshRequestsData();\n    },\n    showToast(title, message, type = 'info') {\n      // Log to console for debugging\n      console.log(`[${type.toUpperCase()}] ${title}: ${message}`);\n\n      // Create a simple toast notification\n      const toast = document.createElement('div');\n      toast.className = `toast-notification toast-${type}`;\n      toast.innerHTML = `\n        <div class=\"toast-header\">\n          <strong>${title}</strong>\n          <button type=\"button\" class=\"toast-close\" onclick=\"this.parentElement.parentElement.remove()\">×</button>\n        </div>\n        <div class=\"toast-body\">${message}</div>\n      `;\n\n      // Add toast styles if not already added\n      if (!document.getElementById('toast-styles')) {\n        const styles = document.createElement('style');\n        styles.id = 'toast-styles';\n        styles.textContent = `\n          .toast-notification {\n            position: fixed;\n            top: 20px;\n            right: 20px;\n            min-width: 300px;\n            background: white;\n            border-radius: 8px;\n            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n            z-index: 9999;\n            animation: slideIn 0.3s ease;\n          }\n          .toast-header {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            padding: 12px 16px 8px;\n            border-bottom: 1px solid #e9ecef;\n          }\n          .toast-body {\n            padding: 8px 16px 12px;\n            color: #6c757d;\n          }\n          .toast-close {\n            background: none;\n            border: none;\n            font-size: 18px;\n            cursor: pointer;\n            color: #6c757d;\n          }\n          .toast-success { border-left: 4px solid #28a745; }\n          .toast-error { border-left: 4px solid #dc3545; }\n          .toast-info { border-left: 4px solid #17a2b8; }\n          .toast-warning { border-left: 4px solid #ffc107; }\n          @keyframes slideIn {\n            from { transform: translateX(100%); opacity: 0; }\n            to { transform: translateX(0); opacity: 1; }\n          }\n        `;\n        document.head.appendChild(styles);\n      }\n\n      // Add toast to page\n      document.body.appendChild(toast);\n\n      // Auto-remove after 5 seconds\n      setTimeout(() => {\n        if (toast.parentElement) {\n          toast.style.animation = 'slideIn 0.3s ease reverse';\n          setTimeout(() => toast.remove(), 300);\n        }\n      }, 5000);\n    },\n    // Document handling methods\n    getDocumentTypeDisplayName(type) {\n      const displayNames = {\n        'government_id': 'Government ID',\n        'proof_of_residency': 'Proof of Residency',\n        'cedula': 'Community Tax Certificate (Cedula)',\n        'birth_certificate': 'Birth Certificate',\n        'marriage_certificate': 'Marriage Certificate',\n        'other': 'Other Document'\n      };\n      return displayNames[type] || type;\n    },\n    isImageFile(mimeType) {\n      return mimeType && (mimeType.startsWith('image/') || ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'].includes(mimeType));\n    },\n    isPdfFile(mimeType) {\n      return mimeType === 'application/pdf';\n    },\n    async loadDocumentUrls(documents) {\n      // Filter documents that need loading (images only, not already loaded/loading/failed)\n      const documentsToLoad = documents.filter(doc => this.isImageFile(doc.mime_type) && !this.documentUrls[doc.id] && !this.loadingDocuments.has(doc.id) && !this.failedDocuments.has(doc.id));\n      if (documentsToLoad.length === 0) return;\n\n      // Load documents in parallel with concurrency limit\n      const CONCURRENT_LIMIT = 3;\n      const chunks = this.chunkArray(documentsToLoad, CONCURRENT_LIMIT);\n      for (const chunk of chunks) {\n        await Promise.allSettled(chunk.map(document => this.loadSingleDocument(document)));\n      }\n    },\n    async loadSingleDocument(document, isForModal = false) {\n      const docId = document.id;\n      try {\n        // Mark as loading\n        this.loadingDocuments.add(docId);\n        if (isForModal) this.imageLoadingInModal = true;\n\n        // Use authenticated API call to get the document\n        const response = await api.get(`/documents/view/${docId}`, {\n          responseType: 'blob',\n          timeout: 15000,\n          // Increased timeout for large images\n          onDownloadProgress: progressEvent => {\n            // Optional: Could emit progress events here\n            if (progressEvent.lengthComputable) {\n              const percentCompleted = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n              console.log(`Loading ${docId}: ${percentCompleted}%`);\n            }\n          }\n        });\n\n        // Validate response\n        if (!response.data || response.data.size === 0) {\n          throw new Error('Empty response received');\n        }\n\n        // Check file size and optimize if needed\n        const blob = response.data;\n        const optimizedBlob = await this.optimizeImageBlob(blob, document.mime_type, isForModal);\n\n        // Create blob URL using requestIdleCallback for better performance\n        await this.createBlobUrlWhenIdle(docId, optimizedBlob);\n\n        // Remove from failed set if it was there\n        this.failedDocuments.delete(docId);\n        if (isForModal) this.modalImageError = false;\n      } catch (error) {\n        console.warn(`Failed to load document ${docId}:`, error.message);\n        this.failedDocuments.add(docId);\n        if (isForModal) this.modalImageError = true;\n\n        // Optionally retry after a delay for network errors\n        if (error.code === 'NETWORK_ERROR' || error.code === 'ECONNABORTED') {\n          setTimeout(() => {\n            this.failedDocuments.delete(docId);\n          }, 30000); // Retry after 30 seconds\n        }\n      } finally {\n        // Remove from loading set\n        this.loadingDocuments.delete(docId);\n        if (isForModal) this.imageLoadingInModal = false;\n      }\n    },\n    chunkArray(array, size) {\n      const chunks = [];\n      for (let i = 0; i < array.length; i += size) {\n        chunks.push(array.slice(i, i + size));\n      }\n      return chunks;\n    },\n    async getDocumentUrl(document) {\n      // This method is now deprecated in favor of loadDocumentUrls\n      // Keeping for backward compatibility\n      if (this.documentUrls[document.id]) {\n        return this.documentUrls[document.id];\n      }\n      return null;\n    },\n    formatFileSize(bytes) {\n      if (!bytes) return '0 B';\n      const k = 1024;\n      const sizes = ['B', 'KB', 'MB', 'GB'];\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];\n    },\n    async openImageModal(document) {\n      // Prevent multiple rapid clicks\n      if (this.imageLoadingInModal) return;\n\n      // Don't open modal if document failed to load and we're not retrying\n      if (this.failedDocuments.has(document.id)) {\n        return;\n      }\n\n      // Set modal state immediately for responsiveness\n      this.selectedImage = document;\n      this.showImageModal = true;\n      this.modalImageError = false;\n\n      // Use nextTick to ensure DOM is updated before heavy operations\n      await this.$nextTick();\n\n      // If image isn't loaded yet, try to load it with modal optimization\n      if (!this.documentUrls[document.id] && !this.loadingDocuments.has(document.id)) {\n        await this.loadSingleDocument(document, true);\n      }\n    },\n    async retryLoadDocument(document) {\n      // Remove from failed set and retry loading\n      this.failedDocuments.delete(document.id);\n      this.modalImageError = false;\n      await this.loadSingleDocument(document, true);\n    },\n    onModalImageLoad() {\n      // Called when modal image finishes loading\n      this.imageLoadingInModal = false;\n    },\n    cleanupDocumentUrls() {\n      // Revoke all blob URLs to prevent memory leaks\n      Object.values(this.documentUrls).forEach(url => {\n        if (url) URL.revokeObjectURL(url);\n      });\n\n      // Clear all tracking sets and objects\n      this.documentUrls = {};\n      this.loadingDocuments.clear();\n      this.failedDocuments.clear();\n    },\n    preloadImage(document) {\n      // Preload image on hover for better UX\n      if (!this.documentUrls[document.id] && !this.loadingDocuments.has(document.id) && !this.failedDocuments.has(document.id)) {\n        this.loadSingleDocument(document, false);\n      }\n    },\n    async optimizeImageBlob(blob, mimeType, isForModal = false) {\n      // For very large images, we might want to resize them\n      const MAX_SIZE = isForModal ? 5 * 1024 * 1024 : 2 * 1024 * 1024; // 5MB for modal, 2MB for preview\n\n      if (blob.size <= MAX_SIZE) {\n        return blob; // No optimization needed\n      }\n      try {\n        // Create image element for resizing\n        const img = new Image();\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        return new Promise(resolve => {\n          img.onload = () => {\n            // Calculate new dimensions (maintain aspect ratio)\n            const maxDimension = isForModal ? 1920 : 800;\n            let {\n              width,\n              height\n            } = img;\n            if (width > height && width > maxDimension) {\n              height = height * maxDimension / width;\n              width = maxDimension;\n            } else if (height > maxDimension) {\n              width = width * maxDimension / height;\n              height = maxDimension;\n            }\n\n            // Set canvas size and draw resized image\n            canvas.width = width;\n            canvas.height = height;\n            ctx.drawImage(img, 0, 0, width, height);\n\n            // Convert to blob with compression\n            canvas.toBlob(optimizedBlob => {\n              resolve(optimizedBlob || blob); // Fallback to original if optimization fails\n            }, mimeType, 0.85 // 85% quality\n            );\n          };\n          img.onerror = () => resolve(blob); // Fallback to original\n          img.src = URL.createObjectURL(blob);\n        });\n      } catch (error) {\n        console.warn('Image optimization failed:', error);\n        return blob; // Fallback to original\n      }\n    },\n    async createBlobUrlWhenIdle(docId, blob) {\n      return new Promise(resolve => {\n        const createUrl = () => {\n          this.documentUrls[docId] = URL.createObjectURL(blob);\n          resolve();\n        };\n\n        // Use requestIdleCallback if available, otherwise use setTimeout\n        if (window.requestIdleCallback) {\n          window.requestIdleCallback(createUrl, {\n            timeout: 1000\n          });\n        } else {\n          setTimeout(createUrl, 0);\n        }\n      });\n    },\n    closeImageModal() {\n      // Prevent rapid clicking during image loading\n      if (this.imageLoadingInModal) return;\n      this.showImageModal = false;\n      this.selectedImage = null;\n      this.imageLoadingInModal = false;\n      this.modalImageError = false;\n    },\n    async downloadDocument(documentFile) {\n      try {\n        // Use authenticated API call to download the document\n        const response = await api.get(`/documents/download/${documentFile.id}`, {\n          responseType: 'blob'\n        });\n\n        // Create a download link\n        const blob = new Blob([response.data], {\n          type: documentFile.mime_type\n        });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = documentFile.document_name;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n      } catch (error) {\n        console.error('Failed to download document:', error);\n        this.showToast('Error', 'Failed to download document', 'error');\n      }\n    },\n    handleImageError(event) {\n      console.error('Failed to load image:', event.target.src);\n      // You could set a placeholder image here\n      event.target.style.display = 'none';\n\n      // Show error message\n      const errorDiv = document.createElement('div');\n      errorDiv.className = 'text-center text-muted p-3';\n      errorDiv.innerHTML = '<i class=\"fas fa-exclamation-triangle\"></i><br>Failed to load image';\n      event.target.parentNode.appendChild(errorDiv);\n    }\n  }\n};", "map": {"version": 3, "names": ["Ad<PERSON><PERSON><PERSON><PERSON>", "AdminSidebar", "adminAuthService", "adminDocumentService", "api", "notificationService", "name", "components", "data", "loading", "sidebarCollapsed", "showUserDropdown", "isMobile", "adminData", "errorMessage", "viewMode", "requests", "selectedRequests", "currentRequest", "statusOptions", "pagination", "currentPage", "totalPages", "totalItems", "itemsPerPage", "filters", "status", "document_type", "priority", "search", "date_from", "date_to", "requestStats", "total", "pending", "approved", "completed", "thisMonth", "showFilters", "showBulkActions", "showRequestDetails", "showRejectForm", "showQuickReject", "showQuickApprove", "showImageModal", "selectedImage", "bulkAction", "documentUrls", "loadingDocuments", "Set", "failedDocuments", "imageLoadingInModal", "modalImageError", "statusUpdateForm", "status_id", "rejectForm", "reason", "quickRejectForm", "error", "selectedRequestForReject", "quickApproveForm", "selectedRequestForApprove", "paymentVerificationForm", "amount_received", "receipt_number", "pickupScheduleForm", "scheduled_date", "scheduled_time_start", "scheduled_time_end", "refreshInterval", "autoRefreshEnabled", "refreshRate", "lastRefresh", "mounted", "isLoggedIn", "$router", "push", "initializeUI", "loadComponentData", "initializeRealTimeFeatures", "beforeUnmount", "handleResize", "window", "removeEventListener", "cleanupRealTimeFeatures", "cleanupDocumentUrls", "computed", "activeMenu", "path", "$route", "includes", "methods", "innerWidth", "saved", "localStorage", "getItem", "JSON", "parse", "was<PERSON><PERSON><PERSON>", "addEventListener", "handleSidebarToggle", "setItem", "stringify", "handleMenuChange", "menu", "routes", "handleUserDropdownToggle", "handleOpenRequestModal", "modalData", "console", "log", "requestId", "focusTab", "viewRequestDetails", "setTimeout", "focusModalTab", "showErrorMessage", "tabName", "tabMappings", "payment", "paymentSection", "document", "querySelector", "scrollIntoView", "behavior", "block", "classList", "add", "remove", "statusSection", "documents", "documentsSection", "focusAction", "handleMenuAction", "action", "closeMobileSidebar", "handleLogout", "logout", "loadAdminProfile", "response", "getProfile", "success", "getAdminData", "Promise", "all", "loadStatusOptions", "loadRequests", "loadDashboardStats", "errorData", "parseError", "message", "getStatusOptions", "showToast", "getDashboardStats", "overview", "total_requests", "pending_requests", "approved_requests", "completed_requests", "time_based", "today_requests", "params", "page", "limit", "getAllRequests", "current_page", "total_pages", "total_records", "per_page", "applyFilters", "clearFilters", "changePage", "changeItemsPerPage", "goBack", "toggleRequestSelection", "index", "indexOf", "splice", "selectAllRequests", "length", "map", "r", "id", "getRequestDetails", "Object", "keys", "client_birth_date", "client_gender", "client_civil_status_id", "client_nationality", "client_years_of_residency", "client_months_of_residency", "for<PERSON>ach", "key", "uploaded_documents", "loadDocumentUrls", "request_number", "refreshRequestDetails", "updateRequestStatusFromModal", "currentStatus", "status_name", "newStatusId", "newStatus", "find", "s", "getAllowedStatusTransitions", "toLowerCase", "getAvailableStatusOptions", "isValidStatusChange", "updateData", "parseInt", "updateRequestStatus", "rejectRequestFromModal", "trim", "rejectRequest", "statusId", "approveRequest", "canApprove", "request", "allowedTransitions", "canReject", "getStatusExplanation", "formatStatus", "currentStatusName", "newStatusName", "needsPaymentVerification", "payment_method", "payment_status", "canSchedulePickup", "getPaymentStatusColor", "colors", "formatPaymentStatus", "statuses", "getTomorrowDate", "tomorrow", "Date", "setDate", "getDate", "toISOString", "split", "isPickupFormValid", "finalStates", "filter", "transitions", "getUpdateButtonTitle", "getActionButtonText", "selectedStatus", "statusName", "quickApprove", "showQuickRejectModal", "closeQuickRejectModal", "confirmQuickReject", "showQuickApproveModal", "closeQuickApproveModal", "confirmQuickApprove", "performBulkAction", "bulkUpdateRequests", "request_ids", "exportRequests", "csvData", "filename", "downloadCSV", "verifyInPersonPayment", "totalFee", "parseFloat", "total_fee", "amountReceived", "formatCurrency", "paymentData", "payment_method_id", "schedulePickup", "scheduleData", "getStatusColor", "formatDate", "dateString", "date", "formatted", "toLocaleDateString", "year", "month", "day", "amount", "Intl", "NumberFormat", "style", "currency", "format", "formatDateTime", "toLocaleString", "hour", "minute", "getClientFullName", "parts", "client_first_name", "client_middle_name", "client_last_name", "client_suffix", "Boolean", "join", "client_name", "getClientFullAddress", "client_house_number", "client_street", "client_subdivision", "client_barangay", "client_city_municipality", "client_city", "client_province", "client_address", "formatGender", "gender", "char<PERSON>t", "toUpperCase", "slice", "getCivilStatusName", "getResidencyDisplay", "years", "months", "formatTime", "toLocaleTimeString", "hour12", "init", "on", "handleRealTimeNotification", "handleStatusChange", "handleNewRequest", "startAutoRefresh", "off", "cleanup", "stopAutoRefresh", "clearInterval", "setInterval", "refreshRequestsData", "toggleAutoRefresh", "notification", "type", "handleRequestUpdate", "requestIndex", "findIndex", "req", "request_id", "new_status", "title", "toast", "createElement", "className", "innerHTML", "getElementById", "styles", "textContent", "head", "append<PERSON><PERSON><PERSON>", "body", "parentElement", "animation", "getDocumentTypeDisplayName", "displayNames", "isImageFile", "mimeType", "startsWith", "isPdfFile", "documentsToLoad", "doc", "mime_type", "has", "CONCURRENT_LIMIT", "chunks", "chunkArray", "chunk", "allSettled", "loadSingleDocument", "isForModal", "docId", "get", "responseType", "timeout", "onDownloadProgress", "progressEvent", "lengthComputable", "percentCompleted", "Math", "round", "loaded", "size", "Error", "blob", "optimizedBlob", "optimizeImageBlob", "createBlobUrlWhenIdle", "delete", "warn", "code", "array", "i", "getDocumentUrl", "formatFileSize", "bytes", "k", "sizes", "floor", "pow", "toFixed", "openImageModal", "$nextTick", "retryLoadDocument", "onModalImageLoad", "values", "url", "URL", "revokeObjectURL", "clear", "preloadImage", "MAX_SIZE", "img", "Image", "canvas", "ctx", "getContext", "resolve", "onload", "maxDimension", "width", "height", "drawImage", "toBlob", "onerror", "src", "createObjectURL", "createUrl", "requestIdleCallback", "closeImageModal", "downloadDocument", "documentFile", "Blob", "link", "href", "download", "document_name", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleImageError", "event", "target", "display", "errorDiv", "parentNode"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminRequests.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-requests\">\n    <AdminHeader\n      :userName=\"adminData?.first_name || 'Admin'\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @open-request-modal=\"handleOpenRequestModal\"\n      @logout=\"handleLogout\"\n    />\n\n    <!-- Mobile Overlay -->\n    <div\n      class=\"mobile-overlay\"\n      :class=\"{ active: !sidebarCollapsed && isMobile }\"\n      @click=\"closeMobileSidebar\"\n    ></div>\n\n    <div class=\"dashboard-container\">\n      <AdminSidebar\n        :collapsed=\"sidebarCollapsed\"\n        :activeMenu=\"activeMenu\"\n        @menu-change=\"handleMenuChange\"\n        @logout=\"handleLogout\"\n        @toggle-sidebar=\"handleSidebarToggle\"\n      />\n\n      <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n        <!-- Loading State -->\n        <div v-if=\"loading\" class=\"d-flex justify-content-center align-items-center\" style=\"min-height: 400px;\">\n          <div class=\"spinner-border text-primary\" role=\"status\">\n            <span class=\"visually-hidden\">Loading...</span>\n          </div>\n        </div>\n\n        <!-- Main Content -->\n        <div v-else class=\"container-fluid py-4\">\n          <!-- Error Message -->\n          <div v-if=\"errorMessage\" class=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">\n            <i class=\"fas fa-exclamation-triangle me-2\"></i>\n            {{ errorMessage }}\n            <button type=\"button\" class=\"btn-close\" @click=\"errorMessage = ''\" aria-label=\"Close\"></button>\n          </div>\n\n          <!-- Page Header -->\n          <div class=\"row mb-4\">\n            <div class=\"col-12\">\n              <div class=\"d-flex justify-content-between align-items-center flex-wrap\">\n                <div>\n                  <p class=\"text-muted mb-0\">\n                    <span v-if=\"lastRefresh\" class=\"ms-2 small\">\n                      <i class=\"fas fa-clock text-muted\"></i>\n                      Last updated: {{ formatTime(lastRefresh) }}\n                    </span>\n                  </p>\n                </div>\n                <div class=\"d-flex gap-2 align-items-center\">\n                  <!-- Real-time status indicator -->\n                  <div class=\"real-time-status me-2\">\n                    <span class=\"badge\" :class=\"autoRefreshEnabled ? 'bg-success' : 'bg-secondary'\">\n                      <i class=\"fas fa-circle pulse\" v-if=\"autoRefreshEnabled\"></i>\n                      <i class=\"fas fa-pause\" v-else></i>\n                      {{ autoRefreshEnabled ? 'Live' : 'Paused' }}\n                    </span>\n                  </div>\n\n                  <button class=\"btn btn-outline-secondary btn-sm\" @click=\"toggleAutoRefresh\" :title=\"autoRefreshEnabled ? 'Disable auto-refresh' : 'Enable auto-refresh'\">\n                    <i class=\"fas\" :class=\"autoRefreshEnabled ? 'fa-pause' : 'fa-play'\"></i>\n                  </button>\n                  <button class=\"btn btn-outline-primary btn-sm\" @click=\"showFilters = !showFilters\">\n                    <i class=\"fas fa-filter me-1\"></i>\n                    {{ showFilters ? 'Hide' : 'Show' }} Filters\n                  </button>\n                  <!-- <button class=\"btn btn-success btn-sm\" @click=\"exportRequests\" :disabled=\"loading\">\n                    <i class=\"fas fa-download me-1\"></i>\n                    Export CSV\n                  </button> -->\n                  <button class=\"btn btn-primary btn-sm\" @click=\"refreshRequestsData\" :disabled=\"loading\">\n                    <i class=\"fas fa-sync-alt me-1\" :class=\"{ 'fa-spin': loading }\"></i>\n                    Refresh\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Request Statistics -->\n          <div class=\"row mb-3\">\n            <div class=\"col-6 col-md-3 mb-2\">\n              <div class=\"card border-left-primary shadow py-1\">\n                <div class=\"card-body p-2\">\n                  <div class=\"d-flex align-items-center\">\n                    <div class=\"flex-grow-1\">\n                      <div class=\"text-xs fw-bold text-primary text-uppercase mb-1\">Total Requests</div>\n                      <div class=\"h6 mb-0 fw-bold text-dark\">{{ requestStats.total || 0 }}</div>\n                    </div>\n                    <i class=\"fas fa-file-alt fa-lg text-muted ms-2\"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-md-3 mb-2\">\n              <div class=\"card border-left-warning shadow py-1\">\n                <div class=\"card-body p-2\">\n                  <div class=\"d-flex align-items-center\">\n                    <div class=\"flex-grow-1\">\n                      <div class=\"text-xs fw-bold text-warning text-uppercase mb-1\">Pending</div>\n                      <div class=\"h6 mb-0 fw-bold text-dark\">{{ requestStats.pending || 0 }}</div>\n                    </div>\n                    <i class=\"fas fa-clock fa-lg text-muted ms-2\"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-md-3 mb-2\">\n              <div class=\"card border-left-success shadow py-1\">\n                <div class=\"card-body p-2\">\n                  <div class=\"d-flex align-items-center\">\n                    <div class=\"flex-grow-1\">\n                      <div class=\"text-xs fw-bold text-success text-uppercase mb-1\">Completed</div>\n                      <div class=\"h6 mb-0 fw-bold text-dark\">{{ requestStats.completed || 0 }}</div>\n                    </div>\n                    <i class=\"fas fa-check-circle fa-lg text-muted ms-2\"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-md-3 mb-2\">\n              <div class=\"card border-left-info shadow py-1\">\n                <div class=\"card-body p-2\">\n                  <div class=\"d-flex align-items-center\">\n                    <div class=\"flex-grow-1\">\n                      <div class=\"text-xs fw-bold text-info text-uppercase mb-1\">Approved</div>\n                      <div class=\"h6 mb-0 fw-bold text-dark\">{{ requestStats.approved || 0 }}</div>\n                    </div>\n                    <i class=\"fas fa-thumbs-up fa-lg text-muted ms-2\"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Filters Panel -->\n          <div v-if=\"showFilters\" class=\"card shadow mb-4\">\n            <div class=\"card-header py-3\">\n              <h6 class=\"m-0 fw-bold text-primary\">Filter Requests</h6>\n            </div>\n            <div class=\"card-body\">\n              <div class=\"row\">\n                <div class=\"col-md-3 mb-3\">\n                  <label class=\"form-label\">Search</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    v-model=\"filters.search\"\n                    placeholder=\"Search by name, email, or request number\"\n                    @keyup.enter=\"applyFilters\"\n                  >\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Status</label>\n                  <select class=\"form-select\" v-model=\"filters.status\">\n                    <option value=\"\">All Statuses</option>\n                    <option v-for=\"status in statusOptions\" :key=\"status.id\" :value=\"status.status_name\">\n                      {{ formatStatus(status.status_name) }}\n                    </option>\n                  </select>\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Document Type</label>\n                  <select class=\"form-select\" v-model=\"filters.document_type\">\n                    <option value=\"\">All Types</option>\n                    <option value=\"barangay_clearance\">Barangay Clearance</option>\n                    <option value=\"cedula\">Cedula</option>\n                  </select>\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Date From</label>\n                  <input type=\"date\" class=\"form-control\" v-model=\"filters.date_from\">\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Date To</label>\n                  <input type=\"date\" class=\"form-control\" v-model=\"filters.date_to\">\n                </div>\n                <div class=\"col-md-1 mb-3 d-flex align-items-end\">\n                  <div class=\"d-flex gap-1 w-100\">\n                    <button class=\"btn btn-primary btn-sm\" @click=\"applyFilters\">\n                      <i class=\"fas fa-search\"></i>\n                    </button>\n                    <button class=\"btn btn-outline-secondary btn-sm\" @click=\"clearFilters\">\n                      <i class=\"fas fa-times\"></i>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Bulk Actions Panel -->\n          <div v-if=\"selectedRequests.length > 0\" class=\"card shadow mb-4\">\n            <div class=\"card-header py-3 bg-warning\">\n              <h6 class=\"m-0 fw-bold text-dark\">\n                <i class=\"fas fa-tasks me-2\"></i>\n                Bulk Actions ({{ selectedRequests.length }} selected)\n              </h6>\n            </div>\n            <div class=\"card-body\">\n              <div class=\"row align-items-end\">\n                <div class=\"col-md-3 mb-3\">\n                  <label class=\"form-label\">Action</label>\n                  <select class=\"form-select\" v-model=\"bulkAction\">\n                    <option value=\"\">Select Action</option>\n                    <option v-for=\"status in statusOptions\" :key=\"status.id\" :value=\"status.id\">\n                      Change to {{ formatStatus(status.status_name) }}\n                    </option>\n                  </select>\n                </div>\n\n                <div class=\"col-md-3 mb-3\">\n                  <div class=\"d-flex gap-2\">\n                    <button class=\"btn btn-warning\" @click=\"performBulkAction\" :disabled=\"!bulkAction\">\n                      <i class=\"fas fa-play me-1\"></i>\n                      Apply\n                    </button>\n                    <button class=\"btn btn-outline-secondary\" @click=\"selectedRequests = []\">\n                      <i class=\"fas fa-times me-1\"></i>\n                      Cancel\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- View Toggle -->\n          <div class=\"d-flex justify-content-between align-items-center mb-4\">\n            <div class=\"d-flex align-items-center gap-3\">\n              <div class=\"btn-group\" role=\"group\" aria-label=\"View toggle\">\n                <input type=\"radio\" class=\"btn-check\" name=\"viewMode\" id=\"cardView\" v-model=\"viewMode\" value=\"card\" autocomplete=\"off\">\n                <label class=\"btn btn-outline-primary btn-sm\" for=\"cardView\">\n                  <i class=\"fas fa-th-large me-1\"></i>Cards\n                </label>\n\n                <input type=\"radio\" class=\"btn-check\" name=\"viewMode\" id=\"tableView\" v-model=\"viewMode\" value=\"table\" autocomplete=\"off\">\n                <label class=\"btn btn-outline-primary btn-sm\" for=\"tableView\">\n                  <i class=\"fas fa-table me-1\"></i>Table\n                </label>\n              </div>\n\n              <div class=\"d-flex align-items-center gap-2\">\n                <span class=\"text-muted small\">\n                  Showing {{ ((pagination.currentPage - 1) * pagination.itemsPerPage) + 1 }} -\n                  {{ Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems) }}\n                  of {{ pagination.totalItems }} requests\n                </span>\n                <select class=\"form-select form-select-sm\" style=\"width: auto;\" v-model=\"pagination.itemsPerPage\" @change=\"changeItemsPerPage(pagination.itemsPerPage)\">\n                  <option value=\"10\">10 per page</option>\n                  <option value=\"25\">25 per page</option>\n                  <option value=\"50\">50 per page</option>\n                  <option value=\"100\">100 per page</option>\n                </select>\n              </div>\n            </div>\n\n            <div class=\"d-flex align-items-center gap-2\">\n              <button class=\"btn btn-sm btn-outline-secondary\" @click=\"selectAllRequests\" v-if=\"requests.length > 0\">\n                <i class=\"fas fa-check-square me-1\"></i>\n                {{ selectedRequests.length === requests.length ? 'Deselect All' : 'Select All' }}\n              </button>\n            </div>\n          </div>\n\n          <!-- Card View -->\n          <div v-if=\"viewMode === 'card'\" class=\"requests-grid\">\n            <!-- Empty State -->\n            <div v-if=\"requests.length === 0\" class=\"empty-state text-center py-5\">\n              <div class=\"empty-state-icon mb-3\">\n                <i class=\"fas fa-inbox fa-4x text-muted\"></i>\n              </div>\n              <h5 class=\"text-muted mb-2\">No Document Requests Found</h5>\n              <p class=\"text-muted\">There are no document requests matching your current filters.</p>\n            </div>\n\n            <!-- Request Cards -->\n            <div v-else class=\"row g-4\">\n              <div v-for=\"request in requests\" :key=\"request.id\" class=\"col-xl-4 col-lg-6 col-md-6\">\n                <div class=\"request-card\" :class=\"{ 'selected': selectedRequests.includes(request.id) }\">\n                  <!-- Card Header -->\n                  <div class=\"request-card-header\">\n                    <div class=\"d-flex justify-content-between align-items-start\">\n                      <div class=\"d-flex align-items-center gap-2\">\n                        <input\n                          type=\"checkbox\"\n                          class=\"form-check-input\"\n                          :checked=\"selectedRequests.includes(request.id)\"\n                          @change=\"toggleRequestSelection(request.id)\"\n                        >\n                        <div class=\"request-number\">\n                          <span class=\"badge bg-primary\">{{ request.request_number }}</span>\n                        </div>\n                      </div>\n                      <div class=\"request-actions-simple\">\n                        <button class=\"btn btn-sm btn-primary\" @click=\"viewRequestDetails(request.id)\" title=\"View & Manage Request\">\n                          <i class=\"fas fa-edit me-1\"></i>Manage\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Card Body -->\n                  <div class=\"request-card-body\">\n                    <!-- Client Info -->\n                    <div class=\"client-info mb-3\">\n                      <div class=\"d-flex align-items-center gap-2 mb-2\">\n                        <div class=\"client-avatar\">\n                          <i class=\"fas fa-user-circle fa-2x text-primary\"></i>\n                        </div>\n                        <div>\n                          <h6 class=\"mb-0 fw-bold\">{{ request.client_name }}</h6>\n                          <small class=\"text-muted\">{{ request.client_email }}</small>\n                        </div>\n                      </div>\n\n                      <!-- Additional Client Details -->\n                      <div class=\"client-details-grid mt-2\">\n                        <div class=\"row g-1\">\n                          <div class=\"col-6\" v-if=\"getCivilStatusName(request.client_civil_status_id)\">\n                            <small class=\"text-muted d-block\">Civil Status</small>\n                            <small class=\"fw-medium\">{{ getCivilStatusName(request.client_civil_status_id) }}</small>\n                          </div>\n                          <div class=\"col-6\" v-if=\"request.client_nationality\">\n                            <small class=\"text-muted d-block\">Nationality</small>\n                            <small class=\"fw-medium\">{{ request.client_nationality }}</small>\n                          </div>\n                          <div class=\"col-12\" v-if=\"getResidencyDisplay(request)\">\n                            <small class=\"text-muted d-block\">Years of Residency</small>\n                            <small class=\"fw-medium\">{{ getResidencyDisplay(request) }}</small>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Document Type -->\n                    <div class=\"document-type mb-3\">\n                      <div class=\"d-flex align-items-center gap-2\">\n                        <i class=\"fas fa-file-alt text-info\"></i>\n                        <span class=\"badge bg-info-subtle text-info-emphasis px-3 py-2\">\n                          {{ request.document_type }}\n                        </span>\n                      </div>\n                    </div>\n\n                    <!-- Status and Amount -->\n                    <div class=\"request-meta mb-3\">\n                      <div class=\"row g-2\">\n                        <div class=\"col-6\">\n                          <div class=\"meta-item\">\n                            <small class=\"text-muted d-block\">Status</small>\n                            <span class=\"badge\" :class=\"`bg-${getStatusColor(request.status_name)}`\">\n                              {{ formatStatus(request.status_name) }}\n                            </span>\n                          </div>\n                        </div>\n                        <div class=\"col-6\">\n                          <div class=\"meta-item\">\n                            <small class=\"text-muted d-block\">Amount</small>\n                            <span class=\"fw-bold text-success\">{{ formatCurrency(request.total_fee) }}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Date -->\n                    <div class=\"request-date\">\n                      <small class=\"text-muted\">\n                        <i class=\"fas fa-calendar-alt me-1\"></i>\n                        Submitted {{ formatDate(request.requested_at) }}\n                      </small>\n                    </div>\n                  </div>\n\n                  <!-- Card Footer -->\n                  <div class=\"request-card-footer\">\n                    <div class=\"d-grid\">\n                      <button class=\"btn btn-sm btn-primary\" @click=\"viewRequestDetails(request.id)\">\n                        <i class=\"fas fa-edit me-1\"></i>Manage Request\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Table View -->\n          <div v-else class=\"modern-table-container\">\n\n            <!-- Empty State -->\n            <div v-if=\"requests.length === 0\" class=\"modern-table-empty\">\n              <div class=\"empty-content\">\n                <div class=\"empty-icon\">\n                  <i class=\"fas fa-inbox\"></i>\n                </div>\n                <h6 class=\"empty-title\">No Document Requests Found</h6>\n                <p class=\"empty-text\">There are no document requests matching your current filters.</p>\n              </div>\n            </div>\n\n            <!-- Modern Compact Table -->\n            <div v-else class=\"compact-table-wrapper\">\n              <!-- Table Header -->\n              <div class=\"compact-table-header\">\n                <div class=\"header-cell selection-header\">\n                  <input\n                    type=\"checkbox\"\n                    class=\"form-check-input\"\n                    :checked=\"selectedRequests.length === requests.length && requests.length > 0\"\n                    @change=\"selectAllRequests\"\n                  >\n                </div>\n                <div class=\"header-cell\">Request ID</div>\n                <div class=\"header-cell\">Client</div>\n                <div class=\"header-cell\">Document</div>\n                <div class=\"header-cell\">Status</div>\n                <div class=\"header-cell\">Amount</div>\n                <div class=\"header-cell\">Date</div>\n                <div class=\"header-cell\">Actions</div>\n              </div>\n\n              <!-- Table Body -->\n              <div class=\"compact-table-body\">\n                <div v-for=\"request in requests\" :key=\"request.id\"\n                     class=\"compact-row\"\n                     :class=\"{ 'selected': selectedRequests.includes(request.id) }\">\n\n                  <!-- Selection -->\n                  <div class=\"row-cell selection-cell\">\n                    <input\n                      type=\"checkbox\"\n                      class=\"form-check-input\"\n                      :checked=\"selectedRequests.includes(request.id)\"\n                      @change=\"toggleRequestSelection(request.id)\"\n                    >\n                  </div>\n\n                  <!-- Request ID -->\n                  <div class=\"row-cell request-id-cell\">\n                    <div class=\"request-id-content\">\n                      <span class=\"request-number\">{{ request.request_number }}</span>\n                      <span class=\"request-id-small\">{{ request.id }}</span>\n                    </div>\n                  </div>\n\n                  <!-- Client -->\n                  <div class=\"row-cell client-cell\">\n                    <div class=\"client-compact\">\n                      <div class=\"client-avatar-tiny\">\n                        <i class=\"fas fa-user\"></i>\n                      </div>\n                      <div class=\"client-info-compact\">\n                        <div class=\"client-name-compact\">{{ request.client_name }}</div>\n                        <div class=\"client-email-compact\">{{ request.client_email }}</div>\n                        <div class=\"client-details-compact\">\n                          <span v-if=\"request.client_birth_date\" class=\"detail-item\">\n                            <i class=\"fas fa-birthday-cake me-1\"></i>{{ formatDate(request.client_birth_date) }}\n                          </span>\n                          <span v-if=\"request.client_gender\" class=\"detail-item\">\n                            <i class=\"fas fa-venus-mars me-1\"></i>{{ formatGender(request.client_gender) }}\n                          </span>\n                          <span v-if=\"getCivilStatusName(request.client_civil_status_id)\" class=\"detail-item\">\n                            <i class=\"fas fa-ring me-1\"></i>{{ getCivilStatusName(request.client_civil_status_id) }}\n                          </span>\n                          <span v-if=\"request.client_nationality\" class=\"detail-item\">\n                            <i class=\"fas fa-flag me-1\"></i>{{ request.client_nationality }}\n                          </span>\n                          <span v-if=\"getResidencyDisplay(request)\" class=\"detail-item\">\n                            <i class=\"fas fa-home me-1\"></i>{{ getResidencyDisplay(request) }}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Document Type -->\n                  <div class=\"row-cell document-cell\">\n                    <span class=\"document-badge\">\n                      <i class=\"fas fa-file-alt\"></i>\n                      {{ request.document_type }}\n                    </span>\n                  </div>\n\n                  <!-- Status -->\n                  <div class=\"row-cell status-cell\">\n                    <span class=\"status-compact\" :class=\"`status-${getStatusColor(request.status_name)}`\">\n                      <i class=\"fas fa-circle\"></i>\n                      {{ formatStatus(request.status_name) }}\n                    </span>\n                  </div>\n\n                  <!-- Amount -->\n                  <div class=\"row-cell amount-cell\">\n                    <span class=\"amount-compact\">{{ formatCurrency(request.total_fee) }}</span>\n                  </div>\n\n                  <!-- Date -->\n                  <div class=\"row-cell date-cell\">\n                    <div class=\"date-compact\">\n                      <span class=\"date-main\">{{ formatDate(request.requested_at) }}</span>\n                      <span class=\"time-small\">{{ formatTime(request.requested_at) }}</span>\n                    </div>\n                  </div>\n\n                  <!-- Actions -->\n                  <div class=\"row-cell actions-cell\">\n                    <div class=\"actions-simple\">\n                      <button class=\"action-btn-sm primary-btn-sm\" @click=\"viewRequestDetails(request.id)\" title=\"View & Manage Request\">\n                        <i class=\"fas fa-edit\"></i>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Pagination -->\n          <div v-if=\"pagination.totalPages > 1\" class=\"pagination-container\">\n              <nav aria-label=\"Requests pagination\">\n                <ul class=\"pagination pagination-sm justify-content-center mb-0\">\n                  <li class=\"page-item\" :class=\"{ disabled: pagination.currentPage === 1 }\">\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(pagination.currentPage - 1)\">\n                      <i class=\"fas fa-chevron-left\"></i>\n                    </a>\n                  </li>\n                  <li\n                    v-for=\"page in Math.min(pagination.totalPages, 10)\"\n                    :key=\"page\"\n                    class=\"page-item\"\n                    :class=\"{ active: page === pagination.currentPage }\"\n                  >\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(page)\">{{ page }}</a>\n                  </li>\n                  <li class=\"page-item\" :class=\"{ disabled: pagination.currentPage === pagination.totalPages }\">\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(pagination.currentPage + 1)\">\n                      <i class=\"fas fa-chevron-right\"></i>\n                    </a>\n                  </li>\n                </ul>\n              </nav>\n            </div>\n          </div>\n\n          <!-- Request Details Modal -->\n          <div v-if=\"showRequestDetails && currentRequest\" class=\"modal fade show d-block\" tabindex=\"-1\" style=\"background-color: rgba(0,0,0,0.5);\">\n            <div class=\"modal-dialog modal-xl modal-dialog-scrollable\">\n              <div class=\"modal-content\">\n                <div class=\"modal-header\">\n                  <h5 class=\"modal-title\">\n                    <i class=\"fas fa-file-alt me-2\"></i>\n                    Request Details - {{ currentRequest.request_number }}\n                  </h5>\n                  <button type=\"button\" class=\"btn-close\" @click=\"showRequestDetails = false\"></button>\n                </div>\n                <div class=\"modal-body\">\n                  <div class=\"row\">\n                    <!-- Left Column - Request Information -->\n                    <div class=\"col-lg-8\">\n                      <!-- Basic Information -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-info-circle me-2\"></i>Request Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"row\">\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Request Number</label>\n                                <p class=\"mb-0\">{{ currentRequest.request_number }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Document Type</label>\n                                <p class=\"mb-0\">\n                                  <span class=\"badge bg-info\">{{ currentRequest.document_type }}</span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Purpose Category</label>\n                                <p class=\"mb-0\">{{ currentRequest.purpose_category }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Purpose Details</label>\n                                <p class=\"mb-0\">{{ currentRequest.purpose_details || 'Not specified' }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Current Status</label>\n                                <p class=\"mb-0\">\n                                  <span class=\"badge\" :class=\"`bg-${getStatusColor(currentRequest.status_name)}`\">\n                                    {{ formatStatus(currentRequest.status_name) }}\n                                  </span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Priority</label>\n                                <p class=\"mb-0\">\n                                  <span class=\"badge\" :class=\"currentRequest.priority === 'high' ? 'bg-danger' : currentRequest.priority === 'medium' ? 'bg-warning' : 'bg-secondary'\">\n                                    {{ currentRequest.priority || 'Normal' }}\n                                  </span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Delivery Method</label>\n                                <p class=\"mb-0\">{{ currentRequest.delivery_method || 'Pickup' }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Date Submitted</label>\n                                <p class=\"mb-0\">{{ formatDateTime(currentRequest.requested_at) }}</p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Client Information -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-user me-2\"></i>Client Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <!-- Basic Information -->\n                          <div class=\"row\">\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Full Name</label>\n                                <p class=\"mb-0\" :class=\"{ 'text-muted': getClientFullName(currentRequest) === 'Not provided' }\">\n                                  <span v-if=\"getClientFullName(currentRequest) !== 'Not provided'\">{{ getClientFullName(currentRequest) }}</span>\n                                  <span v-else class=\"not-provided\">Not provided</span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Email Address</label>\n                                <p class=\"mb-0\" :class=\"{ 'text-muted': !currentRequest.client_email }\">\n                                  <a v-if=\"currentRequest.client_email\" :href=\"`mailto:${currentRequest.client_email}`\">{{ currentRequest.client_email }}</a>\n                                  <span v-else class=\"not-provided\">Not provided</span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Phone Number</label>\n                                <p class=\"mb-0\" :class=\"{ 'text-muted': !currentRequest.client_phone }\">\n                                  <a v-if=\"currentRequest.client_phone\" :href=\"`tel:${currentRequest.client_phone}`\">{{ currentRequest.client_phone }}</a>\n                                  <span v-else class=\"not-provided\">Not provided</span>\n                                </p>\n                              </div>\n                            </div>\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Date of Birth</label>\n                                <p class=\"mb-0\" :class=\"{ 'text-muted': !currentRequest.client_birth_date }\">\n                                  <span v-if=\"formatDate(currentRequest.client_birth_date)\">{{ formatDate(currentRequest.client_birth_date) }}</span>\n                                  <span v-else class=\"not-provided\">Not provided</span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Gender</label>\n                                <p class=\"mb-0\" :class=\"{ 'text-muted': !currentRequest.client_gender }\">\n                                  <span v-if=\"formatGender(currentRequest.client_gender)\">{{ formatGender(currentRequest.client_gender) }}</span>\n                                  <span v-else class=\"not-provided\">Not provided</span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Civil Status</label>\n                                <p class=\"mb-0\" :class=\"{ 'text-muted': !currentRequest.client_civil_status_id }\">\n                                  <span v-if=\"getCivilStatusName(currentRequest.client_civil_status_id)\">{{ getCivilStatusName(currentRequest.client_civil_status_id) }}</span>\n                                  <span v-else class=\"not-provided\">Not provided</span>\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n\n                          <!-- Address Information -->\n                          <div class=\"row\">\n                            <div class=\"col-12\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Complete Address</label>\n                                <p class=\"mb-0\" :class=\"{ 'text-muted': !getClientFullAddress(currentRequest) }\">\n                                  <span v-if=\"getClientFullAddress(currentRequest)\">{{ getClientFullAddress(currentRequest) }}</span>\n                                  <span v-else class=\"not-provided\">Not provided</span>\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n\n                          <!-- Additional Information -->\n                          <div class=\"row\">\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Nationality</label>\n                                <p class=\"mb-0\" :class=\"{ 'text-muted': !currentRequest.client_nationality }\">\n                                  <span v-if=\"currentRequest.client_nationality\">{{ currentRequest.client_nationality }}</span>\n                                  <span v-else class=\"not-provided\">Not provided</span>\n                                </p>\n                              </div>\n                            </div>\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Years of Residency</label>\n                                <p class=\"mb-0\" :class=\"{ 'text-muted': !getResidencyDisplay(currentRequest) }\">\n                                  <span v-if=\"getResidencyDisplay(currentRequest)\">{{ getResidencyDisplay(currentRequest) }}</span>\n                                  <span v-else class=\"not-provided\">Not provided</span>\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Uploaded Documents -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-paperclip me-2\"></i>Uploaded Documents</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div v-if=\"currentRequest.uploaded_documents && currentRequest.uploaded_documents.length > 0\">\n                            <div class=\"row g-3\">\n                              <div v-for=\"document in currentRequest.uploaded_documents\" :key=\"document.id\" class=\"col-md-4\">\n                                <div class=\"document-preview-card\">\n                                  <div class=\"document-preview-header\">\n                                    <div class=\"document-type-badge\">\n                                      <i class=\"fas fa-file-alt me-1\"></i>\n                                      {{ getDocumentTypeDisplayName(document.document_type) }}\n                                    </div>\n                                  </div>\n                                  <div class=\"document-preview-content\">\n                                    <!-- Image Preview -->\n                                    <div v-if=\"isImageFile(document.mime_type)\"\n                                         class=\"image-preview\"\n                                         @click=\"openImageModal(document)\"\n                                         @mouseenter=\"preloadImage(document)\">\n                                      <!-- Successfully loaded image -->\n                                      <img\n                                        v-if=\"documentUrls[document.id]\"\n                                        :src=\"documentUrls[document.id]\"\n                                        :alt=\"document.document_name\"\n                                        class=\"document-image\"\n                                        @error=\"handleImageError\"\n                                      />\n\n                                      <!-- Loading state -->\n                                      <div v-else-if=\"loadingDocuments.has(document.id)\" class=\"loading-placeholder\">\n                                        <i class=\"fas fa-spinner fa-spin\"></i>\n                                        <span>Loading image...</span>\n                                      </div>\n\n                                      <!-- Failed state with retry option -->\n                                      <div v-else-if=\"failedDocuments.has(document.id)\" class=\"error-placeholder\" @click.stop=\"retryLoadDocument(document)\">\n                                        <i class=\"fas fa-exclamation-triangle\"></i>\n                                        <span>Failed to load</span>\n                                        <small>Click to retry</small>\n                                      </div>\n\n                                      <!-- Initial state (not yet attempted) -->\n                                      <div v-else class=\"loading-placeholder\">\n                                        <i class=\"fas fa-image\"></i>\n                                        <span>Click to load</span>\n                                      </div>\n                                      <div class=\"image-overlay\">\n                                        <i class=\"fas fa-search-plus\"></i>\n                                        <span>Click to view</span>\n                                      </div>\n                                    </div>\n                                    <!-- PDF Preview -->\n                                    <div v-else-if=\"isPdfFile(document.mime_type)\" class=\"pdf-preview\">\n                                      <div class=\"pdf-icon\">\n                                        <i class=\"fas fa-file-pdf fa-3x text-danger\"></i>\n                                      </div>\n                                      <div class=\"pdf-info\">\n                                        <p class=\"mb-1 fw-bold\">{{ document.document_name }}</p>\n                                        <small class=\"text-muted\">{{ formatFileSize(document.file_size) }}</small>\n                                      </div>\n                                      <button\n                                        class=\"btn btn-sm btn-outline-primary mt-2\"\n                                        @click=\"downloadDocument(document)\"\n                                      >\n                                        <i class=\"fas fa-download me-1\"></i>Download\n                                      </button>\n                                    </div>\n                                    <!-- Other File Types -->\n                                    <div v-else class=\"file-preview\">\n                                      <div class=\"file-icon\">\n                                        <i class=\"fas fa-file fa-3x text-secondary\"></i>\n                                      </div>\n                                      <div class=\"file-info\">\n                                        <p class=\"mb-1 fw-bold\">{{ document.document_name }}</p>\n                                        <small class=\"text-muted\">{{ formatFileSize(document.file_size) }}</small>\n                                      </div>\n                                      <button\n                                        class=\"btn btn-sm btn-outline-primary mt-2\"\n                                        @click=\"downloadDocument(document)\"\n                                      >\n                                        <i class=\"fas fa-download me-1\"></i>Download\n                                      </button>\n                                    </div>\n                                  </div>\n                                  <div class=\"document-preview-footer\">\n                                    <small class=\"text-muted\">\n                                      <i class=\"fas fa-clock me-1\"></i>\n                                      Uploaded {{ formatDate(document.created_at) }}\n                                    </small>\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                          <div v-else class=\"no-documents\">\n                            <div class=\"text-center py-4\">\n                              <i class=\"fas fa-folder-open fa-3x text-muted mb-3\"></i>\n                              <h6 class=\"text-muted\">No Documents Uploaded</h6>\n                              <p class=\"text-muted mb-0\">\n                                <span v-if=\"currentRequest.document_type === 'Cedula'\">\n                                  Cedula requests typically don't require supporting documents.\n                                </span>\n                                <span v-else>\n                                  The client hasn't uploaded any supporting documents yet.\n                                </span>\n                              </p>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Right Column - Status Management -->\n                    <div class=\"col-lg-4\">\n                      <!-- Status Management -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-tasks me-2\"></i>Status Management</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Change Status</label>\n                            <select\n                              class=\"form-select\"\n                              v-model=\"statusUpdateForm.status_id\"\n                              :disabled=\"getAvailableStatusOptions().length === 0\"\n                            >\n                              <option value=\"\">\n                                {{ getAvailableStatusOptions().length === 0 ? 'No status changes available' : 'Select new status' }}\n                              </option>\n                              <option v-for=\"status in getAvailableStatusOptions()\" :key=\"status.id\" :value=\"status.id\">\n                                {{ formatStatus(status.status_name) }}\n                              </option>\n                            </select>\n                            <div v-if=\"getAvailableStatusOptions().length === 0\" class=\"form-text text-muted\">\n                              <i class=\"fas fa-info-circle me-1\"></i>\n                              This request status cannot be changed ({{ formatStatus(currentRequest.status_name) }})\n                            </div>\n                          </div>\n\n                          <!-- Single Action Button -->\n                          <div class=\"d-grid\">\n                            <button\n                              class=\"btn btn-primary\"\n                              @click=\"updateRequestStatusFromModal\"\n                              :disabled=\"!statusUpdateForm.status_id || !isValidStatusChange(currentRequest.status_name, statusUpdateForm.status_id)\"\n                              :title=\"getUpdateButtonTitle()\"\n                            >\n                              <i class=\"fas fa-save me-1\"></i>\n                              {{ getActionButtonText() }}\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Payment Information -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-credit-card me-2\"></i>Payment Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Payment Method</label>\n                            <p class=\"mb-0\">{{ currentRequest.payment_method || 'Not specified' }}</p>\n                          </div>\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Payment Status</label>\n                            <p class=\"mb-0\">\n                              <span class=\"badge\" :class=\"getPaymentStatusColor(currentRequest.payment_status)\">\n                                {{ formatPaymentStatus(currentRequest.payment_status) }}\n                              </span>\n                            </p>\n                          </div>\n                          <div class=\"row\">\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Base Fee</label>\n                                <p class=\"mb-0\">{{ formatCurrency(currentRequest.base_fee) }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Additional Fees</label>\n                                <p class=\"mb-0\">{{ formatCurrency(currentRequest.additional_fees) }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Processing Fee</label>\n                                <p class=\"mb-0\">{{ formatCurrency(currentRequest.processing_fee) }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Total Amount</label>\n                                <p class=\"mb-0 fw-bold text-primary\">{{ formatCurrency(currentRequest.total_fee) }}</p>\n                              </div>\n                            </div>\n                          </div>\n\n                          <!-- In-Person Payment Verification -->\n                          <div v-if=\"needsPaymentVerification(currentRequest)\" class=\"mt-4 p-3 border rounded bg-light\">\n                            <h6 class=\"text-primary mb-3\">\n                              <i class=\"fas fa-money-bill me-2\"></i>\n                              Verify In-Person Payment\n                            </h6>\n                            <div class=\"row\">\n                              <div class=\"col-md-6\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Amount Received *</label>\n                                  <input\n                                    type=\"number\"\n                                    class=\"form-control\"\n                                    v-model=\"paymentVerificationForm.amount_received\"\n                                    :min=\"currentRequest.total_fee\"\n                                    step=\"0.01\"\n                                    placeholder=\"Enter amount received\"\n                                  >\n                                </div>\n                              </div>\n                              <div class=\"col-md-6\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Receipt Number</label>\n                                  <input\n                                    type=\"text\"\n                                    class=\"form-control\"\n                                    v-model=\"paymentVerificationForm.receipt_number\"\n                                    placeholder=\"Enter receipt number\"\n                                  >\n                                </div>\n                              </div>\n                            </div>\n\n                            <div class=\"d-grid\">\n                              <button\n                                class=\"btn btn-success\"\n                                @click=\"verifyInPersonPayment\"\n                                :disabled=\"!paymentVerificationForm.amount_received || paymentVerificationForm.loading\"\n                              >\n                                <i class=\"fas fa-check-circle me-1\"></i>\n                                <span v-if=\"paymentVerificationForm.loading\">\n                                  <i class=\"fas fa-spinner fa-spin me-1\"></i>\n                                  Verifying...\n                                </span>\n                                <span v-else>Verify Payment</span>\n                              </button>\n                            </div>\n                          </div>\n\n                          <!-- Pickup Scheduling -->\n                          <div v-if=\"canSchedulePickup(currentRequest)\" class=\"mt-4 p-3 border rounded bg-light\">\n                            <h6 class=\"text-info mb-3\">\n                              <i class=\"fas fa-calendar-alt me-2\"></i>\n                              Schedule Pickup Appointment\n                            </h6>\n                            <div class=\"row\">\n                              <div class=\"col-md-4\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Date *</label>\n                                  <input\n                                    type=\"date\"\n                                    class=\"form-control\"\n                                    v-model=\"pickupScheduleForm.scheduled_date\"\n                                    :min=\"getTomorrowDate()\"\n                                  >\n                                </div>\n                              </div>\n                              <div class=\"col-md-4\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Start Time *</label>\n                                  <input\n                                    type=\"time\"\n                                    class=\"form-control\"\n                                    v-model=\"pickupScheduleForm.scheduled_time_start\"\n                                  >\n                                </div>\n                              </div>\n                              <div class=\"col-md-4\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">End Time *</label>\n                                  <input\n                                    type=\"time\"\n                                    class=\"form-control\"\n                                    v-model=\"pickupScheduleForm.scheduled_time_end\"\n                                  >\n                                </div>\n                              </div>\n                            </div>\n\n                            <div class=\"d-grid\">\n                              <button\n                                class=\"btn btn-info\"\n                                @click=\"schedulePickup\"\n                                :disabled=\"!isPickupFormValid() || pickupScheduleForm.loading\"\n                              >\n                                <i class=\"fas fa-calendar-check me-1\"></i>\n                                <span v-if=\"pickupScheduleForm.loading\">\n                                  <i class=\"fas fa-spinner fa-spin me-1\"></i>\n                                  Scheduling...\n                                </span>\n                                <span v-else>Schedule Pickup</span>\n                              </button>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Status History Timeline -->\n                  <div class=\"card\">\n                    <div class=\"card-header\">\n                      <h6 class=\"mb-0\"><i class=\"fas fa-history me-2\"></i>Status History</h6>\n                    </div>\n                    <div class=\"card-body\">\n                      <div v-if=\"currentRequest.status_history && currentRequest.status_history.length > 0\" class=\"timeline\">\n                        <div\n                          v-for=\"(history, index) in currentRequest.status_history\"\n                          :key=\"history.id\"\n                          class=\"timeline-item\"\n                          :class=\"{ 'timeline-item-last': index === currentRequest.status_history.length - 1 }\"\n                        >\n                          <div class=\"timeline-marker\" :class=\"`bg-${getStatusColor(history.new_status_name)}`\">\n                            <i class=\"fas fa-circle\"></i>\n                          </div>\n                          <div class=\"timeline-content\">\n                            <div class=\"timeline-header\">\n                              <span class=\"badge\" :class=\"`bg-${getStatusColor(history.new_status_name)}`\">\n                                {{ formatStatus(history.new_status_name) }}\n                              </span>\n                              <small class=\"text-muted ms-2\">{{ formatDateTime(history.changed_at) }}</small>\n                            </div>\n                            <div class=\"timeline-body\">\n                              <p class=\"mb-1\">\n                                <strong>Changed by:</strong> {{ history.changed_by_name }}\n                              </p>\n                              <p v-if=\"history.old_status_name\" class=\"mb-1\">\n                                <strong>From:</strong> {{ formatStatus(history.old_status_name) }}\n                              </p>\n                              <p v-if=\"history.change_reason\" class=\"mb-0\">\n                                <strong>Reason:</strong> {{ history.change_reason }}\n                              </p>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      <div v-else class=\"text-center text-muted py-3\">\n                        <i class=\"fas fa-history fa-2x mb-2\"></i>\n                        <p>No status history available</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <div class=\"modal-footer\">\n                  <button type=\"button\" class=\"btn btn-secondary\" @click=\"showRequestDetails = false\">\n                    <i class=\"fas fa-times me-1\"></i>\n                    Close\n                  </button>\n                  <button type=\"button\" class=\"btn btn-primary\" @click=\"refreshRequestDetails\">\n                    <i class=\"fas fa-sync-alt me-1\"></i>\n                    Refresh\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Quick Reject Modal -->\n          <div v-if=\"showQuickReject && selectedRequestForReject\" class=\"modal fade show d-block\" tabindex=\"-1\" style=\"background-color: rgba(0,0,0,0.5);\">\n            <div class=\"modal-dialog\">\n              <div class=\"modal-content\">\n                <div class=\"modal-header\">\n                  <h5 class=\"modal-title\">\n                    <i class=\"fas fa-times-circle text-danger me-2\"></i>\n                    Reject Request\n                  </h5>\n                  <button type=\"button\" class=\"btn-close\" @click=\"closeQuickRejectModal\"></button>\n                </div>\n                <div class=\"modal-body\">\n                  <div class=\"alert alert-warning\">\n                    <i class=\"fas fa-exclamation-triangle me-2\"></i>\n                    You are about to reject this document request. This action will notify the client immediately.\n                  </div>\n\n                  <div class=\"mb-3\">\n                    <strong>Request Details:</strong>\n                    <ul class=\"list-unstyled mt-2\">\n                      <li><strong>Request Number:</strong> {{ selectedRequestForReject.request_number }}</li>\n                      <li><strong>Document Type:</strong> {{ selectedRequestForReject.document_type }}</li>\n                      <li><strong>Client:</strong> {{ selectedRequestForReject.client_name }}</li>\n                    </ul>\n                  </div>\n\n\n                </div>\n                <div class=\"modal-footer\">\n                  <button type=\"button\" class=\"btn btn-secondary\" @click=\"closeQuickRejectModal\" :disabled=\"quickRejectForm.loading\">\n                    <i class=\"fas fa-times me-1\"></i>\n                    Cancel\n                  </button>\n                  <button type=\"button\" class=\"btn btn-danger\" @click=\"confirmQuickReject\" :disabled=\"quickRejectForm.loading\">\n                    <i class=\"fas fa-times-circle me-1\"></i>\n                    <span v-if=\"quickRejectForm.loading\">\n                      <i class=\"fas fa-spinner fa-spin me-1\"></i>\n                      Rejecting...\n                    </span>\n                    <span v-else>Reject Request</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Quick Approve Modal -->\n          <div v-if=\"showQuickApprove && selectedRequestForApprove\" class=\"modal fade show d-block\" tabindex=\"-1\" style=\"background-color: rgba(0,0,0,0.5);\">\n            <div class=\"modal-dialog\">\n              <div class=\"modal-content\">\n                <div class=\"modal-header\">\n                  <h5 class=\"modal-title\">\n                    <i class=\"fas fa-check-circle text-success me-2\"></i>\n                    Approve Request\n                  </h5>\n                  <button type=\"button\" class=\"btn-close\" @click=\"closeQuickApproveModal\"></button>\n                </div>\n                <div class=\"modal-body\">\n                  <div class=\"alert alert-info\">\n                    <i class=\"fas fa-info-circle me-2\"></i>\n                    You are about to approve this document request. This action will notify the client immediately and move the request to the next processing stage.\n                  </div>\n\n                  <div class=\"mb-3\">\n                    <strong>Request Details:</strong>\n                    <ul class=\"list-unstyled mt-2\">\n                      <li><strong>Request Number:</strong> {{ selectedRequestForApprove.request_number }}</li>\n                      <li><strong>Document Type:</strong> {{ selectedRequestForApprove.document_type }}</li>\n                      <li><strong>Client:</strong> {{ selectedRequestForApprove.client_name }}</li>\n                    </ul>\n                  </div>\n\n                  <div v-if=\"quickApproveForm.error\" class=\"alert alert-danger\">\n                    <i class=\"fas fa-exclamation-triangle me-2\"></i>\n                    {{ quickApproveForm.error }}\n                  </div>\n                </div>\n                <div class=\"modal-footer\">\n                  <button type=\"button\" class=\"btn btn-secondary\" @click=\"closeQuickApproveModal\" :disabled=\"quickApproveForm.loading\">\n                    <i class=\"fas fa-times me-1\"></i>\n                    Cancel\n                  </button>\n                  <button type=\"button\" class=\"btn btn-success\" @click=\"confirmQuickApprove\" :disabled=\"quickApproveForm.loading\">\n                    <i class=\"fas fa-check-circle me-1\"></i>\n                    <span v-if=\"quickApproveForm.loading\">\n                      <i class=\"fas fa-spinner fa-spin me-1\"></i>\n                      Approving...\n                    </span>\n                    <span v-else>Approve Request</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n\n    <!-- Image Modal -->\n    <div v-if=\"showImageModal && selectedImage\" class=\"modal fade show d-block\" tabindex=\"-1\" style=\"background-color: rgba(0,0,0,0.8);\" @click.self=\"closeImageModal\">\n      <div class=\"modal-dialog modal-xl modal-dialog-centered\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header sticky-header\">\n            <h5 class=\"modal-title\">\n              <i class=\"fas fa-image me-2\"></i>\n              {{ selectedImage.document_name }}\n            </h5>\n            <div class=\"header-controls\">\n              <button\n                type=\"button\"\n                class=\"btn btn-outline-light btn-sm me-2\"\n                @click=\"downloadDocument(selectedImage)\"\n                :disabled=\"!documentUrls[selectedImage.id] || imageLoadingInModal\"\n                title=\"Download\">\n                <i class=\"fas fa-download\"></i>\n              </button>\n              <button\n                type=\"button\"\n                class=\"btn-close btn-close-white\"\n                @click=\"closeImageModal\"\n                aria-label=\"Close\"\n                title=\"Close\">\n              </button>\n            </div>\n          </div>\n          <div class=\"modal-body text-center p-0\">\n            <div class=\"image-modal-container\">\n              <!-- Successfully loaded image -->\n              <img\n                v-if=\"documentUrls[selectedImage.id] && !imageLoadingInModal\"\n                :src=\"documentUrls[selectedImage.id]\"\n                :alt=\"selectedImage.document_name\"\n                class=\"modal-image\"\n                @error=\"handleImageError\"\n                @load=\"onModalImageLoad\"\n                loading=\"lazy\"\n              />\n\n              <!-- Loading state -->\n              <div v-else-if=\"imageLoadingInModal || loadingDocuments.has(selectedImage.id)\" class=\"loading-placeholder modal-loading\">\n                <div class=\"loading-content\">\n                  <i class=\"fas fa-spinner fa-spin fa-3x mb-3\"></i>\n                  <span class=\"loading-text\">Loading high-resolution image...</span>\n                  <div class=\"loading-progress mt-2\">\n                    <div class=\"progress-bar\"></div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Failed state -->\n              <div v-else-if=\"modalImageError || failedDocuments.has(selectedImage.id)\" class=\"error-placeholder modal-error\">\n                <i class=\"fas fa-exclamation-triangle fa-3x mb-3\"></i>\n                <span class=\"error-text\">Failed to load image</span>\n                <button\n                  class=\"btn btn-outline-light mt-3\"\n                  @click=\"retryLoadDocument(selectedImage)\"\n                  :disabled=\"imageLoadingInModal\">\n                  <i class=\"fas fa-redo me-2\"></i>Retry\n                </button>\n              </div>\n\n              <!-- Fallback -->\n              <div v-else class=\"loading-placeholder modal-loading\">\n                <i class=\"fas fa-image fa-3x mb-3\"></i>\n                <span class=\"loading-text\">Preparing image...</span>\n              </div>\n            </div>\n          </div>\n          <div class=\"modal-footer\">\n            <div class=\"d-flex justify-content-between align-items-center w-100\">\n              <div class=\"image-info\">\n                <span class=\"badge bg-info me-2\">{{ getDocumentTypeDisplayName(selectedImage.document_type) }}</span>\n                <small class=\"text-muted\">\n                  {{ formatFileSize(selectedImage.file_size) }} •\n                  Uploaded {{ formatDate(selectedImage.created_at) }}\n                </small>\n              </div>\n              <div class=\"image-actions\">\n                <button\n                  type=\"button\"\n                  class=\"btn btn-outline-primary me-2\"\n                  @click=\"downloadDocument(selectedImage)\"\n                  :disabled=\"!documentUrls[selectedImage.id] || imageLoadingInModal\">\n                  <i class=\"fas fa-download me-1\"></i>Download\n                </button>\n                <button type=\"button\" class=\"btn btn-secondary\" @click=\"closeImageModal\">\n                  <i class=\"fas fa-times me-1\"></i>Close\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n</template>\n\n<script>\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\nimport adminAuthService from '@/services/adminAuthService';\nimport adminDocumentService from '@/services/adminDocumentService';\nimport api from '@/services/api';\nimport notificationService from '@/services/notificationService';\n\nexport default {\n  name: 'AdminRequests',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n\n\n\n  data() {\n    return {\n      // UI State\n      loading: true,\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      errorMessage: '',\n      viewMode: 'table', // 'card' or 'table' - default to table view\n\n      // Request Management Data\n      requests: [],\n      selectedRequests: [],\n      currentRequest: null,\n      statusOptions: [],\n\n      // Pagination\n      pagination: {\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n      },\n\n      // Filters\n      filters: {\n        status: '',\n        document_type: '',\n        priority: '',\n        search: '',\n        date_from: '',\n        date_to: ''\n      },\n\n      // Statistics\n      requestStats: {\n        total: 0,\n        pending: 0,\n        approved: 0,\n        completed: 0,\n        thisMonth: 0\n      },\n\n      // UI State\n      showFilters: false,\n      showBulkActions: false,\n      showRequestDetails: false,\n      showRejectForm: false,\n      showQuickReject: false,\n      showQuickApprove: false,\n      showImageModal: false,\n      selectedImage: null,\n      bulkAction: '',\n      documentUrls: {}, // Store blob URLs for documents\n      loadingDocuments: new Set(), // Track which documents are currently loading\n      failedDocuments: new Set(), // Track which documents failed to load\n      imageLoadingInModal: false, // Track if modal image is loading\n      modalImageError: false, // Track if modal image failed\n\n      // Status Update Forms\n      statusUpdateForm: {\n        status_id: ''\n      },\n      rejectForm: {\n        reason: ''\n      },\n      quickRejectForm: {\n        loading: false,\n        error: ''\n      },\n      selectedRequestForReject: null,\n      quickApproveForm: {\n        loading: false,\n        error: ''\n      },\n      selectedRequestForApprove: null,\n\n      // Payment verification form\n      paymentVerificationForm: {\n        amount_received: '',\n        receipt_number: '',\n        loading: false,\n        error: ''\n      },\n\n      // Pickup scheduling form\n      pickupScheduleForm: {\n        scheduled_date: '',\n        scheduled_time_start: '',\n        scheduled_time_end: '',\n        loading: false,\n        error: ''\n      },\n\n      // Real-time features\n      refreshInterval: null,\n      autoRefreshEnabled: true,\n      refreshRate: 30000, // 30 seconds\n      lastRefresh: null\n    };\n  },\n\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Load component data\n    await this.loadComponentData();\n\n    // Initialize real-time features\n    this.initializeRealTimeFeatures();\n  },\n\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n\n    // Clean up real-time features\n    this.cleanupRealTimeFeatures();\n\n    // Clean up blob URLs to prevent memory leaks\n    this.cleanupDocumentUrls();\n  },\n\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    }\n  },\n\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    // Handle opening request modal from notifications\n    async handleOpenRequestModal(modalData) {\n      console.log('🔔 AdminRequests: Opening request modal from notification:', modalData);\n\n      try {\n        const { requestId, focusTab } = modalData;\n\n        if (!requestId) {\n          console.error('❌ No request ID provided for modal');\n          return;\n        }\n\n        // Use the existing viewRequestDetails method to open the modal\n        await this.viewRequestDetails(requestId);\n\n        // If a specific tab should be focused, handle that after modal opens\n        if (focusTab) {\n          // Wait a bit for the modal to fully render\n          setTimeout(() => {\n            this.focusModalTab(focusTab);\n          }, 300);\n        }\n\n        console.log('✅ Request modal opened successfully');\n\n      } catch (error) {\n        console.error('❌ Error opening request modal:', error);\n        // Show error message to user\n        this.showErrorMessage('Failed to open request details');\n      }\n    },\n\n    // Focus on a specific tab in the request details modal\n    focusModalTab(tabName) {\n      try {\n        console.log('🎯 Focusing on modal tab:', tabName);\n\n        // Map tab names to actual tab elements or actions\n        const tabMappings = {\n          'payment': () => {\n            // Focus on payment section in the modal\n            const paymentSection = document.querySelector('#requestDetailsModal .payment-section');\n            if (paymentSection) {\n              paymentSection.scrollIntoView({ behavior: 'smooth', block: 'center' });\n              paymentSection.classList.add('highlight-section');\n              setTimeout(() => paymentSection.classList.remove('highlight-section'), 2000);\n            }\n          },\n          'status': () => {\n            // Focus on status section\n            const statusSection = document.querySelector('#requestDetailsModal .status-section');\n            if (statusSection) {\n              statusSection.scrollIntoView({ behavior: 'smooth', block: 'center' });\n              statusSection.classList.add('highlight-section');\n              setTimeout(() => statusSection.classList.remove('highlight-section'), 2000);\n            }\n          },\n          'documents': () => {\n            // Focus on documents section\n            const documentsSection = document.querySelector('#requestDetailsModal .documents-section');\n            if (documentsSection) {\n              documentsSection.scrollIntoView({ behavior: 'smooth', block: 'center' });\n              documentsSection.classList.add('highlight-section');\n              setTimeout(() => documentsSection.classList.remove('highlight-section'), 2000);\n            }\n          }\n        };\n\n        const focusAction = tabMappings[tabName];\n        if (focusAction) {\n          focusAction();\n        } else {\n          console.log('⚠️ Unknown tab name:', tabName);\n        }\n\n      } catch (error) {\n        console.error('❌ Error focusing modal tab:', error);\n      }\n    },\n\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n\n    // Load admin profile\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin profile:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n\n    // Load all component data\n    async loadComponentData() {\n      this.loading = true;\n      try {\n        await Promise.all([\n          this.loadAdminProfile(),\n          this.loadStatusOptions(),\n          this.loadRequests(),\n          this.loadDashboardStats()\n        ]);\n      } catch (error) {\n        console.error('Failed to load component data:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load request data';\n\n        if (errorData.status === 401) {\n          adminAuthService.logout();\n          this.$router.push('/admin/login');\n        }\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // Load status options\n    async loadStatusOptions() {\n      try {\n        console.log('🔄 Loading status options...');\n        const response = await adminDocumentService.getStatusOptions();\n        console.log('📋 Status options response:', response);\n\n        if (response.success) {\n          this.statusOptions = response.data || [];\n          console.log('✅ Status options loaded:', this.statusOptions);\n        } else {\n          console.error('❌ Failed to load status options:', response.message);\n          this.statusOptions = [];\n        }\n      } catch (error) {\n        console.error('❌ Error loading status options:', error);\n        this.statusOptions = [];\n        this.showToast('Error', 'Failed to load status options', 'error');\n      }\n    },\n\n    // Load dashboard statistics\n    async loadDashboardStats() {\n      try {\n        console.log('🔄 Loading dashboard stats...');\n        const response = await adminDocumentService.getDashboardStats();\n        console.log('📊 Dashboard stats response:', response);\n\n        if (response.success) {\n          // Map the backend response structure to frontend expectations\n          const data = response.data;\n          this.requestStats = {\n            total: data.overview?.total_requests || 0,\n            pending: data.overview?.pending_requests || 0,\n            approved: data.overview?.approved_requests || 0,\n            completed: data.overview?.completed_requests || 0,\n            thisMonth: data.time_based?.today_requests || 0\n          };\n          console.log('✅ Request stats updated:', this.requestStats);\n        } else {\n          console.error('❌ Failed to load dashboard stats:', response.message);\n        }\n      } catch (error) {\n        console.error('❌ Error loading dashboard stats:', error);\n        // Set default values on error\n        this.requestStats = {\n          total: 0,\n          pending: 0,\n          approved: 0,\n          completed: 0,\n          thisMonth: 0\n        };\n      }\n    },\n\n    // Load requests with current filters and pagination\n    async loadRequests() {\n      try {\n        const params = {\n          page: this.pagination.currentPage,\n          limit: this.pagination.itemsPerPage,\n          ...this.filters\n        };\n\n        const response = await adminDocumentService.getAllRequests(params);\n        if (response.success) {\n          this.requests = response.data.requests || [];\n          this.pagination = {\n            currentPage: response.data.pagination?.current_page || 1,\n            totalPages: response.data.pagination?.total_pages || 1,\n            totalItems: response.data.pagination?.total_records || 0,\n            itemsPerPage: response.data.pagination?.per_page || 10\n          };\n        }\n      } catch (error) {\n        console.error('Failed to load requests:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load requests';\n        this.requests = [];\n      }\n    },\n\n    // Filter and search methods\n    applyFilters() {\n      this.pagination.currentPage = 1;\n      this.loadRequests();\n    },\n\n    clearFilters() {\n      this.filters = {\n        status: '',\n        document_type: '',\n        priority: '',\n        search: '',\n        date_from: '',\n        date_to: ''\n      };\n      this.applyFilters();\n    },\n\n    // Pagination methods\n    changePage(page) {\n      if (page >= 1 && page <= this.pagination.totalPages) {\n        this.pagination.currentPage = page;\n        this.loadRequests();\n      }\n    },\n\n    changeItemsPerPage(itemsPerPage) {\n      this.pagination.itemsPerPage = itemsPerPage;\n      this.pagination.currentPage = 1;\n      this.loadRequests();\n    },\n\n    goBack() {\n      this.$router.push('/admin/dashboard');\n    },\n\n    // Request selection methods\n    toggleRequestSelection(requestId) {\n      const index = this.selectedRequests.indexOf(requestId);\n      if (index > -1) {\n        this.selectedRequests.splice(index, 1);\n      } else {\n        this.selectedRequests.push(requestId);\n      }\n    },\n\n    selectAllRequests() {\n      if (this.selectedRequests.length === this.requests.length) {\n        this.selectedRequests = [];\n      } else {\n        this.selectedRequests = this.requests.map(r => r.id);\n      }\n    },\n\n    // Request details\n    async viewRequestDetails(requestId) {\n      console.log('🚀 View details clicked for request ID:', requestId);\n\n      try {\n        const response = await adminDocumentService.getRequestDetails(requestId);\n        console.log('📋 API Response received:', response);\n\n        if (response.success) {\n          console.log('✅ Response successful, data:', response.data);\n\n          // Debug client profile fields\n          const data = response.data;\n          console.log('🎯 COMPLETE RESPONSE DATA:', data);\n          console.log('🎯 ALL DATA KEYS:', Object.keys(data));\n          console.log('🎯 CLIENT PROFILE FIELDS DEBUG:');\n          console.log('   Birth Date:', data.client_birth_date);\n          console.log('   Gender:', data.client_gender);\n          console.log('   Civil Status ID:', data.client_civil_status_id);\n          console.log('   Nationality:', data.client_nationality);\n          console.log('   Years of Residency:', data.client_years_of_residency);\n          console.log('   Months of Residency:', data.client_months_of_residency);\n\n          // Check if fields exist with different names\n          console.log('🔍 SEARCHING FOR SIMILAR FIELDS:');\n          Object.keys(data).forEach(key => {\n            if (key.includes('birth') || key.includes('gender') || key.includes('civil') ||\n                key.includes('nationality') || key.includes('residency')) {\n              console.log(`   Found: ${key} = ${data[key]}`);\n            }\n          });\n\n          this.currentRequest = response.data;\n          this.showRequestDetails = true;\n          // Reset forms\n          this.statusUpdateForm = { status_id: '' };\n          this.rejectForm = { reason: '' };\n          this.showRejectForm = false;\n          console.log('📋 Request details loaded:', response.data);\n\n          // Load document URLs for images\n          if (response.data.uploaded_documents && response.data.uploaded_documents.length > 0) {\n            this.loadDocumentUrls(response.data.uploaded_documents);\n          }\n\n          this.showToast('Success', `Request details loaded for ${response.data.request_number}`, 'success');\n        }\n      } catch (error) {\n        console.error('Failed to load request details:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load request details';\n        this.showToast('Error', 'Failed to load request details', 'error');\n      }\n    },\n\n    // Refresh request details\n    async refreshRequestDetails() {\n      if (this.currentRequest) {\n        await this.viewRequestDetails(this.currentRequest.id);\n      }\n    },\n\n    // Update request status from modal\n    async updateRequestStatusFromModal() {\n      console.log('🔄 Updating request status...');\n      console.log('📋 Status form data:', this.statusUpdateForm);\n      console.log('📋 Current request:', this.currentRequest);\n\n      if (!this.statusUpdateForm.status_id || !this.currentRequest) {\n        console.error('❌ Missing required data for status update');\n        this.showToast('Error', 'Please select a status to update', 'error');\n        return;\n      }\n\n      // Enhanced debugging for status validation\n      const currentStatus = this.currentRequest.status_name;\n      const newStatusId = this.statusUpdateForm.status_id;\n      const newStatus = this.statusOptions.find(s => s.id == newStatusId);\n\n      console.log('🔍 Status validation debug:');\n      console.log('   Current status:', currentStatus);\n      console.log('   New status ID:', newStatusId, '(type:', typeof newStatusId, ')');\n      console.log('   New status object:', newStatus);\n      console.log('   Available transitions:', this.getAllowedStatusTransitions(currentStatus.toLowerCase()));\n      console.log('   Available status options:', this.getAvailableStatusOptions());\n      console.log('   All status options:', this.statusOptions);\n\n      if (!this.isValidStatusChange(currentStatus, newStatusId)) {\n        console.error('❌ Invalid status change attempted');\n        console.error('   From:', currentStatus, 'To:', newStatus?.status_name);\n        this.showToast('Error', 'This status change is not allowed', 'error');\n        return;\n      }\n\n      try {\n        const updateData = {\n          status_id: parseInt(this.statusUpdateForm.status_id)\n        };\n\n        console.log('📤 Sending status update:', updateData);\n\n        const response = await adminDocumentService.updateRequestStatus(\n          this.currentRequest.id,\n          updateData\n        );\n\n        console.log('📥 Status update response:', response);\n\n        if (response.success) {\n          // Refresh the request details\n          await this.refreshRequestDetails();\n          // Refresh the main requests list\n          await this.loadRequests();\n          // Reset form\n          this.statusUpdateForm = { status_id: '' };\n\n          // Show success message\n          this.errorMessage = '';\n          this.showToast('Success', 'Request status updated successfully', 'success');\n        } else {\n          console.error('❌ Status update failed:', response.message);\n          this.showToast('Error', response.message || 'Failed to update request status', 'error');\n        }\n      } catch (error) {\n        console.error('❌ Error updating request status:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to update request status';\n        this.showToast('Error', errorData.message || 'Failed to update request status', 'error');\n      }\n    },\n\n\n\n    // Reject request from modal\n    async rejectRequestFromModal() {\n      if (!this.currentRequest || !this.rejectForm.reason.trim()) return;\n\n      try {\n        const response = await adminDocumentService.rejectRequest(\n          this.currentRequest.id,\n          { reason: this.rejectForm.reason }\n        );\n\n        if (response.success) {\n          await this.refreshRequestDetails();\n          await this.loadRequests();\n          this.rejectForm = { reason: '' };\n          this.showRejectForm = false;\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to reject request';\n      }\n    },\n\n    // Status update methods\n    async updateRequestStatus(requestId, statusId, reason = '') {\n      try {\n        const response = await adminDocumentService.updateRequestStatus(requestId, {\n          status_id: statusId,\n          reason: reason\n        });\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to update request status:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to update request status';\n      }\n    },\n\n    async approveRequest(requestId, reason = '') {\n      try {\n        const response = await adminDocumentService.approveRequest(requestId, { reason });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to approve request';\n      }\n    },\n\n    async rejectRequest(requestId, reason) {\n      if (!reason || reason.trim() === '') {\n        this.errorMessage = 'Rejection reason is required';\n        return;\n      }\n\n      try {\n        const response = await adminDocumentService.rejectRequest(requestId, { reason });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to reject request';\n      }\n    },\n\n    // Quick approval/rejection methods\n    canApprove(request) {\n      // Can approve if 'approved' is in allowed transitions\n      const allowedTransitions = this.getAllowedStatusTransitions(request.status_name.toLowerCase());\n      return allowedTransitions.includes('approved');\n    },\n\n    canReject(request) {\n      // Can reject if 'rejected' is in allowed transitions\n      const allowedTransitions = this.getAllowedStatusTransitions(request.status_name.toLowerCase());\n      return allowedTransitions.includes('rejected');\n    },\n\n    // Helper method to get status explanation for disabled buttons\n    getStatusExplanation(request, action) {\n      const status = request.status_name.toLowerCase();\n      const allowedTransitions = this.getAllowedStatusTransitions(status);\n\n      if (action === 'approve') {\n        if (allowedTransitions.includes('approved')) {\n          return 'Click to approve this request';\n        } else if (status === 'approved') {\n          return 'This request has already been approved';\n        } else if (status === 'rejected') {\n          return 'Rejected requests can be resubmitted, not directly approved';\n        } else if (status === 'completed') {\n          return 'This request has already been completed';\n        } else {\n          return `Cannot approve from ${this.formatStatus(status)} status`;\n        }\n      } else if (action === 'reject') {\n        if (allowedTransitions.includes('rejected')) {\n          return 'Click to reject this request';\n        } else if (status === 'rejected') {\n          return 'This request has already been rejected';\n        } else if (status === 'completed') {\n          return 'Cannot reject a completed request';\n        } else {\n          return `Cannot reject from ${this.formatStatus(status)} status`;\n        }\n      }\n\n      return `Request status: ${this.formatStatus(status)}`;\n    },\n\n    // Check if status change is valid\n    isValidStatusChange(currentStatus, newStatusId) {\n      if (!currentStatus || !newStatusId) return false;\n\n      // Find the new status name\n      const newStatus = this.statusOptions.find(s => s.id == newStatusId);\n      if (!newStatus) return false;\n\n      const currentStatusName = currentStatus.toLowerCase();\n      const newStatusName = newStatus.status_name.toLowerCase();\n\n      // Same status - no change needed\n      if (currentStatusName === newStatusName) {\n        return false;\n      }\n\n      // Check if transition is allowed based on workflow rules\n      const allowedTransitions = this.getAllowedStatusTransitions(currentStatusName);\n\n      return allowedTransitions.includes(newStatusName);\n    },\n\n    // Check if request needs payment verification\n    needsPaymentVerification(request) {\n      return request.status_name === 'payment_pending' &&\n             request.payment_method &&\n             !request.payment_method.includes('PayMongo') &&\n             request.payment_status !== 'paid';\n    },\n\n    // Check if pickup can be scheduled\n    canSchedulePickup(request) {\n      return request.status_name === 'ready_for_pickup';\n    },\n\n    // Get payment status color\n    getPaymentStatusColor(status) {\n      const colors = {\n        'pending': 'bg-warning',\n        'processing': 'bg-info',\n        'paid': 'bg-success',\n        'failed': 'bg-danger',\n        'refunded': 'bg-secondary',\n        'cancelled': 'bg-dark'\n      };\n      return colors[status] || 'bg-secondary';\n    },\n\n    // Format payment status\n    formatPaymentStatus(status) {\n      const statuses = {\n        'pending': 'Pending',\n        'processing': 'Processing',\n        'paid': 'Paid',\n        'failed': 'Failed',\n        'refunded': 'Refunded',\n        'cancelled': 'Cancelled'\n      };\n      return statuses[status] || 'Unknown';\n    },\n\n    // Get tomorrow's date for pickup scheduling\n    getTomorrowDate() {\n      const tomorrow = new Date();\n      tomorrow.setDate(tomorrow.getDate() + 1);\n      return tomorrow.toISOString().split('T')[0];\n    },\n\n    // Validate pickup form\n    isPickupFormValid() {\n      return this.pickupScheduleForm.scheduled_date &&\n             this.pickupScheduleForm.scheduled_time_start &&\n             this.pickupScheduleForm.scheduled_time_end &&\n             this.pickupScheduleForm.scheduled_time_start < this.pickupScheduleForm.scheduled_time_end;\n    },\n\n    // Get filtered status options based on current status\n    getAvailableStatusOptions() {\n      if (!this.currentRequest || !this.statusOptions) return [];\n\n      const currentStatus = this.currentRequest.status_name.toLowerCase();\n\n      // Only these states are truly final (cannot be changed)\n      const finalStates = ['completed', 'cancelled'];\n\n      // If current status is final, no changes allowed\n      if (finalStates.includes(currentStatus)) {\n        return [];\n      }\n\n      // Define allowed transitions based on current status\n      const allowedTransitions = this.getAllowedStatusTransitions(currentStatus);\n\n      // Return only allowed status options\n      return this.statusOptions.filter(status =>\n        allowedTransitions.includes(status.status_name.toLowerCase())\n      );\n    },\n\n    // Define allowed status transitions based on government workflow best practices\n    // This must match the backend validateStatusTransition logic exactly\n    getAllowedStatusTransitions(currentStatus) {\n      const transitions = {\n        'pending': ['under_review', 'approved', 'cancelled', 'rejected'],\n        'under_review': ['approved', 'rejected', 'cancelled'], // Removed additional_info_required\n        // Removed 'additional_info_required' status entirely\n        'approved': ['payment_pending', 'cancelled'], // Updated to match backend: approved must go to payment_pending first\n        'payment_pending': ['payment_confirmed', 'payment_failed', 'cancelled'],\n        'payment_confirmed': ['processing'], // Automatic transition after payment\n        'payment_failed': ['payment_pending', 'cancelled'],\n        'processing': ['ready_for_pickup'], // Processing can only complete successfully\n        'ready_for_pickup': ['pickup_scheduled', 'completed', 'cancelled'],\n        'pickup_scheduled': ['completed', 'ready_for_pickup', 'cancelled'], // Can reschedule\n        'rejected': ['pending', 'under_review'], // Allow resubmission after corrections\n        // Final states - no transitions allowed\n        'completed': [],\n        'cancelled': []\n      };\n\n      return transitions[currentStatus] || [];\n    },\n\n    // Get title for update button based on validation state\n    getUpdateButtonTitle() {\n      if (!this.statusUpdateForm.status_id) {\n        return 'Please select a new status';\n      }\n      if (!this.isValidStatusChange(this.currentRequest.status_name, this.statusUpdateForm.status_id)) {\n        return 'Invalid status change';\n      }\n      return 'Update request status';\n    },\n\n    // Get dynamic button text based on selected status\n    getActionButtonText() {\n      if (!this.statusUpdateForm.status_id) {\n        return 'Update Status';\n      }\n\n      const selectedStatus = this.statusOptions.find(s => s.id === parseInt(this.statusUpdateForm.status_id));\n      if (!selectedStatus) {\n        return 'Update Status';\n      }\n\n      const statusName = selectedStatus.status_name.toLowerCase();\n\n      // Special button text for common actions\n      switch (statusName) {\n        case 'approved':\n          return 'Approve Request';\n        case 'rejected':\n          return 'Reject Request';\n        case 'under_review':\n          return 'Move to Review';\n        case 'processing':\n          return 'Start Processing';\n        case 'ready_for_pickup':\n          return 'Mark Ready for Pickup';\n        case 'completed':\n          return 'Complete Request';\n        default:\n          return `Update to ${selectedStatus.status_name}`;\n      }\n    },\n\n    async quickApprove(request) {\n      console.log('🚀 Quick approve clicked for request:', request);\n\n      try {\n        this.loading = true;\n        const response = await adminDocumentService.approveRequest(request.id, {\n          reason: 'Quick approval from admin interface'\n        });\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.showToast('Success', `Request ${request.request_number} approved successfully`, 'success');\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.showToast('Error', errorData.message || 'Failed to approve request', 'error');\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    showQuickRejectModal(request) {\n      console.log('🚀 Quick reject clicked for request:', request);\n\n      this.selectedRequestForReject = request;\n      this.quickRejectForm = {\n        loading: false,\n        error: ''\n      };\n      this.showQuickReject = true;\n    },\n\n    closeQuickRejectModal() {\n      this.showQuickReject = false;\n      this.selectedRequestForReject = null;\n      this.quickRejectForm = {\n        loading: false,\n        error: ''\n      };\n    },\n\n    async confirmQuickReject() {\n      this.quickRejectForm.loading = true;\n      this.quickRejectForm.error = '';\n\n      try {\n        const response = await adminDocumentService.rejectRequest(\n          this.selectedRequestForReject.id,\n          { reason: 'Request rejected by admin' }\n        );\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.showToast('Success', `Request ${this.selectedRequestForReject.request_number} rejected successfully`, 'success');\n          this.closeQuickRejectModal();\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.quickRejectForm.error = errorData.message || 'Failed to reject request';\n      } finally {\n        this.quickRejectForm.loading = false;\n      }\n    },\n\n    showQuickApproveModal(request) {\n      console.log('🚀 Quick approve clicked for request:', request);\n\n      this.selectedRequestForApprove = request;\n      this.quickApproveForm = {\n        loading: false,\n        error: ''\n      };\n      this.showQuickApprove = true;\n    },\n\n    closeQuickApproveModal() {\n      this.showQuickApprove = false;\n      this.selectedRequestForApprove = null;\n      this.quickApproveForm = {\n        loading: false,\n        error: ''\n      };\n    },\n\n    async confirmQuickApprove() {\n      this.quickApproveForm.loading = true;\n      this.quickApproveForm.error = '';\n\n      try {\n        const response = await adminDocumentService.approveRequest(\n          this.selectedRequestForApprove.id,\n          { reason: 'Quick approval from admin interface' }\n        );\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.showToast('Success', `Request ${this.selectedRequestForApprove.request_number} approved successfully`, 'success');\n          this.closeQuickApproveModal();\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.quickApproveForm.error = errorData.message || 'Failed to approve request';\n      } finally {\n        this.quickApproveForm.loading = false;\n      }\n    },\n\n    // Bulk operations\n    async performBulkAction() {\n      if (this.selectedRequests.length === 0) {\n        this.errorMessage = 'Please select at least one request';\n        return;\n      }\n\n      if (!this.bulkAction) {\n        this.errorMessage = 'Please select a bulk action';\n        return;\n      }\n\n      try {\n        const response = await adminDocumentService.bulkUpdateRequests({\n          request_ids: this.selectedRequests,\n          status_id: parseInt(this.bulkAction)\n        });\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.selectedRequests = [];\n          this.bulkAction = '';\n          this.showBulkActions = false;\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to perform bulk action:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to perform bulk action';\n      }\n    },\n\n    // Export functionality\n    async exportRequests() {\n      try {\n        const csvData = await adminDocumentService.exportRequests(this.filters);\n        const filename = `document_requests_${new Date().toISOString().split('T')[0]}.csv`;\n        adminDocumentService.downloadCSV(csvData, filename);\n      } catch (error) {\n        console.error('Failed to export requests:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to export requests';\n      }\n    },\n\n    // Verify in-person payment\n    async verifyInPersonPayment() {\n      if (!this.paymentVerificationForm.amount_received || !this.currentRequest) {\n        this.showToast('Error', 'Please enter the amount received', 'error');\n        return;\n      }\n\n      const totalFee = parseFloat(this.currentRequest.total_fee);\n      const amountReceived = parseFloat(this.paymentVerificationForm.amount_received);\n\n      if (amountReceived < totalFee) {\n        this.showToast('Error', `Insufficient payment. Required: ${this.formatCurrency(totalFee)}`, 'error');\n        return;\n      }\n\n      this.paymentVerificationForm.loading = true;\n      this.paymentVerificationForm.error = '';\n\n      try {\n        const paymentData = {\n          amount_received: amountReceived,\n          payment_method_id: this.currentRequest.payment_method_id || 1, // Default to cash\n          receipt_number: this.paymentVerificationForm.receipt_number\n        };\n\n        const response = await adminDocumentService.verifyInPersonPayment(this.currentRequest.id, paymentData);\n\n        if (response.success) {\n          await this.refreshRequestDetails();\n          await this.loadRequests();\n\n          // Reset form\n          this.paymentVerificationForm = {\n            amount_received: '',\n            receipt_number: '',\n            loading: false,\n            error: ''\n          };\n\n          this.showToast('Success', 'Payment verified successfully', 'success');\n        }\n      } catch (error) {\n        console.error('Failed to verify payment:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.paymentVerificationForm.error = errorData.message || 'Failed to verify payment';\n        this.showToast('Error', errorData.message || 'Failed to verify payment', 'error');\n      } finally {\n        this.paymentVerificationForm.loading = false;\n      }\n    },\n\n    // Schedule pickup appointment\n    async schedulePickup() {\n      if (!this.isPickupFormValid() || !this.currentRequest) {\n        this.showToast('Error', 'Please fill in all required fields', 'error');\n        return;\n      }\n\n      this.pickupScheduleForm.loading = true;\n      this.pickupScheduleForm.error = '';\n\n      try {\n        const scheduleData = {\n          scheduled_date: this.pickupScheduleForm.scheduled_date,\n          scheduled_time_start: this.pickupScheduleForm.scheduled_time_start,\n          scheduled_time_end: this.pickupScheduleForm.scheduled_time_end\n        };\n\n        const response = await adminDocumentService.schedulePickup(this.currentRequest.id, scheduleData);\n\n        if (response.success) {\n          await this.refreshRequestDetails();\n          await this.loadRequests();\n\n          // Reset form\n          this.pickupScheduleForm = {\n            scheduled_date: '',\n            scheduled_time_start: '',\n            scheduled_time_end: '',\n            loading: false,\n            error: ''\n          };\n\n          this.showToast('Success', 'Pickup scheduled successfully', 'success');\n        }\n      } catch (error) {\n        console.error('Failed to schedule pickup:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.pickupScheduleForm.error = errorData.message || 'Failed to schedule pickup';\n        this.showToast('Error', errorData.message || 'Failed to schedule pickup', 'error');\n      } finally {\n        this.pickupScheduleForm.loading = false;\n      }\n    },\n\n    // Utility methods\n    formatStatus(status) {\n      return adminDocumentService.formatStatus(status);\n    },\n\n    getStatusColor(status) {\n      return adminDocumentService.getStatusColor(status);\n    },\n\n    formatDate(dateString) {\n      console.log('🗓️ formatDate called with:', dateString);\n      if (!dateString) {\n        console.log('🗓️ formatDate: No date provided, returning null');\n        return null;\n      }\n      const date = new Date(dateString);\n      const formatted = date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n      console.log('🗓️ formatDate result:', formatted);\n      return formatted;\n    },\n\n    formatCurrency(amount) {\n      return new Intl.NumberFormat('en-PH', {\n        style: 'currency',\n        currency: 'PHP'\n      }).format(amount || 0);\n    },\n\n    formatDateTime(dateString) {\n      if (!dateString) return 'N/A';\n      return new Date(dateString).toLocaleString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    // New helper methods for complete client information\n    getClientFullName(request) {\n      if (!request) return 'Not provided';\n      const parts = [\n        request.client_first_name,\n        request.client_middle_name,\n        request.client_last_name,\n        request.client_suffix\n      ].filter(Boolean);\n      return parts.length > 0 ? parts.join(' ') : request.client_name || 'Not provided';\n    },\n\n    getClientFullAddress(request) {\n      if (!request) return null;\n      const parts = [\n        request.client_house_number,\n        request.client_street,\n        request.client_subdivision,\n        request.client_barangay,\n        request.client_city_municipality || request.client_city,\n        request.client_province\n      ].filter(Boolean);\n      return parts.length > 0 ? parts.join(', ') : (request.client_address || null);\n    },\n\n    formatGender(gender) {\n      if (!gender) {\n        return null;\n      }\n      return gender.charAt(0).toUpperCase() + gender.slice(1);\n    },\n\n    getCivilStatusName(statusId) {\n      const statuses = {\n        1: 'Single',\n        2: 'Married',\n        3: 'Divorced',\n        4: 'Widowed',\n        5: 'Separated'\n      };\n      return statuses[statusId] || null;\n    },\n\n    getResidencyDisplay(request) {\n      if (!request) return null;\n      const years = request.client_years_of_residency;\n      const months = request.client_months_of_residency;\n\n      if (!years && !months) return null; // Return null so the template can handle \"Not provided\"\n\n      const parts = [];\n      if (years) parts.push(`${years} year${years > 1 ? 's' : ''}`);\n      if (months) parts.push(`${months} month${months > 1 ? 's' : ''}`);\n\n      return parts.join(' and ');\n    },\n\n    formatTime(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    },\n\n    // Real-time features\n    async initializeRealTimeFeatures() {\n      console.log('Initializing real-time features for AdminRequests');\n\n      try {\n        // Initialize notification service\n        await notificationService.init('admin');\n\n        // Listen for request-related notifications\n        notificationService.on('notification', this.handleRealTimeNotification);\n        notificationService.on('request_status_changed', this.handleStatusChange);\n        notificationService.on('new_request', this.handleNewRequest);\n\n        // Start auto-refresh if enabled\n        if (this.autoRefreshEnabled) {\n          this.startAutoRefresh();\n        }\n      } catch (error) {\n        console.error('Failed to initialize real-time features:', error);\n      }\n    },\n\n    cleanupRealTimeFeatures() {\n      console.log('Cleaning up real-time features for AdminRequests');\n\n      // Remove notification listeners\n      notificationService.off('notification', this.handleRealTimeNotification);\n      notificationService.off('request_status_changed', this.handleStatusChange);\n      notificationService.off('new_request', this.handleNewRequest);\n\n      // Cleanup (simplified)\n      notificationService.cleanup();\n\n      // Stop auto-refresh\n      this.stopAutoRefresh();\n    },\n\n    startAutoRefresh() {\n      if (this.refreshInterval) {\n        clearInterval(this.refreshInterval);\n      }\n\n      this.refreshInterval = setInterval(() => {\n        if (this.autoRefreshEnabled && !this.loading) {\n          console.log('Auto-refreshing requests data...');\n          this.refreshRequestsData();\n        }\n      }, this.refreshRate);\n\n      console.log(`Auto-refresh started with ${this.refreshRate / 1000}s interval`);\n    },\n\n    stopAutoRefresh() {\n      if (this.refreshInterval) {\n        clearInterval(this.refreshInterval);\n        this.refreshInterval = null;\n        console.log('Auto-refresh stopped');\n      }\n    },\n\n    toggleAutoRefresh() {\n      this.autoRefreshEnabled = !this.autoRefreshEnabled;\n\n      if (this.autoRefreshEnabled) {\n        this.startAutoRefresh();\n      } else {\n        this.stopAutoRefresh();\n      }\n    },\n\n    async refreshRequestsData() {\n      try {\n        this.lastRefresh = new Date();\n\n        // Refresh requests list\n        await this.loadRequests();\n\n        // Refresh statistics\n        await this.loadDashboardStats();\n\n        // If request details modal is open, refresh it\n        if (this.showRequestDetails && this.currentRequest) {\n          await this.refreshRequestDetails();\n        }\n\n        console.log('Requests data refreshed successfully');\n      } catch (error) {\n        console.error('Failed to refresh requests data:', error);\n      }\n    },\n\n    handleRealTimeNotification(notification) {\n      console.log('Real-time notification received:', notification);\n\n      // Handle different notification types\n      switch (notification.type) {\n        case 'request_status_changed':\n          this.handleStatusChange(notification.data);\n          break;\n        case 'new_request':\n          this.handleNewRequest(notification.data);\n          break;\n        case 'request_updated':\n          this.handleRequestUpdate(notification.data);\n          break;\n        case 'unread_count_update':\n        case 'heartbeat':\n          // Polling system notifications - handled by notification service\n          break;\n        default:\n          // Only log unknown types, not system types\n          if (!['unread_count_update', 'heartbeat'].includes(notification.type)) {\n            console.log('Unhandled notification type:', notification.type);\n          }\n      }\n    },\n\n    handleStatusChange(data) {\n      console.log('Request status changed:', data);\n\n      // Update the request in the list if it exists\n      const requestIndex = this.requests.findIndex(req => req.id === data.request_id);\n      if (requestIndex !== -1) {\n        // Refresh the specific request or reload all requests\n        this.refreshRequestsData();\n      }\n\n      // Show toast notification\n      this.showToast('Request status updated', `Request #${data.request_id} status changed to ${data.new_status}`, 'info');\n    },\n\n    handleNewRequest(data) {\n      console.log('New request received:', data);\n\n      // Refresh requests to show the new request\n      this.refreshRequestsData();\n\n      // Show toast notification\n      this.showToast('New Request', `New ${data.document_type} request received`, 'success');\n    },\n\n    handleRequestUpdate(data) {\n      console.log('Request updated:', data);\n\n      // If the updated request is currently being viewed, refresh details\n      if (this.currentRequest && this.currentRequest.id === data.request_id) {\n        this.refreshRequestDetails();\n      }\n\n      // Refresh the requests list\n      this.refreshRequestsData();\n    },\n\n    showToast(title, message, type = 'info') {\n      // Log to console for debugging\n      console.log(`[${type.toUpperCase()}] ${title}: ${message}`);\n\n      // Create a simple toast notification\n      const toast = document.createElement('div');\n      toast.className = `toast-notification toast-${type}`;\n      toast.innerHTML = `\n        <div class=\"toast-header\">\n          <strong>${title}</strong>\n          <button type=\"button\" class=\"toast-close\" onclick=\"this.parentElement.parentElement.remove()\">×</button>\n        </div>\n        <div class=\"toast-body\">${message}</div>\n      `;\n\n      // Add toast styles if not already added\n      if (!document.getElementById('toast-styles')) {\n        const styles = document.createElement('style');\n        styles.id = 'toast-styles';\n        styles.textContent = `\n          .toast-notification {\n            position: fixed;\n            top: 20px;\n            right: 20px;\n            min-width: 300px;\n            background: white;\n            border-radius: 8px;\n            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n            z-index: 9999;\n            animation: slideIn 0.3s ease;\n          }\n          .toast-header {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            padding: 12px 16px 8px;\n            border-bottom: 1px solid #e9ecef;\n          }\n          .toast-body {\n            padding: 8px 16px 12px;\n            color: #6c757d;\n          }\n          .toast-close {\n            background: none;\n            border: none;\n            font-size: 18px;\n            cursor: pointer;\n            color: #6c757d;\n          }\n          .toast-success { border-left: 4px solid #28a745; }\n          .toast-error { border-left: 4px solid #dc3545; }\n          .toast-info { border-left: 4px solid #17a2b8; }\n          .toast-warning { border-left: 4px solid #ffc107; }\n          @keyframes slideIn {\n            from { transform: translateX(100%); opacity: 0; }\n            to { transform: translateX(0); opacity: 1; }\n          }\n        `;\n        document.head.appendChild(styles);\n      }\n\n      // Add toast to page\n      document.body.appendChild(toast);\n\n      // Auto-remove after 5 seconds\n      setTimeout(() => {\n        if (toast.parentElement) {\n          toast.style.animation = 'slideIn 0.3s ease reverse';\n          setTimeout(() => toast.remove(), 300);\n        }\n      }, 5000);\n    },\n\n    // Document handling methods\n    getDocumentTypeDisplayName(type) {\n      const displayNames = {\n        'government_id': 'Government ID',\n        'proof_of_residency': 'Proof of Residency',\n        'cedula': 'Community Tax Certificate (Cedula)',\n        'birth_certificate': 'Birth Certificate',\n        'marriage_certificate': 'Marriage Certificate',\n        'other': 'Other Document'\n      };\n      return displayNames[type] || type;\n    },\n\n    isImageFile(mimeType) {\n      return mimeType && (\n        mimeType.startsWith('image/') ||\n        ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'].includes(mimeType)\n      );\n    },\n\n    isPdfFile(mimeType) {\n      return mimeType === 'application/pdf';\n    },\n\n    async loadDocumentUrls(documents) {\n      // Filter documents that need loading (images only, not already loaded/loading/failed)\n      const documentsToLoad = documents.filter(doc =>\n        this.isImageFile(doc.mime_type) &&\n        !this.documentUrls[doc.id] &&\n        !this.loadingDocuments.has(doc.id) &&\n        !this.failedDocuments.has(doc.id)\n      );\n\n      if (documentsToLoad.length === 0) return;\n\n      // Load documents in parallel with concurrency limit\n      const CONCURRENT_LIMIT = 3;\n      const chunks = this.chunkArray(documentsToLoad, CONCURRENT_LIMIT);\n\n      for (const chunk of chunks) {\n        await Promise.allSettled(\n          chunk.map(document => this.loadSingleDocument(document))\n        );\n      }\n    },\n\n    async loadSingleDocument(document, isForModal = false) {\n      const docId = document.id;\n\n      try {\n        // Mark as loading\n        this.loadingDocuments.add(docId);\n        if (isForModal) this.imageLoadingInModal = true;\n\n        // Use authenticated API call to get the document\n        const response = await api.get(`/documents/view/${docId}`, {\n          responseType: 'blob',\n          timeout: 15000, // Increased timeout for large images\n          onDownloadProgress: (progressEvent) => {\n            // Optional: Could emit progress events here\n            if (progressEvent.lengthComputable) {\n              const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n              console.log(`Loading ${docId}: ${percentCompleted}%`);\n            }\n          }\n        });\n\n        // Validate response\n        if (!response.data || response.data.size === 0) {\n          throw new Error('Empty response received');\n        }\n\n        // Check file size and optimize if needed\n        const blob = response.data;\n        const optimizedBlob = await this.optimizeImageBlob(blob, document.mime_type, isForModal);\n\n        // Create blob URL using requestIdleCallback for better performance\n        await this.createBlobUrlWhenIdle(docId, optimizedBlob);\n\n        // Remove from failed set if it was there\n        this.failedDocuments.delete(docId);\n        if (isForModal) this.modalImageError = false;\n\n      } catch (error) {\n        console.warn(`Failed to load document ${docId}:`, error.message);\n        this.failedDocuments.add(docId);\n        if (isForModal) this.modalImageError = true;\n\n        // Optionally retry after a delay for network errors\n        if (error.code === 'NETWORK_ERROR' || error.code === 'ECONNABORTED') {\n          setTimeout(() => {\n            this.failedDocuments.delete(docId);\n          }, 30000); // Retry after 30 seconds\n        }\n      } finally {\n        // Remove from loading set\n        this.loadingDocuments.delete(docId);\n        if (isForModal) this.imageLoadingInModal = false;\n      }\n    },\n\n    chunkArray(array, size) {\n      const chunks = [];\n      for (let i = 0; i < array.length; i += size) {\n        chunks.push(array.slice(i, i + size));\n      }\n      return chunks;\n    },\n\n    async getDocumentUrl(document) {\n      // This method is now deprecated in favor of loadDocumentUrls\n      // Keeping for backward compatibility\n      if (this.documentUrls[document.id]) {\n        return this.documentUrls[document.id];\n      }\n      return null;\n    },\n\n    formatFileSize(bytes) {\n      if (!bytes) return '0 B';\n      const k = 1024;\n      const sizes = ['B', 'KB', 'MB', 'GB'];\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];\n    },\n\n    async openImageModal(document) {\n      // Prevent multiple rapid clicks\n      if (this.imageLoadingInModal) return;\n\n      // Don't open modal if document failed to load and we're not retrying\n      if (this.failedDocuments.has(document.id)) {\n        return;\n      }\n\n      // Set modal state immediately for responsiveness\n      this.selectedImage = document;\n      this.showImageModal = true;\n      this.modalImageError = false;\n\n      // Use nextTick to ensure DOM is updated before heavy operations\n      await this.$nextTick();\n\n      // If image isn't loaded yet, try to load it with modal optimization\n      if (!this.documentUrls[document.id] && !this.loadingDocuments.has(document.id)) {\n        await this.loadSingleDocument(document, true);\n      }\n    },\n\n    async retryLoadDocument(document) {\n      // Remove from failed set and retry loading\n      this.failedDocuments.delete(document.id);\n      this.modalImageError = false;\n      await this.loadSingleDocument(document, true);\n    },\n\n    onModalImageLoad() {\n      // Called when modal image finishes loading\n      this.imageLoadingInModal = false;\n    },\n\n    cleanupDocumentUrls() {\n      // Revoke all blob URLs to prevent memory leaks\n      Object.values(this.documentUrls).forEach(url => {\n        if (url) URL.revokeObjectURL(url);\n      });\n\n      // Clear all tracking sets and objects\n      this.documentUrls = {};\n      this.loadingDocuments.clear();\n      this.failedDocuments.clear();\n    },\n\n    preloadImage(document) {\n      // Preload image on hover for better UX\n      if (!this.documentUrls[document.id] &&\n          !this.loadingDocuments.has(document.id) &&\n          !this.failedDocuments.has(document.id)) {\n        this.loadSingleDocument(document, false);\n      }\n    },\n\n    async optimizeImageBlob(blob, mimeType, isForModal = false) {\n      // For very large images, we might want to resize them\n      const MAX_SIZE = isForModal ? 5 * 1024 * 1024 : 2 * 1024 * 1024; // 5MB for modal, 2MB for preview\n\n      if (blob.size <= MAX_SIZE) {\n        return blob; // No optimization needed\n      }\n\n      try {\n        // Create image element for resizing\n        const img = new Image();\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n\n        return new Promise((resolve) => {\n          img.onload = () => {\n            // Calculate new dimensions (maintain aspect ratio)\n            const maxDimension = isForModal ? 1920 : 800;\n            let { width, height } = img;\n\n            if (width > height && width > maxDimension) {\n              height = (height * maxDimension) / width;\n              width = maxDimension;\n            } else if (height > maxDimension) {\n              width = (width * maxDimension) / height;\n              height = maxDimension;\n            }\n\n            // Set canvas size and draw resized image\n            canvas.width = width;\n            canvas.height = height;\n            ctx.drawImage(img, 0, 0, width, height);\n\n            // Convert to blob with compression\n            canvas.toBlob(\n              (optimizedBlob) => {\n                resolve(optimizedBlob || blob); // Fallback to original if optimization fails\n              },\n              mimeType,\n              0.85 // 85% quality\n            );\n          };\n\n          img.onerror = () => resolve(blob); // Fallback to original\n          img.src = URL.createObjectURL(blob);\n        });\n      } catch (error) {\n        console.warn('Image optimization failed:', error);\n        return blob; // Fallback to original\n      }\n    },\n\n    async createBlobUrlWhenIdle(docId, blob) {\n      return new Promise((resolve) => {\n        const createUrl = () => {\n          this.documentUrls[docId] = URL.createObjectURL(blob);\n          resolve();\n        };\n\n        // Use requestIdleCallback if available, otherwise use setTimeout\n        if (window.requestIdleCallback) {\n          window.requestIdleCallback(createUrl, { timeout: 1000 });\n        } else {\n          setTimeout(createUrl, 0);\n        }\n      });\n    },\n\n    closeImageModal() {\n      // Prevent rapid clicking during image loading\n      if (this.imageLoadingInModal) return;\n\n      this.showImageModal = false;\n      this.selectedImage = null;\n      this.imageLoadingInModal = false;\n      this.modalImageError = false;\n    },\n\n    async downloadDocument(documentFile) {\n      try {\n        // Use authenticated API call to download the document\n        const response = await api.get(`/documents/download/${documentFile.id}`, {\n          responseType: 'blob'\n        });\n\n        // Create a download link\n        const blob = new Blob([response.data], { type: documentFile.mime_type });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = documentFile.document_name;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n      } catch (error) {\n        console.error('Failed to download document:', error);\n        this.showToast('Error', 'Failed to download document', 'error');\n      }\n    },\n\n    handleImageError(event) {\n      console.error('Failed to load image:', event.target.src);\n      // You could set a placeholder image here\n      event.target.style.display = 'none';\n\n      // Show error message\n      const errorDiv = document.createElement('div');\n      errorDiv.className = 'text-center text-muted p-3';\n      errorDiv.innerHTML = '<i class=\"fas fa-exclamation-triangle\"></i><br>Failed to load image';\n      event.target.parentNode.appendChild(errorDiv);\n    }\n  }\n};\n</script>\n\n<style scoped>\n@import './css/adminDashboard.css';\n\n/* Additional styles specific to AdminRequests */\n.admin-requests {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);\n}\n\n/* Ensure proper spacing for request statistics cards */\n.card.border-left-primary {\n  border-left: 4px solid #3b82f6 !important;\n}\n\n.card.border-left-warning {\n  border-left: 4px solid #f59e0b !important;\n}\n\n.card.border-left-success {\n  border-left: 4px solid #059669 !important;\n}\n\n.card.border-left-info {\n  border-left: 4px solid #06b6d4 !important;\n}\n\n/* Bootstrap utility classes for compatibility */\n.text-xs {\n  font-size: 0.75rem !important;\n}\n\n.text-gray-800 {\n  color: #1f2937 !important;\n}\n\n.text-gray-300 {\n  color: #d1d5db !important;\n}\n\n.text-muted {\n  color: #6c757d !important;\n}\n\n.fw-bold {\n  font-weight: 700 !important;\n}\n\n.g-0 {\n  --bs-gutter-x: 0;\n  --bs-gutter-y: 0;\n}\n\n.me-2 {\n  margin-right: 0.5rem !important;\n}\n\n/* Improve button spacing */\n.d-flex.gap-2 {\n  gap: 0.5rem !important;\n}\n\n/* Timeline Styles */\n.timeline {\n  position: relative;\n  padding-left: 2rem;\n}\n\n.timeline::before {\n  content: '';\n  position: absolute;\n  left: 1rem;\n  top: 0;\n  bottom: 0;\n  width: 2px;\n  background: #e3e6f0;\n}\n\n.timeline-item {\n  position: relative;\n  margin-bottom: 2rem;\n}\n\n.timeline-item:last-child {\n  margin-bottom: 0;\n}\n\n.timeline-item.timeline-item-last::after {\n  display: none;\n}\n\n.timeline-marker {\n  position: absolute;\n  left: -2rem;\n  top: 0.25rem;\n  width: 2rem;\n  height: 2rem;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 0.75rem;\n  z-index: 1;\n}\n\n.timeline-content {\n  background: #f8f9fc;\n  border-radius: 8px;\n  padding: 1rem;\n  border-left: 3px solid #e3e6f0;\n}\n\n.timeline-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.timeline-body p {\n  margin-bottom: 0.25rem;\n  font-size: 0.875rem;\n}\n\n.timeline-body p:last-child {\n  margin-bottom: 0;\n}\n\n/* Modal Styles */\n.modal-xl {\n  max-width: 1200px;\n}\n\n.modal-dialog-scrollable .modal-body {\n  max-height: 70vh;\n  overflow-y: auto;\n}\n\n/* Real-time status indicator styles */\n.real-time-status .badge {\n  font-size: 0.75rem;\n  padding: 0.375rem 0.75rem;\n  border-radius: 1rem;\n}\n\n.pulse {\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n/* Card View Styles */\n.requests-grid {\n  min-height: 400px;\n}\n\n.empty-state {\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 3rem 2rem;\n  margin: 2rem 0;\n}\n\n.empty-state-icon {\n  opacity: 0.5;\n}\n\n.request-card {\n  background: #ffffff;\n  border: 1px solid #e3e6f0;\n  border-radius: 12px;\n  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);\n  transition: all 0.3s ease;\n  overflow: hidden;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.request-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);\n  border-color: #5a5c69;\n}\n\n.request-card.selected {\n  border-color: #4e73df;\n  box-shadow: 0 0 0 2px rgba(78, 115, 223, 0.25);\n}\n\n.request-card-header {\n  padding: 1rem 1.25rem 0.5rem;\n  border-bottom: 1px solid #f1f1f1;\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\n}\n\n.request-card-body {\n  padding: 1.25rem;\n  flex-grow: 1;\n}\n\n.request-card-footer {\n  padding: 0.75rem 1.25rem 1.25rem;\n  background: #f8f9fa;\n  border-top: 1px solid #e3e6f0;\n}\n\n.client-avatar {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  color: white;\n  font-size: 1.2rem;\n}\n\n.client-info h6 {\n  color: #2c3e50;\n  font-weight: 600;\n}\n\n.document-type {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 0.75rem;\n  border-left: 4px solid #17a2b8;\n}\n\n.document-type .badge {\n  font-size: 0.875rem;\n  font-weight: 500;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n}\n\n.request-meta {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 0.75rem;\n}\n\n.meta-item {\n  text-align: center;\n}\n\n.meta-item small {\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.request-date {\n  padding-top: 0.75rem;\n  border-top: 1px solid #e9ecef;\n  margin-top: 0.75rem;\n}\n\n.request-actions .dropdown-toggle {\n  border: none;\n  background: transparent;\n  color: #6c757d;\n  padding: 0.25rem 0.5rem;\n  border-radius: 6px;\n}\n\n.request-actions .dropdown-toggle:hover {\n  background: #e9ecef;\n  color: #495057;\n}\n\n/* View Toggle Styles */\n.btn-check:checked + .btn-outline-primary {\n  background-color: #4e73df;\n  border-color: #4e73df;\n  color: white;\n}\n\n/* Badge Enhancements */\n.badge.bg-info-subtle {\n  background-color: #cff4fc !important;\n  color: #055160 !important;\n  border: 1px solid #b6effb;\n}\n\n/* Button Enhancements */\n.request-card-footer .btn {\n  border-radius: 6px;\n  font-weight: 500;\n  transition: all 0.2s ease;\n}\n\n.request-card-footer .btn:hover {\n  transform: translateY(-1px);\n}\n\n/* Modern Table Styles */\n.modern-table-container {\n  background: #ffffff;\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  overflow: visible;\n  border: 1px solid #e8ecef;\n}\n\n.modern-table-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.modern-table-header h5 {\n  color: white;\n  margin: 0;\n  font-weight: 600;\n}\n\n.table-actions .btn {\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  color: white;\n  backdrop-filter: blur(10px);\n}\n\n.table-actions .btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n  border-color: rgba(255, 255, 255, 0.3);\n  color: white;\n}\n\n.modern-table-empty {\n  padding: 4rem 2rem;\n  text-align: center;\n  background: #f8f9fa;\n}\n\n.empty-content {\n  max-width: 400px;\n  margin: 0 auto;\n}\n\n.empty-icon {\n  font-size: 4rem;\n  color: #6c757d;\n  margin-bottom: 1.5rem;\n  opacity: 0.5;\n}\n\n.empty-title {\n  color: #495057;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n}\n\n.empty-text {\n  color: #6c757d;\n  margin: 0;\n}\n\n/* Compact Table Styles */\n.compact-table-wrapper {\n  background: white;\n  border-radius: 12px;\n  overflow: visible;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  border: 1px solid #e8ecef;\n}\n\n.compact-table-header {\n  display: grid;\n  grid-template-columns: 40px 140px 1fr 140px 120px 100px 120px 120px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  font-weight: 600;\n  font-size: 0.875rem;\n  padding: 0.75rem 1rem;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  gap: 0.5rem;\n}\n\n.header-cell {\n  display: flex;\n  align-items: center;\n  padding: 0 0.5rem;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  font-size: 0.75rem;\n}\n\n.selection-header {\n  justify-content: center;\n}\n\n.compact-table-body {\n  background: white;\n  overflow: visible;\n}\n\n.compact-row {\n  display: grid;\n  grid-template-columns: 40px 140px 1fr 140px 120px 100px 120px 120px;\n  align-items: center;\n  padding: 0.5rem 1rem;\n  border-bottom: 1px solid #f1f3f4;\n  transition: all 0.15s ease;\n  position: relative;\n  min-height: 48px;\n  gap: 0.5rem;\n}\n\n.compact-row:hover {\n  background: #f8f9fa;\n  transform: translateX(2px);\n  box-shadow: 2px 0 0 #667eea;\n}\n\n.compact-row.selected {\n  background: #e3f2fd;\n  border-left: 3px solid #2196f3;\n}\n\n.compact-row:last-child {\n  border-bottom: none;\n}\n\n.row-cell {\n  display: flex;\n  align-items: center;\n  padding: 0 0.5rem;\n  font-size: 0.875rem;\n}\n\n/* Selection Cell */\n.selection-cell {\n  justify-content: center;\n}\n\n.selection-cell .form-check-input {\n  width: 18px;\n  height: 18px;\n  border-radius: 4px;\n  border: 2px solid #dee2e6;\n}\n\n.selection-cell .form-check-input:checked {\n  background-color: #667eea;\n  border-color: #667eea;\n}\n\n/* Request Number Cell */\n.request-number-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.request-number {\n  font-weight: 700;\n  color: #667eea;\n  font-size: 1rem;\n  letter-spacing: 0.5px;\n}\n\n.request-id {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-top: 2px;\n}\n\n/* Client Cell */\n.client-info {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.client-avatar-sm {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1rem;\n  flex-shrink: 0;\n}\n\n.client-details {\n  min-width: 0;\n  flex: 1;\n}\n\n.client-name {\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 0.95rem;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.client-email {\n  font-size: 0.8rem;\n  color: #6c757d;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-top: 2px;\n}\n\n/* Document Type Cell */\n.document-type-badge {\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.85rem;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);\n}\n\n/* Status Cell */\n.status-badge {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.85rem;\n  font-weight: 500;\n  text-transform: capitalize;\n}\n\n.status-indicator {\n  font-size: 0.6rem;\n  animation: pulse 2s infinite;\n}\n\n.status-success {\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);\n}\n\n.status-warning {\n  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);\n  color: #212529;\n  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);\n}\n\n.status-danger {\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);\n}\n\n.status-info {\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);\n}\n\n.status-secondary {\n  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);\n}\n\n/* Amount Cell */\n.amount-content {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.amount {\n  font-weight: 700;\n  color: #28a745;\n  font-size: 1.1rem;\n}\n\n.currency {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-top: 2px;\n}\n\n/* Date Cell */\n.date-content {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.date {\n  font-weight: 500;\n  color: #495057;\n  font-size: 0.9rem;\n}\n\n.time {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-top: 2px;\n}\n\n/* Actions Cell */\n.action-buttons {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  position: relative;\n}\n\n.action-btn {\n  width: 36px;\n  height: 36px;\n  border: none;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.875rem;\n  transition: all 0.2s ease;\n  cursor: pointer;\n}\n\n.view-btn {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.view-btn:hover {\n  background: #bbdefb;\n  transform: translateY(-2px);\n}\n\n.approve-btn {\n  background: #e8f5e8;\n  color: #2e7d32;\n}\n\n.approve-btn:hover {\n  background: #c8e6c9;\n  transform: translateY(-2px);\n}\n\n.reject-btn {\n  background: #ffebee;\n  color: #c62828;\n}\n\n.reject-btn:hover {\n  background: #ffcdd2;\n  transform: translateY(-2px);\n}\n\n.more-btn {\n  background: #f5f5f5;\n  color: #666;\n}\n\n.more-btn:hover {\n  background: #e0e0e0;\n  transform: translateY(-2px);\n}\n\n.more-btn::after {\n  display: none;\n}\n\n/* Dropdown positioning fixes */\n.modern-table {\n  overflow: visible;\n}\n\n.table-row {\n  overflow: visible;\n}\n\n.action-buttons .dropdown {\n  position: static;\n}\n\n.action-buttons .dropdown-menu {\n  position: absolute !important;\n  top: 100% !important;\n  right: 0 !important;\n  left: auto !important;\n  z-index: 1050 !important;\n  transform: none !important;\n  margin-top: 0.25rem;\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 8px;\n  background: white;\n  min-width: 160px;\n}\n\n.action-buttons .dropdown-menu.show {\n  display: block !important;\n}\n\n/* Ensure dropdown appears above other elements */\n.action-buttons .dropdown.show {\n  z-index: 1051;\n}\n\n/* Pagination Container */\n.pagination-container {\n  background: white;\n  border-radius: 0 0 16px 16px;\n  padding: 1.5rem 2rem;\n  border-top: 1px solid #f1f3f4;\n  margin-top: -1px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.pagination-container .pagination {\n  margin: 0;\n}\n\n.pagination-container .page-link {\n  border: 1px solid #e3e6f0;\n  color: #667eea;\n  padding: 0.5rem 0.75rem;\n  margin: 0 2px;\n  border-radius: 8px;\n  transition: all 0.2s ease;\n}\n\n.pagination-container .page-link:hover {\n  background: #667eea;\n  border-color: #667eea;\n  color: white;\n  transform: translateY(-1px);\n}\n\n.pagination-container .page-item.active .page-link {\n  background: #667eea;\n  border-color: #667eea;\n  color: white;\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\n}\n\n.pagination-container .page-item.disabled .page-link {\n  color: #6c757d;\n  background: #f8f9fa;\n  border-color: #e3e6f0;\n}\n\n/* Responsive improvements */\n@media (max-width: 768px) {\n  .d-flex.gap-2 {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .d-flex.gap-2 .btn {\n    margin-bottom: 0.5rem;\n  }\n\n  .modal-xl {\n    max-width: 95%;\n    margin: 1rem auto;\n  }\n\n  .timeline {\n    padding-left: 1.5rem;\n  }\n\n  .timeline-marker {\n    left: -1.5rem;\n    width: 1.5rem;\n    height: 1.5rem;\n    font-size: 0.625rem;\n  }\n\n  /* Compact table mobile adjustments */\n  .compact-table-header {\n    display: none;\n  }\n\n  .compact-row {\n    grid-template-columns: 1fr;\n    padding: 1rem;\n    gap: 0.75rem;\n    border-radius: 8px;\n    margin-bottom: 0.75rem;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  }\n\n  .row-cell {\n    min-height: auto;\n    flex-direction: column;\n    align-items: flex-start;\n    padding: 0.25rem 0;\n    border-bottom: 1px solid #f1f3f4;\n  }\n\n  .row-cell:last-child {\n    border-bottom: none;\n  }\n\n  .selection-cell {\n    flex-direction: row;\n    justify-content: flex-start;\n  }\n\n  .selection-cell {\n    flex-direction: row;\n    justify-content: flex-start;\n  }\n\n  .client-info {\n    width: 100%;\n  }\n\n  .client-details {\n    flex: 1;\n  }\n\n  .document-type-badge,\n  .status-badge {\n    align-self: flex-start;\n  }\n\n  .amount-content,\n  .date-content {\n    align-items: flex-start;\n  }\n\n  .action-buttons {\n    width: 100%;\n    justify-content: space-between;\n  }\n\n  .action-btn {\n    flex: 1;\n    max-width: 60px;\n  }\n\n  /* Mobile fixes for simple actions */\n  .actions-simple {\n    justify-content: center;\n    gap: 0.5rem;\n  }\n\n  .request-actions-simple {\n    justify-content: center;\n    gap: 0.5rem;\n  }\n\n  /* Card view mobile adjustments */\n  .request-card {\n    margin-bottom: 1rem;\n  }\n\n  .request-card-header,\n  .request-card-body,\n  .request-card-footer {\n    padding: 1rem;\n  }\n\n  .client-info .d-flex {\n    flex-direction: column;\n    text-align: center;\n    gap: 0.5rem;\n  }\n\n  .client-avatar {\n    align-self: center;\n  }\n\n  .request-meta .row {\n    text-align: center;\n  }\n\n  .request-card-footer .d-flex {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n\n  .request-card-footer .btn {\n    width: 100%;\n  }\n\n  /* View toggle mobile */\n  .btn-group {\n    width: 100%;\n  }\n\n  .btn-group .btn {\n    flex: 1;\n  }\n}\n\n@media (max-width: 576px) {\n  .empty-state {\n    padding: 2rem 1rem;\n  }\n\n  .empty-state-icon {\n    font-size: 3rem;\n  }\n\n  .request-card-body {\n    padding: 1rem;\n  }\n\n  .document-type,\n  .request-meta {\n    padding: 0.5rem;\n  }\n}\n\n/* Compact Table Additional Styles */\n.document-badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.25rem 0.75rem;\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\n  color: white;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 500;\n  box-shadow: 0 1px 4px rgba(23, 162, 184, 0.3);\n}\n\n.client-compact {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  width: 100%;\n}\n\n.client-avatar-tiny {\n  width: 28px;\n  height: 28px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 0.75rem;\n  flex-shrink: 0;\n}\n\n.client-info-compact {\n  flex: 1;\n  min-width: 0;\n}\n\n.client-name-compact {\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 0.8rem;\n  line-height: 1.2;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.client-email-compact {\n  font-size: 0.7rem;\n  color: #6c757d;\n  margin-top: 1px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.request-id-small {\n  font-size: 0.7rem;\n  color: #6c757d;\n  margin-top: 1px;\n}\n\n.status-compact {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.25rem 0.75rem;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 500;\n  text-transform: capitalize;\n}\n\n.status-compact i {\n  font-size: 0.5rem;\n}\n\n.amount-compact {\n  font-weight: 700;\n  color: #28a745;\n  font-size: 0.85rem;\n}\n\n.date-compact {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.date-main {\n  font-weight: 500;\n  color: #495057;\n  font-size: 0.8rem;\n  line-height: 1.2;\n}\n\n.time-small {\n  font-size: 0.7rem;\n  color: #6c757d;\n  margin-top: 1px;\n}\n\n.actions-compact {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n/* Fixed Actions Layout */\n.actions-compact-fixed {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  min-width: 120px;\n}\n\n.primary-actions {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n.dropdown-wrapper .dropdown {\n  position: static;\n}\n\n.compact-dropdown {\n  z-index: 1050 !important;\n}\n\n.compact-dropdown .dropdown-item {\n  padding: 0.5rem 1rem !important;\n  font-size: 0.875rem !important;\n  white-space: nowrap !important;\n  display: flex !important;\n  align-items: center !important;\n}\n\n.compact-dropdown .dropdown-item:hover {\n  background-color: #f8f9fa !important;\n}\n\n.compact-dropdown .dropdown-divider {\n  margin: 0.25rem 0 !important;\n}\n\n/* Ensure dropdown appears above table rows */\n.compact-row {\n  position: relative;\n  z-index: 1;\n}\n\n.compact-row:hover {\n  z-index: 2;\n}\n\n.dropdown-wrapper .dropdown.show {\n  z-index: 1051 !important;\n}\n\n.compact-dropdown.show {\n  display: block !important;\n}\n\n.action-btn-sm {\n  width: 28px;\n  height: 28px;\n  border: none;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.75rem;\n  transition: all 0.2s ease;\n  cursor: pointer;\n}\n\n.primary-btn-sm {\n  background: #007bff;\n  color: white;\n  border: 1px solid #007bff;\n}\n\n.primary-btn-sm:hover {\n  background: #0056b3;\n  border-color: #0056b3;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);\n}\n\n.view-btn-sm {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.view-btn-sm:hover {\n  background: #bbdefb;\n  transform: translateY(-1px);\n}\n\n.approve-btn-sm {\n  background: #e8f5e8;\n  color: #2e7d32;\n}\n\n.approve-btn-sm:hover {\n  background: #c8e6c9;\n  transform: translateY(-1px);\n}\n\n.reject-btn-sm {\n  background: #ffebee;\n  color: #c62828;\n}\n\n.reject-btn-sm:hover {\n  background: #ffcdd2;\n  transform: translateY(-1px);\n}\n\n.more-btn-sm {\n  background: #f5f5f5;\n  color: #666;\n}\n\n.more-btn-sm:hover {\n  background: #e0e0e0;\n  transform: translateY(-1px);\n}\n\n.more-btn-sm::after {\n  display: none;\n}\n\n/* Simple Actions Layout */\n.actions-simple {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  justify-content: center;\n}\n\n.request-actions-simple {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n/* Disabled button styles */\n.btn:disabled,\n.action-btn-sm:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  pointer-events: none;\n}\n\n.btn:disabled:hover,\n.action-btn-sm:disabled:hover {\n  transform: none;\n  box-shadow: none;\n}\n\n/* Status-based button styling */\n.btn-outline-success:disabled {\n  background-color: #f8f9fa;\n  border-color: #28a745;\n  color: #28a745;\n}\n\n.btn-outline-danger:disabled {\n  background-color: #f8f9fa;\n  border-color: #dc3545;\n  color: #dc3545;\n}\n\n/* Document Preview Styles */\n.document-preview-card {\n  border: 1px solid #e9ecef;\n  border-radius: 12px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  background: white;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.document-preview-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n}\n\n.document-preview-header {\n  padding: 0.75rem;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.document-type-badge {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.25rem 0.75rem;\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\n  color: white;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n.document-preview-content {\n  position: relative;\n  min-height: 200px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.image-preview {\n  position: relative;\n  width: 100%;\n  height: 200px;\n  overflow: hidden;\n  cursor: pointer;\n}\n\n.document-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.image-preview:hover .document-image {\n  transform: scale(1.05);\n}\n\n.image-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0,0,0,0.7);\n  color: white;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.image-preview:hover .image-overlay {\n  opacity: 1;\n}\n\n.pdf-preview, .file-preview {\n  text-align: center;\n  padding: 1rem;\n}\n\n.pdf-icon, .file-icon {\n  margin-bottom: 1rem;\n}\n\n.document-preview-footer {\n  padding: 0.75rem;\n  background: #f8f9fa;\n  border-top: 1px solid #e9ecef;\n  text-align: center;\n}\n\n.no-documents {\n  background: #f8f9fa;\n  border-radius: 8px;\n  margin: 1rem 0;\n}\n\n/* Image Modal Styles */\n.image-modal-container {\n  max-height: 70vh;\n  overflow: auto;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.modal-image {\n  max-width: 100%;\n  max-height: 70vh;\n  object-fit: contain;\n  border-radius: 8px;\n}\n\n.image-info {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.image-actions {\n  display: flex;\n  gap: 0.5rem;\n}\n\n/* Not Provided Styling */\n.not-provided {\n  font-style: italic;\n  color: #6c757d;\n}\n\n/* Client Details Styling */\n.client-details-grid {\n  font-size: 0.75rem;\n}\n\n.client-details-compact {\n  margin-top: 0.25rem;\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n  font-size: 0.7rem;\n}\n\n.detail-item {\n  display: inline-flex;\n  align-items: center;\n  color: #6c757d;\n  white-space: nowrap;\n}\n\n.detail-item i {\n  font-size: 0.65rem;\n  opacity: 0.7;\n}\n\n/* Loading Placeholder for Documents */\n.loading-placeholder {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #6c757d;\n  background: #f8f9fa;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n}\n\n.loading-placeholder:hover {\n  background: #e9ecef;\n}\n\n.loading-placeholder i {\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n}\n\n.loading-placeholder span {\n  font-size: 0.875rem;\n}\n\n/* Error Placeholder for Failed Documents */\n.error-placeholder {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #dc3545;\n  background: #f8d7da;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n}\n\n.error-placeholder:hover {\n  background: #f5c6cb;\n}\n\n.error-placeholder i {\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n}\n\n.error-placeholder span {\n  font-size: 0.875rem;\n}\n\n.error-placeholder small {\n  font-size: 0.75rem;\n  opacity: 0.8;\n  margin-top: 0.25rem;\n}\n\n/* Modal Loading and Error States */\n.modal-loading {\n  height: 400px;\n  background: rgba(0, 0, 0, 0.1);\n  color: #fff;\n  will-change: opacity;\n}\n\n.modal-error {\n  height: 400px;\n  background: rgba(220, 53, 69, 0.1);\n  color: #fff;\n}\n\n/* Optimized Modal Styles */\n.sticky-header {\n  position: sticky;\n  top: 0;\n  z-index: 1050;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n}\n\n.header-controls {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.modal-image {\n  max-width: 100%;\n  max-height: 80vh;\n  object-fit: contain;\n  will-change: transform;\n  transition: opacity 0.3s ease;\n}\n\n.loading-content, .error-text, .loading-text {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.loading-progress {\n  width: 200px;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 2px;\n  overflow: hidden;\n}\n\n.progress-bar {\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);\n  animation: loading-shimmer 1.5s infinite;\n}\n\n@keyframes loading-shimmer {\n  0% { transform: translateX(-100%); }\n  100% { transform: translateX(100%); }\n}\n\n/* Performance optimizations */\n.image-modal-container {\n  contain: layout style paint;\n  transform: translateZ(0); /* Force hardware acceleration */\n}\n\n.modal-content {\n  will-change: transform;\n}\n\n/* Responsive modal improvements */\n@media (max-width: 768px) {\n  .modal-xl {\n    max-width: 95vw;\n  }\n\n  .modal-image {\n    max-height: 70vh;\n  }\n\n  .header-controls .btn-sm {\n    padding: 0.25rem 0.5rem;\n  }\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .document-preview-card {\n    margin-bottom: 1rem;\n  }\n\n  .image-modal-container {\n    max-height: 60vh;\n  }\n\n  .modal-image {\n    max-height: 60vh;\n  }\n}\n\n/* Highlight section animation for notification focus */\n.highlight-section {\n  background: linear-gradient(90deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 123, 255, 0.05) 100%);\n  border-left: 4px solid #007bff;\n  padding: 1rem;\n  border-radius: 0.375rem;\n  transition: all 0.3s ease;\n  animation: highlightPulse 2s ease-in-out;\n}\n\n@keyframes highlightPulse {\n  0% {\n    background: rgba(0, 123, 255, 0.2);\n    transform: scale(1.02);\n  }\n  50% {\n    background: rgba(0, 123, 255, 0.1);\n    transform: scale(1.01);\n  }\n  100% {\n    background: rgba(0, 123, 255, 0.05);\n    transform: scale(1);\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;AAuwCA,OAAOA,WAAU,MAAO,mBAAmB;AAC3C,OAAOC,YAAW,MAAO,oBAAoB;AAC7C,OAAOC,gBAAe,MAAO,6BAA6B;AAC1D,OAAOC,oBAAmB,MAAO,iCAAiC;AAClE,OAAOC,GAAE,MAAO,gBAAgB;AAChC,OAAOC,mBAAkB,MAAO,gCAAgC;AAEhE,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,UAAU,EAAE;IACVP,WAAW;IACXC;EACF,CAAC;EAIDO,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,OAAO,EAAE,IAAI;MACbC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,KAAK;MACvBC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE,IAAI;MACfC,YAAY,EAAE,EAAE;MAChBC,QAAQ,EAAE,OAAO;MAAE;;MAEnB;MACAC,QAAQ,EAAE,EAAE;MACZC,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,IAAI;MACpBC,aAAa,EAAE,EAAE;MAEjB;MACAC,UAAU,EAAE;QACVC,WAAW,EAAE,CAAC;QACdC,UAAU,EAAE,CAAC;QACbC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE;MAChB,CAAC;MAED;MACAC,OAAO,EAAE;QACPC,MAAM,EAAE,EAAE;QACVC,aAAa,EAAE,EAAE;QACjBC,QAAQ,EAAE,EAAE;QACZC,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE,EAAE;QACbC,OAAO,EAAE;MACX,CAAC;MAED;MACAC,YAAY,EAAE;QACZC,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,CAAC;QACXC,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE;MACb,CAAC;MAED;MACAC,WAAW,EAAE,KAAK;MAClBC,eAAe,EAAE,KAAK;MACtBC,kBAAkB,EAAE,KAAK;MACzBC,cAAc,EAAE,KAAK;MACrBC,eAAe,EAAE,KAAK;MACtBC,gBAAgB,EAAE,KAAK;MACvBC,cAAc,EAAE,KAAK;MACrBC,aAAa,EAAE,IAAI;MACnBC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,CAAC,CAAC;MAAE;MAClBC,gBAAgB,EAAE,IAAIC,GAAG,CAAC,CAAC;MAAE;MAC7BC,eAAe,EAAE,IAAID,GAAG,CAAC,CAAC;MAAE;MAC5BE,mBAAmB,EAAE,KAAK;MAAE;MAC5BC,eAAe,EAAE,KAAK;MAAE;;MAExB;MACAC,gBAAgB,EAAE;QAChBC,SAAS,EAAE;MACb,CAAC;MACDC,UAAU,EAAE;QACVC,MAAM,EAAE;MACV,CAAC;MACDC,eAAe,EAAE;QACfhD,OAAO,EAAE,KAAK;QACdiD,KAAK,EAAE;MACT,CAAC;MACDC,wBAAwB,EAAE,IAAI;MAC9BC,gBAAgB,EAAE;QAChBnD,OAAO,EAAE,KAAK;QACdiD,KAAK,EAAE;MACT,CAAC;MACDG,yBAAyB,EAAE,IAAI;MAE/B;MACAC,uBAAuB,EAAE;QACvBC,eAAe,EAAE,EAAE;QACnBC,cAAc,EAAE,EAAE;QAClBvD,OAAO,EAAE,KAAK;QACdiD,KAAK,EAAE;MACT,CAAC;MAED;MACAO,kBAAkB,EAAE;QAClBC,cAAc,EAAE,EAAE;QAClBC,oBAAoB,EAAE,EAAE;QACxBC,kBAAkB,EAAE,EAAE;QACtB3D,OAAO,EAAE,KAAK;QACdiD,KAAK,EAAE;MACT,CAAC;MAED;MACAW,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE,IAAI;MACxBC,WAAW,EAAE,KAAK;MAAE;MACpBC,WAAW,EAAE;IACf,CAAC;EACH,CAAC;EAED,MAAMC,OAAOA,CAAA,EAAG;IACd;IACA,IAAI,CAACvE,gBAAgB,CAACwE,UAAU,CAAC,CAAC,EAAE;MAClC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,cAAc,CAAC;MACjC;IACF;;IAEA;IACA,IAAI,CAACC,YAAY,CAAC,CAAC;;IAEnB;IACA,MAAM,IAAI,CAACC,iBAAiB,CAAC,CAAC;;IAE9B;IACA,IAAI,CAACC,0BAA0B,CAAC,CAAC;EACnC,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd,IAAI,IAAI,CAACC,YAAY,EAAE;MACrBC,MAAM,CAACC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACF,YAAY,CAAC;IACzD;;IAEA;IACA,IAAI,CAACG,uBAAuB,CAAC,CAAC;;IAE9B;IACA,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC5B,CAAC;EAEDC,QAAQ,EAAE;IACRC,UAAUA,CAAA,EAAG;MACX,MAAMC,IAAG,GAAI,IAAI,CAACC,MAAM,CAACD,IAAI;MAC7B,IAAIA,IAAI,CAACE,QAAQ,CAAC,cAAc,CAAC,EAAE,OAAO,OAAO;MACjD,IAAIF,IAAI,CAACE,QAAQ,CAAC,iBAAiB,CAAC,EAAE,OAAO,UAAU;MACvD,IAAIF,IAAI,CAACE,QAAQ,CAAC,gBAAgB,CAAC,EAAE,OAAO,SAAS;MACrD,IAAIF,IAAI,CAACE,QAAQ,CAAC,iBAAiB,CAAC,EAAE,OAAO,UAAU;MACvD,IAAIF,IAAI,CAACE,QAAQ,CAAC,sBAAsB,CAAC,EAAE,OAAO,UAAU;MAC5D,IAAIF,IAAI,CAACE,QAAQ,CAAC,gBAAgB,CAAC,EAAE,OAAO,SAAS;MACrD,OAAO,WAAW;IACpB;EACF,CAAC;EAEDC,OAAO,EAAE;IACP;IACAd,YAAYA,CAAA,EAAG;MACb,IAAI,CAACjE,QAAO,GAAIsE,MAAM,CAACU,UAAS,IAAK,GAAG;;MAExC;MACA,IAAI,CAAC,IAAI,CAAChF,QAAQ,EAAE;QAClB,MAAMiF,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;QAC3D,IAAI,CAACrF,gBAAe,GAAImF,KAAI,GAAIG,IAAI,CAACC,KAAK,CAACJ,KAAK,IAAI,KAAK;MAC3D,OAAO;QACL,IAAI,CAACnF,gBAAe,GAAI,IAAI,EAAE;MAChC;;MAEA;MACA,IAAI,CAACuE,YAAW,GAAI,MAAM;QACxB,MAAMiB,SAAQ,GAAI,IAAI,CAACtF,QAAQ;QAC/B,IAAI,CAACA,QAAO,GAAIsE,MAAM,CAACU,UAAS,IAAK,GAAG;QAExC,IAAI,IAAI,CAAChF,QAAO,IAAK,CAACsF,SAAS,EAAE;UAC/B,IAAI,CAACxF,gBAAe,GAAI,IAAI,EAAE;QAChC,OAAO,IAAI,CAAC,IAAI,CAACE,QAAO,IAAKsF,SAAS,EAAE;UACtC;UACA,MAAML,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;UAC3D,IAAI,CAACrF,gBAAe,GAAImF,KAAI,GAAIG,IAAI,CAACC,KAAK,CAACJ,KAAK,IAAI,KAAK;QAC3D;MACF,CAAC;MACDX,MAAM,CAACiB,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAClB,YAAY,CAAC;IACtD,CAAC;IAED;IACAmB,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAAC1F,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;MAC9CoF,YAAY,CAACO,OAAO,CAAC,uBAAuB,EAAEL,IAAI,CAACM,SAAS,CAAC,IAAI,CAAC5F,gBAAgB,CAAC,CAAC;IACtF,CAAC;IAED;IACA6F,gBAAgBA,CAACC,IAAI,EAAE;MACrB,MAAMC,MAAK,GAAI;QACb,WAAW,EAAE,kBAAkB;QAC/B,OAAO,EAAE,cAAc;QACvB,UAAU,EAAE,iBAAiB;QAC7B,SAAS,EAAE,gBAAgB;QAC3B,UAAU,EAAE,iBAAiB;QAC7B,UAAU,EAAE,sBAAsB;QAClC,SAAS,EAAE;MACb,CAAC;;MAED;MACA,IAAI,IAAI,CAAC7F,QAAQ,EAAE;QACjB,IAAI,CAACF,gBAAe,GAAI,IAAI;MAC9B;MAEA,IAAI+F,MAAM,CAACD,IAAI,CAAC,EAAE;QAChB,IAAI,CAAC7B,OAAO,CAACC,IAAI,CAAC6B,MAAM,CAACD,IAAI,CAAC,CAAC;MACjC;IACF,CAAC;IAED;IACAE,wBAAwBA,CAAA,EAAG;MACzB,IAAI,CAAC/F,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;IAChD,CAAC;IAED;IACA,MAAMgG,sBAAsBA,CAACC,SAAS,EAAE;MACtCC,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAEF,SAAS,CAAC;MAEpF,IAAI;QACF,MAAM;UAAEG,SAAS;UAAEC;QAAS,IAAIJ,SAAS;QAEzC,IAAI,CAACG,SAAS,EAAE;UACdF,OAAO,CAACnD,KAAK,CAAC,oCAAoC,CAAC;UACnD;QACF;;QAEA;QACA,MAAM,IAAI,CAACuD,kBAAkB,CAACF,SAAS,CAAC;;QAExC;QACA,IAAIC,QAAQ,EAAE;UACZ;UACAE,UAAU,CAAC,MAAM;YACf,IAAI,CAACC,aAAa,CAACH,QAAQ,CAAC;UAC9B,CAAC,EAAE,GAAG,CAAC;QACT;QAEAH,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAEpD,EAAE,OAAOpD,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD;QACA,IAAI,CAAC0D,gBAAgB,CAAC,gCAAgC,CAAC;MACzD;IACF,CAAC;IAED;IACAD,aAAaA,CAACE,OAAO,EAAE;MACrB,IAAI;QACFR,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEO,OAAO,CAAC;;QAEjD;QACA,MAAMC,WAAU,GAAI;UAClB,SAAS,EAAEC,CAAA,KAAM;YACf;YACA,MAAMC,cAAa,GAAIC,QAAQ,CAACC,aAAa,CAAC,uCAAuC,CAAC;YACtF,IAAIF,cAAc,EAAE;cAClBA,cAAc,CAACG,cAAc,CAAC;gBAAEC,QAAQ,EAAE,QAAQ;gBAAEC,KAAK,EAAE;cAAS,CAAC,CAAC;cACtEL,cAAc,CAACM,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;cACjDb,UAAU,CAAC,MAAMM,cAAc,CAACM,SAAS,CAACE,MAAM,CAAC,mBAAmB,CAAC,EAAE,IAAI,CAAC;YAC9E;UACF,CAAC;UACD,QAAQ,EAAEtG,CAAA,KAAM;YACd;YACA,MAAMuG,aAAY,GAAIR,QAAQ,CAACC,aAAa,CAAC,sCAAsC,CAAC;YACpF,IAAIO,aAAa,EAAE;cACjBA,aAAa,CAACN,cAAc,CAAC;gBAAEC,QAAQ,EAAE,QAAQ;gBAAEC,KAAK,EAAE;cAAS,CAAC,CAAC;cACrEI,aAAa,CAACH,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;cAChDb,UAAU,CAAC,MAAMe,aAAa,CAACH,SAAS,CAACE,MAAM,CAAC,mBAAmB,CAAC,EAAE,IAAI,CAAC;YAC7E;UACF,CAAC;UACD,WAAW,EAAEE,CAAA,KAAM;YACjB;YACA,MAAMC,gBAAe,GAAIV,QAAQ,CAACC,aAAa,CAAC,yCAAyC,CAAC;YAC1F,IAAIS,gBAAgB,EAAE;cACpBA,gBAAgB,CAACR,cAAc,CAAC;gBAAEC,QAAQ,EAAE,QAAQ;gBAAEC,KAAK,EAAE;cAAS,CAAC,CAAC;cACxEM,gBAAgB,CAACL,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;cACnDb,UAAU,CAAC,MAAMiB,gBAAgB,CAACL,SAAS,CAACE,MAAM,CAAC,mBAAmB,CAAC,EAAE,IAAI,CAAC;YAChF;UACF;QACF,CAAC;QAED,MAAMI,WAAU,GAAId,WAAW,CAACD,OAAO,CAAC;QACxC,IAAIe,WAAW,EAAE;UACfA,WAAW,CAAC,CAAC;QACf,OAAO;UACLvB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,OAAO,CAAC;QAC9C;MAEF,EAAE,OAAO3D,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;IACF,CAAC;IAED;IACA2E,gBAAgBA,CAACC,MAAM,EAAE;MACvB,IAAIA,MAAK,KAAM,SAAS,EAAE;QACxB,IAAI,CAAC3D,OAAO,CAACC,IAAI,CAAC,gBAAgB,CAAC;MACrC,OAAO,IAAI0D,MAAK,KAAM,UAAU,EAAE;QAChC,IAAI,CAAC3D,OAAO,CAACC,IAAI,CAAC,iBAAiB,CAAC;MACtC;MACA,IAAI,CAACjE,gBAAe,GAAI,KAAK;IAC/B,CAAC;IAED;IACA4H,kBAAkBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAAC3H,QAAQ,EAAE;QACjB,IAAI,CAACF,gBAAe,GAAI,IAAI;MAC9B;IACF,CAAC;IAED;IACA8H,YAAYA,CAAA,EAAG;MACbtI,gBAAgB,CAACuI,MAAM,CAAC,CAAC;MACzB,IAAI,CAAC9D,OAAO,CAACC,IAAI,CAAC,cAAc,CAAC;IACnC,CAAC;IAED;IACA,MAAM8D,gBAAgBA,CAAA,EAAG;MACvB,IAAI;QACF,MAAMC,QAAO,GAAI,MAAMzI,gBAAgB,CAAC0I,UAAU,CAAC,CAAC;QACpD,IAAID,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAAChI,SAAQ,GAAI8H,QAAQ,CAACnI,IAAI;QAChC;MACF,EAAE,OAAOkD,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAAC7C,SAAQ,GAAIX,gBAAgB,CAAC4I,YAAY,CAAC,CAAC;MAClD;IACF,CAAC;IAED;IACA,MAAMhE,iBAAiBA,CAAA,EAAG;MACxB,IAAI,CAACrE,OAAM,GAAI,IAAI;MACnB,IAAI;QACF,MAAMsI,OAAO,CAACC,GAAG,CAAC,CAChB,IAAI,CAACN,gBAAgB,CAAC,CAAC,EACvB,IAAI,CAACO,iBAAiB,CAAC,CAAC,EACxB,IAAI,CAACC,YAAY,CAAC,CAAC,EACnB,IAAI,CAACC,kBAAkB,CAAC,EACzB,CAAC;MACJ,EAAE,OAAOzF,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,MAAM0F,SAAQ,GAAIjJ,oBAAoB,CAACkJ,UAAU,CAAC3F,KAAK,CAAC;QACxD,IAAI,CAAC5C,YAAW,GAAIsI,SAAS,CAACE,OAAM,IAAK,6BAA6B;QAEtE,IAAIF,SAAS,CAAC1H,MAAK,KAAM,GAAG,EAAE;UAC5BxB,gBAAgB,CAACuI,MAAM,CAAC,CAAC;UACzB,IAAI,CAAC9D,OAAO,CAACC,IAAI,CAAC,cAAc,CAAC;QACnC;MACF,UAAU;QACR,IAAI,CAACnE,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAED;IACA,MAAMwI,iBAAiBA,CAAA,EAAG;MACxB,IAAI;QACFpC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3C,MAAM6B,QAAO,GAAI,MAAMxI,oBAAoB,CAACoJ,gBAAgB,CAAC,CAAC;QAC9D1C,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE6B,QAAQ,CAAC;QAEpD,IAAIA,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAAC1H,aAAY,GAAIwH,QAAQ,CAACnI,IAAG,IAAK,EAAE;UACxCqG,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC3F,aAAa,CAAC;QAC7D,OAAO;UACL0F,OAAO,CAACnD,KAAK,CAAC,kCAAkC,EAAEiF,QAAQ,CAACW,OAAO,CAAC;UACnE,IAAI,CAACnI,aAAY,GAAI,EAAE;QACzB;MACF,EAAE,OAAOuC,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,IAAI,CAACvC,aAAY,GAAI,EAAE;QACvB,IAAI,CAACqI,SAAS,CAAC,OAAO,EAAE,+BAA+B,EAAE,OAAO,CAAC;MACnE;IACF,CAAC;IAED;IACA,MAAML,kBAAkBA,CAAA,EAAG;MACzB,IAAI;QACFtC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C,MAAM6B,QAAO,GAAI,MAAMxI,oBAAoB,CAACsJ,iBAAiB,CAAC,CAAC;QAC/D5C,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE6B,QAAQ,CAAC;QAErD,IAAIA,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,MAAMrI,IAAG,GAAImI,QAAQ,CAACnI,IAAI;UAC1B,IAAI,CAACwB,YAAW,GAAI;YAClBC,KAAK,EAAEzB,IAAI,CAACkJ,QAAQ,EAAEC,cAAa,IAAK,CAAC;YACzCzH,OAAO,EAAE1B,IAAI,CAACkJ,QAAQ,EAAEE,gBAAe,IAAK,CAAC;YAC7CzH,QAAQ,EAAE3B,IAAI,CAACkJ,QAAQ,EAAEG,iBAAgB,IAAK,CAAC;YAC/CzH,SAAS,EAAE5B,IAAI,CAACkJ,QAAQ,EAAEI,kBAAiB,IAAK,CAAC;YACjDzH,SAAS,EAAE7B,IAAI,CAACuJ,UAAU,EAAEC,cAAa,IAAK;UAChD,CAAC;UACDnD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC9E,YAAY,CAAC;QAC5D,OAAO;UACL6E,OAAO,CAACnD,KAAK,CAAC,mCAAmC,EAAEiF,QAAQ,CAACW,OAAO,CAAC;QACtE;MACF,EAAE,OAAO5F,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD;QACA,IAAI,CAAC1B,YAAW,GAAI;UAClBC,KAAK,EAAE,CAAC;UACRC,OAAO,EAAE,CAAC;UACVC,QAAQ,EAAE,CAAC;UACXC,SAAS,EAAE,CAAC;UACZC,SAAS,EAAE;QACb,CAAC;MACH;IACF,CAAC;IAED;IACA,MAAM6G,YAAYA,CAAA,EAAG;MACnB,IAAI;QACF,MAAMe,MAAK,GAAI;UACbC,IAAI,EAAE,IAAI,CAAC9I,UAAU,CAACC,WAAW;UACjC8I,KAAK,EAAE,IAAI,CAAC/I,UAAU,CAACI,YAAY;UACnC,GAAG,IAAI,CAACC;QACV,CAAC;QAED,MAAMkH,QAAO,GAAI,MAAMxI,oBAAoB,CAACiK,cAAc,CAACH,MAAM,CAAC;QAClE,IAAItB,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAAC7H,QAAO,GAAI2H,QAAQ,CAACnI,IAAI,CAACQ,QAAO,IAAK,EAAE;UAC5C,IAAI,CAACI,UAAS,GAAI;YAChBC,WAAW,EAAEsH,QAAQ,CAACnI,IAAI,CAACY,UAAU,EAAEiJ,YAAW,IAAK,CAAC;YACxD/I,UAAU,EAAEqH,QAAQ,CAACnI,IAAI,CAACY,UAAU,EAAEkJ,WAAU,IAAK,CAAC;YACtD/I,UAAU,EAAEoH,QAAQ,CAACnI,IAAI,CAACY,UAAU,EAAEmJ,aAAY,IAAK,CAAC;YACxD/I,YAAY,EAAEmH,QAAQ,CAACnI,IAAI,CAACY,UAAU,EAAEoJ,QAAO,IAAK;UACtD,CAAC;QACH;MACF,EAAE,OAAO9G,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,MAAM0F,SAAQ,GAAIjJ,oBAAoB,CAACkJ,UAAU,CAAC3F,KAAK,CAAC;QACxD,IAAI,CAAC5C,YAAW,GAAIsI,SAAS,CAACE,OAAM,IAAK,yBAAyB;QAClE,IAAI,CAACtI,QAAO,GAAI,EAAE;MACpB;IACF,CAAC;IAED;IACAyJ,YAAYA,CAAA,EAAG;MACb,IAAI,CAACrJ,UAAU,CAACC,WAAU,GAAI,CAAC;MAC/B,IAAI,CAAC6H,YAAY,CAAC,CAAC;IACrB,CAAC;IAEDwB,YAAYA,CAAA,EAAG;MACb,IAAI,CAACjJ,OAAM,GAAI;QACbC,MAAM,EAAE,EAAE;QACVC,aAAa,EAAE,EAAE;QACjBC,QAAQ,EAAE,EAAE;QACZC,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE,EAAE;QACbC,OAAO,EAAE;MACX,CAAC;MACD,IAAI,CAAC0I,YAAY,CAAC,CAAC;IACrB,CAAC;IAED;IACAE,UAAUA,CAACT,IAAI,EAAE;MACf,IAAIA,IAAG,IAAK,KAAKA,IAAG,IAAK,IAAI,CAAC9I,UAAU,CAACE,UAAU,EAAE;QACnD,IAAI,CAACF,UAAU,CAACC,WAAU,GAAI6I,IAAI;QAClC,IAAI,CAAChB,YAAY,CAAC,CAAC;MACrB;IACF,CAAC;IAED0B,kBAAkBA,CAACpJ,YAAY,EAAE;MAC/B,IAAI,CAACJ,UAAU,CAACI,YAAW,GAAIA,YAAY;MAC3C,IAAI,CAACJ,UAAU,CAACC,WAAU,GAAI,CAAC;MAC/B,IAAI,CAAC6H,YAAY,CAAC,CAAC;IACrB,CAAC;IAED2B,MAAMA,CAAA,EAAG;MACP,IAAI,CAAClG,OAAO,CAACC,IAAI,CAAC,kBAAkB,CAAC;IACvC,CAAC;IAED;IACAkG,sBAAsBA,CAAC/D,SAAS,EAAE;MAChC,MAAMgE,KAAI,GAAI,IAAI,CAAC9J,gBAAgB,CAAC+J,OAAO,CAACjE,SAAS,CAAC;MACtD,IAAIgE,KAAI,GAAI,CAAC,CAAC,EAAE;QACd,IAAI,CAAC9J,gBAAgB,CAACgK,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACxC,OAAO;QACL,IAAI,CAAC9J,gBAAgB,CAAC2D,IAAI,CAACmC,SAAS,CAAC;MACvC;IACF,CAAC;IAEDmE,iBAAiBA,CAAA,EAAG;MAClB,IAAI,IAAI,CAACjK,gBAAgB,CAACkK,MAAK,KAAM,IAAI,CAACnK,QAAQ,CAACmK,MAAM,EAAE;QACzD,IAAI,CAAClK,gBAAe,GAAI,EAAE;MAC5B,OAAO;QACL,IAAI,CAACA,gBAAe,GAAI,IAAI,CAACD,QAAQ,CAACoK,GAAG,CAACC,CAAA,IAAKA,CAAC,CAACC,EAAE,CAAC;MACtD;IACF,CAAC;IAED;IACA,MAAMrE,kBAAkBA,CAACF,SAAS,EAAE;MAClCF,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEC,SAAS,CAAC;MAEjE,IAAI;QACF,MAAM4B,QAAO,GAAI,MAAMxI,oBAAoB,CAACoL,iBAAiB,CAACxE,SAAS,CAAC;QACxEF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE6B,QAAQ,CAAC;QAElD,IAAIA,QAAQ,CAACE,OAAO,EAAE;UACpBhC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE6B,QAAQ,CAACnI,IAAI,CAAC;;UAE1D;UACA,MAAMA,IAAG,GAAImI,QAAQ,CAACnI,IAAI;UAC1BqG,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEtG,IAAI,CAAC;UAC/CqG,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE0E,MAAM,CAACC,IAAI,CAACjL,IAAI,CAAC,CAAC;UACnDqG,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9CD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEtG,IAAI,CAACkL,iBAAiB,CAAC;UACrD7E,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEtG,IAAI,CAACmL,aAAa,CAAC;UAC7C9E,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEtG,IAAI,CAACoL,sBAAsB,CAAC;UAC/D/E,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEtG,IAAI,CAACqL,kBAAkB,CAAC;UACvDhF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEtG,IAAI,CAACsL,yBAAyB,CAAC;UACrEjF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEtG,IAAI,CAACuL,0BAA0B,CAAC;;UAEvE;UACAlF,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/C0E,MAAM,CAACC,IAAI,CAACjL,IAAI,CAAC,CAACwL,OAAO,CAACC,GAAE,IAAK;YAC/B,IAAIA,GAAG,CAACvG,QAAQ,CAAC,OAAO,KAAKuG,GAAG,CAACvG,QAAQ,CAAC,QAAQ,KAAKuG,GAAG,CAACvG,QAAQ,CAAC,OAAO,KACvEuG,GAAG,CAACvG,QAAQ,CAAC,aAAa,KAAKuG,GAAG,CAACvG,QAAQ,CAAC,WAAW,CAAC,EAAE;cAC5DmB,OAAO,CAACC,GAAG,CAAC,aAAamF,GAAG,MAAMzL,IAAI,CAACyL,GAAG,CAAC,EAAE,CAAC;YAChD;UACF,CAAC,CAAC;UAEF,IAAI,CAAC/K,cAAa,GAAIyH,QAAQ,CAACnI,IAAI;UACnC,IAAI,CAACgC,kBAAiB,GAAI,IAAI;UAC9B;UACA,IAAI,CAACa,gBAAe,GAAI;YAAEC,SAAS,EAAE;UAAG,CAAC;UACzC,IAAI,CAACC,UAAS,GAAI;YAAEC,MAAM,EAAE;UAAG,CAAC;UAChC,IAAI,CAACf,cAAa,GAAI,KAAK;UAC3BoE,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE6B,QAAQ,CAACnI,IAAI,CAAC;;UAExD;UACA,IAAImI,QAAQ,CAACnI,IAAI,CAAC0L,kBAAiB,IAAKvD,QAAQ,CAACnI,IAAI,CAAC0L,kBAAkB,CAACf,MAAK,GAAI,CAAC,EAAE;YACnF,IAAI,CAACgB,gBAAgB,CAACxD,QAAQ,CAACnI,IAAI,CAAC0L,kBAAkB,CAAC;UACzD;UAEA,IAAI,CAAC1C,SAAS,CAAC,SAAS,EAAE,8BAA8Bb,QAAQ,CAACnI,IAAI,CAAC4L,cAAc,EAAE,EAAE,SAAS,CAAC;QACpG;MACF,EAAE,OAAO1I,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,MAAM0F,SAAQ,GAAIjJ,oBAAoB,CAACkJ,UAAU,CAAC3F,KAAK,CAAC;QACxD,IAAI,CAAC5C,YAAW,GAAIsI,SAAS,CAACE,OAAM,IAAK,gCAAgC;QACzE,IAAI,CAACE,SAAS,CAAC,OAAO,EAAE,gCAAgC,EAAE,OAAO,CAAC;MACpE;IACF,CAAC;IAED;IACA,MAAM6C,qBAAqBA,CAAA,EAAG;MAC5B,IAAI,IAAI,CAACnL,cAAc,EAAE;QACvB,MAAM,IAAI,CAAC+F,kBAAkB,CAAC,IAAI,CAAC/F,cAAc,CAACoK,EAAE,CAAC;MACvD;IACF,CAAC;IAED;IACA,MAAMgB,4BAA4BA,CAAA,EAAG;MACnCzF,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5CD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACzD,gBAAgB,CAAC;MAC1DwD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC5F,cAAc,CAAC;MAEvD,IAAI,CAAC,IAAI,CAACmC,gBAAgB,CAACC,SAAQ,IAAK,CAAC,IAAI,CAACpC,cAAc,EAAE;QAC5D2F,OAAO,CAACnD,KAAK,CAAC,2CAA2C,CAAC;QAC1D,IAAI,CAAC8F,SAAS,CAAC,OAAO,EAAE,kCAAkC,EAAE,OAAO,CAAC;QACpE;MACF;;MAEA;MACA,MAAM+C,aAAY,GAAI,IAAI,CAACrL,cAAc,CAACsL,WAAW;MACrD,MAAMC,WAAU,GAAI,IAAI,CAACpJ,gBAAgB,CAACC,SAAS;MACnD,MAAMoJ,SAAQ,GAAI,IAAI,CAACvL,aAAa,CAACwL,IAAI,CAACC,CAAA,IAAKA,CAAC,CAACtB,EAAC,IAAKmB,WAAW,CAAC;MAEnE5F,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1CD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEyF,aAAa,CAAC;MAChD1F,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE2F,WAAW,EAAE,QAAQ,EAAE,OAAOA,WAAW,EAAE,GAAG,CAAC;MAChF5F,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE4F,SAAS,CAAC;MAC/C7F,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAC+F,2BAA2B,CAACN,aAAa,CAACO,WAAW,CAAC,CAAC,CAAC,CAAC;MACvGjG,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACiG,yBAAyB,CAAC,CAAC,CAAC;MAC7ElG,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC3F,aAAa,CAAC;MAEzD,IAAI,CAAC,IAAI,CAAC6L,mBAAmB,CAACT,aAAa,EAAEE,WAAW,CAAC,EAAE;QACzD5F,OAAO,CAACnD,KAAK,CAAC,mCAAmC,CAAC;QAClDmD,OAAO,CAACnD,KAAK,CAAC,UAAU,EAAE6I,aAAa,EAAE,KAAK,EAAEG,SAAS,EAAEF,WAAW,CAAC;QACvE,IAAI,CAAChD,SAAS,CAAC,OAAO,EAAE,mCAAmC,EAAE,OAAO,CAAC;QACrE;MACF;MAEA,IAAI;QACF,MAAMyD,UAAS,GAAI;UACjB3J,SAAS,EAAE4J,QAAQ,CAAC,IAAI,CAAC7J,gBAAgB,CAACC,SAAS;QACrD,CAAC;QAEDuD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEmG,UAAU,CAAC;QAEpD,MAAMtE,QAAO,GAAI,MAAMxI,oBAAoB,CAACgN,mBAAmB,CAC7D,IAAI,CAACjM,cAAc,CAACoK,EAAE,EACtB2B,UACF,CAAC;QAEDpG,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE6B,QAAQ,CAAC;QAEnD,IAAIA,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,MAAM,IAAI,CAACwD,qBAAqB,CAAC,CAAC;UAClC;UACA,MAAM,IAAI,CAACnD,YAAY,CAAC,CAAC;UACzB;UACA,IAAI,CAAC7F,gBAAe,GAAI;YAAEC,SAAS,EAAE;UAAG,CAAC;;UAEzC;UACA,IAAI,CAACxC,YAAW,GAAI,EAAE;UACtB,IAAI,CAAC0I,SAAS,CAAC,SAAS,EAAE,qCAAqC,EAAE,SAAS,CAAC;QAC7E,OAAO;UACL3C,OAAO,CAACnD,KAAK,CAAC,yBAAyB,EAAEiF,QAAQ,CAACW,OAAO,CAAC;UAC1D,IAAI,CAACE,SAAS,CAAC,OAAO,EAAEb,QAAQ,CAACW,OAAM,IAAK,iCAAiC,EAAE,OAAO,CAAC;QACzF;MACF,EAAE,OAAO5F,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,MAAM0F,SAAQ,GAAIjJ,oBAAoB,CAACkJ,UAAU,CAAC3F,KAAK,CAAC;QACxD,IAAI,CAAC5C,YAAW,GAAIsI,SAAS,CAACE,OAAM,IAAK,iCAAiC;QAC1E,IAAI,CAACE,SAAS,CAAC,OAAO,EAAEJ,SAAS,CAACE,OAAM,IAAK,iCAAiC,EAAE,OAAO,CAAC;MAC1F;IACF,CAAC;IAID;IACA,MAAM8D,sBAAsBA,CAAA,EAAG;MAC7B,IAAI,CAAC,IAAI,CAAClM,cAAa,IAAK,CAAC,IAAI,CAACqC,UAAU,CAACC,MAAM,CAAC6J,IAAI,CAAC,CAAC,EAAE;MAE5D,IAAI;QACF,MAAM1E,QAAO,GAAI,MAAMxI,oBAAoB,CAACmN,aAAa,CACvD,IAAI,CAACpM,cAAc,CAACoK,EAAE,EACtB;UAAE9H,MAAM,EAAE,IAAI,CAACD,UAAU,CAACC;QAAO,CACnC,CAAC;QAED,IAAImF,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAACwD,qBAAqB,CAAC,CAAC;UAClC,MAAM,IAAI,CAACnD,YAAY,CAAC,CAAC;UACzB,IAAI,CAAC3F,UAAS,GAAI;YAAEC,MAAM,EAAE;UAAG,CAAC;UAChC,IAAI,CAACf,cAAa,GAAI,KAAK;QAC7B;MACF,EAAE,OAAOiB,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAM0F,SAAQ,GAAIjJ,oBAAoB,CAACkJ,UAAU,CAAC3F,KAAK,CAAC;QACxD,IAAI,CAAC5C,YAAW,GAAIsI,SAAS,CAACE,OAAM,IAAK,0BAA0B;MACrE;IACF,CAAC;IAED;IACA,MAAM6D,mBAAmBA,CAACpG,SAAS,EAAEwG,QAAQ,EAAE/J,MAAK,GAAI,EAAE,EAAE;MAC1D,IAAI;QACF,MAAMmF,QAAO,GAAI,MAAMxI,oBAAoB,CAACgN,mBAAmB,CAACpG,SAAS,EAAE;UACzEzD,SAAS,EAAEiK,QAAQ;UACnB/J,MAAM,EAAEA;QACV,CAAC,CAAC;QAEF,IAAImF,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAACK,YAAY,CAAC,CAAC;UACzB,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;UAC/B,IAAI,CAACrI,YAAW,GAAI,EAAE;QACxB;MACF,EAAE,OAAO4C,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,MAAM0F,SAAQ,GAAIjJ,oBAAoB,CAACkJ,UAAU,CAAC3F,KAAK,CAAC;QACxD,IAAI,CAAC5C,YAAW,GAAIsI,SAAS,CAACE,OAAM,IAAK,iCAAiC;MAC5E;IACF,CAAC;IAED,MAAMkE,cAAcA,CAACzG,SAAS,EAAEvD,MAAK,GAAI,EAAE,EAAE;MAC3C,IAAI;QACF,MAAMmF,QAAO,GAAI,MAAMxI,oBAAoB,CAACqN,cAAc,CAACzG,SAAS,EAAE;UAAEvD;QAAO,CAAC,CAAC;QACjF,IAAImF,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAACK,YAAY,CAAC,CAAC;UACzB,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;UAC/B,IAAI,CAACrI,YAAW,GAAI,EAAE;QACxB;MACF,EAAE,OAAO4C,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAM0F,SAAQ,GAAIjJ,oBAAoB,CAACkJ,UAAU,CAAC3F,KAAK,CAAC;QACxD,IAAI,CAAC5C,YAAW,GAAIsI,SAAS,CAACE,OAAM,IAAK,2BAA2B;MACtE;IACF,CAAC;IAED,MAAMgE,aAAaA,CAACvG,SAAS,EAAEvD,MAAM,EAAE;MACrC,IAAI,CAACA,MAAK,IAAKA,MAAM,CAAC6J,IAAI,CAAC,MAAM,EAAE,EAAE;QACnC,IAAI,CAACvM,YAAW,GAAI,8BAA8B;QAClD;MACF;MAEA,IAAI;QACF,MAAM6H,QAAO,GAAI,MAAMxI,oBAAoB,CAACmN,aAAa,CAACvG,SAAS,EAAE;UAAEvD;QAAO,CAAC,CAAC;QAChF,IAAImF,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAACK,YAAY,CAAC,CAAC;UACzB,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;UAC/B,IAAI,CAACrI,YAAW,GAAI,EAAE;QACxB;MACF,EAAE,OAAO4C,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAM0F,SAAQ,GAAIjJ,oBAAoB,CAACkJ,UAAU,CAAC3F,KAAK,CAAC;QACxD,IAAI,CAAC5C,YAAW,GAAIsI,SAAS,CAACE,OAAM,IAAK,0BAA0B;MACrE;IACF,CAAC;IAED;IACAmE,UAAUA,CAACC,OAAO,EAAE;MAClB;MACA,MAAMC,kBAAiB,GAAI,IAAI,CAACd,2BAA2B,CAACa,OAAO,CAAClB,WAAW,CAACM,WAAW,CAAC,CAAC,CAAC;MAC9F,OAAOa,kBAAkB,CAACjI,QAAQ,CAAC,UAAU,CAAC;IAChD,CAAC;IAEDkI,SAASA,CAACF,OAAO,EAAE;MACjB;MACA,MAAMC,kBAAiB,GAAI,IAAI,CAACd,2BAA2B,CAACa,OAAO,CAAClB,WAAW,CAACM,WAAW,CAAC,CAAC,CAAC;MAC9F,OAAOa,kBAAkB,CAACjI,QAAQ,CAAC,UAAU,CAAC;IAChD,CAAC;IAED;IACAmI,oBAAoBA,CAACH,OAAO,EAAEpF,MAAM,EAAE;MACpC,MAAM5G,MAAK,GAAIgM,OAAO,CAAClB,WAAW,CAACM,WAAW,CAAC,CAAC;MAChD,MAAMa,kBAAiB,GAAI,IAAI,CAACd,2BAA2B,CAACnL,MAAM,CAAC;MAEnE,IAAI4G,MAAK,KAAM,SAAS,EAAE;QACxB,IAAIqF,kBAAkB,CAACjI,QAAQ,CAAC,UAAU,CAAC,EAAE;UAC3C,OAAO,+BAA+B;QACxC,OAAO,IAAIhE,MAAK,KAAM,UAAU,EAAE;UAChC,OAAO,wCAAwC;QACjD,OAAO,IAAIA,MAAK,KAAM,UAAU,EAAE;UAChC,OAAO,6DAA6D;QACtE,OAAO,IAAIA,MAAK,KAAM,WAAW,EAAE;UACjC,OAAO,yCAAyC;QAClD,OAAO;UACL,OAAO,uBAAuB,IAAI,CAACoM,YAAY,CAACpM,MAAM,CAAC,SAAS;QAClE;MACF,OAAO,IAAI4G,MAAK,KAAM,QAAQ,EAAE;QAC9B,IAAIqF,kBAAkB,CAACjI,QAAQ,CAAC,UAAU,CAAC,EAAE;UAC3C,OAAO,8BAA8B;QACvC,OAAO,IAAIhE,MAAK,KAAM,UAAU,EAAE;UAChC,OAAO,wCAAwC;QACjD,OAAO,IAAIA,MAAK,KAAM,WAAW,EAAE;UACjC,OAAO,mCAAmC;QAC5C,OAAO;UACL,OAAO,sBAAsB,IAAI,CAACoM,YAAY,CAACpM,MAAM,CAAC,SAAS;QACjE;MACF;MAEA,OAAO,mBAAmB,IAAI,CAACoM,YAAY,CAACpM,MAAM,CAAC,EAAE;IACvD,CAAC;IAED;IACAsL,mBAAmBA,CAACT,aAAa,EAAEE,WAAW,EAAE;MAC9C,IAAI,CAACF,aAAY,IAAK,CAACE,WAAW,EAAE,OAAO,KAAK;;MAEhD;MACA,MAAMC,SAAQ,GAAI,IAAI,CAACvL,aAAa,CAACwL,IAAI,CAACC,CAAA,IAAKA,CAAC,CAACtB,EAAC,IAAKmB,WAAW,CAAC;MACnE,IAAI,CAACC,SAAS,EAAE,OAAO,KAAK;MAE5B,MAAMqB,iBAAgB,GAAIxB,aAAa,CAACO,WAAW,CAAC,CAAC;MACrD,MAAMkB,aAAY,GAAItB,SAAS,CAACF,WAAW,CAACM,WAAW,CAAC,CAAC;;MAEzD;MACA,IAAIiB,iBAAgB,KAAMC,aAAa,EAAE;QACvC,OAAO,KAAK;MACd;;MAEA;MACA,MAAML,kBAAiB,GAAI,IAAI,CAACd,2BAA2B,CAACkB,iBAAiB,CAAC;MAE9E,OAAOJ,kBAAkB,CAACjI,QAAQ,CAACsI,aAAa,CAAC;IACnD,CAAC;IAED;IACAC,wBAAwBA,CAACP,OAAO,EAAE;MAChC,OAAOA,OAAO,CAAClB,WAAU,KAAM,iBAAgB,IACxCkB,OAAO,CAACQ,cAAa,IACrB,CAACR,OAAO,CAACQ,cAAc,CAACxI,QAAQ,CAAC,UAAU,KAC3CgI,OAAO,CAACS,cAAa,KAAM,MAAM;IAC1C,CAAC;IAED;IACAC,iBAAiBA,CAACV,OAAO,EAAE;MACzB,OAAOA,OAAO,CAAClB,WAAU,KAAM,kBAAkB;IACnD,CAAC;IAED;IACA6B,qBAAqBA,CAAC3M,MAAM,EAAE;MAC5B,MAAM4M,MAAK,GAAI;QACb,SAAS,EAAE,YAAY;QACvB,YAAY,EAAE,SAAS;QACvB,MAAM,EAAE,YAAY;QACpB,QAAQ,EAAE,WAAW;QACrB,UAAU,EAAE,cAAc;QAC1B,WAAW,EAAE;MACf,CAAC;MACD,OAAOA,MAAM,CAAC5M,MAAM,KAAK,cAAc;IACzC,CAAC;IAED;IACA6M,mBAAmBA,CAAC7M,MAAM,EAAE;MAC1B,MAAM8M,QAAO,GAAI;QACf,SAAS,EAAE,SAAS;QACpB,YAAY,EAAE,YAAY;QAC1B,MAAM,EAAE,MAAM;QACd,QAAQ,EAAE,QAAQ;QAClB,UAAU,EAAE,UAAU;QACtB,WAAW,EAAE;MACf,CAAC;MACD,OAAOA,QAAQ,CAAC9M,MAAM,KAAK,SAAS;IACtC,CAAC;IAED;IACA+M,eAAeA,CAAA,EAAG;MAChB,MAAMC,QAAO,GAAI,IAAIC,IAAI,CAAC,CAAC;MAC3BD,QAAQ,CAACE,OAAO,CAACF,QAAQ,CAACG,OAAO,CAAC,IAAI,CAAC,CAAC;MACxC,OAAOH,QAAQ,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED;IACAC,iBAAiBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAAC/K,kBAAkB,CAACC,cAAa,IACrC,IAAI,CAACD,kBAAkB,CAACE,oBAAmB,IAC3C,IAAI,CAACF,kBAAkB,CAACG,kBAAiB,IACzC,IAAI,CAACH,kBAAkB,CAACE,oBAAmB,GAAI,IAAI,CAACF,kBAAkB,CAACG,kBAAkB;IAClG,CAAC;IAED;IACA2I,yBAAyBA,CAAA,EAAG;MAC1B,IAAI,CAAC,IAAI,CAAC7L,cAAa,IAAK,CAAC,IAAI,CAACC,aAAa,EAAE,OAAO,EAAE;MAE1D,MAAMoL,aAAY,GAAI,IAAI,CAACrL,cAAc,CAACsL,WAAW,CAACM,WAAW,CAAC,CAAC;;MAEnE;MACA,MAAMmC,WAAU,GAAI,CAAC,WAAW,EAAE,WAAW,CAAC;;MAE9C;MACA,IAAIA,WAAW,CAACvJ,QAAQ,CAAC6G,aAAa,CAAC,EAAE;QACvC,OAAO,EAAE;MACX;;MAEA;MACA,MAAMoB,kBAAiB,GAAI,IAAI,CAACd,2BAA2B,CAACN,aAAa,CAAC;;MAE1E;MACA,OAAO,IAAI,CAACpL,aAAa,CAAC+N,MAAM,CAACxN,MAAK,IACpCiM,kBAAkB,CAACjI,QAAQ,CAAChE,MAAM,CAAC8K,WAAW,CAACM,WAAW,CAAC,CAAC,CAC9D,CAAC;IACH,CAAC;IAED;IACA;IACAD,2BAA2BA,CAACN,aAAa,EAAE;MACzC,MAAM4C,WAAU,GAAI;QAClB,SAAS,EAAE,CAAC,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC;QAChE,cAAc,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC;QAAE;QACvD;QACA,UAAU,EAAE,CAAC,iBAAiB,EAAE,WAAW,CAAC;QAAE;QAC9C,iBAAiB,EAAE,CAAC,mBAAmB,EAAE,gBAAgB,EAAE,WAAW,CAAC;QACvE,mBAAmB,EAAE,CAAC,YAAY,CAAC;QAAE;QACrC,gBAAgB,EAAE,CAAC,iBAAiB,EAAE,WAAW,CAAC;QAClD,YAAY,EAAE,CAAC,kBAAkB,CAAC;QAAE;QACpC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,WAAW,EAAE,WAAW,CAAC;QAClE,kBAAkB,EAAE,CAAC,WAAW,EAAE,kBAAkB,EAAE,WAAW,CAAC;QAAE;QACpE,UAAU,EAAE,CAAC,SAAS,EAAE,cAAc,CAAC;QAAE;QACzC;QACA,WAAW,EAAE,EAAE;QACf,WAAW,EAAE;MACf,CAAC;MAED,OAAOA,WAAW,CAAC5C,aAAa,KAAK,EAAE;IACzC,CAAC;IAED;IACA6C,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAAC,IAAI,CAAC/L,gBAAgB,CAACC,SAAS,EAAE;QACpC,OAAO,4BAA4B;MACrC;MACA,IAAI,CAAC,IAAI,CAAC0J,mBAAmB,CAAC,IAAI,CAAC9L,cAAc,CAACsL,WAAW,EAAE,IAAI,CAACnJ,gBAAgB,CAACC,SAAS,CAAC,EAAE;QAC/F,OAAO,uBAAuB;MAChC;MACA,OAAO,uBAAuB;IAChC,CAAC;IAED;IACA+L,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAAC,IAAI,CAAChM,gBAAgB,CAACC,SAAS,EAAE;QACpC,OAAO,eAAe;MACxB;MAEA,MAAMgM,cAAa,GAAI,IAAI,CAACnO,aAAa,CAACwL,IAAI,CAACC,CAAA,IAAKA,CAAC,CAACtB,EAAC,KAAM4B,QAAQ,CAAC,IAAI,CAAC7J,gBAAgB,CAACC,SAAS,CAAC,CAAC;MACvG,IAAI,CAACgM,cAAc,EAAE;QACnB,OAAO,eAAe;MACxB;MAEA,MAAMC,UAAS,GAAID,cAAc,CAAC9C,WAAW,CAACM,WAAW,CAAC,CAAC;;MAE3D;MACA,QAAQyC,UAAU;QAChB,KAAK,UAAU;UACb,OAAO,iBAAiB;QAC1B,KAAK,UAAU;UACb,OAAO,gBAAgB;QACzB,KAAK,cAAc;UACjB,OAAO,gBAAgB;QACzB,KAAK,YAAY;UACf,OAAO,kBAAkB;QAC3B,KAAK,kBAAkB;UACrB,OAAO,uBAAuB;QAChC,KAAK,WAAW;UACd,OAAO,kBAAkB;QAC3B;UACE,OAAO,aAAaD,cAAc,CAAC9C,WAAW,EAAE;MACpD;IACF,CAAC;IAED,MAAMgD,YAAYA,CAAC9B,OAAO,EAAE;MAC1B7G,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE4G,OAAO,CAAC;MAE7D,IAAI;QACF,IAAI,CAACjN,OAAM,GAAI,IAAI;QACnB,MAAMkI,QAAO,GAAI,MAAMxI,oBAAoB,CAACqN,cAAc,CAACE,OAAO,CAACpC,EAAE,EAAE;UACrE9H,MAAM,EAAE;QACV,CAAC,CAAC;QAEF,IAAImF,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAACK,YAAY,CAAC,CAAC;UACzB,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;UAC/B,IAAI,CAACK,SAAS,CAAC,SAAS,EAAE,WAAWkE,OAAO,CAACtB,cAAc,wBAAwB,EAAE,SAAS,CAAC;QACjG;MACF,EAAE,OAAO1I,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAM0F,SAAQ,GAAIjJ,oBAAoB,CAACkJ,UAAU,CAAC3F,KAAK,CAAC;QACxD,IAAI,CAAC8F,SAAS,CAAC,OAAO,EAAEJ,SAAS,CAACE,OAAM,IAAK,2BAA2B,EAAE,OAAO,CAAC;MACpF,UAAU;QACR,IAAI,CAAC7I,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAEDgP,oBAAoBA,CAAC/B,OAAO,EAAE;MAC5B7G,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE4G,OAAO,CAAC;MAE5D,IAAI,CAAC/J,wBAAuB,GAAI+J,OAAO;MACvC,IAAI,CAACjK,eAAc,GAAI;QACrBhD,OAAO,EAAE,KAAK;QACdiD,KAAK,EAAE;MACT,CAAC;MACD,IAAI,CAAChB,eAAc,GAAI,IAAI;IAC7B,CAAC;IAEDgN,qBAAqBA,CAAA,EAAG;MACtB,IAAI,CAAChN,eAAc,GAAI,KAAK;MAC5B,IAAI,CAACiB,wBAAuB,GAAI,IAAI;MACpC,IAAI,CAACF,eAAc,GAAI;QACrBhD,OAAO,EAAE,KAAK;QACdiD,KAAK,EAAE;MACT,CAAC;IACH,CAAC;IAED,MAAMiM,kBAAkBA,CAAA,EAAG;MACzB,IAAI,CAAClM,eAAe,CAAChD,OAAM,GAAI,IAAI;MACnC,IAAI,CAACgD,eAAe,CAACC,KAAI,GAAI,EAAE;MAE/B,IAAI;QACF,MAAMiF,QAAO,GAAI,MAAMxI,oBAAoB,CAACmN,aAAa,CACvD,IAAI,CAAC3J,wBAAwB,CAAC2H,EAAE,EAChC;UAAE9H,MAAM,EAAE;QAA4B,CACxC,CAAC;QAED,IAAImF,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAACK,YAAY,CAAC,CAAC;UACzB,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;UAC/B,IAAI,CAACK,SAAS,CAAC,SAAS,EAAE,WAAW,IAAI,CAAC7F,wBAAwB,CAACyI,cAAc,wBAAwB,EAAE,SAAS,CAAC;UACrH,IAAI,CAACsD,qBAAqB,CAAC,CAAC;QAC9B;MACF,EAAE,OAAOhM,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAM0F,SAAQ,GAAIjJ,oBAAoB,CAACkJ,UAAU,CAAC3F,KAAK,CAAC;QACxD,IAAI,CAACD,eAAe,CAACC,KAAI,GAAI0F,SAAS,CAACE,OAAM,IAAK,0BAA0B;MAC9E,UAAU;QACR,IAAI,CAAC7F,eAAe,CAAChD,OAAM,GAAI,KAAK;MACtC;IACF,CAAC;IAEDmP,qBAAqBA,CAAClC,OAAO,EAAE;MAC7B7G,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE4G,OAAO,CAAC;MAE7D,IAAI,CAAC7J,yBAAwB,GAAI6J,OAAO;MACxC,IAAI,CAAC9J,gBAAe,GAAI;QACtBnD,OAAO,EAAE,KAAK;QACdiD,KAAK,EAAE;MACT,CAAC;MACD,IAAI,CAACf,gBAAe,GAAI,IAAI;IAC9B,CAAC;IAEDkN,sBAAsBA,CAAA,EAAG;MACvB,IAAI,CAAClN,gBAAe,GAAI,KAAK;MAC7B,IAAI,CAACkB,yBAAwB,GAAI,IAAI;MACrC,IAAI,CAACD,gBAAe,GAAI;QACtBnD,OAAO,EAAE,KAAK;QACdiD,KAAK,EAAE;MACT,CAAC;IACH,CAAC;IAED,MAAMoM,mBAAmBA,CAAA,EAAG;MAC1B,IAAI,CAAClM,gBAAgB,CAACnD,OAAM,GAAI,IAAI;MACpC,IAAI,CAACmD,gBAAgB,CAACF,KAAI,GAAI,EAAE;MAEhC,IAAI;QACF,MAAMiF,QAAO,GAAI,MAAMxI,oBAAoB,CAACqN,cAAc,CACxD,IAAI,CAAC3J,yBAAyB,CAACyH,EAAE,EACjC;UAAE9H,MAAM,EAAE;QAAsC,CAClD,CAAC;QAED,IAAImF,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAACK,YAAY,CAAC,CAAC;UACzB,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;UAC/B,IAAI,CAACK,SAAS,CAAC,SAAS,EAAE,WAAW,IAAI,CAAC3F,yBAAyB,CAACuI,cAAc,wBAAwB,EAAE,SAAS,CAAC;UACtH,IAAI,CAACyD,sBAAsB,CAAC,CAAC;QAC/B;MACF,EAAE,OAAOnM,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAM0F,SAAQ,GAAIjJ,oBAAoB,CAACkJ,UAAU,CAAC3F,KAAK,CAAC;QACxD,IAAI,CAACE,gBAAgB,CAACF,KAAI,GAAI0F,SAAS,CAACE,OAAM,IAAK,2BAA2B;MAChF,UAAU;QACR,IAAI,CAAC1F,gBAAgB,CAACnD,OAAM,GAAI,KAAK;MACvC;IACF,CAAC;IAED;IACA,MAAMsP,iBAAiBA,CAAA,EAAG;MACxB,IAAI,IAAI,CAAC9O,gBAAgB,CAACkK,MAAK,KAAM,CAAC,EAAE;QACtC,IAAI,CAACrK,YAAW,GAAI,oCAAoC;QACxD;MACF;MAEA,IAAI,CAAC,IAAI,CAACgC,UAAU,EAAE;QACpB,IAAI,CAAChC,YAAW,GAAI,6BAA6B;QACjD;MACF;MAEA,IAAI;QACF,MAAM6H,QAAO,GAAI,MAAMxI,oBAAoB,CAAC6P,kBAAkB,CAAC;UAC7DC,WAAW,EAAE,IAAI,CAAChP,gBAAgB;UAClCqC,SAAS,EAAE4J,QAAQ,CAAC,IAAI,CAACpK,UAAU;QACrC,CAAC,CAAC;QAEF,IAAI6F,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAACK,YAAY,CAAC,CAAC;UACzB,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;UAC/B,IAAI,CAAClI,gBAAe,GAAI,EAAE;UAC1B,IAAI,CAAC6B,UAAS,GAAI,EAAE;UACpB,IAAI,CAACP,eAAc,GAAI,KAAK;UAC5B,IAAI,CAACzB,YAAW,GAAI,EAAE;QACxB;MACF,EAAE,OAAO4C,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,MAAM0F,SAAQ,GAAIjJ,oBAAoB,CAACkJ,UAAU,CAAC3F,KAAK,CAAC;QACxD,IAAI,CAAC5C,YAAW,GAAIsI,SAAS,CAACE,OAAM,IAAK,+BAA+B;MAC1E;IACF,CAAC;IAED;IACA,MAAM4G,cAAcA,CAAA,EAAG;MACrB,IAAI;QACF,MAAMC,OAAM,GAAI,MAAMhQ,oBAAoB,CAAC+P,cAAc,CAAC,IAAI,CAACzO,OAAO,CAAC;QACvE,MAAM2O,QAAO,GAAI,qBAAqB,IAAIzB,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;QAClF5O,oBAAoB,CAACkQ,WAAW,CAACF,OAAO,EAAEC,QAAQ,CAAC;MACrD,EAAE,OAAO1M,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAM0F,SAAQ,GAAIjJ,oBAAoB,CAACkJ,UAAU,CAAC3F,KAAK,CAAC;QACxD,IAAI,CAAC5C,YAAW,GAAIsI,SAAS,CAACE,OAAM,IAAK,2BAA2B;MACtE;IACF,CAAC;IAED;IACA,MAAMgH,qBAAqBA,CAAA,EAAG;MAC5B,IAAI,CAAC,IAAI,CAACxM,uBAAuB,CAACC,eAAc,IAAK,CAAC,IAAI,CAAC7C,cAAc,EAAE;QACzE,IAAI,CAACsI,SAAS,CAAC,OAAO,EAAE,kCAAkC,EAAE,OAAO,CAAC;QACpE;MACF;MAEA,MAAM+G,QAAO,GAAIC,UAAU,CAAC,IAAI,CAACtP,cAAc,CAACuP,SAAS,CAAC;MAC1D,MAAMC,cAAa,GAAIF,UAAU,CAAC,IAAI,CAAC1M,uBAAuB,CAACC,eAAe,CAAC;MAE/E,IAAI2M,cAAa,GAAIH,QAAQ,EAAE;QAC7B,IAAI,CAAC/G,SAAS,CAAC,OAAO,EAAE,mCAAmC,IAAI,CAACmH,cAAc,CAACJ,QAAQ,CAAC,EAAE,EAAE,OAAO,CAAC;QACpG;MACF;MAEA,IAAI,CAACzM,uBAAuB,CAACrD,OAAM,GAAI,IAAI;MAC3C,IAAI,CAACqD,uBAAuB,CAACJ,KAAI,GAAI,EAAE;MAEvC,IAAI;QACF,MAAMkN,WAAU,GAAI;UAClB7M,eAAe,EAAE2M,cAAc;UAC/BG,iBAAiB,EAAE,IAAI,CAAC3P,cAAc,CAAC2P,iBAAgB,IAAK,CAAC;UAAE;UAC/D7M,cAAc,EAAE,IAAI,CAACF,uBAAuB,CAACE;QAC/C,CAAC;QAED,MAAM2E,QAAO,GAAI,MAAMxI,oBAAoB,CAACmQ,qBAAqB,CAAC,IAAI,CAACpP,cAAc,CAACoK,EAAE,EAAEsF,WAAW,CAAC;QAEtG,IAAIjI,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAACwD,qBAAqB,CAAC,CAAC;UAClC,MAAM,IAAI,CAACnD,YAAY,CAAC,CAAC;;UAEzB;UACA,IAAI,CAACpF,uBAAsB,GAAI;YAC7BC,eAAe,EAAE,EAAE;YACnBC,cAAc,EAAE,EAAE;YAClBvD,OAAO,EAAE,KAAK;YACdiD,KAAK,EAAE;UACT,CAAC;UAED,IAAI,CAAC8F,SAAS,CAAC,SAAS,EAAE,+BAA+B,EAAE,SAAS,CAAC;QACvE;MACF,EAAE,OAAO9F,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAM0F,SAAQ,GAAIjJ,oBAAoB,CAACkJ,UAAU,CAAC3F,KAAK,CAAC;QACxD,IAAI,CAACI,uBAAuB,CAACJ,KAAI,GAAI0F,SAAS,CAACE,OAAM,IAAK,0BAA0B;QACpF,IAAI,CAACE,SAAS,CAAC,OAAO,EAAEJ,SAAS,CAACE,OAAM,IAAK,0BAA0B,EAAE,OAAO,CAAC;MACnF,UAAU;QACR,IAAI,CAACxF,uBAAuB,CAACrD,OAAM,GAAI,KAAK;MAC9C;IACF,CAAC;IAED;IACA,MAAMqQ,cAAcA,CAAA,EAAG;MACrB,IAAI,CAAC,IAAI,CAAC9B,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC9N,cAAc,EAAE;QACrD,IAAI,CAACsI,SAAS,CAAC,OAAO,EAAE,oCAAoC,EAAE,OAAO,CAAC;QACtE;MACF;MAEA,IAAI,CAACvF,kBAAkB,CAACxD,OAAM,GAAI,IAAI;MACtC,IAAI,CAACwD,kBAAkB,CAACP,KAAI,GAAI,EAAE;MAElC,IAAI;QACF,MAAMqN,YAAW,GAAI;UACnB7M,cAAc,EAAE,IAAI,CAACD,kBAAkB,CAACC,cAAc;UACtDC,oBAAoB,EAAE,IAAI,CAACF,kBAAkB,CAACE,oBAAoB;UAClEC,kBAAkB,EAAE,IAAI,CAACH,kBAAkB,CAACG;QAC9C,CAAC;QAED,MAAMuE,QAAO,GAAI,MAAMxI,oBAAoB,CAAC2Q,cAAc,CAAC,IAAI,CAAC5P,cAAc,CAACoK,EAAE,EAAEyF,YAAY,CAAC;QAEhG,IAAIpI,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAACwD,qBAAqB,CAAC,CAAC;UAClC,MAAM,IAAI,CAACnD,YAAY,CAAC,CAAC;;UAEzB;UACA,IAAI,CAACjF,kBAAiB,GAAI;YACxBC,cAAc,EAAE,EAAE;YAClBC,oBAAoB,EAAE,EAAE;YACxBC,kBAAkB,EAAE,EAAE;YACtB3D,OAAO,EAAE,KAAK;YACdiD,KAAK,EAAE;UACT,CAAC;UAED,IAAI,CAAC8F,SAAS,CAAC,SAAS,EAAE,+BAA+B,EAAE,SAAS,CAAC;QACvE;MACF,EAAE,OAAO9F,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAM0F,SAAQ,GAAIjJ,oBAAoB,CAACkJ,UAAU,CAAC3F,KAAK,CAAC;QACxD,IAAI,CAACO,kBAAkB,CAACP,KAAI,GAAI0F,SAAS,CAACE,OAAM,IAAK,2BAA2B;QAChF,IAAI,CAACE,SAAS,CAAC,OAAO,EAAEJ,SAAS,CAACE,OAAM,IAAK,2BAA2B,EAAE,OAAO,CAAC;MACpF,UAAU;QACR,IAAI,CAACrF,kBAAkB,CAACxD,OAAM,GAAI,KAAK;MACzC;IACF,CAAC;IAED;IACAqN,YAAYA,CAACpM,MAAM,EAAE;MACnB,OAAOvB,oBAAoB,CAAC2N,YAAY,CAACpM,MAAM,CAAC;IAClD,CAAC;IAEDsP,cAAcA,CAACtP,MAAM,EAAE;MACrB,OAAOvB,oBAAoB,CAAC6Q,cAAc,CAACtP,MAAM,CAAC;IACpD,CAAC;IAEDuP,UAAUA,CAACC,UAAU,EAAE;MACrBrK,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEoK,UAAU,CAAC;MACtD,IAAI,CAACA,UAAU,EAAE;QACfrK,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;QAC/D,OAAO,IAAI;MACb;MACA,MAAMqK,IAAG,GAAI,IAAIxC,IAAI,CAACuC,UAAU,CAAC;MACjC,MAAME,SAAQ,GAAID,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACjDC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;MACF3K,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEsK,SAAS,CAAC;MAChD,OAAOA,SAAS;IAClB,CAAC;IAEDT,cAAcA,CAACc,MAAM,EAAE;MACrB,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;QACpCC,KAAK,EAAE,UAAU;QACjBC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAK,IAAK,CAAC,CAAC;IACxB,CAAC;IAEDM,cAAcA,CAACb,UAAU,EAAE;MACzB,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;MAC7B,OAAO,IAAIvC,IAAI,CAACuC,UAAU,CAAC,CAACc,cAAc,CAAC,OAAO,EAAE;QAClDV,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdS,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,iBAAiBA,CAACzE,OAAO,EAAE;MACzB,IAAI,CAACA,OAAO,EAAE,OAAO,cAAc;MACnC,MAAM0E,KAAI,GAAI,CACZ1E,OAAO,CAAC2E,iBAAiB,EACzB3E,OAAO,CAAC4E,kBAAkB,EAC1B5E,OAAO,CAAC6E,gBAAgB,EACxB7E,OAAO,CAAC8E,aAAY,CACrB,CAACtD,MAAM,CAACuD,OAAO,CAAC;MACjB,OAAOL,KAAK,CAACjH,MAAK,GAAI,IAAIiH,KAAK,CAACM,IAAI,CAAC,GAAG,IAAIhF,OAAO,CAACiF,WAAU,IAAK,cAAc;IACnF,CAAC;IAEDC,oBAAoBA,CAAClF,OAAO,EAAE;MAC5B,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;MACzB,MAAM0E,KAAI,GAAI,CACZ1E,OAAO,CAACmF,mBAAmB,EAC3BnF,OAAO,CAACoF,aAAa,EACrBpF,OAAO,CAACqF,kBAAkB,EAC1BrF,OAAO,CAACsF,eAAe,EACvBtF,OAAO,CAACuF,wBAAuB,IAAKvF,OAAO,CAACwF,WAAW,EACvDxF,OAAO,CAACyF,eAAc,CACvB,CAACjE,MAAM,CAACuD,OAAO,CAAC;MACjB,OAAOL,KAAK,CAACjH,MAAK,GAAI,IAAIiH,KAAK,CAACM,IAAI,CAAC,IAAI,IAAKhF,OAAO,CAAC0F,cAAa,IAAK,IAAK;IAC/E,CAAC;IAEDC,YAAYA,CAACC,MAAM,EAAE;MACnB,IAAI,CAACA,MAAM,EAAE;QACX,OAAO,IAAI;MACb;MACA,OAAOA,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,IAAIF,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC;IACzD,CAAC;IAEDC,kBAAkBA,CAACnG,QAAQ,EAAE;MAC3B,MAAMiB,QAAO,GAAI;QACf,CAAC,EAAE,QAAQ;QACX,CAAC,EAAE,SAAS;QACZ,CAAC,EAAE,UAAU;QACb,CAAC,EAAE,SAAS;QACZ,CAAC,EAAE;MACL,CAAC;MACD,OAAOA,QAAQ,CAACjB,QAAQ,KAAK,IAAI;IACnC,CAAC;IAEDoG,mBAAmBA,CAACjG,OAAO,EAAE;MAC3B,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;MACzB,MAAMkG,KAAI,GAAIlG,OAAO,CAAC5B,yBAAyB;MAC/C,MAAM+H,MAAK,GAAInG,OAAO,CAAC3B,0BAA0B;MAEjD,IAAI,CAAC6H,KAAI,IAAK,CAACC,MAAM,EAAE,OAAO,IAAI,EAAE;;MAEpC,MAAMzB,KAAI,GAAI,EAAE;MAChB,IAAIwB,KAAK,EAAExB,KAAK,CAACxN,IAAI,CAAC,GAAGgP,KAAK,QAAQA,KAAI,GAAI,IAAI,GAAE,GAAI,EAAE,EAAE,CAAC;MAC7D,IAAIC,MAAM,EAAEzB,KAAK,CAACxN,IAAI,CAAC,GAAGiP,MAAM,SAASA,MAAK,GAAI,IAAI,GAAE,GAAI,EAAE,EAAE,CAAC;MAEjE,OAAOzB,KAAK,CAACM,IAAI,CAAC,OAAO,CAAC;IAC5B,CAAC;IAEDoB,UAAUA,CAAC5C,UAAU,EAAE;MACrB,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;MAC1B,MAAMC,IAAG,GAAI,IAAIxC,IAAI,CAACuC,UAAU,CAAC;MACjC,OAAOC,IAAI,CAAC4C,kBAAkB,CAAC,OAAO,EAAE;QACtC9B,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjB8B,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IAED;IACA,MAAMjP,0BAA0BA,CAAA,EAAG;MACjC8B,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MAEhE,IAAI;QACF;QACA,MAAMzG,mBAAmB,CAAC4T,IAAI,CAAC,OAAO,CAAC;;QAEvC;QACA5T,mBAAmB,CAAC6T,EAAE,CAAC,cAAc,EAAE,IAAI,CAACC,0BAA0B,CAAC;QACvE9T,mBAAmB,CAAC6T,EAAE,CAAC,wBAAwB,EAAE,IAAI,CAACE,kBAAkB,CAAC;QACzE/T,mBAAmB,CAAC6T,EAAE,CAAC,aAAa,EAAE,IAAI,CAACG,gBAAgB,CAAC;;QAE5D;QACA,IAAI,IAAI,CAAC/P,kBAAkB,EAAE;UAC3B,IAAI,CAACgQ,gBAAgB,CAAC,CAAC;QACzB;MACF,EAAE,OAAO5Q,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAClE;IACF,CAAC;IAED0B,uBAAuBA,CAAA,EAAG;MACxByB,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;;MAE/D;MACAzG,mBAAmB,CAACkU,GAAG,CAAC,cAAc,EAAE,IAAI,CAACJ,0BAA0B,CAAC;MACxE9T,mBAAmB,CAACkU,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACH,kBAAkB,CAAC;MAC1E/T,mBAAmB,CAACkU,GAAG,CAAC,aAAa,EAAE,IAAI,CAACF,gBAAgB,CAAC;;MAE7D;MACAhU,mBAAmB,CAACmU,OAAO,CAAC,CAAC;;MAE7B;MACA,IAAI,CAACC,eAAe,CAAC,CAAC;IACxB,CAAC;IAEDH,gBAAgBA,CAAA,EAAG;MACjB,IAAI,IAAI,CAACjQ,eAAe,EAAE;QACxBqQ,aAAa,CAAC,IAAI,CAACrQ,eAAe,CAAC;MACrC;MAEA,IAAI,CAACA,eAAc,GAAIsQ,WAAW,CAAC,MAAM;QACvC,IAAI,IAAI,CAACrQ,kBAAiB,IAAK,CAAC,IAAI,CAAC7D,OAAO,EAAE;UAC5CoG,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/C,IAAI,CAAC8N,mBAAmB,CAAC,CAAC;QAC5B;MACF,CAAC,EAAE,IAAI,CAACrQ,WAAW,CAAC;MAEpBsC,OAAO,CAACC,GAAG,CAAC,6BAA6B,IAAI,CAACvC,WAAU,GAAI,IAAI,YAAY,CAAC;IAC/E,CAAC;IAEDkQ,eAAeA,CAAA,EAAG;MAChB,IAAI,IAAI,CAACpQ,eAAe,EAAE;QACxBqQ,aAAa,CAAC,IAAI,CAACrQ,eAAe,CAAC;QACnC,IAAI,CAACA,eAAc,GAAI,IAAI;QAC3BwC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACrC;IACF,CAAC;IAED+N,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACvQ,kBAAiB,GAAI,CAAC,IAAI,CAACA,kBAAkB;MAElD,IAAI,IAAI,CAACA,kBAAkB,EAAE;QAC3B,IAAI,CAACgQ,gBAAgB,CAAC,CAAC;MACzB,OAAO;QACL,IAAI,CAACG,eAAe,CAAC,CAAC;MACxB;IACF,CAAC;IAED,MAAMG,mBAAmBA,CAAA,EAAG;MAC1B,IAAI;QACF,IAAI,CAACpQ,WAAU,GAAI,IAAImK,IAAI,CAAC,CAAC;;QAE7B;QACA,MAAM,IAAI,CAACzF,YAAY,CAAC,CAAC;;QAEzB;QACA,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;;QAE/B;QACA,IAAI,IAAI,CAAC3G,kBAAiB,IAAK,IAAI,CAACtB,cAAc,EAAE;UAClD,MAAM,IAAI,CAACmL,qBAAqB,CAAC,CAAC;QACpC;QAEAxF,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACrD,EAAE,OAAOpD,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;IACF,CAAC;IAEDyQ,0BAA0BA,CAACW,YAAY,EAAE;MACvCjO,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEgO,YAAY,CAAC;;MAE7D;MACA,QAAQA,YAAY,CAACC,IAAI;QACvB,KAAK,wBAAwB;UAC3B,IAAI,CAACX,kBAAkB,CAACU,YAAY,CAACtU,IAAI,CAAC;UAC1C;QACF,KAAK,aAAa;UAChB,IAAI,CAAC6T,gBAAgB,CAACS,YAAY,CAACtU,IAAI,CAAC;UACxC;QACF,KAAK,iBAAiB;UACpB,IAAI,CAACwU,mBAAmB,CAACF,YAAY,CAACtU,IAAI,CAAC;UAC3C;QACF,KAAK,qBAAqB;QAC1B,KAAK,WAAW;UACd;UACA;QACF;UACE;UACA,IAAI,CAAC,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAACkF,QAAQ,CAACoP,YAAY,CAACC,IAAI,CAAC,EAAE;YACrElO,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgO,YAAY,CAACC,IAAI,CAAC;UAChE;MACJ;IACF,CAAC;IAEDX,kBAAkBA,CAAC5T,IAAI,EAAE;MACvBqG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEtG,IAAI,CAAC;;MAE5C;MACA,MAAMyU,YAAW,GAAI,IAAI,CAACjU,QAAQ,CAACkU,SAAS,CAACC,GAAE,IAAKA,GAAG,CAAC7J,EAAC,KAAM9K,IAAI,CAAC4U,UAAU,CAAC;MAC/E,IAAIH,YAAW,KAAM,CAAC,CAAC,EAAE;QACvB;QACA,IAAI,CAACL,mBAAmB,CAAC,CAAC;MAC5B;;MAEA;MACA,IAAI,CAACpL,SAAS,CAAC,wBAAwB,EAAE,YAAYhJ,IAAI,CAAC4U,UAAU,sBAAsB5U,IAAI,CAAC6U,UAAU,EAAE,EAAE,MAAM,CAAC;IACtH,CAAC;IAEDhB,gBAAgBA,CAAC7T,IAAI,EAAE;MACrBqG,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEtG,IAAI,CAAC;;MAE1C;MACA,IAAI,CAACoU,mBAAmB,CAAC,CAAC;;MAE1B;MACA,IAAI,CAACpL,SAAS,CAAC,aAAa,EAAE,OAAOhJ,IAAI,CAACmB,aAAa,mBAAmB,EAAE,SAAS,CAAC;IACxF,CAAC;IAEDqT,mBAAmBA,CAACxU,IAAI,EAAE;MACxBqG,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEtG,IAAI,CAAC;;MAErC;MACA,IAAI,IAAI,CAACU,cAAa,IAAK,IAAI,CAACA,cAAc,CAACoK,EAAC,KAAM9K,IAAI,CAAC4U,UAAU,EAAE;QACrE,IAAI,CAAC/I,qBAAqB,CAAC,CAAC;MAC9B;;MAEA;MACA,IAAI,CAACuI,mBAAmB,CAAC,CAAC;IAC5B,CAAC;IAEDpL,SAASA,CAAC8L,KAAK,EAAEhM,OAAO,EAAEyL,IAAG,GAAI,MAAM,EAAE;MACvC;MACAlO,OAAO,CAACC,GAAG,CAAC,IAAIiO,IAAI,CAACvB,WAAW,CAAC,CAAC,KAAK8B,KAAK,KAAKhM,OAAO,EAAE,CAAC;;MAE3D;MACA,MAAMiM,KAAI,GAAI9N,QAAQ,CAAC+N,aAAa,CAAC,KAAK,CAAC;MAC3CD,KAAK,CAACE,SAAQ,GAAI,4BAA4BV,IAAI,EAAE;MACpDQ,KAAK,CAACG,SAAQ,GAAI;;oBAEJJ,KAAK;;;kCAGShM,OAAO;OAClC;;MAED;MACA,IAAI,CAAC7B,QAAQ,CAACkO,cAAc,CAAC,cAAc,CAAC,EAAE;QAC5C,MAAMC,MAAK,GAAInO,QAAQ,CAAC+N,aAAa,CAAC,OAAO,CAAC;QAC9CI,MAAM,CAACtK,EAAC,GAAI,cAAc;QAC1BsK,MAAM,CAACC,WAAU,GAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAsCpB;QACDpO,QAAQ,CAACqO,IAAI,CAACC,WAAW,CAACH,MAAM,CAAC;MACnC;;MAEA;MACAnO,QAAQ,CAACuO,IAAI,CAACD,WAAW,CAACR,KAAK,CAAC;;MAEhC;MACArO,UAAU,CAAC,MAAM;QACf,IAAIqO,KAAK,CAACU,aAAa,EAAE;UACvBV,KAAK,CAAC3D,KAAK,CAACsE,SAAQ,GAAI,2BAA2B;UACnDhP,UAAU,CAAC,MAAMqO,KAAK,CAACvN,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC;QACvC;MACF,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IAED;IACAmO,0BAA0BA,CAACpB,IAAI,EAAE;MAC/B,MAAMqB,YAAW,GAAI;QACnB,eAAe,EAAE,eAAe;QAChC,oBAAoB,EAAE,oBAAoB;QAC1C,QAAQ,EAAE,oCAAoC;QAC9C,mBAAmB,EAAE,mBAAmB;QACxC,sBAAsB,EAAE,sBAAsB;QAC9C,OAAO,EAAE;MACX,CAAC;MACD,OAAOA,YAAY,CAACrB,IAAI,KAAKA,IAAI;IACnC,CAAC;IAEDsB,WAAWA,CAACC,QAAQ,EAAE;MACpB,OAAOA,QAAO,KACZA,QAAQ,CAACC,UAAU,CAAC,QAAQ,KAC5B,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC7Q,QAAQ,CAAC4Q,QAAQ,EACtF;IACH,CAAC;IAEDE,SAASA,CAACF,QAAQ,EAAE;MAClB,OAAOA,QAAO,KAAM,iBAAiB;IACvC,CAAC;IAED,MAAMnK,gBAAgBA,CAACjE,SAAS,EAAE;MAChC;MACA,MAAMuO,eAAc,GAAIvO,SAAS,CAACgH,MAAM,CAACwH,GAAE,IACzC,IAAI,CAACL,WAAW,CAACK,GAAG,CAACC,SAAS,KAC9B,CAAC,IAAI,CAAC5T,YAAY,CAAC2T,GAAG,CAACpL,EAAE,KACzB,CAAC,IAAI,CAACtI,gBAAgB,CAAC4T,GAAG,CAACF,GAAG,CAACpL,EAAE,KACjC,CAAC,IAAI,CAACpI,eAAe,CAAC0T,GAAG,CAACF,GAAG,CAACpL,EAAE,CAClC,CAAC;MAED,IAAImL,eAAe,CAACtL,MAAK,KAAM,CAAC,EAAE;;MAElC;MACA,MAAM0L,gBAAe,GAAI,CAAC;MAC1B,MAAMC,MAAK,GAAI,IAAI,CAACC,UAAU,CAACN,eAAe,EAAEI,gBAAgB,CAAC;MAEjE,KAAK,MAAMG,KAAI,IAAKF,MAAM,EAAE;QAC1B,MAAM/N,OAAO,CAACkO,UAAU,CACtBD,KAAK,CAAC5L,GAAG,CAAC3D,QAAO,IAAK,IAAI,CAACyP,kBAAkB,CAACzP,QAAQ,CAAC,CACzD,CAAC;MACH;IACF,CAAC;IAED,MAAMyP,kBAAkBA,CAACzP,QAAQ,EAAE0P,UAAS,GAAI,KAAK,EAAE;MACrD,MAAMC,KAAI,GAAI3P,QAAQ,CAAC6D,EAAE;MAEzB,IAAI;QACF;QACA,IAAI,CAACtI,gBAAgB,CAAC+E,GAAG,CAACqP,KAAK,CAAC;QAChC,IAAID,UAAU,EAAE,IAAI,CAAChU,mBAAkB,GAAI,IAAI;;QAE/C;QACA,MAAMwF,QAAO,GAAI,MAAMvI,GAAG,CAACiX,GAAG,CAAC,mBAAmBD,KAAK,EAAE,EAAE;UACzDE,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE,KAAK;UAAE;UAChBC,kBAAkB,EAAGC,aAAa,IAAK;YACrC;YACA,IAAIA,aAAa,CAACC,gBAAgB,EAAE;cAClC,MAAMC,gBAAe,GAAIC,IAAI,CAACC,KAAK,CAAEJ,aAAa,CAACK,MAAK,GAAI,GAAG,GAAIL,aAAa,CAACxV,KAAK,CAAC;cACvF4E,OAAO,CAACC,GAAG,CAAC,WAAWsQ,KAAK,KAAKO,gBAAgB,GAAG,CAAC;YACvD;UACF;QACF,CAAC,CAAC;;QAEF;QACA,IAAI,CAAChP,QAAQ,CAACnI,IAAG,IAAKmI,QAAQ,CAACnI,IAAI,CAACuX,IAAG,KAAM,CAAC,EAAE;UAC9C,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;QAC5C;;QAEA;QACA,MAAMC,IAAG,GAAItP,QAAQ,CAACnI,IAAI;QAC1B,MAAM0X,aAAY,GAAI,MAAM,IAAI,CAACC,iBAAiB,CAACF,IAAI,EAAExQ,QAAQ,CAACkP,SAAS,EAAEQ,UAAU,CAAC;;QAExF;QACA,MAAM,IAAI,CAACiB,qBAAqB,CAAChB,KAAK,EAAEc,aAAa,CAAC;;QAEtD;QACA,IAAI,CAAChV,eAAe,CAACmV,MAAM,CAACjB,KAAK,CAAC;QAClC,IAAID,UAAU,EAAE,IAAI,CAAC/T,eAAc,GAAI,KAAK;MAE9C,EAAE,OAAOM,KAAK,EAAE;QACdmD,OAAO,CAACyR,IAAI,CAAC,2BAA2BlB,KAAK,GAAG,EAAE1T,KAAK,CAAC4F,OAAO,CAAC;QAChE,IAAI,CAACpG,eAAe,CAAC6E,GAAG,CAACqP,KAAK,CAAC;QAC/B,IAAID,UAAU,EAAE,IAAI,CAAC/T,eAAc,GAAI,IAAI;;QAE3C;QACA,IAAIM,KAAK,CAAC6U,IAAG,KAAM,eAAc,IAAK7U,KAAK,CAAC6U,IAAG,KAAM,cAAc,EAAE;UACnErR,UAAU,CAAC,MAAM;YACf,IAAI,CAAChE,eAAe,CAACmV,MAAM,CAACjB,KAAK,CAAC;UACpC,CAAC,EAAE,KAAK,CAAC,EAAE;QACb;MACF,UAAU;QACR;QACA,IAAI,CAACpU,gBAAgB,CAACqV,MAAM,CAACjB,KAAK,CAAC;QACnC,IAAID,UAAU,EAAE,IAAI,CAAChU,mBAAkB,GAAI,KAAK;MAClD;IACF,CAAC;IAED4T,UAAUA,CAACyB,KAAK,EAAET,IAAI,EAAE;MACtB,MAAMjB,MAAK,GAAI,EAAE;MACjB,KAAK,IAAI2B,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAID,KAAK,CAACrN,MAAM,EAAEsN,CAAA,IAAKV,IAAI,EAAE;QAC3CjB,MAAM,CAAClS,IAAI,CAAC4T,KAAK,CAAC/E,KAAK,CAACgF,CAAC,EAAEA,CAAA,GAAIV,IAAI,CAAC,CAAC;MACvC;MACA,OAAOjB,MAAM;IACf,CAAC;IAED,MAAM4B,cAAcA,CAACjR,QAAQ,EAAE;MAC7B;MACA;MACA,IAAI,IAAI,CAAC1E,YAAY,CAAC0E,QAAQ,CAAC6D,EAAE,CAAC,EAAE;QAClC,OAAO,IAAI,CAACvI,YAAY,CAAC0E,QAAQ,CAAC6D,EAAE,CAAC;MACvC;MACA,OAAO,IAAI;IACb,CAAC;IAEDqN,cAAcA,CAACC,KAAK,EAAE;MACpB,IAAI,CAACA,KAAK,EAAE,OAAO,KAAK;MACxB,MAAMC,CAAA,GAAI,IAAI;MACd,MAAMC,KAAI,GAAI,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MACrC,MAAML,CAAA,GAAIb,IAAI,CAACmB,KAAK,CAACnB,IAAI,CAAC9Q,GAAG,CAAC8R,KAAK,IAAIhB,IAAI,CAAC9Q,GAAG,CAAC+R,CAAC,CAAC,CAAC;MACnD,OAAOrI,UAAU,CAAC,CAACoI,KAAI,GAAIhB,IAAI,CAACoB,GAAG,CAACH,CAAC,EAAEJ,CAAC,CAAC,EAAEQ,OAAO,CAAC,CAAC,CAAC,IAAI,GAAE,GAAIH,KAAK,CAACL,CAAC,CAAC;IACzE,CAAC;IAED,MAAMS,cAAcA,CAACzR,QAAQ,EAAE;MAC7B;MACA,IAAI,IAAI,CAACtE,mBAAmB,EAAE;;MAE9B;MACA,IAAI,IAAI,CAACD,eAAe,CAAC0T,GAAG,CAACnP,QAAQ,CAAC6D,EAAE,CAAC,EAAE;QACzC;MACF;;MAEA;MACA,IAAI,CAACzI,aAAY,GAAI4E,QAAQ;MAC7B,IAAI,CAAC7E,cAAa,GAAI,IAAI;MAC1B,IAAI,CAACQ,eAAc,GAAI,KAAK;;MAE5B;MACA,MAAM,IAAI,CAAC+V,SAAS,CAAC,CAAC;;MAEtB;MACA,IAAI,CAAC,IAAI,CAACpW,YAAY,CAAC0E,QAAQ,CAAC6D,EAAE,KAAK,CAAC,IAAI,CAACtI,gBAAgB,CAAC4T,GAAG,CAACnP,QAAQ,CAAC6D,EAAE,CAAC,EAAE;QAC9E,MAAM,IAAI,CAAC4L,kBAAkB,CAACzP,QAAQ,EAAE,IAAI,CAAC;MAC/C;IACF,CAAC;IAED,MAAM2R,iBAAiBA,CAAC3R,QAAQ,EAAE;MAChC;MACA,IAAI,CAACvE,eAAe,CAACmV,MAAM,CAAC5Q,QAAQ,CAAC6D,EAAE,CAAC;MACxC,IAAI,CAAClI,eAAc,GAAI,KAAK;MAC5B,MAAM,IAAI,CAAC8T,kBAAkB,CAACzP,QAAQ,EAAE,IAAI,CAAC;IAC/C,CAAC;IAED4R,gBAAgBA,CAAA,EAAG;MACjB;MACA,IAAI,CAAClW,mBAAkB,GAAI,KAAK;IAClC,CAAC;IAEDkC,mBAAmBA,CAAA,EAAG;MACpB;MACAmG,MAAM,CAAC8N,MAAM,CAAC,IAAI,CAACvW,YAAY,CAAC,CAACiJ,OAAO,CAACuN,GAAE,IAAK;QAC9C,IAAIA,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACF,GAAG,CAAC;MACnC,CAAC,CAAC;;MAEF;MACA,IAAI,CAACxW,YAAW,GAAI,CAAC,CAAC;MACtB,IAAI,CAACC,gBAAgB,CAAC0W,KAAK,CAAC,CAAC;MAC7B,IAAI,CAACxW,eAAe,CAACwW,KAAK,CAAC,CAAC;IAC9B,CAAC;IAEDC,YAAYA,CAAClS,QAAQ,EAAE;MACrB;MACA,IAAI,CAAC,IAAI,CAAC1E,YAAY,CAAC0E,QAAQ,CAAC6D,EAAE,KAC9B,CAAC,IAAI,CAACtI,gBAAgB,CAAC4T,GAAG,CAACnP,QAAQ,CAAC6D,EAAE,KACtC,CAAC,IAAI,CAACpI,eAAe,CAAC0T,GAAG,CAACnP,QAAQ,CAAC6D,EAAE,CAAC,EAAE;QAC1C,IAAI,CAAC4L,kBAAkB,CAACzP,QAAQ,EAAE,KAAK,CAAC;MAC1C;IACF,CAAC;IAED,MAAM0Q,iBAAiBA,CAACF,IAAI,EAAE3B,QAAQ,EAAEa,UAAS,GAAI,KAAK,EAAE;MAC1D;MACA,MAAMyC,QAAO,GAAIzC,UAAS,GAAI,IAAI,IAAG,GAAI,IAAG,GAAI,IAAI,IAAG,GAAI,IAAI,EAAE;;MAEjE,IAAIc,IAAI,CAACF,IAAG,IAAK6B,QAAQ,EAAE;QACzB,OAAO3B,IAAI,EAAE;MACf;MAEA,IAAI;QACF;QACA,MAAM4B,GAAE,GAAI,IAAIC,KAAK,CAAC,CAAC;QACvB,MAAMC,MAAK,GAAItS,QAAQ,CAAC+N,aAAa,CAAC,QAAQ,CAAC;QAC/C,MAAMwE,GAAE,GAAID,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;QAEnC,OAAO,IAAIlR,OAAO,CAAEmR,OAAO,IAAK;UAC9BL,GAAG,CAACM,MAAK,GAAI,MAAM;YACjB;YACA,MAAMC,YAAW,GAAIjD,UAAS,GAAI,IAAG,GAAI,GAAG;YAC5C,IAAI;cAAEkD,KAAK;cAAEC;YAAO,IAAIT,GAAG;YAE3B,IAAIQ,KAAI,GAAIC,MAAK,IAAKD,KAAI,GAAID,YAAY,EAAE;cAC1CE,MAAK,GAAKA,MAAK,GAAIF,YAAY,GAAIC,KAAK;cACxCA,KAAI,GAAID,YAAY;YACtB,OAAO,IAAIE,MAAK,GAAIF,YAAY,EAAE;cAChCC,KAAI,GAAKA,KAAI,GAAID,YAAY,GAAIE,MAAM;cACvCA,MAAK,GAAIF,YAAY;YACvB;;YAEA;YACAL,MAAM,CAACM,KAAI,GAAIA,KAAK;YACpBN,MAAM,CAACO,MAAK,GAAIA,MAAM;YACtBN,GAAG,CAACO,SAAS,CAACV,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEQ,KAAK,EAAEC,MAAM,CAAC;;YAEvC;YACAP,MAAM,CAACS,MAAM,CACVtC,aAAa,IAAK;cACjBgC,OAAO,CAAChC,aAAY,IAAKD,IAAI,CAAC,EAAE;YAClC,CAAC,EACD3B,QAAQ,EACR,IAAG,CAAE;YACP,CAAC;UACH,CAAC;UAEDuD,GAAG,CAACY,OAAM,GAAI,MAAMP,OAAO,CAACjC,IAAI,CAAC,EAAE;UACnC4B,GAAG,CAACa,GAAE,GAAIlB,GAAG,CAACmB,eAAe,CAAC1C,IAAI,CAAC;QACrC,CAAC,CAAC;MACJ,EAAE,OAAOvU,KAAK,EAAE;QACdmD,OAAO,CAACyR,IAAI,CAAC,4BAA4B,EAAE5U,KAAK,CAAC;QACjD,OAAOuU,IAAI,EAAE;MACf;IACF,CAAC;IAED,MAAMG,qBAAqBA,CAAChB,KAAK,EAAEa,IAAI,EAAE;MACvC,OAAO,IAAIlP,OAAO,CAAEmR,OAAO,IAAK;QAC9B,MAAMU,SAAQ,GAAIA,CAAA,KAAM;UACtB,IAAI,CAAC7X,YAAY,CAACqU,KAAK,IAAIoC,GAAG,CAACmB,eAAe,CAAC1C,IAAI,CAAC;UACpDiC,OAAO,CAAC,CAAC;QACX,CAAC;;QAED;QACA,IAAIhV,MAAM,CAAC2V,mBAAmB,EAAE;UAC9B3V,MAAM,CAAC2V,mBAAmB,CAACD,SAAS,EAAE;YAAErD,OAAO,EAAE;UAAK,CAAC,CAAC;QAC1D,OAAO;UACLrQ,UAAU,CAAC0T,SAAS,EAAE,CAAC,CAAC;QAC1B;MACF,CAAC,CAAC;IACJ,CAAC;IAEDE,eAAeA,CAAA,EAAG;MAChB;MACA,IAAI,IAAI,CAAC3X,mBAAmB,EAAE;MAE9B,IAAI,CAACP,cAAa,GAAI,KAAK;MAC3B,IAAI,CAACC,aAAY,GAAI,IAAI;MACzB,IAAI,CAACM,mBAAkB,GAAI,KAAK;MAChC,IAAI,CAACC,eAAc,GAAI,KAAK;IAC9B,CAAC;IAED,MAAM2X,gBAAgBA,CAACC,YAAY,EAAE;MACnC,IAAI;QACF;QACA,MAAMrS,QAAO,GAAI,MAAMvI,GAAG,CAACiX,GAAG,CAAC,uBAAuB2D,YAAY,CAAC1P,EAAE,EAAE,EAAE;UACvEgM,YAAY,EAAE;QAChB,CAAC,CAAC;;QAEF;QACA,MAAMW,IAAG,GAAI,IAAIgD,IAAI,CAAC,CAACtS,QAAQ,CAACnI,IAAI,CAAC,EAAE;UAAEuU,IAAI,EAAEiG,YAAY,CAACrE;QAAU,CAAC,CAAC;QACxE,MAAM4C,GAAE,GAAIC,GAAG,CAACmB,eAAe,CAAC1C,IAAI,CAAC;QACrC,MAAMiD,IAAG,GAAIzT,QAAQ,CAAC+N,aAAa,CAAC,GAAG,CAAC;QACxC0F,IAAI,CAACC,IAAG,GAAI5B,GAAG;QACf2B,IAAI,CAACE,QAAO,GAAIJ,YAAY,CAACK,aAAa;QAC1C5T,QAAQ,CAACuO,IAAI,CAACD,WAAW,CAACmF,IAAI,CAAC;QAC/BA,IAAI,CAACI,KAAK,CAAC,CAAC;QACZ7T,QAAQ,CAACuO,IAAI,CAACuF,WAAW,CAACL,IAAI,CAAC;QAC/B1B,GAAG,CAACC,eAAe,CAACF,GAAG,CAAC;MAC1B,EAAE,OAAO7V,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,IAAI,CAAC8F,SAAS,CAAC,OAAO,EAAE,6BAA6B,EAAE,OAAO,CAAC;MACjE;IACF,CAAC;IAEDgS,gBAAgBA,CAACC,KAAK,EAAE;MACtB5U,OAAO,CAACnD,KAAK,CAAC,uBAAuB,EAAE+X,KAAK,CAACC,MAAM,CAAChB,GAAG,CAAC;MACxD;MACAe,KAAK,CAACC,MAAM,CAAC9J,KAAK,CAAC+J,OAAM,GAAI,MAAM;;MAEnC;MACA,MAAMC,QAAO,GAAInU,QAAQ,CAAC+N,aAAa,CAAC,KAAK,CAAC;MAC9CoG,QAAQ,CAACnG,SAAQ,GAAI,4BAA4B;MACjDmG,QAAQ,CAAClG,SAAQ,GAAI,qEAAqE;MAC1F+F,KAAK,CAACC,MAAM,CAACG,UAAU,CAAC9F,WAAW,CAAC6F,QAAQ,CAAC;IAC/C;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}