{"name": "bosfdr", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@vee-validate/rules": "^4.15.1", "axios": "^1.10.0", "bootstrap": "^5.3.6", "chart.js": "^4.5.0", "core-js": "^3.8.3", "vee-validate": "^4.15.1", "vue": "^3.2.13", "vue-chartjs": "^5.3.2", "vue-router": "^4.5.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@tailwindcss/postcss": "^4.1.11", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "autoprefixer": "^10.4.21", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "postcss": "^8.5.6", "tailwindcss": "^4.1.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}