<!-- src/components/AdminDashboard.vue -->
<template>
  <div class="dashboard-wrapper flex bg-[#222b38] text-white font-sans min-h-screen">
    <!-- Sidebar -->
    <aside class="flex flex-col bg-[#2a3244] w-20 sm:w-56 p-4 space-y-8">
      <div class="flex flex-col items-center sm:items-start space-y-6">
        <img
          alt="Circular logo"
          class="w-14 h-14 rounded-full"
          src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSdR6_1Sf1K11N6EpACygPUZKHwpgbwA18WkQ&s"
        />
        <nav class="hidden sm:flex flex-col space-y-4 w-full">
          <h2 class="text-gray-400 uppercase text-xs font-semibold mb-2">
            Generals
          </h2>
          <a
            class="flex items-center space-x-3 text-[#f15a24] font-semibold"
            href="#"
          >
            <i class="fas fa-th-large text-lg"></i>
            <span>Dashboard</span>
          </a>
          <div class="flex flex-col space-y-2 text-gray-400 text-sm">
            <button
              class="flex items-center space-x-3 w-full hover:text-white"
            >
              <i class="fas fa-file-alt text-base"></i>
              <span>Documents</span>
              <i class="fas fa-chevron-down ml-auto text-xs"></i>
            </button>
            <button
              class="flex items-center space-x-3 w-full hover:text-white"
            >
              <i class="fas fa-users text-base"></i>
              <span>Users</span>
              <i class="fas fa-chevron-down ml-auto text-xs"></i>
            </button>
            <a class="flex items-center space-x-3 hover:text-white" href="#">
              <i class="fas fa-cog text-base"></i>
              <span>Settings</span>
            </a>
          </div>
        </nav>
      </div>
      <button class="flex items-center space-x-3 text-gray-400 hover:text-white text-sm sm:text-base">
        <i class="fas fa-sign-out-alt text-lg"></i>
        <span class="hidden sm:inline">Logout</span>
      </button>
    </aside>

    
    <main class="flex-1 p-6 sm:p-8 space-y-6 overflow-auto">
      <header class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <h1 class="text-white font-semibold text-base sm:text-lg">
          Welcome, <EMAIL>
        </h1>
        <div class="flex items-center space-x-4">
          <button aria-label="Toggle dark mode" class="text-[#f15a24] hover:text-[#d94a1a] text-lg">
            <i class="fas fa-sun"></i>
          </button>
          <button aria-label="Notifications" class="text-[#f15a24] hover:text-[#d94a1a] text-lg relative">
            <i class="fas fa-bell"></i>
            <span class="absolute -top-1 -right-1 bg-[#f15a24] rounded-full w-2 h-2"></span>
          </button>
          <img
            alt="User avatar"
            class="w-8 h-8 rounded-full"
            src="https://storage.googleapis.com/a1aa/image/ec4fed38-cbfe-42dd-fad5-fa8b0fcd6044.jpg"
          />
          <div class="relative">
            <input
              class="bg-[#2a3244] rounded-md text-gray-400 placeholder-gray-500 text-sm pl-3 pr-8 py-1 focus:outline-none focus:ring-1 focus:ring-[#f15a24]"
              placeholder="Search..."
              type="search"
            />
            <i class="fas fa-search absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 text-xs"></i>
          </div>
        </div>
      </header>

      
      <section class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-center">
        <div class="bg-[#2a3244] rounded-md p-4 flex flex-col items-center space-y-2">
          <div class="flex items-center space-x-2 text-[#f15a24]">
            <i class="fas fa-file-alt text-xl"></i>
            <span class="text-2xl font-bold">999</span>
          </div>
          <p class="text-xs text-gray-400 font-semibold">Pending Request</p>
        </div>
      
      </section>

     
      <section class="bg-[#2a3244] rounded-md p-4 overflow-x-auto scrollbar-thin">
       
      </section>
    </main>
  </div>
</template>

<script>
export default {
  name: 'AdminDashboard'
}
</script>

<style scoped>
.scrollbar-thin::-webkit-scrollbar {
  height: 6px;
}
.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}
.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 10px;
}
</style>