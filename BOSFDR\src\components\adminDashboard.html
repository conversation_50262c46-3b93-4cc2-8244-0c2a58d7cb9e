<html lang="en">
  <head>
    <title>Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      .scrollbar-thin::-webkit-scrollbar {
        height: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-track {
        background: transparent;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: #4b5563;
        border-radius: 10px;
      }
    </style>
  </head>
  <body class="bg-[#222b38] text-white font-sans min-h-screen flex">
    <aside class="flex flex-col bg-[#2a3244] w-20 sm:w-56 p-4 space-y-8">
      <div class="flex flex-col items-center sm:items-start space-y-6">
        <img
          alt="Barangay Bula Council official seal"
          class="w-14 h-14 rounded-full"
          height="64"
          src="../assets/icon-of-bula.jpg"
          width="64"
        />
        <nav class="hidden sm:flex flex-col space-y-4 w-full">
          <h2 class="text-gray-400 uppercase text-xs font-semibold mb-2">
            Generals
          </h2>
          <a
            class="flex items-center space-x-3 text-[#f15a24] font-semibold"
            href="#"
          >
            <i class="fas fa-th-large text-[#f15a24] text-lg"> </i>
            <span> Dashboard </span>
          </a>
          <div class="flex flex-col space-y-2 text-gray-400 text-sm">
            <button
              aria-expanded="false"
              class="flex items-center space-x-3 w-full hover:text-white focus:outline-none"
            >
              <i class="fas fa-file-alt text-base"> </i>
              <span> Documents </span>
              <i class="fas fa-chevron-down ml-auto text-xs"> </i>
            </button>
            <button
              aria-expanded="false"
              class="flex items-center space-x-3 w-full hover:text-white focus:outline-none"
            >
              <i class="fas fa-users text-base"> </i>
              <span> Users </span>
              <i class="fas fa-chevron-down ml-auto text-xs"> </i>
            </button>
            <a class="flex items-center space-x-3 hover:text-white" href="#">
              <i class="fas fa-cog text-base"> </i>
              <span> Settings </span>
            </a>
          </div>
        </nav>
      </div>
      <button
        class="flex items-center space-x-3 text-gray-400 hover:text-white text-sm sm:text-base self-center sm:self-start"
      >
        <i class="fas fa-sign-out-alt text-lg"> </i>
        <span class="hidden sm:inline"> Logout </span>
      </button>
    </aside>
    <main class="flex-1 p-6 sm:p-8 space-y-6 overflow-auto">
      
      <header
        class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"
      >
        <h1 class="text-white font-semibold text-base sm:text-lg">
          Welcome, Username001
        </h1>
        <div class="flex items-center space-x-4">
          <button
            aria-label="Toggle dark mode"
            class="text-[#f15a24] hover:text-[#d94a1a] text-lg"
          >
            <i class="fas fa-sun"> </i>
          </button>
          <button
            aria-label="Notifications"
            class="text-[#f15a24] hover:text-[#d94a1a] text-lg relative"
          >
            <i class="fas fa-bell"> </i>
            <span
              class="absolute -top-1 -right-1 bg-[#f15a24] rounded-full w-2 h-2"
            >
            </span>
          </button>
          <img
            alt="User avatar with pink background and female avatar icon"
            class="w-8 h-8 rounded-full"
            height="32"
            src="https://storage.googleapis.com/a1aa/image/ec4fed38-cbfe-42dd-fad5-fa8b0fcd6044.jpg"
            width="32"
          />
          <div class="relative">
            <input
              class="bg-[#2a3244] rounded-md text-gray-400 placeholder-gray-500 text-sm pl-3 pr-8 py-1 focus:outline-none focus:ring-1 focus:ring-[#f15a24]"
              placeholder="Search..."
              type="search"
            />
            <i
              class="fas fa-search absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 text-xs"
            >
            </i>
          </div>
        </div>
      </header>
      <section
        class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-center"
      >
        <div
          class="bg-[#2a3244] rounded-md p-4 flex flex-col items-center space-y-2"
        >
          <div class="flex items-center space-x-2 text-[#f15a24]">
            <i class="fas fa-file-alt text-xl"> </i>
            <span class="text-2xl font-bold"> 999 </span>
          </div>
          <p class="text-xs text-gray-400 font-semibold">Pending Request</p>
        </div>
        <div
          class="bg-[#2a3244] rounded-md p-4 flex flex-col items-center space-y-2"
        >
          <div class="flex items-center space-x-2 text-[#f15a24]">
            <i class="fas fa-clipboard-list text-xl"> </i>
            <span class="text-2xl font-bold"> 999 </span>
          </div>
          <p class="text-xs text-gray-400 font-semibold">Processing</p>
        </div>
        <div
          class="bg-[#2a3244] rounded-md p-4 flex flex-col items-center space-y-2"
        >
          <div class="flex items-center space-x-2 text-[#f15a24]">
            <i class="fas fa-file-invoice text-xl"> </i>
            <span class="text-2xl font-bold"> 999 </span>
          </div>
          <p class="text-xs text-gray-400 font-semibold">Approved Request</p>
        </div>
        <div
          class="bg-[#2a3244] rounded-md p-4 flex flex-col items-center space-y-2"
        >
          <div class="flex items-center space-x-2 text-[#f15a24]">
            <i class="fas fa-file-signature text-xl"> </i>
            <span class="text-2xl font-bold"> 999 </span>
          </div>
          <p class="text-xs text-gray-400 font-semibold">Verified Documents</p>
        </div>
      </section>
      <section class="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div class="bg-[#2a3244] rounded-md p-4">
          <div class="flex justify-between items-center mb-2">
            <p class="text-xs text-gray-400 font-semibold">Performance</p>
            <div class="flex space-x-1 text-gray-400 text-[9px] font-semibold">
              <button class="hover:text-white">1H</button>
              <button class="hover:text-white">1M</button>
              <button class="hover:text-white">6M</button>
              <button class="hover:text-white">1Y</button>
            </div>
          </div>
          <img
            alt="Bar and line chart showing performance data from Jan to Dec with orange bars and green line"
            class="w-full h-28 object-contain"
            height="120"
            src="https://storage.googleapis.com/a1aa/image/c8b5a9c3-1ca4-4e36-6c95-75152d5dc2af.jpg"
            width="400"
          />
        </div>
        <div class="bg-[#2a3244] rounded-md p-4">
          <div class="flex justify-between items-center mb-2">
            <p class="text-xs text-gray-400 font-semibold">Performance</p>
            <div class="flex space-x-1 text-gray-400 text-[9px] font-semibold">
              <button class="hover:text-white">ALL</button>
              <button class="hover:text-white">1M</button>
              <button class="hover:text-white">6M</button>
              <button class="hover:text-white">1Y</button>
            </div>
          </div>
          <img
            alt="Bar and line chart showing performance data from Jan to Dec with orange bars and green line"
            class="w-full h-28 object-contain"
            height="120"
            src="https://storage.googleapis.com/a1aa/image/c8b5a9c3-1ca4-4e36-6c95-75152d5dc2af.jpg"
            width="400"
          />
        </div>
      </section>
      <section
        class="bg-[#2a3244] rounded-md p-4 overflow-x-auto scrollbar-thin"
      >
        <table
          class="w-full text-xs text-gray-400 border-separate border-spacing-y-2"
        >
          <thead>
            <tr>
              <th class="text-left w-6">ID</th>
              <th class="text-left w-24">Employee ID</th>
              <th class="text-left w-24">Date</th>
              <th class="text-left w-20">Time</th>
              <th class="text-left w-28">Action Type</th>
              <th class="text-left">Description</th>
              <th class="text-left w-20">Status</th>
            </tr>
          </thead>
          <tbody>
            <tr class="bg-[#222b38] rounded-md">
              <td class="font-semibold text-[#f15a24] py-1">1</td>
              <td
                class="font-semibold text-[#f15a24] underline py-1 cursor-pointer"
              >
                9745
              </td>
              <td class="py-1">29 Apr 2025</td>
              <td class="py-1">10:42 AM</td>
              <td class="py-1 font-semibold">Request</td>
              <td class="py-1">Requested Barangay Certificate for</td>
              <td class="py-1 flex items-center space-x-2">
                <span class="w-3 h-3 rounded-full bg-[#f15a24] inline-block">
                </span>
                <span> Processing </span>
              </td>
            </tr>
            <tr class="bg-[#222b38] rounded-md">
              <td class="font-semibold text-[#f15a24] py-1">2</td>
              <td
                class="font-semibold text-[#f15a24] underline py-1 cursor-pointer"
              >
                3423
              </td>
              <td class="py-1">29 Apr 2025</td>
              <td class="py-1">10:41 AM</td>
              <td class="py-1 font-semibold">Add</td>
              <td class="py-1">Added new resident record: Maria Santos</td>
              <td class="py-1 flex items-center space-x-2">
                <span class="w-3 h-3 rounded-full bg-green-500 inline-block">
                </span>
                <span> Completed </span>
              </td>
            </tr>
            <tr class="bg-[#222b38] rounded-md">
              <td class="font-semibold text-[#f15a24] py-1">3</td>
              <td
                class="font-semibold text-[#f15a24] underline py-1 cursor-pointer"
              >
                3462
              </td>
              <td class="py-1">29 Apr 2025</td>
              <td class="py-1">10:39 AM</td>
              <td class="py-1 font-semibold">Update</td>
              <td class="py-1">
                Updated address and contact number for resident: John Dela Cruz
              </td>
              <td class="py-1 flex items-center space-x-2">
                <span class="w-3 h-3 rounded-full bg-red-600 inline-block">
                </span>
                <span> Failed </span>
              </td>
            </tr>
            <tr class="bg-[#222b38] rounded-md">
              <td class="font-semibold text-[#f15a24] py-1">4</td>
              <td
                class="font-semibold text-[#f15a24] underline py-1 cursor-pointer"
              >
                7454
              </td>
              <td class="py-1">29 Apr 2025</td>
              <td class="py-1">10:38 AM</td>
              <td class="py-1 font-semibold">Approved</td>
              <td class="py-1">
                Approved request for Certificate of Indigency by Carlo Ramos
              </td>
              <td class="py-1 flex items-center space-x-2">
                <span class="w-3 h-3 rounded-full bg-green-500 inline-block">
                </span>
                <span> Completed </span>
              </td>
            </tr>
            <tr class="bg-[#222b38] rounded-md">
              <td class="font-semibold text-[#f15a24] py-1">5</td>
              <td
                class="font-semibold text-[#f15a24] underline py-1 cursor-pointer"
              >
                8643
              </td>
              <td class="py-1">29 Apr 2025</td>
              <td class="py-1">10:35 AM</td>
              <td class="py-1 font-semibold">Rejected</td>
              <td class="py-1">
                Rejected request due to incomplete information for Barangay
                Business Permit
              </td>
              <td class="py-1 flex items-center space-x-2">
                <span class="w-3 h-3 rounded-full bg-[#f15a24] inline-block">
                </span>
                <span> Processing </span>
              </td>
            </tr>
          </tbody>
        </table>
        <p class="text-gray-500 text-[10px] mt-2">Showing 5 of 1,323 logs</p>
        <nav
          aria-label="Pagination"
          class="flex items-center justify-end space-x-2 mt-3 text-gray-400 text-xs font-semibold select-none"
        >
          <button
            aria-label="Previous page"
            class="flex items-center space-x-1 hover:text-white focus:outline-none"
          >
            <span> ← </span>
          </button>
          <button
            aria-current="page"
            class="px-2 py-0.5 rounded bg-[#f15a24] text-black"
          >
            1
          </button>
          <button
            class="px-2 py-0.5 rounded hover:bg-[#f15a24] hover:text-black"
          >
            2
          </button>
          <button
            class="px-2 py-0.5 rounded hover:bg-[#f15a24] hover:text-black"
          >
            3
          </button>
          <button
            aria-label="Next page"
            class="flex items-center space-x-1 hover:text-white focus:outline-none"
          >
            <span> → </span>
          </button>
        </nav>
      </section>
    </main>
  </body>
</html>
