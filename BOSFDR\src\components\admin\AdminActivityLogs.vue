<template>
  <div class="admin-activity-logs">
    <AdminHeader
      :userName="adminData?.first_name || 'Admin'"
      :showUserDropdown="showUserDropdown"
      :sidebarCollapsed="sidebarCollapsed"
      :activeMenu="activeMenu"
      @sidebar-toggle="handleSidebarToggle"
      @user-dropdown-toggle="handleUserDropdownToggle"
      @menu-action="handleMenuAction"
      @logout="handleLogout"
    />

    <!-- Mobile Overlay -->
    <div
      class="mobile-overlay"
      :class="{ active: !sidebarCollapsed && isMobile }"
      @click="closeMobileSidebar"
    ></div>

    <div class="dashboard-container">
      <AdminSidebar
        :collapsed="sidebarCollapsed"
        :activeMenu="activeMenu"
        @menu-change="handleMenuChange"
        @logout="handleLogout"
        @toggle-sidebar="handleSidebarToggle"
      />
      
      <main class="main-content" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
        <div class="container-fluid p-4">
          <!-- Page Header -->
          <div class="row mb-4">
            <div class="col-12">
              <div class="d-flex justify-content-between align-items-center flex-wrap">

                <div class="d-flex gap-2">
                  <button class="btn btn-outline-success btn-sm" @click="loadLogs" :disabled="loading">
                    <i class="fas fa-sync-alt me-1" :class="{ 'fa-spin': loading }"></i>
                    Refresh
                  </button>
                  <button class="btn btn-success btn-sm" @click="exportLogs">
                    <i class="fas fa-download me-1"></i>
                    Export
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Filters -->
          <div class="row mb-4">
            <div class="col-12">
              <div class="card shadow">
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-3 mb-3">
                      <label class="form-label">Date From</label>
                      <input type="date" class="form-control" v-model="filters.dateFrom">
                    </div>
                    <div class="col-md-3 mb-3">
                      <label class="form-label">Date To</label>
                      <input type="date" class="form-control" v-model="filters.dateTo">
                    </div>
                    <div class="col-md-3 mb-3">
                      <label class="form-label">Activity Type</label>
                      <select class="form-select" v-model="filters.type">
                        <option value="">All Types</option>
                        <option value="login">Login</option>
                        <option value="logout">Logout</option>
                        <option value="user_action">User Action</option>
                        <option value="system">System</option>
                        <option value="error">Error</option>
                      </select>
                    </div>
                    <div class="col-md-3 mb-3">
                      <label class="form-label">User</label>
                      <input type="text" class="form-control" placeholder="Search by user..." v-model="filters.user">
                    </div>
                  </div>
                  <div class="text-end">
                    <button class="btn btn-primary btn-sm" @click="applyFilters">
                      <i class="fas fa-filter me-1"></i>
                      Apply Filters
                    </button>
                    <button class="btn btn-outline-secondary btn-sm ms-2" @click="clearFilters">
                      <i class="fas fa-times me-1"></i>
                      Clear
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Activity Logs Table -->
          <div class="row">
            <div class="col-12">
              <div class="card shadow">
                <div class="card-header py-3">
                  <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>
                    Activity Log Entries
                  </h6>
                </div>
                <div class="card-body">
                  <!-- Loading State -->
                  <div v-if="loading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="text-muted mt-2">Loading activity logs...</p>
                  </div>

                  <!-- Empty State -->
                  <div v-else-if="filteredLogs.length === 0" class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">No activity logs found</h5>
                    <p class="text-muted">No activities match your current filters.</p>
                  </div>

                  <!-- Logs Table -->
                  <div v-else class="table-responsive">
                    <table class="table table-hover">
                      <thead class="table-light">
                        <tr>
                          <th>Timestamp</th>
                          <th>User</th>
                          <th>Activity</th>
                          <th>Type</th>
                          <th>IP Address</th>
                          <th>Details</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="log in paginatedLogs" :key="log.id">
                          <td>{{ formatDateTime(log.timestamp) }}</td>
                          <td>
                            <div class="d-flex align-items-center">
                              <div class="user-avatar me-2">
                                <div class="avatar-placeholder">
                                  {{ getInitials(log.user_name) }}
                                </div>
                              </div>
                              <div>
                                <div class="fw-bold">{{ log.user_name }}</div>
                                <div class="text-muted small">{{ log.user_role }}</div>
                              </div>
                            </div>
                          </td>
                          <td>{{ log.activity }}</td>
                          <td>
                            <span class="badge" :class="getTypeBadgeClass(log.type)">
                              {{ formatType(log.type) }}
                            </span>
                          </td>
                          <td>{{ log.ip_address }}</td>
                          <td>
                            <button class="btn btn-outline-info btn-sm" @click="viewDetails(log)">
                              <i class="fas fa-eye"></i>
                            </button>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <!-- Pagination -->
                  <div v-if="totalPages > 1" class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                      Showing {{ (currentPage - 1) * itemsPerPage + 1 }} to {{ Math.min(currentPage * itemsPerPage, filteredLogs.length) }} of {{ filteredLogs.length }} entries
                    </div>
                    <nav>
                      <ul class="pagination pagination-sm mb-0">
                        <li class="page-item" :class="{ disabled: currentPage === 1 }">
                          <button class="page-link" @click="changePage(currentPage - 1)" :disabled="currentPage === 1">
                            Previous
                          </button>
                        </li>
                        <li 
                          v-for="page in visiblePages" 
                          :key="page" 
                          class="page-item" 
                          :class="{ active: page === currentPage }"
                        >
                          <button class="page-link" @click="changePage(page)">{{ page }}</button>
                        </li>
                        <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                          <button class="page-link" @click="changePage(currentPage + 1)" :disabled="currentPage === totalPages">
                            Next
                          </button>
                        </li>
                      </ul>
                    </nav>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import AdminHeader from './AdminHeader.vue';
import AdminSidebar from './AdminSidebar.vue';
import adminAuthService from '@/services/adminAuthService';

export default {
  name: 'AdminActivityLogs',
  components: {
    AdminHeader,
    AdminSidebar
  },

  data() {
    return {
      // UI State
      sidebarCollapsed: false,
      showUserDropdown: false,
      isMobile: false,
      adminData: null,
      // Component Data
      loading: false,
      logs: [],
      filteredLogs: [],
      currentPage: 1,
      itemsPerPage: 20,
      filters: {
        dateFrom: '',
        dateTo: '',
        type: '',
        user: ''
      }
    };
  },

  computed: {
    activeMenu() {
      const path = this.$route.path;
      if (path.includes('/admin/users')) return 'users';
      if (path.includes('/admin/requests')) return 'requests';
      if (path.includes('/admin/reports')) return 'reports';
      if (path.includes('/admin/settings')) return 'settings';
      if (path.includes('/admin/activity-logs')) return 'activity';
      if (path.includes('/admin/profile')) return 'profile';
      return 'dashboard';
    },

    paginatedLogs() {
      const start = (this.currentPage - 1) * this.itemsPerPage;
      const end = start + this.itemsPerPage;
      return this.filteredLogs.slice(start, end);
    },

    totalPages() {
      return Math.ceil(this.filteredLogs.length / this.itemsPerPage);
    },

    visiblePages() {
      const pages = [];
      const start = Math.max(1, this.currentPage - 2);
      const end = Math.min(this.totalPages, this.currentPage + 2);
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      return pages;
    }
  },
  
  async mounted() {
    // Check authentication
    if (!adminAuthService.isLoggedIn()) {
      this.$router.push('/admin/login');
      return;
    }

    // Initialize UI state
    this.initializeUI();

    // Set default date range (last 30 days)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

    this.filters.dateTo = today.toISOString().split('T')[0];
    this.filters.dateFrom = thirtyDaysAgo.toISOString().split('T')[0];

    // Load component data
    await this.loadAdminProfile();
    await this.loadLogs();
  },

  beforeUnmount() {
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
    }
  },

  methods: {
    // Initialize UI state
    initializeUI() {
      this.isMobile = window.innerWidth <= 768;

      if (!this.isMobile) {
        const saved = localStorage.getItem('adminSidebarCollapsed');
        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;
      } else {
        this.sidebarCollapsed = true;
      }

      this.handleResize = () => {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;

        if (this.isMobile && !wasMobile) {
          this.sidebarCollapsed = true;
        } else if (!this.isMobile && wasMobile) {
          const saved = localStorage.getItem('adminSidebarCollapsed');
          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;
        }
      };
      window.addEventListener('resize', this.handleResize);
    },

    handleSidebarToggle() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));
    },

    handleMenuChange(menu) {
      const routes = {
        'dashboard': '/admin/dashboard',
        'users': '/admin/users',
        'requests': '/admin/requests',
        'reports': '/admin/reports',
        'settings': '/admin/settings',
        'activity': '/admin/activity-logs',
        'profile': '/admin/profile'
      };

      if (this.isMobile) {
        this.sidebarCollapsed = true;
      }

      if (routes[menu]) {
        this.$router.push(routes[menu]);
      }
    },

    handleUserDropdownToggle() {
      this.showUserDropdown = !this.showUserDropdown;
    },

    handleMenuAction(action) {
      if (action === 'profile') {
        this.$router.push('/admin/profile');
      } else if (action === 'settings') {
        this.$router.push('/admin/settings');
      }
      this.showUserDropdown = false;
    },

    closeMobileSidebar() {
      if (this.isMobile) {
        this.sidebarCollapsed = true;
      }
    },

    handleLogout() {
      adminAuthService.logout();
      this.$router.push('/admin/login');
    },

    async loadAdminProfile() {
      try {
        const response = await adminAuthService.getProfile();
        if (response.success) {
          this.adminData = response.data;
        }
      } catch (error) {
        console.error('Failed to load admin profile:', error);
        this.adminData = adminAuthService.getAdminData();
      }
    },

    async loadLogs() {
      this.loading = true;
      try {
        // Simulate API call - replace with actual service call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data - replace with actual API call
        this.logs = [
          {
            id: 1,
            timestamp: '2024-01-31T14:30:00Z',
            user_name: 'John Admin',
            user_role: 'Administrator',
            activity: 'User login',
            type: 'login',
            ip_address: '*************',
            details: 'Successful login from Chrome browser'
          },
          {
            id: 2,
            timestamp: '2024-01-31T14:25:00Z',
            user_name: 'Jane Smith',
            user_role: 'Employee',
            activity: 'Document approved',
            type: 'user_action',
            ip_address: '*************',
            details: 'Approved barangay clearance request #12345'
          },
          {
            id: 3,
            timestamp: '2024-01-31T14:20:00Z',
            user_name: 'System',
            user_role: 'System',
            activity: 'Database backup completed',
            type: 'system',
            ip_address: 'localhost',
            details: 'Automated daily backup completed successfully'
          }
        ];

        this.filteredLogs = [...this.logs];
      } catch (error) {
        console.error('Failed to load activity logs:', error);
      } finally {
        this.loading = false;
      }
    },

    applyFilters() {
      let filtered = [...this.logs];

      // Apply date filters
      if (this.filters.dateFrom) {
        filtered = filtered.filter(log => new Date(log.timestamp) >= new Date(this.filters.dateFrom));
      }
      if (this.filters.dateTo) {
        filtered = filtered.filter(log => new Date(log.timestamp) <= new Date(this.filters.dateTo + 'T23:59:59'));
      }

      // Apply type filter
      if (this.filters.type) {
        filtered = filtered.filter(log => log.type === this.filters.type);
      }

      // Apply user filter
      if (this.filters.user) {
        const query = this.filters.user.toLowerCase();
        filtered = filtered.filter(log => 
          log.user_name.toLowerCase().includes(query) ||
          log.user_role.toLowerCase().includes(query)
        );
      }

      this.filteredLogs = filtered;
      this.currentPage = 1;
    },

    clearFilters() {
      this.filters = {
        dateFrom: '',
        dateTo: '',
        type: '',
        user: ''
      };
      this.filteredLogs = [...this.logs];
      this.currentPage = 1;
    },

    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page;
      }
    },

    getInitials(name) {
      if (!name) return '?';
      return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase().slice(0, 2);
    },

    getTypeBadgeClass(type) {
      const classes = {
        'login': 'bg-success',
        'logout': 'bg-secondary',
        'user_action': 'bg-primary',
        'system': 'bg-info',
        'error': 'bg-danger'
      };
      return classes[type] || 'bg-secondary';
    },

    formatType(type) {
      return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    },

    formatDateTime(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    viewDetails(log) {
      alert(`Activity Details:\n\n${log.details}`);
    },

    exportLogs() {
      console.log('Exporting activity logs...');
      alert('Export functionality will be implemented soon.');
    }
  }
};
</script>

<style scoped src="./css/adminDashboard.css"></style>
<style scoped>
.user-avatar {
  width: 32px;
  height: 32px;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
}
</style>
