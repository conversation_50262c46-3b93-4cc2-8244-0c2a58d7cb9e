<template>
  <div class="admin-requests">
    <AdminHeader
      :userName="adminData?.first_name || 'Admin'"
      :showUserDropdown="showUserDropdown"
      :sidebarCollapsed="sidebarCollapsed"
      :activeMenu="activeMenu"
      @sidebar-toggle="handleSidebarToggle"
      @user-dropdown-toggle="handleUserDropdownToggle"
      @menu-action="handleMenuAction"
      @open-request-modal="handleOpenRequestModal"
      @logout="handleLogout"
    />

    <!-- Mobile Overlay -->
    <div
      class="mobile-overlay"
      :class="{ active: !sidebarCollapsed && isMobile }"
      @click="closeMobileSidebar"
    ></div>

    <div class="dashboard-container">
      <AdminSidebar
        :collapsed="sidebarCollapsed"
        :activeMenu="activeMenu"
        @menu-change="handleMenuChange"
        @logout="handleLogout"
        @toggle-sidebar="handleSidebarToggle"
      />

      <main class="main-content" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
        <!-- Loading State -->
        <div v-if="loading" class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>

        <!-- Main Content -->
        <div v-else class="container-fluid py-4">
          <!-- Error Message -->
          <div v-if="errorMessage" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ errorMessage }}
            <button type="button" class="btn-close" @click="errorMessage = ''" aria-label="Close"></button>
          </div>

          <!-- Page Header -->
          <div class="row mb-4">
            <div class="col-12">
              <div class="d-flex justify-content-between align-items-center flex-wrap">
                <div>
                  <p class="text-muted mb-0">
                    <span v-if="lastRefresh" class="ms-2 small">
                      <i class="fas fa-clock text-muted"></i>
                      Last updated: {{ formatTime(lastRefresh) }}
                    </span>
                  </p>
                </div>
                <div class="d-flex gap-2 align-items-center">
                  <!-- Real-time status indicator -->
                  <div class="real-time-status me-2">
                    <span class="badge" :class="autoRefreshEnabled ? 'bg-success' : 'bg-secondary'">
                      <i class="fas fa-circle pulse" v-if="autoRefreshEnabled"></i>
                      <i class="fas fa-pause" v-else></i>
                      {{ autoRefreshEnabled ? 'Live' : 'Paused' }}
                    </span>
                  </div>

                  <button class="btn btn-outline-secondary btn-sm" @click="toggleAutoRefresh" :title="autoRefreshEnabled ? 'Disable auto-refresh' : 'Enable auto-refresh'">
                    <i class="fas" :class="autoRefreshEnabled ? 'fa-pause' : 'fa-play'"></i>
                  </button>
                  <button class="btn btn-outline-primary btn-sm" @click="showFilters = !showFilters">
                    <i class="fas fa-filter me-1"></i>
                    {{ showFilters ? 'Hide' : 'Show' }} Filters
                  </button>
                  <!-- <button class="btn btn-success btn-sm" @click="exportRequests" :disabled="loading">
                    <i class="fas fa-download me-1"></i>
                    Export CSV
                  </button> -->
                  <button class="btn btn-primary btn-sm" @click="refreshRequestsData" :disabled="loading">
                    <i class="fas fa-sync-alt me-1" :class="{ 'fa-spin': loading }"></i>
                    Refresh
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Request Statistics -->
          <div class="row mb-3">
            <div class="col-6 col-md-3 mb-2">
              <div class="card border-left-primary shadow py-1">
                <div class="card-body p-2">
                  <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                      <div class="text-xs fw-bold text-primary text-uppercase mb-1">Total Requests</div>
                      <div class="h6 mb-0 fw-bold text-dark">{{ requestStats.total || 0 }}</div>
                    </div>
                    <i class="fas fa-file-alt fa-lg text-muted ms-2"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-6 col-md-3 mb-2">
              <div class="card border-left-warning shadow py-1">
                <div class="card-body p-2">
                  <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                      <div class="text-xs fw-bold text-warning text-uppercase mb-1">Pending</div>
                      <div class="h6 mb-0 fw-bold text-dark">{{ requestStats.pending || 0 }}</div>
                    </div>
                    <i class="fas fa-clock fa-lg text-muted ms-2"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-6 col-md-3 mb-2">
              <div class="card border-left-success shadow py-1">
                <div class="card-body p-2">
                  <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                      <div class="text-xs fw-bold text-success text-uppercase mb-1">Completed</div>
                      <div class="h6 mb-0 fw-bold text-dark">{{ requestStats.completed || 0 }}</div>
                    </div>
                    <i class="fas fa-check-circle fa-lg text-muted ms-2"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-6 col-md-3 mb-2">
              <div class="card border-left-info shadow py-1">
                <div class="card-body p-2">
                  <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                      <div class="text-xs fw-bold text-info text-uppercase mb-1">Approved</div>
                      <div class="h6 mb-0 fw-bold text-dark">{{ requestStats.approved || 0 }}</div>
                    </div>
                    <i class="fas fa-thumbs-up fa-lg text-muted ms-2"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Filters Panel -->
          <div v-if="showFilters" class="card shadow mb-4">
            <div class="card-header py-3">
              <h6 class="m-0 fw-bold text-primary">Filter Requests</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-3 mb-3">
                  <label class="form-label">Search</label>
                  <input
                    type="text"
                    class="form-control"
                    v-model="filters.search"
                    placeholder="Search by name, email, or request number"
                    @keyup.enter="applyFilters"
                  >
                </div>
                <div class="col-md-2 mb-3">
                  <label class="form-label">Status</label>
                  <select class="form-select" v-model="filters.status">
                    <option value="">All Statuses</option>
                    <option v-for="status in statusOptions" :key="status.id" :value="status.status_name">
                      {{ formatStatus(status.status_name) }}
                    </option>
                  </select>
                </div>
                <div class="col-md-2 mb-3">
                  <label class="form-label">Document Type</label>
                  <select class="form-select" v-model="filters.document_type">
                    <option value="">All Types</option>
                    <option value="barangay_clearance">Barangay Clearance</option>
                    <option value="cedula">Cedula</option>
                  </select>
                </div>
                <div class="col-md-2 mb-3">
                  <label class="form-label">Date From</label>
                  <input type="date" class="form-control" v-model="filters.date_from">
                </div>
                <div class="col-md-2 mb-3">
                  <label class="form-label">Date To</label>
                  <input type="date" class="form-control" v-model="filters.date_to">
                </div>
                <div class="col-md-1 mb-3 d-flex align-items-end">
                  <div class="d-flex gap-1 w-100">
                    <button class="btn btn-primary btn-sm" @click="applyFilters">
                      <i class="fas fa-search"></i>
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" @click="clearFilters">
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Bulk Actions Panel -->
          <div v-if="selectedRequests.length > 0" class="card shadow mb-4">
            <div class="card-header py-3 bg-warning">
              <h6 class="m-0 fw-bold text-dark">
                <i class="fas fa-tasks me-2"></i>
                Bulk Actions ({{ selectedRequests.length }} selected)
              </h6>
            </div>
            <div class="card-body">
              <div class="row align-items-end">
                <div class="col-md-3 mb-3">
                  <label class="form-label">Action</label>
                  <select class="form-select" v-model="bulkAction">
                    <option value="">Select Action</option>
                    <option v-for="status in statusOptions" :key="status.id" :value="status.id">
                      Change to {{ formatStatus(status.status_name) }}
                    </option>
                  </select>
                </div>

                <div class="col-md-3 mb-3">
                  <div class="d-flex gap-2">
                    <button class="btn btn-warning" @click="performBulkAction" :disabled="!bulkAction">
                      <i class="fas fa-play me-1"></i>
                      Apply
                    </button>
                    <button class="btn btn-outline-secondary" @click="selectedRequests = []">
                      <i class="fas fa-times me-1"></i>
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- View Toggle -->
          <div class="d-flex justify-content-between align-items-center mb-4">
            <div class="d-flex align-items-center gap-3">
              <div class="btn-group" role="group" aria-label="View toggle">
                <input type="radio" class="btn-check" name="viewMode" id="cardView" v-model="viewMode" value="card" autocomplete="off">
                <label class="btn btn-outline-primary btn-sm" for="cardView">
                  <i class="fas fa-th-large me-1"></i>Cards
                </label>

                <input type="radio" class="btn-check" name="viewMode" id="tableView" v-model="viewMode" value="table" autocomplete="off">
                <label class="btn btn-outline-primary btn-sm" for="tableView">
                  <i class="fas fa-table me-1"></i>Table
                </label>
              </div>

              <div class="d-flex align-items-center gap-2">
                <span class="text-muted small">
                  Showing {{ ((pagination.currentPage - 1) * pagination.itemsPerPage) + 1 }} -
                  {{ Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems) }}
                  of {{ pagination.totalItems }} requests
                </span>
                <select class="form-select form-select-sm" style="width: auto;" v-model="pagination.itemsPerPage" @change="changeItemsPerPage(pagination.itemsPerPage)">
                  <option value="10">10 per page</option>
                  <option value="25">25 per page</option>
                  <option value="50">50 per page</option>
                  <option value="100">100 per page</option>
                </select>
              </div>
            </div>

            <div class="d-flex align-items-center gap-2">
              <button class="btn btn-sm btn-outline-secondary" @click="selectAllRequests" v-if="requests.length > 0">
                <i class="fas fa-check-square me-1"></i>
                {{ selectedRequests.length === requests.length ? 'Deselect All' : 'Select All' }}
              </button>
            </div>
          </div>

          <!-- Card View -->
          <div v-if="viewMode === 'card'" class="requests-grid">
            <!-- Empty State -->
            <div v-if="requests.length === 0" class="empty-state text-center py-5">
              <div class="empty-state-icon mb-3">
                <i class="fas fa-inbox fa-4x text-muted"></i>
              </div>
              <h5 class="text-muted mb-2">No Document Requests Found</h5>
              <p class="text-muted">There are no document requests matching your current filters.</p>
            </div>

            <!-- Request Cards -->
            <div v-else class="row g-4">
              <div v-for="request in requests" :key="request.id" class="col-xl-4 col-lg-6 col-md-6">
                <div class="request-card" :class="{ 'selected': selectedRequests.includes(request.id) }">
                  <!-- Card Header -->
                  <div class="request-card-header">
                    <div class="d-flex justify-content-between align-items-start">
                      <div class="d-flex align-items-center gap-2">
                        <input
                          type="checkbox"
                          class="form-check-input"
                          :checked="selectedRequests.includes(request.id)"
                          @change="toggleRequestSelection(request.id)"
                        >
                        <div class="request-number">
                          <span class="badge bg-primary">{{ request.request_number }}</span>
                        </div>
                      </div>
                      <div class="request-actions-simple">
                        <button class="btn btn-sm btn-primary" @click="viewRequestDetails(request.id)" title="View & Manage Request">
                          <i class="fas fa-edit me-1"></i>Manage
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- Card Body -->
                  <div class="request-card-body">
                    <!-- Client Info -->
                    <div class="client-info mb-3">
                      <div class="d-flex align-items-center gap-2 mb-2">
                        <div class="client-avatar">
                          <i class="fas fa-user-circle fa-2x text-primary"></i>
                        </div>
                        <div>
                          <h6 class="mb-0 fw-bold">{{ request.client_name }}</h6>
                          <small class="text-muted">{{ request.client_email }}</small>
                        </div>
                      </div>

                      <!-- Additional Client Details -->
                      <div class="client-details-grid mt-2">
                        <div class="row g-1">
                          <div class="col-6" v-if="request.client_birth_date">
                            <small class="text-muted d-block">Date of Birth</small>
                            <small class="fw-medium">{{ formatDate(request.client_birth_date) }}</small>
                          </div>
                          <div class="col-6" v-if="request.client_gender">
                            <small class="text-muted d-block">Gender</small>
                            <small class="fw-medium">{{ formatGender(request.client_gender) }}</small>
                          </div>
                          <div class="col-6" v-if="getCivilStatusName(request.client_civil_status_id)">
                            <small class="text-muted d-block">Civil Status</small>
                            <small class="fw-medium">{{ getCivilStatusName(request.client_civil_status_id) }}</small>
                          </div>
                          <div class="col-6" v-if="request.client_nationality">
                            <small class="text-muted d-block">Nationality</small>
                            <small class="fw-medium">{{ request.client_nationality }}</small>
                          </div>
                          <div class="col-12" v-if="getResidencyDisplay(request)">
                            <small class="text-muted d-block">Years of Residency</small>
                            <small class="fw-medium">{{ getResidencyDisplay(request) }}</small>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Document Type -->
                    <div class="document-type mb-3">
                      <div class="d-flex align-items-center gap-2">
                        <i class="fas fa-file-alt text-info"></i>
                        <span class="badge bg-info-subtle text-info-emphasis px-3 py-2">
                          {{ request.document_type }}
                        </span>
                      </div>
                    </div>

                    <!-- Status and Amount -->
                    <div class="request-meta mb-3">
                      <div class="row g-2">
                        <div class="col-6">
                          <div class="meta-item">
                            <small class="text-muted d-block">Status</small>
                            <span class="badge" :class="`bg-${getStatusColor(request.status_name)}`">
                              {{ formatStatus(request.status_name) }}
                            </span>
                          </div>
                        </div>
                        <div class="col-6">
                          <div class="meta-item">
                            <small class="text-muted d-block">Amount</small>
                            <span class="fw-bold text-success">{{ formatCurrency(request.total_fee) }}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Date -->
                    <div class="request-date">
                      <small class="text-muted">
                        <i class="fas fa-calendar-alt me-1"></i>
                        Submitted {{ formatDate(request.requested_at) }}
                      </small>
                    </div>
                  </div>

                  <!-- Card Footer -->
                  <div class="request-card-footer">
                    <div class="d-grid">
                      <button class="btn btn-sm btn-primary" @click="viewRequestDetails(request.id)">
                        <i class="fas fa-edit me-1"></i>Manage Request
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Table View -->
          <div v-else class="modern-table-container">

            <!-- Empty State -->
            <div v-if="requests.length === 0" class="modern-table-empty">
              <div class="empty-content">
                <div class="empty-icon">
                  <i class="fas fa-inbox"></i>
                </div>
                <h6 class="empty-title">No Document Requests Found</h6>
                <p class="empty-text">There are no document requests matching your current filters.</p>
              </div>
            </div>

            <!-- Modern Compact Table -->
            <div v-else class="compact-table-wrapper">
              <!-- Table Header -->
              <div class="compact-table-header">
                <div class="header-cell selection-header">
                  <input
                    type="checkbox"
                    class="form-check-input"
                    :checked="selectedRequests.length === requests.length && requests.length > 0"
                    @change="selectAllRequests"
                  >
                </div>
                <div class="header-cell">Request ID</div>
                <div class="header-cell">Client</div>
                <div class="header-cell">Document</div>
                <div class="header-cell">Status</div>
                <div class="header-cell">Amount</div>
                <div class="header-cell">Date</div>
                <div class="header-cell">Actions</div>
              </div>

              <!-- Table Body -->
              <div class="compact-table-body">
                <div v-for="request in requests" :key="request.id"
                     class="compact-row"
                     :class="{ 'selected': selectedRequests.includes(request.id) }">

                  <!-- Selection -->
                  <div class="row-cell selection-cell">
                    <input
                      type="checkbox"
                      class="form-check-input"
                      :checked="selectedRequests.includes(request.id)"
                      @change="toggleRequestSelection(request.id)"
                    >
                  </div>

                  <!-- Request ID -->
                  <div class="row-cell request-id-cell">
                    <div class="request-id-content">
                      <span class="request-number">{{ request.request_number }}</span>
                      <span class="request-id-small">{{ request.id }}</span>
                    </div>
                  </div>

                  <!-- Client -->
                  <div class="row-cell client-cell">
                    <div class="client-compact">
                      <div class="client-avatar-tiny">
                        <i class="fas fa-user"></i>
                      </div>
                      <div class="client-info-compact">
                        <div class="client-name-compact">{{ request.client_name }}</div>
                        <div class="client-email-compact">{{ request.client_email }}</div>
                        <div class="client-details-compact">
                          <span v-if="request.client_birth_date" class="detail-item">
                            <i class="fas fa-birthday-cake me-1"></i>{{ formatDate(request.client_birth_date) }}
                          </span>
                          <span v-if="request.client_gender" class="detail-item">
                            <i class="fas fa-venus-mars me-1"></i>{{ formatGender(request.client_gender) }}
                          </span>
                          <span v-if="getCivilStatusName(request.client_civil_status_id)" class="detail-item">
                            <i class="fas fa-ring me-1"></i>{{ getCivilStatusName(request.client_civil_status_id) }}
                          </span>
                          <span v-if="request.client_nationality" class="detail-item">
                            <i class="fas fa-flag me-1"></i>{{ request.client_nationality }}
                          </span>
                          <span v-if="getResidencyDisplay(request)" class="detail-item">
                            <i class="fas fa-home me-1"></i>{{ getResidencyDisplay(request) }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Document Type -->
                  <div class="row-cell document-cell">
                    <span class="document-badge">
                      <i class="fas fa-file-alt"></i>
                      {{ request.document_type }}
                    </span>
                  </div>

                  <!-- Status -->
                  <div class="row-cell status-cell">
                    <span class="status-compact" :class="`status-${getStatusColor(request.status_name)}`">
                      <i class="fas fa-circle"></i>
                      {{ formatStatus(request.status_name) }}
                    </span>
                  </div>

                  <!-- Amount -->
                  <div class="row-cell amount-cell">
                    <span class="amount-compact">{{ formatCurrency(request.total_fee) }}</span>
                  </div>

                  <!-- Date -->
                  <div class="row-cell date-cell">
                    <div class="date-compact">
                      <span class="date-main">{{ formatDate(request.requested_at) }}</span>
                      <span class="time-small">{{ formatTime(request.requested_at) }}</span>
                    </div>
                  </div>

                  <!-- Actions -->
                  <div class="row-cell actions-cell">
                    <div class="actions-simple">
                      <button class="action-btn-sm primary-btn-sm" @click="viewRequestDetails(request.id)" title="View & Manage Request">
                        <i class="fas fa-edit"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Pagination -->
          <div v-if="pagination.totalPages > 1" class="pagination-container">
              <nav aria-label="Requests pagination">
                <ul class="pagination pagination-sm justify-content-center mb-0">
                  <li class="page-item" :class="{ disabled: pagination.currentPage === 1 }">
                    <a class="page-link" href="#" @click.prevent="changePage(pagination.currentPage - 1)">
                      <i class="fas fa-chevron-left"></i>
                    </a>
                  </li>
                  <li
                    v-for="page in Math.min(pagination.totalPages, 10)"
                    :key="page"
                    class="page-item"
                    :class="{ active: page === pagination.currentPage }"
                  >
                    <a class="page-link" href="#" @click.prevent="changePage(page)">{{ page }}</a>
                  </li>
                  <li class="page-item" :class="{ disabled: pagination.currentPage === pagination.totalPages }">
                    <a class="page-link" href="#" @click.prevent="changePage(pagination.currentPage + 1)">
                      <i class="fas fa-chevron-right"></i>
                    </a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>

          <!-- Request Details Modal -->
          <div v-if="showRequestDetails && currentRequest" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
            <div class="modal-dialog modal-xl modal-dialog-scrollable">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">
                    <i class="fas fa-file-alt me-2"></i>
                    Request Details - {{ currentRequest.request_number }}
                  </h5>
                  <button type="button" class="btn-close" @click="showRequestDetails = false"></button>
                </div>
                <div class="modal-body">
                  <div class="row">
                    <!-- Left Column - Request Information -->
                    <div class="col-lg-8">
                      <!-- Basic Information -->
                      <div class="card mb-4">
                        <div class="card-header">
                          <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Request Information</h6>
                        </div>
                        <div class="card-body">
                          <div class="row">
                            <div class="col-md-6">
                              <div class="mb-3">
                                <label class="form-label fw-bold">Request Number</label>
                                <p class="mb-0">{{ currentRequest.request_number }}</p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Document Type</label>
                                <p class="mb-0">
                                  <span class="badge bg-info">{{ currentRequest.document_type }}</span>
                                </p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Purpose Category</label>
                                <p class="mb-0">{{ currentRequest.purpose_category }}</p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Purpose Details</label>
                                <p class="mb-0">{{ currentRequest.purpose_details || 'Not specified' }}</p>
                              </div>
                            </div>
                            <div class="col-md-6">
                              <div class="mb-3">
                                <label class="form-label fw-bold">Current Status</label>
                                <p class="mb-0">
                                  <span class="badge" :class="`bg-${getStatusColor(currentRequest.status_name)}`">
                                    {{ formatStatus(currentRequest.status_name) }}
                                  </span>
                                </p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Priority</label>
                                <p class="mb-0">
                                  <span class="badge" :class="currentRequest.priority === 'high' ? 'bg-danger' : currentRequest.priority === 'medium' ? 'bg-warning' : 'bg-secondary'">
                                    {{ currentRequest.priority || 'Normal' }}
                                  </span>
                                </p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Delivery Method</label>
                                <p class="mb-0">{{ currentRequest.delivery_method || 'Pickup' }}</p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Date Submitted</label>
                                <p class="mb-0">{{ formatDateTime(currentRequest.requested_at) }}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Client Information -->
                      <div class="card mb-4">
                        <div class="card-header">
                          <h6 class="mb-0"><i class="fas fa-user me-2"></i>Client Information</h6>
                        </div>
                        <div class="card-body">
                          <!-- Basic Information -->
                          <div class="row">
                            <div class="col-md-6">
                              <div class="mb-3">
                                <label class="form-label fw-bold">Full Name</label>
                                <p class="mb-0" :class="{ 'text-muted': getClientFullName(currentRequest) === 'Not provided' }">
                                  <span v-if="getClientFullName(currentRequest) !== 'Not provided'">{{ getClientFullName(currentRequest) }}</span>
                                  <span v-else class="not-provided">Not provided</span>
                                </p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Email Address</label>
                                <p class="mb-0" :class="{ 'text-muted': !currentRequest.client_email }">
                                  <a v-if="currentRequest.client_email" :href="`mailto:${currentRequest.client_email}`">{{ currentRequest.client_email }}</a>
                                  <span v-else class="not-provided">Not provided</span>
                                </p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Phone Number</label>
                                <p class="mb-0" :class="{ 'text-muted': !currentRequest.client_phone }">
                                  <a v-if="currentRequest.client_phone" :href="`tel:${currentRequest.client_phone}`">{{ currentRequest.client_phone }}</a>
                                  <span v-else class="not-provided">Not provided</span>
                                </p>
                              </div>
                            </div>
                            <div class="col-md-6">
                              <div class="mb-3">
                                <label class="form-label fw-bold">Date of Birth</label>
                                <p class="mb-0" :class="{ 'text-muted': !currentRequest.client_birth_date }">
                                  <span v-if="formatDate(currentRequest.client_birth_date)">{{ formatDate(currentRequest.client_birth_date) }}</span>
                                  <span v-else class="not-provided">Not provided</span>
                                </p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Gender</label>
                                <p class="mb-0" :class="{ 'text-muted': !currentRequest.client_gender }">
                                  <span v-if="formatGender(currentRequest.client_gender)">{{ formatGender(currentRequest.client_gender) }}</span>
                                  <span v-else class="not-provided">Not provided</span>
                                </p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Civil Status</label>
                                <p class="mb-0" :class="{ 'text-muted': !currentRequest.client_civil_status_id }">
                                  <span v-if="getCivilStatusName(currentRequest.client_civil_status_id)">{{ getCivilStatusName(currentRequest.client_civil_status_id) }}</span>
                                  <span v-else class="not-provided">Not provided</span>
                                </p>
                              </div>
                            </div>
                          </div>

                          <!-- Address Information -->
                          <div class="row">
                            <div class="col-12">
                              <div class="mb-3">
                                <label class="form-label fw-bold">Complete Address</label>
                                <p class="mb-0" :class="{ 'text-muted': !getClientFullAddress(currentRequest) }">
                                  <span v-if="getClientFullAddress(currentRequest)">{{ getClientFullAddress(currentRequest) }}</span>
                                  <span v-else class="not-provided">Not provided</span>
                                </p>
                              </div>
                            </div>
                          </div>

                          <!-- Additional Information -->
                          <div class="row">
                            <div class="col-md-6">
                              <div class="mb-3">
                                <label class="form-label fw-bold">Nationality</label>
                                <p class="mb-0" :class="{ 'text-muted': !currentRequest.client_nationality }">
                                  <span v-if="currentRequest.client_nationality">{{ currentRequest.client_nationality }}</span>
                                  <span v-else class="not-provided">Not provided</span>
                                </p>
                              </div>
                            </div>
                            <div class="col-md-6">
                              <div class="mb-3">
                                <label class="form-label fw-bold">Years of Residency</label>
                                <p class="mb-0" :class="{ 'text-muted': !getResidencyDisplay(currentRequest) }">
                                  <span v-if="getResidencyDisplay(currentRequest)">{{ getResidencyDisplay(currentRequest) }}</span>
                                  <span v-else class="not-provided">Not provided</span>
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Uploaded Documents -->
                      <div class="card mb-4">
                        <div class="card-header">
                          <h6 class="mb-0"><i class="fas fa-paperclip me-2"></i>Uploaded Documents</h6>
                        </div>
                        <div class="card-body">
                          <div v-if="currentRequest.uploaded_documents && currentRequest.uploaded_documents.length > 0">
                            <div class="row g-3">
                              <div v-for="document in currentRequest.uploaded_documents" :key="document.id" class="col-md-4">
                                <div class="document-preview-card">
                                  <div class="document-preview-header">
                                    <div class="document-type-badge">
                                      <i class="fas fa-file-alt me-1"></i>
                                      {{ getDocumentTypeDisplayName(document.document_type) }}
                                    </div>
                                  </div>
                                  <div class="document-preview-content">
                                    <!-- Image Preview -->
                                    <div v-if="isImageFile(document.mime_type)"
                                         class="image-preview"
                                         @click="openImageModal(document)"
                                         @mouseenter="preloadImage(document)">
                                      <!-- Successfully loaded image -->
                                      <img
                                        v-if="documentUrls[document.id]"
                                        :src="documentUrls[document.id]"
                                        :alt="document.document_name"
                                        class="document-image"
                                        @error="handleImageError"
                                      />

                                      <!-- Loading state -->
                                      <div v-else-if="loadingDocuments.has(document.id)" class="loading-placeholder">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        <span>Loading image...</span>
                                      </div>

                                      <!-- Failed state with retry option -->
                                      <div v-else-if="failedDocuments.has(document.id)" class="error-placeholder" @click.stop="retryLoadDocument(document)">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <span>Failed to load</span>
                                        <small>Click to retry</small>
                                      </div>

                                      <!-- Initial state (not yet attempted) -->
                                      <div v-else class="loading-placeholder">
                                        <i class="fas fa-image"></i>
                                        <span>Click to load</span>
                                      </div>
                                      <div class="image-overlay">
                                        <i class="fas fa-search-plus"></i>
                                        <span>Click to view</span>
                                      </div>
                                    </div>
                                    <!-- PDF Preview -->
                                    <div v-else-if="isPdfFile(document.mime_type)" class="pdf-preview">
                                      <div class="pdf-icon">
                                        <i class="fas fa-file-pdf fa-3x text-danger"></i>
                                      </div>
                                      <div class="pdf-info">
                                        <p class="mb-1 fw-bold">{{ document.document_name }}</p>
                                        <small class="text-muted">{{ formatFileSize(document.file_size) }}</small>
                                      </div>
                                      <button
                                        class="btn btn-sm btn-outline-primary mt-2"
                                        @click="downloadDocument(document)"
                                      >
                                        <i class="fas fa-download me-1"></i>Download
                                      </button>
                                    </div>
                                    <!-- Other File Types -->
                                    <div v-else class="file-preview">
                                      <div class="file-icon">
                                        <i class="fas fa-file fa-3x text-secondary"></i>
                                      </div>
                                      <div class="file-info">
                                        <p class="mb-1 fw-bold">{{ document.document_name }}</p>
                                        <small class="text-muted">{{ formatFileSize(document.file_size) }}</small>
                                      </div>
                                      <button
                                        class="btn btn-sm btn-outline-primary mt-2"
                                        @click="downloadDocument(document)"
                                      >
                                        <i class="fas fa-download me-1"></i>Download
                                      </button>
                                    </div>
                                  </div>
                                  <div class="document-preview-footer">
                                    <small class="text-muted">
                                      <i class="fas fa-clock me-1"></i>
                                      Uploaded {{ formatDate(document.created_at) }}
                                    </small>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div v-else class="no-documents">
                            <div class="text-center py-4">
                              <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                              <h6 class="text-muted">No Documents Uploaded</h6>
                              <p class="text-muted mb-0">
                                <span v-if="currentRequest.document_type === 'Cedula'">
                                  Cedula requests typically don't require supporting documents.
                                </span>
                                <span v-else>
                                  The client hasn't uploaded any supporting documents yet.
                                </span>
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Right Column - Status Management -->
                    <div class="col-lg-4">
                      <!-- Status Management -->
                      <div class="card mb-4">
                        <div class="card-header">
                          <h6 class="mb-0"><i class="fas fa-tasks me-2"></i>Status Management</h6>
                        </div>
                        <div class="card-body">
                          <div class="mb-3">
                            <label class="form-label fw-bold">Change Status</label>
                            <select
                              class="form-select"
                              v-model="statusUpdateForm.status_id"
                              :disabled="getAvailableStatusOptions().length === 0"
                            >
                              <option value="">
                                {{ getAvailableStatusOptions().length === 0 ? 'No status changes available' : 'Select new status' }}
                              </option>
                              <option v-for="status in getAvailableStatusOptions()" :key="status.id" :value="status.id">
                                {{ formatStatus(status.status_name) }}
                              </option>
                            </select>
                            <div v-if="getAvailableStatusOptions().length === 0" class="form-text text-muted">
                              <i class="fas fa-info-circle me-1"></i>
                              This request status cannot be changed ({{ formatStatus(currentRequest.status_name) }})
                            </div>
                          </div>

                          <!-- Single Action Button -->
                          <div class="d-grid">
                            <button
                              class="btn btn-primary"
                              @click="updateRequestStatusFromModal"
                              :disabled="!statusUpdateForm.status_id || !isValidStatusChange(currentRequest.status_name, statusUpdateForm.status_id)"
                              :title="getUpdateButtonTitle()"
                            >
                              <i class="fas fa-save me-1"></i>
                              {{ getActionButtonText() }}
                            </button>
                          </div>
                        </div>
                      </div>

                      <!-- Payment Information -->
                      <div class="card mb-4">
                        <div class="card-header">
                          <h6 class="mb-0"><i class="fas fa-credit-card me-2"></i>Payment Information</h6>
                        </div>
                        <div class="card-body">
                          <div class="mb-3">
                            <label class="form-label fw-bold">Payment Method</label>
                            <p class="mb-0">{{ currentRequest.payment_method || 'Not specified' }}</p>
                          </div>
                          <div class="mb-3">
                            <label class="form-label fw-bold">Payment Status</label>
                            <p class="mb-0">
                              <span class="badge" :class="getPaymentStatusColor(currentRequest.payment_status)">
                                {{ formatPaymentStatus(currentRequest.payment_status) }}
                              </span>
                            </p>
                          </div>
                          <div class="row">
                            <div class="col-6">
                              <div class="mb-2">
                                <label class="form-label fw-bold small">Base Fee</label>
                                <p class="mb-0">{{ formatCurrency(currentRequest.base_fee) }}</p>
                              </div>
                            </div>
                            <div class="col-6">
                              <div class="mb-2">
                                <label class="form-label fw-bold small">Additional Fees</label>
                                <p class="mb-0">{{ formatCurrency(currentRequest.additional_fees) }}</p>
                              </div>
                            </div>
                            <div class="col-6">
                              <div class="mb-2">
                                <label class="form-label fw-bold small">Processing Fee</label>
                                <p class="mb-0">{{ formatCurrency(currentRequest.processing_fee) }}</p>
                              </div>
                            </div>
                            <div class="col-6">
                              <div class="mb-2">
                                <label class="form-label fw-bold small">Total Amount</label>
                                <p class="mb-0 fw-bold text-primary">{{ formatCurrency(currentRequest.total_fee) }}</p>
                              </div>
                            </div>
                          </div>

                          <!-- In-Person Payment Verification -->
                          <div v-if="needsPaymentVerification(currentRequest)" class="mt-4 p-3 border rounded bg-light">
                            <h6 class="text-primary mb-3">
                              <i class="fas fa-money-bill me-2"></i>
                              Verify In-Person Payment
                            </h6>
                            <div class="row">
                              <div class="col-md-6">
                                <div class="mb-3">
                                  <label class="form-label fw-bold">Amount Received *</label>
                                  <input
                                    type="number"
                                    class="form-control"
                                    v-model="paymentVerificationForm.amount_received"
                                    :min="currentRequest.total_fee"
                                    step="0.01"
                                    placeholder="Enter amount received"
                                  >
                                </div>
                              </div>
                              <div class="col-md-6">
                                <div class="mb-3">
                                  <label class="form-label fw-bold">Receipt Number</label>
                                  <input
                                    type="text"
                                    class="form-control"
                                    v-model="paymentVerificationForm.receipt_number"
                                    placeholder="Enter receipt number"
                                  >
                                </div>
                              </div>
                            </div>

                            <div class="d-grid">
                              <button
                                class="btn btn-success"
                                @click="verifyInPersonPayment"
                                :disabled="!paymentVerificationForm.amount_received || paymentVerificationForm.loading"
                              >
                                <i class="fas fa-check-circle me-1"></i>
                                <span v-if="paymentVerificationForm.loading">
                                  <i class="fas fa-spinner fa-spin me-1"></i>
                                  Verifying...
                                </span>
                                <span v-else>Verify Payment</span>
                              </button>
                            </div>
                          </div>

                          <!-- Pickup Scheduling -->
                          <div v-if="canSchedulePickup(currentRequest)" class="mt-4 p-3 border rounded bg-light">
                            <h6 class="text-info mb-3">
                              <i class="fas fa-calendar-alt me-2"></i>
                              Schedule Pickup Appointment
                            </h6>
                            <div class="row">
                              <div class="col-md-4">
                                <div class="mb-3">
                                  <label class="form-label fw-bold">Date *</label>
                                  <input
                                    type="date"
                                    class="form-control"
                                    v-model="pickupScheduleForm.scheduled_date"
                                    :min="getTomorrowDate()"
                                  >
                                </div>
                              </div>
                              <div class="col-md-4">
                                <div class="mb-3">
                                  <label class="form-label fw-bold">Start Time *</label>
                                  <input
                                    type="time"
                                    class="form-control"
                                    v-model="pickupScheduleForm.scheduled_time_start"
                                  >
                                </div>
                              </div>
                              <div class="col-md-4">
                                <div class="mb-3">
                                  <label class="form-label fw-bold">End Time *</label>
                                  <input
                                    type="time"
                                    class="form-control"
                                    v-model="pickupScheduleForm.scheduled_time_end"
                                  >
                                </div>
                              </div>
                            </div>

                            <div class="d-grid">
                              <button
                                class="btn btn-info"
                                @click="schedulePickup"
                                :disabled="!isPickupFormValid() || pickupScheduleForm.loading"
                              >
                                <i class="fas fa-calendar-check me-1"></i>
                                <span v-if="pickupScheduleForm.loading">
                                  <i class="fas fa-spinner fa-spin me-1"></i>
                                  Scheduling...
                                </span>
                                <span v-else>Schedule Pickup</span>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Status History Timeline -->
                  <div class="card">
                    <div class="card-header">
                      <h6 class="mb-0"><i class="fas fa-history me-2"></i>Status History</h6>
                    </div>
                    <div class="card-body">
                      <div v-if="currentRequest.status_history && currentRequest.status_history.length > 0" class="timeline">
                        <div
                          v-for="(history, index) in currentRequest.status_history"
                          :key="history.id"
                          class="timeline-item"
                          :class="{ 'timeline-item-last': index === currentRequest.status_history.length - 1 }"
                        >
                          <div class="timeline-marker" :class="`bg-${getStatusColor(history.new_status_name)}`">
                            <i class="fas fa-circle"></i>
                          </div>
                          <div class="timeline-content">
                            <div class="timeline-header">
                              <span class="badge" :class="`bg-${getStatusColor(history.new_status_name)}`">
                                {{ formatStatus(history.new_status_name) }}
                              </span>
                              <small class="text-muted ms-2">{{ formatDateTime(history.changed_at) }}</small>
                            </div>
                            <div class="timeline-body">
                              <p class="mb-1">
                                <strong>Changed by:</strong> {{ history.changed_by_name }}
                              </p>
                              <p v-if="history.old_status_name" class="mb-1">
                                <strong>From:</strong> {{ formatStatus(history.old_status_name) }}
                              </p>
                              <p v-if="history.change_reason" class="mb-0">
                                <strong>Reason:</strong> {{ history.change_reason }}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div v-else class="text-center text-muted py-3">
                        <i class="fas fa-history fa-2x mb-2"></i>
                        <p>No status history available</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" @click="showRequestDetails = false">
                    <i class="fas fa-times me-1"></i>
                    Close
                  </button>
                  <button type="button" class="btn btn-primary" @click="refreshRequestDetails">
                    <i class="fas fa-sync-alt me-1"></i>
                    Refresh
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Reject Modal -->
          <div v-if="showQuickReject && selectedRequestForReject" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
            <div class="modal-dialog">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">
                    <i class="fas fa-times-circle text-danger me-2"></i>
                    Reject Request
                  </h5>
                  <button type="button" class="btn-close" @click="closeQuickRejectModal"></button>
                </div>
                <div class="modal-body">
                  <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    You are about to reject this document request. This action will notify the client immediately.
                  </div>

                  <div class="mb-3">
                    <strong>Request Details:</strong>
                    <ul class="list-unstyled mt-2">
                      <li><strong>Request Number:</strong> {{ selectedRequestForReject.request_number }}</li>
                      <li><strong>Document Type:</strong> {{ selectedRequestForReject.document_type }}</li>
                      <li><strong>Client:</strong> {{ selectedRequestForReject.client_name }}</li>
                    </ul>
                  </div>


                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" @click="closeQuickRejectModal" :disabled="quickRejectForm.loading">
                    <i class="fas fa-times me-1"></i>
                    Cancel
                  </button>
                  <button type="button" class="btn btn-danger" @click="confirmQuickReject" :disabled="quickRejectForm.loading">
                    <i class="fas fa-times-circle me-1"></i>
                    <span v-if="quickRejectForm.loading">
                      <i class="fas fa-spinner fa-spin me-1"></i>
                      Rejecting...
                    </span>
                    <span v-else>Reject Request</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Approve Modal -->
          <div v-if="showQuickApprove && selectedRequestForApprove" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
            <div class="modal-dialog">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    Approve Request
                  </h5>
                  <button type="button" class="btn-close" @click="closeQuickApproveModal"></button>
                </div>
                <div class="modal-body">
                  <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    You are about to approve this document request. This action will notify the client immediately and move the request to the next processing stage.
                  </div>

                  <div class="mb-3">
                    <strong>Request Details:</strong>
                    <ul class="list-unstyled mt-2">
                      <li><strong>Request Number:</strong> {{ selectedRequestForApprove.request_number }}</li>
                      <li><strong>Document Type:</strong> {{ selectedRequestForApprove.document_type }}</li>
                      <li><strong>Client:</strong> {{ selectedRequestForApprove.client_name }}</li>
                    </ul>
                  </div>

                  <div v-if="quickApproveForm.error" class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ quickApproveForm.error }}
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" @click="closeQuickApproveModal" :disabled="quickApproveForm.loading">
                    <i class="fas fa-times me-1"></i>
                    Cancel
                  </button>
                  <button type="button" class="btn btn-success" @click="confirmQuickApprove" :disabled="quickApproveForm.loading">
                    <i class="fas fa-check-circle me-1"></i>
                    <span v-if="quickApproveForm.loading">
                      <i class="fas fa-spinner fa-spin me-1"></i>
                      Approving...
                    </span>
                    <span v-else>Approve Request</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <!-- Image Modal -->
    <div v-if="showImageModal && selectedImage" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.8);" @click.self="closeImageModal">
      <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header sticky-header">
            <h5 class="modal-title">
              <i class="fas fa-image me-2"></i>
              {{ selectedImage.document_name }}
            </h5>
            <div class="header-controls">
              <button
                type="button"
                class="btn btn-outline-light btn-sm me-2"
                @click="downloadDocument(selectedImage)"
                :disabled="!documentUrls[selectedImage.id] || imageLoadingInModal"
                title="Download">
                <i class="fas fa-download"></i>
              </button>
              <button
                type="button"
                class="btn-close btn-close-white"
                @click="closeImageModal"
                aria-label="Close"
                title="Close">
              </button>
            </div>
          </div>
          <div class="modal-body text-center p-0">
            <div class="image-modal-container">
              <!-- Successfully loaded image -->
              <img
                v-if="documentUrls[selectedImage.id] && !imageLoadingInModal"
                :src="documentUrls[selectedImage.id]"
                :alt="selectedImage.document_name"
                class="modal-image"
                @error="handleImageError"
                @load="onModalImageLoad"
                loading="lazy"
              />

              <!-- Loading state -->
              <div v-else-if="imageLoadingInModal || loadingDocuments.has(selectedImage.id)" class="loading-placeholder modal-loading">
                <div class="loading-content">
                  <i class="fas fa-spinner fa-spin fa-3x mb-3"></i>
                  <span class="loading-text">Loading high-resolution image...</span>
                  <div class="loading-progress mt-2">
                    <div class="progress-bar"></div>
                  </div>
                </div>
              </div>

              <!-- Failed state -->
              <div v-else-if="modalImageError || failedDocuments.has(selectedImage.id)" class="error-placeholder modal-error">
                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                <span class="error-text">Failed to load image</span>
                <button
                  class="btn btn-outline-light mt-3"
                  @click="retryLoadDocument(selectedImage)"
                  :disabled="imageLoadingInModal">
                  <i class="fas fa-redo me-2"></i>Retry
                </button>
              </div>

              <!-- Fallback -->
              <div v-else class="loading-placeholder modal-loading">
                <i class="fas fa-image fa-3x mb-3"></i>
                <span class="loading-text">Preparing image...</span>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <div class="d-flex justify-content-between align-items-center w-100">
              <div class="image-info">
                <span class="badge bg-info me-2">{{ getDocumentTypeDisplayName(selectedImage.document_type) }}</span>
                <small class="text-muted">
                  {{ formatFileSize(selectedImage.file_size) }} •
                  Uploaded {{ formatDate(selectedImage.created_at) }}
                </small>
              </div>
              <div class="image-actions">
                <button
                  type="button"
                  class="btn btn-outline-primary me-2"
                  @click="downloadDocument(selectedImage)"
                  :disabled="!documentUrls[selectedImage.id] || imageLoadingInModal">
                  <i class="fas fa-download me-1"></i>Download
                </button>
                <button type="button" class="btn btn-secondary" @click="closeImageModal">
                  <i class="fas fa-times me-1"></i>Close
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<script>
import AdminHeader from './AdminHeader.vue';
import AdminSidebar from './AdminSidebar.vue';
import adminAuthService from '@/services/adminAuthService';
import adminDocumentService from '@/services/adminDocumentService';
import api from '@/services/api';
import notificationService from '@/services/notificationService';

export default {
  name: 'AdminRequests',
  components: {
    AdminHeader,
    AdminSidebar
  },



  data() {
    return {
      // UI State
      loading: true,
      sidebarCollapsed: false,
      showUserDropdown: false,
      isMobile: false,
      adminData: null,
      errorMessage: '',
      viewMode: 'table', // 'card' or 'table' - default to table view

      // Request Management Data
      requests: [],
      selectedRequests: [],
      currentRequest: null,
      statusOptions: [],

      // Pagination
      pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        itemsPerPage: 10
      },

      // Filters
      filters: {
        status: '',
        document_type: '',
        priority: '',
        search: '',
        date_from: '',
        date_to: ''
      },

      // Statistics
      requestStats: {
        total: 0,
        pending: 0,
        approved: 0,
        completed: 0,
        thisMonth: 0
      },

      // UI State
      showFilters: false,
      showBulkActions: false,
      showRequestDetails: false,
      showRejectForm: false,
      showQuickReject: false,
      showQuickApprove: false,
      showImageModal: false,
      selectedImage: null,
      bulkAction: '',
      documentUrls: {}, // Store blob URLs for documents
      loadingDocuments: new Set(), // Track which documents are currently loading
      failedDocuments: new Set(), // Track which documents failed to load
      imageLoadingInModal: false, // Track if modal image is loading
      modalImageError: false, // Track if modal image failed

      // Status Update Forms
      statusUpdateForm: {
        status_id: ''
      },
      rejectForm: {
        reason: ''
      },
      quickRejectForm: {
        loading: false,
        error: ''
      },
      selectedRequestForReject: null,
      quickApproveForm: {
        loading: false,
        error: ''
      },
      selectedRequestForApprove: null,

      // Payment verification form
      paymentVerificationForm: {
        amount_received: '',
        receipt_number: '',
        loading: false,
        error: ''
      },

      // Pickup scheduling form
      pickupScheduleForm: {
        scheduled_date: '',
        scheduled_time_start: '',
        scheduled_time_end: '',
        loading: false,
        error: ''
      },

      // Real-time features
      refreshInterval: null,
      autoRefreshEnabled: true,
      refreshRate: 30000, // 30 seconds
      lastRefresh: null
    };
  },

  async mounted() {
    // Check authentication
    if (!adminAuthService.isLoggedIn()) {
      this.$router.push('/admin/login');
      return;
    }

    // Initialize UI state
    this.initializeUI();

    // Load component data
    await this.loadComponentData();

    // Initialize real-time features
    this.initializeRealTimeFeatures();
  },

  beforeUnmount() {
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
    }

    // Clean up real-time features
    this.cleanupRealTimeFeatures();

    // Clean up blob URLs to prevent memory leaks
    this.cleanupDocumentUrls();
  },

  computed: {
    activeMenu() {
      const path = this.$route.path;
      if (path.includes('/admin/users')) return 'users';
      if (path.includes('/admin/requests')) return 'requests';
      if (path.includes('/admin/reports')) return 'reports';
      if (path.includes('/admin/settings')) return 'settings';
      if (path.includes('/admin/activity-logs')) return 'activity';
      if (path.includes('/admin/profile')) return 'profile';
      return 'dashboard';
    }
  },

  methods: {
    // Initialize UI state
    initializeUI() {
      this.isMobile = window.innerWidth <= 768;

      // Load saved sidebar state (only on desktop)
      if (!this.isMobile) {
        const saved = localStorage.getItem('adminSidebarCollapsed');
        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;
      } else {
        this.sidebarCollapsed = true; // Always collapsed on mobile
      }

      // Setup resize listener
      this.handleResize = () => {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;

        if (this.isMobile && !wasMobile) {
          this.sidebarCollapsed = true; // Collapse when switching to mobile
        } else if (!this.isMobile && wasMobile) {
          // Restore saved state when switching to desktop
          const saved = localStorage.getItem('adminSidebarCollapsed');
          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;
        }
      };
      window.addEventListener('resize', this.handleResize);
    },

    // Sidebar toggle
    handleSidebarToggle() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));
    },

    // Menu navigation
    handleMenuChange(menu) {
      const routes = {
        'dashboard': '/admin/dashboard',
        'users': '/admin/users',
        'requests': '/admin/requests',
        'reports': '/admin/reports',
        'settings': '/admin/settings',
        'activity': '/admin/activity-logs',
        'profile': '/admin/profile'
      };

      // Close sidebar on mobile after navigation
      if (this.isMobile) {
        this.sidebarCollapsed = true;
      }

      if (routes[menu]) {
        this.$router.push(routes[menu]);
      }
    },

    // User dropdown toggle
    handleUserDropdownToggle() {
      this.showUserDropdown = !this.showUserDropdown;
    },

    // Handle opening request modal from notifications
    async handleOpenRequestModal(modalData) {
      console.log('🔔 AdminRequests: Opening request modal from notification:', modalData);

      try {
        const { requestId, focusTab } = modalData;

        if (!requestId) {
          console.error('❌ No request ID provided for modal');
          return;
        }

        // Use the existing viewRequestDetails method to open the modal
        await this.viewRequestDetails(requestId);

        // If a specific tab should be focused, handle that after modal opens
        if (focusTab) {
          // Wait a bit for the modal to fully render
          setTimeout(() => {
            this.focusModalTab(focusTab);
          }, 300);
        }

        console.log('✅ Request modal opened successfully');

      } catch (error) {
        console.error('❌ Error opening request modal:', error);
        // Show error message to user
        this.showErrorMessage('Failed to open request details');
      }
    },

    // Focus on a specific tab in the request details modal
    focusModalTab(tabName) {
      try {
        console.log('🎯 Focusing on modal tab:', tabName);

        // Map tab names to actual tab elements or actions
        const tabMappings = {
          'payment': () => {
            // Focus on payment section in the modal
            const paymentSection = document.querySelector('#requestDetailsModal .payment-section');
            if (paymentSection) {
              paymentSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
              paymentSection.classList.add('highlight-section');
              setTimeout(() => paymentSection.classList.remove('highlight-section'), 2000);
            }
          },
          'status': () => {
            // Focus on status section
            const statusSection = document.querySelector('#requestDetailsModal .status-section');
            if (statusSection) {
              statusSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
              statusSection.classList.add('highlight-section');
              setTimeout(() => statusSection.classList.remove('highlight-section'), 2000);
            }
          },
          'documents': () => {
            // Focus on documents section
            const documentsSection = document.querySelector('#requestDetailsModal .documents-section');
            if (documentsSection) {
              documentsSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
              documentsSection.classList.add('highlight-section');
              setTimeout(() => documentsSection.classList.remove('highlight-section'), 2000);
            }
          }
        };

        const focusAction = tabMappings[tabName];
        if (focusAction) {
          focusAction();
        } else {
          console.log('⚠️ Unknown tab name:', tabName);
        }

      } catch (error) {
        console.error('❌ Error focusing modal tab:', error);
      }
    },

    // Menu actions
    handleMenuAction(action) {
      if (action === 'profile') {
        this.$router.push('/admin/profile');
      } else if (action === 'settings') {
        this.$router.push('/admin/settings');
      }
      this.showUserDropdown = false;
    },

    // Close mobile sidebar
    closeMobileSidebar() {
      if (this.isMobile) {
        this.sidebarCollapsed = true;
      }
    },

    // Logout
    handleLogout() {
      adminAuthService.logout();
      this.$router.push('/admin/login');
    },

    // Load admin profile
    async loadAdminProfile() {
      try {
        const response = await adminAuthService.getProfile();
        if (response.success) {
          this.adminData = response.data;
        }
      } catch (error) {
        console.error('Failed to load admin profile:', error);
        this.adminData = adminAuthService.getAdminData();
      }
    },

    // Load all component data
    async loadComponentData() {
      this.loading = true;
      try {
        await Promise.all([
          this.loadAdminProfile(),
          this.loadStatusOptions(),
          this.loadRequests(),
          this.loadDashboardStats()
        ]);
      } catch (error) {
        console.error('Failed to load component data:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to load request data';

        if (errorData.status === 401) {
          adminAuthService.logout();
          this.$router.push('/admin/login');
        }
      } finally {
        this.loading = false;
      }
    },

    // Load status options
    async loadStatusOptions() {
      try {
        console.log('🔄 Loading status options...');
        const response = await adminDocumentService.getStatusOptions();
        console.log('📋 Status options response:', response);

        if (response.success) {
          this.statusOptions = response.data || [];
          console.log('✅ Status options loaded:', this.statusOptions);
        } else {
          console.error('❌ Failed to load status options:', response.message);
          this.statusOptions = [];
        }
      } catch (error) {
        console.error('❌ Error loading status options:', error);
        this.statusOptions = [];
        this.showToast('Error', 'Failed to load status options', 'error');
      }
    },

    // Load dashboard statistics
    async loadDashboardStats() {
      try {
        console.log('🔄 Loading dashboard stats...');
        const response = await adminDocumentService.getDashboardStats();
        console.log('📊 Dashboard stats response:', response);

        if (response.success) {
          // Map the backend response structure to frontend expectations
          const data = response.data;
          this.requestStats = {
            total: data.overview?.total_requests || 0,
            pending: data.overview?.pending_requests || 0,
            approved: data.overview?.approved_requests || 0,
            completed: data.overview?.completed_requests || 0,
            thisMonth: data.time_based?.today_requests || 0
          };
          console.log('✅ Request stats updated:', this.requestStats);
        } else {
          console.error('❌ Failed to load dashboard stats:', response.message);
        }
      } catch (error) {
        console.error('❌ Error loading dashboard stats:', error);
        // Set default values on error
        this.requestStats = {
          total: 0,
          pending: 0,
          approved: 0,
          completed: 0,
          thisMonth: 0
        };
      }
    },

    // Load requests with current filters and pagination
    async loadRequests() {
      try {
        const params = {
          page: this.pagination.currentPage,
          limit: this.pagination.itemsPerPage,
          ...this.filters
        };

        const response = await adminDocumentService.getAllRequests(params);
        if (response.success) {
          this.requests = response.data.requests || [];
          this.pagination = {
            currentPage: response.data.pagination?.current_page || 1,
            totalPages: response.data.pagination?.total_pages || 1,
            totalItems: response.data.pagination?.total_records || 0,
            itemsPerPage: response.data.pagination?.per_page || 10
          };
        }
      } catch (error) {
        console.error('Failed to load requests:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to load requests';
        this.requests = [];
      }
    },

    // Filter and search methods
    applyFilters() {
      this.pagination.currentPage = 1;
      this.loadRequests();
    },

    clearFilters() {
      this.filters = {
        status: '',
        document_type: '',
        priority: '',
        search: '',
        date_from: '',
        date_to: ''
      };
      this.applyFilters();
    },

    // Pagination methods
    changePage(page) {
      if (page >= 1 && page <= this.pagination.totalPages) {
        this.pagination.currentPage = page;
        this.loadRequests();
      }
    },

    changeItemsPerPage(itemsPerPage) {
      this.pagination.itemsPerPage = itemsPerPage;
      this.pagination.currentPage = 1;
      this.loadRequests();
    },

    goBack() {
      this.$router.push('/admin/dashboard');
    },

    // Request selection methods
    toggleRequestSelection(requestId) {
      const index = this.selectedRequests.indexOf(requestId);
      if (index > -1) {
        this.selectedRequests.splice(index, 1);
      } else {
        this.selectedRequests.push(requestId);
      }
    },

    selectAllRequests() {
      if (this.selectedRequests.length === this.requests.length) {
        this.selectedRequests = [];
      } else {
        this.selectedRequests = this.requests.map(r => r.id);
      }
    },

    // Request details
    async viewRequestDetails(requestId) {
      console.log('🚀 View details clicked for request ID:', requestId);

      try {
        const response = await adminDocumentService.getRequestDetails(requestId);
        console.log('📋 API Response received:', response);

        if (response.success) {
          console.log('✅ Response successful, data:', response.data);

          // Debug client profile fields
          const data = response.data;
          console.log('🎯 COMPLETE RESPONSE DATA:', data);
          console.log('🎯 ALL DATA KEYS:', Object.keys(data));
          console.log('🎯 CLIENT PROFILE FIELDS DEBUG:');
          console.log('   Birth Date:', data.client_birth_date);
          console.log('   Gender:', data.client_gender);
          console.log('   Civil Status ID:', data.client_civil_status_id);
          console.log('   Nationality:', data.client_nationality);
          console.log('   Years of Residency:', data.client_years_of_residency);
          console.log('   Months of Residency:', data.client_months_of_residency);

          // Check if fields exist with different names
          console.log('🔍 SEARCHING FOR SIMILAR FIELDS:');
          Object.keys(data).forEach(key => {
            if (key.includes('birth') || key.includes('gender') || key.includes('civil') ||
                key.includes('nationality') || key.includes('residency')) {
              console.log(`   Found: ${key} = ${data[key]}`);
            }
          });

          this.currentRequest = response.data;
          this.showRequestDetails = true;
          // Reset forms
          this.statusUpdateForm = { status_id: '' };
          this.rejectForm = { reason: '' };
          this.showRejectForm = false;
          console.log('📋 Request details loaded:', response.data);

          // Load document URLs for images
          if (response.data.uploaded_documents && response.data.uploaded_documents.length > 0) {
            this.loadDocumentUrls(response.data.uploaded_documents);
          }

          this.showToast('Success', `Request details loaded for ${response.data.request_number}`, 'success');
        }
      } catch (error) {
        console.error('Failed to load request details:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to load request details';
        this.showToast('Error', 'Failed to load request details', 'error');
      }
    },

    // Refresh request details
    async refreshRequestDetails() {
      if (this.currentRequest) {
        await this.viewRequestDetails(this.currentRequest.id);
      }
    },

    // Update request status from modal
    async updateRequestStatusFromModal() {
      console.log('🔄 Updating request status...');
      console.log('📋 Status form data:', this.statusUpdateForm);
      console.log('📋 Current request:', this.currentRequest);

      if (!this.statusUpdateForm.status_id || !this.currentRequest) {
        console.error('❌ Missing required data for status update');
        this.showToast('Error', 'Please select a status to update', 'error');
        return;
      }

      // Enhanced debugging for status validation
      const currentStatus = this.currentRequest.status_name;
      const newStatusId = this.statusUpdateForm.status_id;
      const newStatus = this.statusOptions.find(s => s.id == newStatusId);

      console.log('🔍 Status validation debug:');
      console.log('   Current status:', currentStatus);
      console.log('   New status ID:', newStatusId, '(type:', typeof newStatusId, ')');
      console.log('   New status object:', newStatus);
      console.log('   Available transitions:', this.getAllowedStatusTransitions(currentStatus.toLowerCase()));
      console.log('   Available status options:', this.getAvailableStatusOptions());
      console.log('   All status options:', this.statusOptions);

      if (!this.isValidStatusChange(currentStatus, newStatusId)) {
        console.error('❌ Invalid status change attempted');
        console.error('   From:', currentStatus, 'To:', newStatus?.status_name);
        this.showToast('Error', 'This status change is not allowed', 'error');
        return;
      }

      try {
        const updateData = {
          status_id: parseInt(this.statusUpdateForm.status_id)
        };

        console.log('📤 Sending status update:', updateData);

        const response = await adminDocumentService.updateRequestStatus(
          this.currentRequest.id,
          updateData
        );

        console.log('📥 Status update response:', response);

        if (response.success) {
          // Refresh the request details
          await this.refreshRequestDetails();
          // Refresh the main requests list
          await this.loadRequests();
          // Reset form
          this.statusUpdateForm = { status_id: '' };

          // Show success message
          this.errorMessage = '';
          this.showToast('Success', 'Request status updated successfully', 'success');
        } else {
          console.error('❌ Status update failed:', response.message);
          this.showToast('Error', response.message || 'Failed to update request status', 'error');
        }
      } catch (error) {
        console.error('❌ Error updating request status:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to update request status';
        this.showToast('Error', errorData.message || 'Failed to update request status', 'error');
      }
    },



    // Reject request from modal
    async rejectRequestFromModal() {
      if (!this.currentRequest || !this.rejectForm.reason.trim()) return;

      try {
        const response = await adminDocumentService.rejectRequest(
          this.currentRequest.id,
          { reason: this.rejectForm.reason }
        );

        if (response.success) {
          await this.refreshRequestDetails();
          await this.loadRequests();
          this.rejectForm = { reason: '' };
          this.showRejectForm = false;
        }
      } catch (error) {
        console.error('Failed to reject request:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to reject request';
      }
    },

    // Status update methods
    async updateRequestStatus(requestId, statusId, reason = '') {
      try {
        const response = await adminDocumentService.updateRequestStatus(requestId, {
          status_id: statusId,
          reason: reason
        });

        if (response.success) {
          await this.loadRequests();
          await this.loadDashboardStats();
          this.errorMessage = '';
        }
      } catch (error) {
        console.error('Failed to update request status:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to update request status';
      }
    },

    async approveRequest(requestId, reason = '') {
      try {
        const response = await adminDocumentService.approveRequest(requestId, { reason });
        if (response.success) {
          await this.loadRequests();
          await this.loadDashboardStats();
          this.errorMessage = '';
        }
      } catch (error) {
        console.error('Failed to approve request:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to approve request';
      }
    },

    async rejectRequest(requestId, reason) {
      if (!reason || reason.trim() === '') {
        this.errorMessage = 'Rejection reason is required';
        return;
      }

      try {
        const response = await adminDocumentService.rejectRequest(requestId, { reason });
        if (response.success) {
          await this.loadRequests();
          await this.loadDashboardStats();
          this.errorMessage = '';
        }
      } catch (error) {
        console.error('Failed to reject request:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to reject request';
      }
    },

    // Quick approval/rejection methods
    canApprove(request) {
      // Can approve if 'approved' is in allowed transitions
      const allowedTransitions = this.getAllowedStatusTransitions(request.status_name.toLowerCase());
      return allowedTransitions.includes('approved');
    },

    canReject(request) {
      // Can reject if 'rejected' is in allowed transitions
      const allowedTransitions = this.getAllowedStatusTransitions(request.status_name.toLowerCase());
      return allowedTransitions.includes('rejected');
    },

    // Helper method to get status explanation for disabled buttons
    getStatusExplanation(request, action) {
      const status = request.status_name.toLowerCase();
      const allowedTransitions = this.getAllowedStatusTransitions(status);

      if (action === 'approve') {
        if (allowedTransitions.includes('approved')) {
          return 'Click to approve this request';
        } else if (status === 'approved') {
          return 'This request has already been approved';
        } else if (status === 'rejected') {
          return 'Rejected requests can be resubmitted, not directly approved';
        } else if (status === 'completed') {
          return 'This request has already been completed';
        } else {
          return `Cannot approve from ${this.formatStatus(status)} status`;
        }
      } else if (action === 'reject') {
        if (allowedTransitions.includes('rejected')) {
          return 'Click to reject this request';
        } else if (status === 'rejected') {
          return 'This request has already been rejected';
        } else if (status === 'completed') {
          return 'Cannot reject a completed request';
        } else {
          return `Cannot reject from ${this.formatStatus(status)} status`;
        }
      }

      return `Request status: ${this.formatStatus(status)}`;
    },

    // Check if status change is valid
    isValidStatusChange(currentStatus, newStatusId) {
      if (!currentStatus || !newStatusId) return false;

      // Find the new status name
      const newStatus = this.statusOptions.find(s => s.id == newStatusId);
      if (!newStatus) return false;

      const currentStatusName = currentStatus.toLowerCase();
      const newStatusName = newStatus.status_name.toLowerCase();

      // Same status - no change needed
      if (currentStatusName === newStatusName) {
        return false;
      }

      // Check if transition is allowed based on workflow rules
      const allowedTransitions = this.getAllowedStatusTransitions(currentStatusName);

      return allowedTransitions.includes(newStatusName);
    },

    // Check if request needs payment verification
    needsPaymentVerification(request) {
      return request.status_name === 'payment_pending' &&
             request.payment_method &&
             !request.payment_method.includes('PayMongo') &&
             request.payment_status !== 'paid';
    },

    // Check if pickup can be scheduled
    canSchedulePickup(request) {
      return request.status_name === 'ready_for_pickup';
    },

    // Get payment status color
    getPaymentStatusColor(status) {
      const colors = {
        'pending': 'bg-warning',
        'processing': 'bg-info',
        'paid': 'bg-success',
        'failed': 'bg-danger',
        'refunded': 'bg-secondary',
        'cancelled': 'bg-dark'
      };
      return colors[status] || 'bg-secondary';
    },

    // Format payment status
    formatPaymentStatus(status) {
      const statuses = {
        'pending': 'Pending',
        'processing': 'Processing',
        'paid': 'Paid',
        'failed': 'Failed',
        'refunded': 'Refunded',
        'cancelled': 'Cancelled'
      };
      return statuses[status] || 'Unknown';
    },

    // Get tomorrow's date for pickup scheduling
    getTomorrowDate() {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      return tomorrow.toISOString().split('T')[0];
    },

    // Validate pickup form
    isPickupFormValid() {
      return this.pickupScheduleForm.scheduled_date &&
             this.pickupScheduleForm.scheduled_time_start &&
             this.pickupScheduleForm.scheduled_time_end &&
             this.pickupScheduleForm.scheduled_time_start < this.pickupScheduleForm.scheduled_time_end;
    },

    // Get filtered status options based on current status
    getAvailableStatusOptions() {
      if (!this.currentRequest || !this.statusOptions) return [];

      const currentStatus = this.currentRequest.status_name.toLowerCase();

      // Only these states are truly final (cannot be changed)
      const finalStates = ['completed', 'cancelled'];

      // If current status is final, no changes allowed
      if (finalStates.includes(currentStatus)) {
        return [];
      }

      // Define allowed transitions based on current status
      const allowedTransitions = this.getAllowedStatusTransitions(currentStatus);

      // Return only allowed status options
      return this.statusOptions.filter(status =>
        allowedTransitions.includes(status.status_name.toLowerCase())
      );
    },

    // Define allowed status transitions based on government workflow best practices
    // This must match the backend validateStatusTransition logic exactly
    getAllowedStatusTransitions(currentStatus) {
      const transitions = {
        'pending': ['under_review', 'approved', 'cancelled', 'rejected'],
        'under_review': ['approved', 'rejected', 'cancelled'], // Removed additional_info_required
        // Removed 'additional_info_required' status entirely
        'approved': ['payment_pending', 'cancelled'], // Updated to match backend: approved must go to payment_pending first
        'payment_pending': ['payment_confirmed', 'payment_failed', 'cancelled'],
        'payment_confirmed': ['processing'], // Automatic transition after payment
        'payment_failed': ['payment_pending', 'cancelled'],
        'processing': ['ready_for_pickup'], // Processing can only complete successfully
        'ready_for_pickup': ['pickup_scheduled', 'completed', 'cancelled'],
        'pickup_scheduled': ['completed', 'ready_for_pickup', 'cancelled'], // Can reschedule
        'rejected': ['pending', 'under_review'], // Allow resubmission after corrections
        // Final states - no transitions allowed
        'completed': [],
        'cancelled': []
      };

      return transitions[currentStatus] || [];
    },

    // Get title for update button based on validation state
    getUpdateButtonTitle() {
      if (!this.statusUpdateForm.status_id) {
        return 'Please select a new status';
      }
      if (!this.isValidStatusChange(this.currentRequest.status_name, this.statusUpdateForm.status_id)) {
        return 'Invalid status change';
      }
      return 'Update request status';
    },

    // Get dynamic button text based on selected status
    getActionButtonText() {
      if (!this.statusUpdateForm.status_id) {
        return 'Update Status';
      }

      const selectedStatus = this.statusOptions.find(s => s.id === parseInt(this.statusUpdateForm.status_id));
      if (!selectedStatus) {
        return 'Update Status';
      }

      const statusName = selectedStatus.status_name.toLowerCase();

      // Special button text for common actions
      switch (statusName) {
        case 'approved':
          return 'Approve Request';
        case 'rejected':
          return 'Reject Request';
        case 'under_review':
          return 'Move to Review';
        case 'processing':
          return 'Start Processing';
        case 'ready_for_pickup':
          return 'Mark Ready for Pickup';
        case 'completed':
          return 'Complete Request';
        default:
          return `Update to ${selectedStatus.status_name}`;
      }
    },

    async quickApprove(request) {
      console.log('🚀 Quick approve clicked for request:', request);

      try {
        this.loading = true;
        const response = await adminDocumentService.approveRequest(request.id, {
          reason: 'Quick approval from admin interface'
        });

        if (response.success) {
          await this.loadRequests();
          await this.loadDashboardStats();
          this.showToast('Success', `Request ${request.request_number} approved successfully`, 'success');
        }
      } catch (error) {
        console.error('Failed to approve request:', error);
        const errorData = adminDocumentService.parseError(error);
        this.showToast('Error', errorData.message || 'Failed to approve request', 'error');
      } finally {
        this.loading = false;
      }
    },

    showQuickRejectModal(request) {
      console.log('🚀 Quick reject clicked for request:', request);

      this.selectedRequestForReject = request;
      this.quickRejectForm = {
        loading: false,
        error: ''
      };
      this.showQuickReject = true;
    },

    closeQuickRejectModal() {
      this.showQuickReject = false;
      this.selectedRequestForReject = null;
      this.quickRejectForm = {
        loading: false,
        error: ''
      };
    },

    async confirmQuickReject() {
      this.quickRejectForm.loading = true;
      this.quickRejectForm.error = '';

      try {
        const response = await adminDocumentService.rejectRequest(
          this.selectedRequestForReject.id,
          { reason: 'Request rejected by admin' }
        );

        if (response.success) {
          await this.loadRequests();
          await this.loadDashboardStats();
          this.showToast('Success', `Request ${this.selectedRequestForReject.request_number} rejected successfully`, 'success');
          this.closeQuickRejectModal();
        }
      } catch (error) {
        console.error('Failed to reject request:', error);
        const errorData = adminDocumentService.parseError(error);
        this.quickRejectForm.error = errorData.message || 'Failed to reject request';
      } finally {
        this.quickRejectForm.loading = false;
      }
    },

    showQuickApproveModal(request) {
      console.log('🚀 Quick approve clicked for request:', request);

      this.selectedRequestForApprove = request;
      this.quickApproveForm = {
        loading: false,
        error: ''
      };
      this.showQuickApprove = true;
    },

    closeQuickApproveModal() {
      this.showQuickApprove = false;
      this.selectedRequestForApprove = null;
      this.quickApproveForm = {
        loading: false,
        error: ''
      };
    },

    async confirmQuickApprove() {
      this.quickApproveForm.loading = true;
      this.quickApproveForm.error = '';

      try {
        const response = await adminDocumentService.approveRequest(
          this.selectedRequestForApprove.id,
          { reason: 'Quick approval from admin interface' }
        );

        if (response.success) {
          await this.loadRequests();
          await this.loadDashboardStats();
          this.showToast('Success', `Request ${this.selectedRequestForApprove.request_number} approved successfully`, 'success');
          this.closeQuickApproveModal();
        }
      } catch (error) {
        console.error('Failed to approve request:', error);
        const errorData = adminDocumentService.parseError(error);
        this.quickApproveForm.error = errorData.message || 'Failed to approve request';
      } finally {
        this.quickApproveForm.loading = false;
      }
    },

    // Bulk operations
    async performBulkAction() {
      if (this.selectedRequests.length === 0) {
        this.errorMessage = 'Please select at least one request';
        return;
      }

      if (!this.bulkAction) {
        this.errorMessage = 'Please select a bulk action';
        return;
      }

      try {
        const response = await adminDocumentService.bulkUpdateRequests({
          request_ids: this.selectedRequests,
          status_id: parseInt(this.bulkAction)
        });

        if (response.success) {
          await this.loadRequests();
          await this.loadDashboardStats();
          this.selectedRequests = [];
          this.bulkAction = '';
          this.showBulkActions = false;
          this.errorMessage = '';
        }
      } catch (error) {
        console.error('Failed to perform bulk action:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to perform bulk action';
      }
    },

    // Export functionality
    async exportRequests() {
      try {
        const csvData = await adminDocumentService.exportRequests(this.filters);
        const filename = `document_requests_${new Date().toISOString().split('T')[0]}.csv`;
        adminDocumentService.downloadCSV(csvData, filename);
      } catch (error) {
        console.error('Failed to export requests:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to export requests';
      }
    },

    // Verify in-person payment
    async verifyInPersonPayment() {
      if (!this.paymentVerificationForm.amount_received || !this.currentRequest) {
        this.showToast('Error', 'Please enter the amount received', 'error');
        return;
      }

      const totalFee = parseFloat(this.currentRequest.total_fee);
      const amountReceived = parseFloat(this.paymentVerificationForm.amount_received);

      if (amountReceived < totalFee) {
        this.showToast('Error', `Insufficient payment. Required: ${this.formatCurrency(totalFee)}`, 'error');
        return;
      }

      this.paymentVerificationForm.loading = true;
      this.paymentVerificationForm.error = '';

      try {
        const paymentData = {
          amount_received: amountReceived,
          payment_method_id: this.currentRequest.payment_method_id || 1, // Default to cash
          receipt_number: this.paymentVerificationForm.receipt_number
        };

        const response = await adminDocumentService.verifyInPersonPayment(this.currentRequest.id, paymentData);

        if (response.success) {
          await this.refreshRequestDetails();
          await this.loadRequests();

          // Reset form
          this.paymentVerificationForm = {
            amount_received: '',
            receipt_number: '',
            loading: false,
            error: ''
          };

          this.showToast('Success', 'Payment verified successfully', 'success');
        }
      } catch (error) {
        console.error('Failed to verify payment:', error);
        const errorData = adminDocumentService.parseError(error);
        this.paymentVerificationForm.error = errorData.message || 'Failed to verify payment';
        this.showToast('Error', errorData.message || 'Failed to verify payment', 'error');
      } finally {
        this.paymentVerificationForm.loading = false;
      }
    },

    // Schedule pickup appointment
    async schedulePickup() {
      if (!this.isPickupFormValid() || !this.currentRequest) {
        this.showToast('Error', 'Please fill in all required fields', 'error');
        return;
      }

      this.pickupScheduleForm.loading = true;
      this.pickupScheduleForm.error = '';

      try {
        const scheduleData = {
          scheduled_date: this.pickupScheduleForm.scheduled_date,
          scheduled_time_start: this.pickupScheduleForm.scheduled_time_start,
          scheduled_time_end: this.pickupScheduleForm.scheduled_time_end
        };

        const response = await adminDocumentService.schedulePickup(this.currentRequest.id, scheduleData);

        if (response.success) {
          await this.refreshRequestDetails();
          await this.loadRequests();

          // Reset form
          this.pickupScheduleForm = {
            scheduled_date: '',
            scheduled_time_start: '',
            scheduled_time_end: '',
            loading: false,
            error: ''
          };

          this.showToast('Success', 'Pickup scheduled successfully', 'success');
        }
      } catch (error) {
        console.error('Failed to schedule pickup:', error);
        const errorData = adminDocumentService.parseError(error);
        this.pickupScheduleForm.error = errorData.message || 'Failed to schedule pickup';
        this.showToast('Error', errorData.message || 'Failed to schedule pickup', 'error');
      } finally {
        this.pickupScheduleForm.loading = false;
      }
    },

    // Utility methods
    formatStatus(status) {
      return adminDocumentService.formatStatus(status);
    },

    getStatusColor(status) {
      return adminDocumentService.getStatusColor(status);
    },

    formatDate(dateString) {
      console.log('🗓️ formatDate called with:', dateString);
      if (!dateString) {
        console.log('🗓️ formatDate: No date provided, returning null');
        return null;
      }
      const date = new Date(dateString);
      const formatted = date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      console.log('🗓️ formatDate result:', formatted);
      return formatted;
    },

    formatCurrency(amount) {
      return new Intl.NumberFormat('en-PH', {
        style: 'currency',
        currency: 'PHP'
      }).format(amount || 0);
    },

    formatDateTime(dateString) {
      if (!dateString) return 'N/A';
      return new Date(dateString).toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    // New helper methods for complete client information
    getClientFullName(request) {
      if (!request) return 'Not provided';
      const parts = [
        request.client_first_name,
        request.client_middle_name,
        request.client_last_name,
        request.client_suffix
      ].filter(Boolean);
      return parts.length > 0 ? parts.join(' ') : request.client_name || 'Not provided';
    },

    getClientFullAddress(request) {
      if (!request) return null;
      const parts = [
        request.client_house_number,
        request.client_street,
        request.client_subdivision,
        request.client_barangay,
        request.client_city_municipality || request.client_city,
        request.client_province
      ].filter(Boolean);
      return parts.length > 0 ? parts.join(', ') : (request.client_address || null);
    },

    formatGender(gender) {
      if (!gender) {
        return null;
      }
      return gender.charAt(0).toUpperCase() + gender.slice(1);
    },

    getCivilStatusName(statusId) {
      const statuses = {
        1: 'Single',
        2: 'Married',
        3: 'Divorced',
        4: 'Widowed',
        5: 'Separated'
      };
      return statuses[statusId] || null;
    },

    getResidencyDisplay(request) {
      if (!request) return null;
      const years = request.client_years_of_residency;
      const months = request.client_months_of_residency;

      if (!years && !months) return null; // Return null so the template can handle "Not provided"

      const parts = [];
      if (years) parts.push(`${years} year${years > 1 ? 's' : ''}`);
      if (months) parts.push(`${months} month${months > 1 ? 's' : ''}`);

      return parts.join(' and ');
    },

    formatTime(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    },

    // Real-time features
    async initializeRealTimeFeatures() {
      console.log('Initializing real-time features for AdminRequests');

      try {
        // Initialize notification service
        await notificationService.init('admin');

        // Listen for request-related notifications
        notificationService.on('notification', this.handleRealTimeNotification);
        notificationService.on('request_status_changed', this.handleStatusChange);
        notificationService.on('new_request', this.handleNewRequest);

        // Start auto-refresh if enabled
        if (this.autoRefreshEnabled) {
          this.startAutoRefresh();
        }
      } catch (error) {
        console.error('Failed to initialize real-time features:', error);
      }
    },

    cleanupRealTimeFeatures() {
      console.log('Cleaning up real-time features for AdminRequests');

      // Remove notification listeners
      notificationService.off('notification', this.handleRealTimeNotification);
      notificationService.off('request_status_changed', this.handleStatusChange);
      notificationService.off('new_request', this.handleNewRequest);

      // Cleanup (simplified)
      notificationService.cleanup();

      // Stop auto-refresh
      this.stopAutoRefresh();
    },

    startAutoRefresh() {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval);
      }

      this.refreshInterval = setInterval(() => {
        if (this.autoRefreshEnabled && !this.loading) {
          console.log('Auto-refreshing requests data...');
          this.refreshRequestsData();
        }
      }, this.refreshRate);

      console.log(`Auto-refresh started with ${this.refreshRate / 1000}s interval`);
    },

    stopAutoRefresh() {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval);
        this.refreshInterval = null;
        console.log('Auto-refresh stopped');
      }
    },

    toggleAutoRefresh() {
      this.autoRefreshEnabled = !this.autoRefreshEnabled;

      if (this.autoRefreshEnabled) {
        this.startAutoRefresh();
      } else {
        this.stopAutoRefresh();
      }
    },

    async refreshRequestsData() {
      try {
        this.lastRefresh = new Date();

        // Refresh requests list
        await this.loadRequests();

        // Refresh statistics
        await this.loadDashboardStats();

        // If request details modal is open, refresh it
        if (this.showRequestDetails && this.currentRequest) {
          await this.refreshRequestDetails();
        }

        console.log('Requests data refreshed successfully');
      } catch (error) {
        console.error('Failed to refresh requests data:', error);
      }
    },

    handleRealTimeNotification(notification) {
      console.log('Real-time notification received:', notification);

      // Handle different notification types
      switch (notification.type) {
        case 'request_status_changed':
          this.handleStatusChange(notification.data);
          break;
        case 'new_request':
          this.handleNewRequest(notification.data);
          break;
        case 'request_updated':
          this.handleRequestUpdate(notification.data);
          break;
        case 'unread_count_update':
        case 'heartbeat':
          // Polling system notifications - handled by notification service
          break;
        default:
          // Only log unknown types, not system types
          if (!['unread_count_update', 'heartbeat'].includes(notification.type)) {
            console.log('Unhandled notification type:', notification.type);
          }
      }
    },

    handleStatusChange(data) {
      console.log('Request status changed:', data);

      // Update the request in the list if it exists
      const requestIndex = this.requests.findIndex(req => req.id === data.request_id);
      if (requestIndex !== -1) {
        // Refresh the specific request or reload all requests
        this.refreshRequestsData();
      }

      // Show toast notification
      this.showToast('Request status updated', `Request #${data.request_id} status changed to ${data.new_status}`, 'info');
    },

    handleNewRequest(data) {
      console.log('New request received:', data);

      // Refresh requests to show the new request
      this.refreshRequestsData();

      // Show toast notification
      this.showToast('New Request', `New ${data.document_type} request received`, 'success');
    },

    handleRequestUpdate(data) {
      console.log('Request updated:', data);

      // If the updated request is currently being viewed, refresh details
      if (this.currentRequest && this.currentRequest.id === data.request_id) {
        this.refreshRequestDetails();
      }

      // Refresh the requests list
      this.refreshRequestsData();
    },

    showToast(title, message, type = 'info') {
      // Log to console for debugging
      console.log(`[${type.toUpperCase()}] ${title}: ${message}`);

      // Create a simple toast notification
      const toast = document.createElement('div');
      toast.className = `toast-notification toast-${type}`;
      toast.innerHTML = `
        <div class="toast-header">
          <strong>${title}</strong>
          <button type="button" class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
        <div class="toast-body">${message}</div>
      `;

      // Add toast styles if not already added
      if (!document.getElementById('toast-styles')) {
        const styles = document.createElement('style');
        styles.id = 'toast-styles';
        styles.textContent = `
          .toast-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            min-width: 300px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 9999;
            animation: slideIn 0.3s ease;
          }
          .toast-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px 8px;
            border-bottom: 1px solid #e9ecef;
          }
          .toast-body {
            padding: 8px 16px 12px;
            color: #6c757d;
          }
          .toast-close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #6c757d;
          }
          .toast-success { border-left: 4px solid #28a745; }
          .toast-error { border-left: 4px solid #dc3545; }
          .toast-info { border-left: 4px solid #17a2b8; }
          .toast-warning { border-left: 4px solid #ffc107; }
          @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
          }
        `;
        document.head.appendChild(styles);
      }

      // Add toast to page
      document.body.appendChild(toast);

      // Auto-remove after 5 seconds
      setTimeout(() => {
        if (toast.parentElement) {
          toast.style.animation = 'slideIn 0.3s ease reverse';
          setTimeout(() => toast.remove(), 300);
        }
      }, 5000);
    },

    // Document handling methods
    getDocumentTypeDisplayName(type) {
      const displayNames = {
        'government_id': 'Government ID',
        'proof_of_residency': 'Proof of Residency',
        'cedula': 'Community Tax Certificate (Cedula)',
        'birth_certificate': 'Birth Certificate',
        'marriage_certificate': 'Marriage Certificate',
        'other': 'Other Document'
      };
      return displayNames[type] || type;
    },

    isImageFile(mimeType) {
      return mimeType && (
        mimeType.startsWith('image/') ||
        ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'].includes(mimeType)
      );
    },

    isPdfFile(mimeType) {
      return mimeType === 'application/pdf';
    },

    async loadDocumentUrls(documents) {
      // Filter documents that need loading (images only, not already loaded/loading/failed)
      const documentsToLoad = documents.filter(doc =>
        this.isImageFile(doc.mime_type) &&
        !this.documentUrls[doc.id] &&
        !this.loadingDocuments.has(doc.id) &&
        !this.failedDocuments.has(doc.id)
      );

      if (documentsToLoad.length === 0) return;

      // Load documents in parallel with concurrency limit
      const CONCURRENT_LIMIT = 3;
      const chunks = this.chunkArray(documentsToLoad, CONCURRENT_LIMIT);

      for (const chunk of chunks) {
        await Promise.allSettled(
          chunk.map(document => this.loadSingleDocument(document))
        );
      }
    },

    async loadSingleDocument(document, isForModal = false) {
      const docId = document.id;

      try {
        // Mark as loading
        this.loadingDocuments.add(docId);
        if (isForModal) this.imageLoadingInModal = true;

        // Use authenticated API call to get the document
        const response = await api.get(`/documents/view/${docId}`, {
          responseType: 'blob',
          timeout: 15000, // Increased timeout for large images
          onDownloadProgress: (progressEvent) => {
            // Optional: Could emit progress events here
            if (progressEvent.lengthComputable) {
              const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              console.log(`Loading ${docId}: ${percentCompleted}%`);
            }
          }
        });

        // Validate response
        if (!response.data || response.data.size === 0) {
          throw new Error('Empty response received');
        }

        // Check file size and optimize if needed
        const blob = response.data;
        const optimizedBlob = await this.optimizeImageBlob(blob, document.mime_type, isForModal);

        // Create blob URL using requestIdleCallback for better performance
        await this.createBlobUrlWhenIdle(docId, optimizedBlob);

        // Remove from failed set if it was there
        this.failedDocuments.delete(docId);
        if (isForModal) this.modalImageError = false;

      } catch (error) {
        console.warn(`Failed to load document ${docId}:`, error.message);
        this.failedDocuments.add(docId);
        if (isForModal) this.modalImageError = true;

        // Optionally retry after a delay for network errors
        if (error.code === 'NETWORK_ERROR' || error.code === 'ECONNABORTED') {
          setTimeout(() => {
            this.failedDocuments.delete(docId);
          }, 30000); // Retry after 30 seconds
        }
      } finally {
        // Remove from loading set
        this.loadingDocuments.delete(docId);
        if (isForModal) this.imageLoadingInModal = false;
      }
    },

    chunkArray(array, size) {
      const chunks = [];
      for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
      }
      return chunks;
    },

    async getDocumentUrl(document) {
      // This method is now deprecated in favor of loadDocumentUrls
      // Keeping for backward compatibility
      if (this.documentUrls[document.id]) {
        return this.documentUrls[document.id];
      }
      return null;
    },

    formatFileSize(bytes) {
      if (!bytes) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    },

    async openImageModal(document) {
      // Prevent multiple rapid clicks
      if (this.imageLoadingInModal) return;

      // Don't open modal if document failed to load and we're not retrying
      if (this.failedDocuments.has(document.id)) {
        return;
      }

      // Set modal state immediately for responsiveness
      this.selectedImage = document;
      this.showImageModal = true;
      this.modalImageError = false;

      // Use nextTick to ensure DOM is updated before heavy operations
      await this.$nextTick();

      // If image isn't loaded yet, try to load it with modal optimization
      if (!this.documentUrls[document.id] && !this.loadingDocuments.has(document.id)) {
        await this.loadSingleDocument(document, true);
      }
    },

    async retryLoadDocument(document) {
      // Remove from failed set and retry loading
      this.failedDocuments.delete(document.id);
      this.modalImageError = false;
      await this.loadSingleDocument(document, true);
    },

    onModalImageLoad() {
      // Called when modal image finishes loading
      this.imageLoadingInModal = false;
    },

    cleanupDocumentUrls() {
      // Revoke all blob URLs to prevent memory leaks
      Object.values(this.documentUrls).forEach(url => {
        if (url) URL.revokeObjectURL(url);
      });

      // Clear all tracking sets and objects
      this.documentUrls = {};
      this.loadingDocuments.clear();
      this.failedDocuments.clear();
    },

    preloadImage(document) {
      // Preload image on hover for better UX
      if (!this.documentUrls[document.id] &&
          !this.loadingDocuments.has(document.id) &&
          !this.failedDocuments.has(document.id)) {
        this.loadSingleDocument(document, false);
      }
    },

    async optimizeImageBlob(blob, mimeType, isForModal = false) {
      // For very large images, we might want to resize them
      const MAX_SIZE = isForModal ? 5 * 1024 * 1024 : 2 * 1024 * 1024; // 5MB for modal, 2MB for preview

      if (blob.size <= MAX_SIZE) {
        return blob; // No optimization needed
      }

      try {
        // Create image element for resizing
        const img = new Image();
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        return new Promise((resolve) => {
          img.onload = () => {
            // Calculate new dimensions (maintain aspect ratio)
            const maxDimension = isForModal ? 1920 : 800;
            let { width, height } = img;

            if (width > height && width > maxDimension) {
              height = (height * maxDimension) / width;
              width = maxDimension;
            } else if (height > maxDimension) {
              width = (width * maxDimension) / height;
              height = maxDimension;
            }

            // Set canvas size and draw resized image
            canvas.width = width;
            canvas.height = height;
            ctx.drawImage(img, 0, 0, width, height);

            // Convert to blob with compression
            canvas.toBlob(
              (optimizedBlob) => {
                resolve(optimizedBlob || blob); // Fallback to original if optimization fails
              },
              mimeType,
              0.85 // 85% quality
            );
          };

          img.onerror = () => resolve(blob); // Fallback to original
          img.src = URL.createObjectURL(blob);
        });
      } catch (error) {
        console.warn('Image optimization failed:', error);
        return blob; // Fallback to original
      }
    },

    async createBlobUrlWhenIdle(docId, blob) {
      return new Promise((resolve) => {
        const createUrl = () => {
          this.documentUrls[docId] = URL.createObjectURL(blob);
          resolve();
        };

        // Use requestIdleCallback if available, otherwise use setTimeout
        if (window.requestIdleCallback) {
          window.requestIdleCallback(createUrl, { timeout: 1000 });
        } else {
          setTimeout(createUrl, 0);
        }
      });
    },

    closeImageModal() {
      // Prevent rapid clicking during image loading
      if (this.imageLoadingInModal) return;

      this.showImageModal = false;
      this.selectedImage = null;
      this.imageLoadingInModal = false;
      this.modalImageError = false;
    },

    async downloadDocument(documentFile) {
      try {
        // Use authenticated API call to download the document
        const response = await api.get(`/documents/download/${documentFile.id}`, {
          responseType: 'blob'
        });

        // Create a download link
        const blob = new Blob([response.data], { type: documentFile.mime_type });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = documentFile.document_name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      } catch (error) {
        console.error('Failed to download document:', error);
        this.showToast('Error', 'Failed to download document', 'error');
      }
    },

    handleImageError(event) {
      console.error('Failed to load image:', event.target.src);
      // You could set a placeholder image here
      event.target.style.display = 'none';

      // Show error message
      const errorDiv = document.createElement('div');
      errorDiv.className = 'text-center text-muted p-3';
      errorDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i><br>Failed to load image';
      event.target.parentNode.appendChild(errorDiv);
    }
  }
};
</script>

<style scoped>
@import './css/adminDashboard.css';

/* Additional styles specific to AdminRequests */
.admin-requests {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
}

/* Ensure proper spacing for request statistics cards */
.card.border-left-primary {
  border-left: 4px solid #3b82f6 !important;
}

.card.border-left-warning {
  border-left: 4px solid #f59e0b !important;
}

.card.border-left-success {
  border-left: 4px solid #059669 !important;
}

.card.border-left-info {
  border-left: 4px solid #06b6d4 !important;
}

/* Bootstrap utility classes for compatibility */
.text-xs {
  font-size: 0.75rem !important;
}

.text-gray-800 {
  color: #1f2937 !important;
}

.text-gray-300 {
  color: #d1d5db !important;
}

.text-muted {
  color: #6c757d !important;
}

.fw-bold {
  font-weight: 700 !important;
}

.g-0 {
  --bs-gutter-x: 0;
  --bs-gutter-y: 0;
}

.me-2 {
  margin-right: 0.5rem !important;
}

/* Improve button spacing */
.d-flex.gap-2 {
  gap: 0.5rem !important;
}

/* Timeline Styles */
.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e3e6f0;
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item.timeline-item-last::after {
  display: none;
}

.timeline-marker {
  position: absolute;
  left: -2rem;
  top: 0.25rem;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.75rem;
  z-index: 1;
}

.timeline-content {
  background: #f8f9fc;
  border-radius: 8px;
  padding: 1rem;
  border-left: 3px solid #e3e6f0;
}

.timeline-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.timeline-body p {
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.timeline-body p:last-child {
  margin-bottom: 0;
}

/* Modal Styles */
.modal-xl {
  max-width: 1200px;
}

.modal-dialog-scrollable .modal-body {
  max-height: 70vh;
  overflow-y: auto;
}

/* Real-time status indicator styles */
.real-time-status .badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 1rem;
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Card View Styles */
.requests-grid {
  min-height: 400px;
}

.empty-state {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 3rem 2rem;
  margin: 2rem 0;
}

.empty-state-icon {
  opacity: 0.5;
}

.request-card {
  background: #ffffff;
  border: 1px solid #e3e6f0;
  border-radius: 12px;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  transition: all 0.3s ease;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.request-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
  border-color: #5a5c69;
}

.request-card.selected {
  border-color: #4e73df;
  box-shadow: 0 0 0 2px rgba(78, 115, 223, 0.25);
}

.request-card-header {
  padding: 1rem 1.25rem 0.5rem;
  border-bottom: 1px solid #f1f1f1;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.request-card-body {
  padding: 1.25rem;
  flex-grow: 1;
}

.request-card-footer {
  padding: 0.75rem 1.25rem 1.25rem;
  background: #f8f9fa;
  border-top: 1px solid #e3e6f0;
}

.client-avatar {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  color: white;
  font-size: 1.2rem;
}

.client-info h6 {
  color: #2c3e50;
  font-weight: 600;
}

.document-type {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 0.75rem;
  border-left: 4px solid #17a2b8;
}

.document-type .badge {
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 20px;
}

.request-meta {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 0.75rem;
}

.meta-item {
  text-align: center;
}

.meta-item small {
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.request-date {
  padding-top: 0.75rem;
  border-top: 1px solid #e9ecef;
  margin-top: 0.75rem;
}

.request-actions .dropdown-toggle {
  border: none;
  background: transparent;
  color: #6c757d;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
}

.request-actions .dropdown-toggle:hover {
  background: #e9ecef;
  color: #495057;
}

/* View Toggle Styles */
.btn-check:checked + .btn-outline-primary {
  background-color: #4e73df;
  border-color: #4e73df;
  color: white;
}

/* Badge Enhancements */
.badge.bg-info-subtle {
  background-color: #cff4fc !important;
  color: #055160 !important;
  border: 1px solid #b6effb;
}

/* Button Enhancements */
.request-card-footer .btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.request-card-footer .btn:hover {
  transform: translateY(-1px);
}

/* Modern Table Styles */
.modern-table-container {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: visible;
  border: 1px solid #e8ecef;
}

.modern-table-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modern-table-header h5 {
  color: white;
  margin: 0;
  font-weight: 600;
}

.table-actions .btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
}

.table-actions .btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.modern-table-empty {
  padding: 4rem 2rem;
  text-align: center;
  background: #f8f9fa;
}

.empty-content {
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 4rem;
  color: #6c757d;
  margin-bottom: 1.5rem;
  opacity: 0.5;
}

.empty-title {
  color: #495057;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.empty-text {
  color: #6c757d;
  margin: 0;
}

/* Compact Table Styles */
.compact-table-wrapper {
  background: white;
  border-radius: 12px;
  overflow: visible;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8ecef;
}

.compact-table-header {
  display: grid;
  grid-template-columns: 40px 140px 1fr 140px 120px 100px 120px 120px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  gap: 0.5rem;
}

.header-cell {
  display: flex;
  align-items: center;
  padding: 0 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.75rem;
}

.selection-header {
  justify-content: center;
}

.compact-table-body {
  background: white;
  overflow: visible;
}

.compact-row {
  display: grid;
  grid-template-columns: 40px 140px 1fr 140px 120px 100px 120px 120px;
  align-items: center;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #f1f3f4;
  transition: all 0.15s ease;
  position: relative;
  min-height: 48px;
  gap: 0.5rem;
}

.compact-row:hover {
  background: #f8f9fa;
  transform: translateX(2px);
  box-shadow: 2px 0 0 #667eea;
}

.compact-row.selected {
  background: #e3f2fd;
  border-left: 3px solid #2196f3;
}

.compact-row:last-child {
  border-bottom: none;
}

.row-cell {
  display: flex;
  align-items: center;
  padding: 0 0.5rem;
  font-size: 0.875rem;
}

/* Selection Cell */
.selection-cell {
  justify-content: center;
}

.selection-cell .form-check-input {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  border: 2px solid #dee2e6;
}

.selection-cell .form-check-input:checked {
  background-color: #667eea;
  border-color: #667eea;
}

/* Request Number Cell */
.request-number-content {
  display: flex;
  flex-direction: column;
}

.request-number {
  font-weight: 700;
  color: #667eea;
  font-size: 1rem;
  letter-spacing: 0.5px;
}

.request-id {
  font-size: 0.75rem;
  color: #6c757d;
  margin-top: 2px;
}

/* Client Cell */
.client-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.client-avatar-sm {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  flex-shrink: 0;
}

.client-details {
  min-width: 0;
  flex: 1;
}

.client-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.95rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.client-email {
  font-size: 0.8rem;
  color: #6c757d;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

/* Document Type Cell */
.document-type-badge {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

/* Status Cell */
.status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-indicator {
  font-size: 0.6rem;
  animation: pulse 2s infinite;
}

.status-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.status-warning {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  color: #212529;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.status-danger {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.status-info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

.status-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

/* Amount Cell */
.amount-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.amount {
  font-weight: 700;
  color: #28a745;
  font-size: 1.1rem;
}

.currency {
  font-size: 0.75rem;
  color: #6c757d;
  margin-top: 2px;
}

/* Date Cell */
.date-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.date {
  font-weight: 500;
  color: #495057;
  font-size: 0.9rem;
}

.time {
  font-size: 0.75rem;
  color: #6c757d;
  margin-top: 2px;
}

/* Actions Cell */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.view-btn {
  background: #e3f2fd;
  color: #1976d2;
}

.view-btn:hover {
  background: #bbdefb;
  transform: translateY(-2px);
}

.approve-btn {
  background: #e8f5e8;
  color: #2e7d32;
}

.approve-btn:hover {
  background: #c8e6c9;
  transform: translateY(-2px);
}

.reject-btn {
  background: #ffebee;
  color: #c62828;
}

.reject-btn:hover {
  background: #ffcdd2;
  transform: translateY(-2px);
}

.more-btn {
  background: #f5f5f5;
  color: #666;
}

.more-btn:hover {
  background: #e0e0e0;
  transform: translateY(-2px);
}

.more-btn::after {
  display: none;
}

/* Dropdown positioning fixes */
.modern-table {
  overflow: visible;
}

.table-row {
  overflow: visible;
}

.action-buttons .dropdown {
  position: static;
}

.action-buttons .dropdown-menu {
  position: absolute !important;
  top: 100% !important;
  right: 0 !important;
  left: auto !important;
  z-index: 1050 !important;
  transform: none !important;
  margin-top: 0.25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  background: white;
  min-width: 160px;
}

.action-buttons .dropdown-menu.show {
  display: block !important;
}

/* Ensure dropdown appears above other elements */
.action-buttons .dropdown.show {
  z-index: 1051;
}

/* Pagination Container */
.pagination-container {
  background: white;
  border-radius: 0 0 16px 16px;
  padding: 1.5rem 2rem;
  border-top: 1px solid #f1f3f4;
  margin-top: -1px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.pagination-container .pagination {
  margin: 0;
}

.pagination-container .page-link {
  border: 1px solid #e3e6f0;
  color: #667eea;
  padding: 0.5rem 0.75rem;
  margin: 0 2px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.pagination-container .page-link:hover {
  background: #667eea;
  border-color: #667eea;
  color: white;
  transform: translateY(-1px);
}

.pagination-container .page-item.active .page-link {
  background: #667eea;
  border-color: #667eea;
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.pagination-container .page-item.disabled .page-link {
  color: #6c757d;
  background: #f8f9fa;
  border-color: #e3e6f0;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .d-flex.gap-2 {
    flex-direction: column;
    align-items: stretch;
  }

  .d-flex.gap-2 .btn {
    margin-bottom: 0.5rem;
  }

  .modal-xl {
    max-width: 95%;
    margin: 1rem auto;
  }

  .timeline {
    padding-left: 1.5rem;
  }

  .timeline-marker {
    left: -1.5rem;
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.625rem;
  }

  /* Compact table mobile adjustments */
  .compact-table-header {
    display: none;
  }

  .compact-row {
    grid-template-columns: 1fr;
    padding: 1rem;
    gap: 0.75rem;
    border-radius: 8px;
    margin-bottom: 0.75rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .row-cell {
    min-height: auto;
    flex-direction: column;
    align-items: flex-start;
    padding: 0.25rem 0;
    border-bottom: 1px solid #f1f3f4;
  }

  .row-cell:last-child {
    border-bottom: none;
  }

  .selection-cell {
    flex-direction: row;
    justify-content: flex-start;
  }

  .selection-cell {
    flex-direction: row;
    justify-content: flex-start;
  }

  .client-info {
    width: 100%;
  }

  .client-details {
    flex: 1;
  }

  .document-type-badge,
  .status-badge {
    align-self: flex-start;
  }

  .amount-content,
  .date-content {
    align-items: flex-start;
  }

  .action-buttons {
    width: 100%;
    justify-content: space-between;
  }

  .action-btn {
    flex: 1;
    max-width: 60px;
  }

  /* Mobile fixes for simple actions */
  .actions-simple {
    justify-content: center;
    gap: 0.5rem;
  }

  .request-actions-simple {
    justify-content: center;
    gap: 0.5rem;
  }

  /* Card view mobile adjustments */
  .request-card {
    margin-bottom: 1rem;
  }

  .request-card-header,
  .request-card-body,
  .request-card-footer {
    padding: 1rem;
  }

  .client-info .d-flex {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .client-avatar {
    align-self: center;
  }

  .request-meta .row {
    text-align: center;
  }

  .request-card-footer .d-flex {
    flex-direction: column;
    gap: 0.5rem;
  }

  .request-card-footer .btn {
    width: 100%;
  }

  /* View toggle mobile */
  .btn-group {
    width: 100%;
  }

  .btn-group .btn {
    flex: 1;
  }
}

@media (max-width: 576px) {
  .empty-state {
    padding: 2rem 1rem;
  }

  .empty-state-icon {
    font-size: 3rem;
  }

  .request-card-body {
    padding: 1rem;
  }

  .document-type,
  .request-meta {
    padding: 0.5rem;
  }
}

/* Compact Table Additional Styles */
.document-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  box-shadow: 0 1px 4px rgba(23, 162, 184, 0.3);
}

.client-compact {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
}

.client-avatar-tiny {
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.client-info-compact {
  flex: 1;
  min-width: 0;
}

.client-name-compact {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.8rem;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.client-email-compact {
  font-size: 0.7rem;
  color: #6c757d;
  margin-top: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.request-id-small {
  font-size: 0.7rem;
  color: #6c757d;
  margin-top: 1px;
}

.status-compact {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-compact i {
  font-size: 0.5rem;
}

.amount-compact {
  font-weight: 700;
  color: #28a745;
  font-size: 0.85rem;
}

.date-compact {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.date-main {
  font-weight: 500;
  color: #495057;
  font-size: 0.8rem;
  line-height: 1.2;
}

.time-small {
  font-size: 0.7rem;
  color: #6c757d;
  margin-top: 1px;
}

.actions-compact {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Fixed Actions Layout */
.actions-compact-fixed {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-width: 120px;
}

.primary-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.dropdown-wrapper .dropdown {
  position: static;
}

.compact-dropdown {
  z-index: 1050 !important;
}

.compact-dropdown .dropdown-item {
  padding: 0.5rem 1rem !important;
  font-size: 0.875rem !important;
  white-space: nowrap !important;
  display: flex !important;
  align-items: center !important;
}

.compact-dropdown .dropdown-item:hover {
  background-color: #f8f9fa !important;
}

.compact-dropdown .dropdown-divider {
  margin: 0.25rem 0 !important;
}

/* Ensure dropdown appears above table rows */
.compact-row {
  position: relative;
  z-index: 1;
}

.compact-row:hover {
  z-index: 2;
}

.dropdown-wrapper .dropdown.show {
  z-index: 1051 !important;
}

.compact-dropdown.show {
  display: block !important;
}

.action-btn-sm {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.primary-btn-sm {
  background: #007bff;
  color: white;
  border: 1px solid #007bff;
}

.primary-btn-sm:hover {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.view-btn-sm {
  background: #e3f2fd;
  color: #1976d2;
}

.view-btn-sm:hover {
  background: #bbdefb;
  transform: translateY(-1px);
}

.approve-btn-sm {
  background: #e8f5e8;
  color: #2e7d32;
}

.approve-btn-sm:hover {
  background: #c8e6c9;
  transform: translateY(-1px);
}

.reject-btn-sm {
  background: #ffebee;
  color: #c62828;
}

.reject-btn-sm:hover {
  background: #ffcdd2;
  transform: translateY(-1px);
}

.more-btn-sm {
  background: #f5f5f5;
  color: #666;
}

.more-btn-sm:hover {
  background: #e0e0e0;
  transform: translateY(-1px);
}

.more-btn-sm::after {
  display: none;
}

/* Simple Actions Layout */
.actions-simple {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  justify-content: center;
}

.request-actions-simple {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Disabled button styles */
.btn:disabled,
.action-btn-sm:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.btn:disabled:hover,
.action-btn-sm:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Status-based button styling */
.btn-outline-success:disabled {
  background-color: #f8f9fa;
  border-color: #28a745;
  color: #28a745;
}

.btn-outline-danger:disabled {
  background-color: #f8f9fa;
  border-color: #dc3545;
  color: #dc3545;
}

/* Document Preview Styles */
.document-preview-card {
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.document-preview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.document-preview-header {
  padding: 0.75rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.document-type-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

.document-preview-content {
  position: relative;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  cursor: pointer;
}

.document-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.image-preview:hover .document-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.7);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.pdf-preview, .file-preview {
  text-align: center;
  padding: 1rem;
}

.pdf-icon, .file-icon {
  margin-bottom: 1rem;
}

.document-preview-footer {
  padding: 0.75rem;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  text-align: center;
}

.no-documents {
  background: #f8f9fa;
  border-radius: 8px;
  margin: 1rem 0;
}

/* Image Modal Styles */
.image-modal-container {
  max-height: 70vh;
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
}

.image-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.image-actions {
  display: flex;
  gap: 0.5rem;
}

/* Not Provided Styling */
.not-provided {
  font-style: italic;
  color: #6c757d;
}

/* Client Details Styling */
.client-details-grid {
  font-size: 0.75rem;
}

.client-details-compact {
  margin-top: 0.25rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  font-size: 0.7rem;
}

.detail-item {
  display: inline-flex;
  align-items: center;
  color: #6c757d;
  white-space: nowrap;
}

.detail-item i {
  font-size: 0.65rem;
  opacity: 0.7;
}

/* Loading Placeholder for Documents */
.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6c757d;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.loading-placeholder:hover {
  background: #e9ecef;
}

.loading-placeholder i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.loading-placeholder span {
  font-size: 0.875rem;
}

/* Error Placeholder for Failed Documents */
.error-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #dc3545;
  background: #f8d7da;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.error-placeholder:hover {
  background: #f5c6cb;
}

.error-placeholder i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.error-placeholder span {
  font-size: 0.875rem;
}

.error-placeholder small {
  font-size: 0.75rem;
  opacity: 0.8;
  margin-top: 0.25rem;
}

/* Modal Loading and Error States */
.modal-loading {
  height: 400px;
  background: rgba(0, 0, 0, 0.1);
  color: #fff;
  will-change: opacity;
}

.modal-error {
  height: 400px;
  background: rgba(220, 53, 69, 0.1);
  color: #fff;
}

/* Optimized Modal Styles */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1050;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  will-change: transform;
  transition: opacity 0.3s ease;
}

.loading-content, .error-text, .loading-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-progress {
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Performance optimizations */
.image-modal-container {
  contain: layout style paint;
  transform: translateZ(0); /* Force hardware acceleration */
}

.modal-content {
  will-change: transform;
}

/* Responsive modal improvements */
@media (max-width: 768px) {
  .modal-xl {
    max-width: 95vw;
  }

  .modal-image {
    max-height: 70vh;
  }

  .header-controls .btn-sm {
    padding: 0.25rem 0.5rem;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .document-preview-card {
    margin-bottom: 1rem;
  }

  .image-modal-container {
    max-height: 60vh;
  }

  .modal-image {
    max-height: 60vh;
  }
}

/* Highlight section animation for notification focus */
.highlight-section {
  background: linear-gradient(90deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 123, 255, 0.05) 100%);
  border-left: 4px solid #007bff;
  padding: 1rem;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
  animation: highlightPulse 2s ease-in-out;
}

@keyframes highlightPulse {
  0% {
    background: rgba(0, 123, 255, 0.2);
    transform: scale(1.02);
  }
  50% {
    background: rgba(0, 123, 255, 0.1);
    transform: scale(1.01);
  }
  100% {
    background: rgba(0, 123, 255, 0.05);
    transform: scale(1);
  }
}
</style>
