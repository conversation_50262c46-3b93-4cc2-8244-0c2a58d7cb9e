/* Admin Dashboard Styles - Professional Blue & Yellow Theme */

/* CSS Variables for consistent theming */
:root {
  --admin-primary: #1e40af;
  --admin-primary-dark: #1e3a8a;
  --admin-secondary: #3b82f6;
  --admin-accent: #fbbf24;
  --admin-success: #10b981;
  --admin-warning: #f59e0b;
  --admin-danger: #ef4444;
  --admin-info: #3b82f6;
  --admin-light: #eff6ff;
  --admin-dark: #1e293b;
  --admin-gray: #64748b;
  --admin-border: #e2e8f0;
  --admin-shadow: 0 4px 20px rgba(30, 64, 175, 0.15);
  --admin-shadow-hover: 0 8px 30px rgba(30, 64, 175, 0.25);
  --admin-radius: 0.75rem;
  --admin-radius-sm: 0.5rem;
  --admin-radius-lg: 1rem;
  --admin-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.admin-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--admin-light) 0%, #f0f9ff 100%);
}

.dashboard-container {
  display: flex;
  min-height: calc(100vh - 70px); /* Adjust based on header height */
}

.main-content {
  flex: 1;
  padding: 0;
  background: linear-gradient(135deg, var(--admin-light) 0%, #f0f9ff 100%);
  overflow-x: hidden;
  overflow-y: auto;
  margin-left: 280px;
  margin-top: 70px;
  transition: var(--admin-transition);
  height: calc(100vh - 70px);
}

.main-content.sidebar-collapsed {
  margin-left: 72px;
}

/* Mobile Overlay */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    margin-top: 60px;
    height: calc(100vh - 60px);
    overflow-y: auto;
  }

  .main-content.sidebar-collapsed {
    margin-left: 0;
  }
}

/* Dashboard Stats Cards */
.card {
  border: none;
  border-radius: var(--admin-radius-lg);
  box-shadow: var(--admin-shadow);
  transition: var(--admin-transition);
  background: white;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: var(--admin-shadow-hover);
}

/* Enhanced card header */
.card-header {
  background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-dark) 100%);
  border-bottom: none;
  padding: 1.25rem 1.5rem;
  border-radius: var(--admin-radius-lg) var(--admin-radius-lg) 0 0 !important;
}

.card-header h6 {
  color: white;
  font-weight: 600;
  margin: 0;
  font-size: 1rem;
}

/* Card body improvements */
.card-body {
  padding: 1.5rem;
}

/* Clickable stat cards */
.stat-card {
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 0.5rem 2.5rem 0 rgba(58, 59, 69, 0.25);
}

.stat-card:active {
  transform: translateY(-1px);
}

.card-body {
  padding: 1.5rem;
}

/* Border left styles for stat cards - Blue & Yellow Theme */
.border-left-primary {
  border-left: 0.25rem solid #1e40af !important;
}

.border-left-success {
  border-left: 0.25rem solid #10b981 !important;
}

.border-left-info {
  border-left: 0.25rem solid #3b82f6 !important;
}

.border-left-warning {
  border-left: 0.25rem solid #fbbf24 !important;
}

/* Text styles */
.text-xs {
  font-size: 0.7rem;
}

.text-primary {
  color: #1e40af !important;
}

.text-success {
  color: #10b981 !important;
}

.text-info {
  color: #3b82f6 !important;
}

.text-warning {
  color: #fbbf24 !important;
}

.text-gray-300 {
  color: #dddfeb !important;
}

.text-gray-800 {
  color: #5a5c69 !important;
}

.font-weight-bold {
  font-weight: 700 !important;
}

/* Dashboard header */
.h3 {
  font-size: 1.75rem;
  font-weight: 400;
  line-height: 1.2;
}

/* Stats numbers */
.h5 {
  font-size: 1.25rem;
  font-weight: 500;
  line-height: 1.2;
}

/* Recent Activity Section */
.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e3e6f0;
  padding: 1rem 1.5rem;
  border-radius: 15px 15px 0 0 !important;
}

.card-header h6 {
  margin: 0;
  font-size: 1rem;
  font-weight: 700;
  color: #1e40af;
}

/* Activity items */
.icon-circle {
  height: 2.5rem;
  width: 2.5rem;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.icon-circle.bg-primary {
  background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
}

.icon-circle.bg-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.icon-circle.bg-info {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.icon-circle.bg-warning {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

.icon-circle.bg-danger {
  background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
}

.icon-circle.bg-secondary {
  background: linear-gradient(135deg, #858796 0%, #6c757d 100%);
}

/* Activity list improvements */
.activity-list {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  transition: all 0.3s ease;
  padding: 0.75rem;
  border-radius: 8px;
  margin: 0 -0.75rem;
}

.activity-item:hover {
  background-color: #f8f9fa;
  transform: translateX(5px);
}

/* Badge styles */
.badge {
  font-size: 0.7rem;
  padding: 0.35rem 0.65rem;
  border-radius: 0.5rem;
  font-weight: 500;
}

.badge-success {
  background-color: #10b981;
  color: white;
}

.badge-warning {
  background-color: #fbbf24;
  color: #1e3a8a;
}

.badge-danger {
  background-color: #ef4444;
  color: white;
}

.badge-info {
  background-color: #3b82f6;
  color: white;
}

.badge-secondary {
  background-color: #858796;
  color: white;
}

/* Mobile Responsive Styles - Additional rules */
@media (max-width: 768px) {

  .content-wrapper {
    padding: 1rem;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .page-subtitle {
    font-size: 1rem;
  }

  /* Card adjustments */
  .card-body {
    padding: 1rem;
  }

  /* Stats cards responsive */
  .stat-card .card-body {
    padding: 1rem 0.75rem;
  }

  .stat-card .h5 {
    font-size: 1.25rem;
  }

  .icon-circle {
    width: 2rem;
    height: 2rem;
    font-size: 1rem;
  }

  /* Activity list mobile */
  .activity-item {
    padding: 0.5rem;
    margin: 0 -0.5rem;
  }

  .activity-item .icon-circle {
    width: 2rem;
    height: 2rem;
  }

  /* Button adjustments */
  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    border-radius: 0.375rem !important;
    margin-bottom: 0.25rem;
  }

  /* Table responsive */
  .table-responsive {
    font-size: 0.875rem;
  }

  .table th,
  .table td {
    padding: 0.5rem 0.25rem;
  }
}

@media (max-width: 576px) {
  .content-wrapper {
    padding: 0.75rem;
  }

  .page-header {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
  }

  .page-title {
    font-size: 1.25rem;
  }

  .page-subtitle {
    font-size: 0.875rem;
  }

  /* Flex direction changes */
  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 0.75rem;
  }

  .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }

  /* Card spacing */
  .card {
    margin-bottom: 1rem;
  }

  .card-body {
    padding: 0.75rem;
  }

  /* Stats cards small screens */
  .stat-card .text-xs {
    font-size: 0.7rem;
  }

  .stat-card .h5 {
    font-size: 1.1rem;
  }

  /* Activity items small */
  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .activity-item .icon-circle {
    align-self: flex-start;
  }

  /* Button sizes */
  .btn-sm {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
  }

  /* Table very small screens */
  .table-responsive {
    font-size: 0.8rem;
  }

  .table th,
  .table td {
    padding: 0.375rem 0.25rem;
  }

  /* Hide less important columns on very small screens */
  .table .d-none-xs {
    display: none !important;
  }
}

/* Quick Actions */
.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
  border-color: #1e40af;
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
  border-color: #1e3a8a;
  transform: translateY(-1px);
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: #10b981;
  color: white;
}

.btn-success:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  border-color: #169b6b;
  transform: translateY(-1px);
}

.btn-info {
  background-color: #36b9cc;
  border-color: #36b9cc;
}

.btn-info:hover {
  background-color: #2c9faf;
  border-color: #2a96a5;
  transform: translateY(-1px);
}

.btn-warning {
  background-color: #f6c23e;
  border-color: #f6c23e;
  color: #fff;
}

.btn-warning:hover {
  background-color: #f4b619;
  border-color: #f4b30d;
  color: #fff;
  transform: translateY(-1px);
}

/* Dropdown styles */
.dropdown-toggle::after {
  display: none;
}

.dropdown-menu {
  border: none;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  border-radius: 0.5rem;
}

.dropdown-header {
  font-size: 0.85rem;
  font-weight: 600;
  color: #6c757d;
  padding: 0.5rem 1rem;
}

.dropdown-item {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #4e73df;
}

/* Empty state */
.fa-inbox {
  color: #dddfeb;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-container {
    flex-direction: column;
  }
  
  .main-content {
    padding: 1rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .h3 {
    font-size: 1.5rem;
  }
  
  .col-xl-3 {
    margin-bottom: 1rem;
  }
  
  /* Stack stat cards on mobile */
  .row .col-xl-3 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media (max-width: 576px) {
  .main-content {
    padding: 0.5rem;
  }
  
  .card-header {
    padding: 0.75rem 1rem;
  }
  
  .card-body {
    padding: 0.75rem;
  }
  
  .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .icon-circle {
    height: 2rem;
    width: 2rem;
  }
  
  .h5 {
    font-size: 1.1rem;
  }
  
  .text-xs {
    font-size: 0.65rem;
  }
}

/* Animation for cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: fadeInUp 0.5s ease-out;
}

/* Stagger animation for multiple cards */
.col-xl-3:nth-child(1) .card { animation-delay: 0.1s; }
.col-xl-3:nth-child(2) .card { animation-delay: 0.2s; }
.col-xl-3:nth-child(3) .card { animation-delay: 0.3s; }
.col-xl-3:nth-child(4) .card { animation-delay: 0.4s; }

/* Utility classes */
.no-gutters {
  margin-right: 0;
  margin-left: 0;
}

.no-gutters > .col,
.no-gutters > [class*="col-"] {
  padding-right: 0;
  padding-left: 0;
}

.mr-2 {
  margin-right: 0.5rem !important;
}

.mb-1 {
  margin-bottom: 0.25rem !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}

.py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.py-4 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.d-flex {
  display: flex !important;
}

.align-items-center {
  align-items: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}
