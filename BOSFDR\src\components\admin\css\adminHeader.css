/* Ad<PERSON>er Styles - Professional Blue & Yellow Theme */
.dashboard-header {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #3b82f6 100%);
  color: white;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  box-shadow: 0 4px 20px rgba(30, 58, 138, 0.25);
  position: fixed;
  top: 0;
  left: 280px;
  right: 0;
  z-index: 1001;
  border-bottom: 3px solid #fbbf24;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: left;
  backdrop-filter: blur(10px);
}

/* Header positioning when sidebar is collapsed */
.dashboard-header.sidebar-collapsed {
  left: 72px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

/* Header Left Section */
.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 1.3rem;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-toggle:hover {
  background-color: rgba(251, 191, 36, 0.2);
  border-color: #fbbf24;
  transform: scale(1.05);
}

.sidebar-toggle:active {
  transform: scale(0.95);
}

.page-title h1 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: white;
  line-height: 1.2;
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  overflow: visible;
  position: relative;
}

/* Notification Button */
.notification-dropdown {
  position: relative;
  overflow: visible;
}

.notification-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.notification-btn:hover {
  background-color: rgba(251, 191, 36, 0.2);
  border-color: #fbbf24;
  transform: scale(1.05);
}

.notification-btn:active {
  transform: scale(0.95);
}

.notification-badge {
  position: absolute;
  top: 0.2rem;
  right: 0.2rem;
  background: #ef4444;
  color: white;
  font-size: 0.65rem;
  font-weight: 600;
  padding: 0.15rem 0.35rem;
  border-radius: 50rem;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
}

/* Notification Menu */
.notification-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  width: 350px;
  z-index: 1050;
  margin-top: 0.5rem;
  border: 1px solid #e9ecef;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 400px;
}

.notification-header {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.notification-header h6 {
  margin: 0;
  font-weight: 600;
  color: #495057;
  flex: 1;
}

.notification-count {
  font-size: 0.875rem;
  color: #6c757d;
}

.notification-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
  max-height: 300px;
}

/* Custom scrollbar for notification list - only show when needed */
.notification-list::-webkit-scrollbar {
  width: 4px;
}

.notification-list::-webkit-scrollbar-track {
  background: transparent;
}

.notification-list::-webkit-scrollbar-thumb {
  background: rgba(209, 213, 219, 0.6);
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.notification-list:hover::-webkit-scrollbar-thumb {
  opacity: 1;
}

.notification-list::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
}

/* Firefox scrollbar */
.notification-list {
  scrollbar-width: thin;
  scrollbar-color: rgba(209, 213, 219, 0.6) transparent;
}

.no-notifications {
  padding: 2rem;
  text-align: center;
  color: #6c757d;
}

.no-notifications i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.notification-item {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid #f1f3f4;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  transition: background-color 0.2s ease;
  min-width: 0;
  width: 100%;
  box-sizing: border-box;
  flex-shrink: 0;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #f0f9ff;
  border-left: 3px solid #3b82f6;
}

.notification-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e9ecef;
}

.notification-content {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  width: 100%;
}

.notification-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: #495057;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.notification-message {
  font-size: 0.8rem;
  color: #6c757d;
  margin: 0 0 0.25rem 0;
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-time {
  font-size: 0.75rem;
  color: #adb5bd;
}

.notification-footer {
  padding: 0.75rem 1.25rem;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  text-align: center;
  flex-shrink: 0;
}

.notification-footer a {
  color: #1e40af;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
}

.notification-footer a:hover {
  color: #1e3a8a;
  text-decoration: underline;
}

/* User Dropdown */
.user-dropdown {
  position: relative;
}

.user-btn {
  background: none;
  border: none;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-height: 44px;
}

.user-btn:hover {
  background-color: rgba(251, 191, 36, 0.2);
  border-color: #fbbf24;
}

.user-btn:active {
  transform: scale(0.98);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  font-size: 1.2rem;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0;
}

.user-name {
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.user-role {
  font-size: 0.75rem;
  opacity: 0.8;
  line-height: 1.2;
}

.dropdown-arrow {
  font-size: 0.75rem;
  transition: transform 0.3s ease;
}

.user-dropdown.active .dropdown-arrow {
  transform: rotate(180deg);
}

/* User Dropdown Menu */
.user-dropdown .dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  min-width: 200px;
  z-index: 1050;
  margin-top: 0.5rem;
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.user-dropdown .dropdown-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #495057;
  text-decoration: none;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f1f3f4;
}

.user-dropdown .dropdown-item:last-child {
  border-bottom: none;
}

.user-dropdown .dropdown-item:hover {
  background-color: #f8f9fa;
  color: #1e40af;
}

.user-dropdown .dropdown-item i {
  width: 16px;
  text-align: center;
}

.user-dropdown .dropdown-divider {
  height: 1px;
  background-color: #e9ecef;
  margin: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .dashboard-header {
    left: 0;
    padding: 0 1rem;
    height: 60px;
  }

  .dashboard-header.sidebar-collapsed {
    left: 0;
  }

  .header-left {
    gap: 1rem;
  }

  .page-title h1 {
    font-size: 1.25rem;
  }

  .user-info {
    display: none;
  }

  .user-btn {
    padding: 0.5rem;
    gap: 0;
  }

  .notification-menu,
  .user-dropdown .dropdown-menu {
    right: -1rem;
    width: calc(100vw - 2rem);
    max-width: 320px;
    min-width: 280px;
  }

  .notification-menu {
    max-height: 70vh;
  }

  .notification-list {
    max-height: calc(70vh - 120px);
    overflow-y: auto;
    overflow-x: hidden;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: 0 0.75rem;
  }

  .header-left {
    gap: 0.75rem;
  }

  .page-title h1 {
    font-size: 1.1rem;
  }

  .header-actions {
    gap: 0.5rem;
  }

  .notification-btn,
  .user-btn {
    min-width: 40px;
    min-height: 40px;
    padding: 0.5rem;
  }

  .notification-menu,
  .user-dropdown .dropdown-menu {
    right: -0.75rem;
    width: calc(100vw - 1.5rem);
    max-width: 300px;
    min-width: 250px;
  }

  .notification-menu {
    max-height: 60vh;
  }

  .notification-list {
    max-height: calc(60vh - 120px);
    overflow-y: auto;
    overflow-x: hidden;
  }

  .notification-item {
    padding: 0.75rem 1rem;
  }

  .notification-title {
    font-size: 0.8rem;
  }

  .notification-message {
    font-size: 0.75rem;
    -webkit-line-clamp: 1;
  }
}

/* Dropdown styles */
.dropdown-menu {
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.75rem;
  padding: 0.5rem 0;
  min-width: 250px;
  margin-top: 0.5rem;
}

.dropdown-header {
  font-size: 0.875rem;
  font-weight: 600;
  color: #6c757d;
  padding: 0.5rem 1rem;
  margin-bottom: 0.25rem;
}

.dropdown-item {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  color: #495057;
  transition: all 0.3s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #212529;
  transform: translateX(5px);
}

.dropdown-item i {
  width: 1.25rem;
  text-align: center;
  margin-right: 0.5rem;
  color: #6c757d;
}

.dropdown-item:hover i {
  color: #495057;
}

.dropdown-divider {
  margin: 0.5rem 0;
  border-color: #e9ecef;
}

/* Notification dropdown */
.notification-dropdown {
  max-width: 320px;
  max-height: 400px;
  overflow-y: auto;
}

.notification-dropdown .dropdown-item {
  padding: 1rem;
  border-bottom: 1px solid #f1f3f4;
  white-space: normal;
}

.notification-dropdown .dropdown-item:last-child {
  border-bottom: none;
}

.notification-dropdown .dropdown-item:hover {
  background-color: #f8f9fa;
  transform: none;
}

/* Notification badge */
.badge {
  font-size: 0.65rem;
  padding: 0.25rem 0.4rem;
}

.position-absolute {
  position: absolute !important;
}

.top-0 {
  top: 0 !important;
}

.start-100 {
  left: 100% !important;
}

.translate-middle {
  transform: translate(-50%, -50%) !important;
}

.rounded-pill {
  border-radius: 50rem !important;
}

.bg-danger {
  background-color: #dc3545 !important;
}

.bg-primary {
  background-color: #0d6efd !important;
}

/* User avatar */
.user-avatar {
  width: 32px;
  height: 32px;
  position: relative;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 0.875rem;
}

/* Mobile sidebar overlay */
.mobile-sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1040;
}

/* Mobile responsive */
@media (max-width: 991.98px) {
  .navbar {
    padding: 0.5rem 1rem;
  }
  
  .navbar-brand {
    font-size: 1.1rem;
  }
  
  .navbar-nav {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .navbar-nav .nav-link {
    padding: 0.75rem 1rem;
    margin: 0.125rem 0;
  }
  
  .dropdown-menu {
    position: static !important;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: rgba(255, 255, 255, 0.05);
    border: none;
    box-shadow: none;
    border-radius: 0.5rem;
  }
  
  .dropdown-item {
    color: rgba(255, 255, 255, 0.8);
  }
  
  .dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
  }
  
  .dropdown-header {
    color: rgba(255, 255, 255, 0.6);
  }
  
  .dropdown-divider {
    border-color: rgba(255, 255, 255, 0.1);
  }
}

@media (max-width: 575.98px) {
  .navbar {
    padding: 0.5rem 0.75rem;
  }
  
  .navbar-brand {
    font-size: 1rem;
  }
  
  .navbar-brand span {
    display: none;
  }
  
  .user-avatar {
    width: 28px;
    height: 28px;
  }
  
  .dropdown-menu {
    min-width: 200px;
  }
  
  .notification-dropdown {
    max-width: 280px;
  }
}

/* Animation for dropdown */
.dropdown-menu {
  animation: fadeInDown 0.3s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Notification icon animation */
.nav-link .fa-bell {
  transition: all 0.3s ease;
}

.nav-link:hover .fa-bell {
  animation: ring 0.5s ease-in-out;
}

@keyframes ring {
  0%, 20%, 50%, 80%, 100% {
    transform: rotate(0deg);
  }
  10% {
    transform: rotate(-10deg);
  }
  30% {
    transform: rotate(10deg);
  }
  60% {
    transform: rotate(-5deg);
  }
  90% {
    transform: rotate(5deg);
  }
}

/* Utility classes */
.d-none {
  display: none !important;
}

.d-lg-none {
  display: none !important;
}

.d-md-inline {
  display: inline !important;
}

@media (min-width: 992px) {
  .d-lg-none {
    display: none !important;
  }
}

@media (max-width: 767.98px) {
  .d-md-inline {
    display: none !important;
  }
}

.fw-bold {
  font-weight: 700 !important;
}

.text-center {
  text-align: center !important;
}

.text-muted {
  color: #6c757d !important;
}

.small {
  font-size: 0.875rem;
}

.me-2 {
  margin-right: 0.5rem !important;
}

.me-3 {
  margin-right: 1rem !important;
}

.ms-2 {
  margin-left: 0.5rem !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

/* Enhanced Mobile-First Responsive Design */

/* Tablet and larger screens */
@media (min-width: 769px) {
  .dashboard-header {
    left: 280px;
  }

  .dashboard-header.sidebar-collapsed {
    left: 70px;
  }
}

/* Mobile and tablet portrait */
@media (max-width: 768px) {
  .dashboard-header {
    padding: 0 1rem;
    height: 60px;
    left: 0;
    right: 0;
    width: 100%;
    position: fixed;
    z-index: 1001;
    box-shadow: 0 2px 20px rgba(30, 58, 138, 0.3);
  }

  .dashboard-header.sidebar-collapsed {
    left: 0;
  }

  .header-content {
    max-width: 100%;
    overflow: hidden;
  }

  .header-left {
    gap: 0.75rem;
    flex: 1;
    min-width: 0;
  }

  .page-title {
    flex: 1;
    min-width: 0;
  }

  .page-title h1 {
    font-size: clamp(1rem, 4vw, 1.2rem);
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0;
  }

  /* Enhanced touch targets */
  .sidebar-toggle {
    padding: 0.625rem;
    font-size: 1.2rem;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    transition: all 0.2s ease;
  }

  .sidebar-toggle:active {
    transform: scale(0.95);
    background-color: rgba(251, 191, 36, 0.3);
  }

  /* Hide user info on mobile */
  .user-info {
    display: none;
  }

  .header-actions {
    gap: 0.5rem;
    flex-shrink: 0;
  }

  /* Enhanced notification and user buttons */
  .notification-btn,
  .user-btn {
    padding: 0.625rem;
    font-size: 1.1rem;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    transition: all 0.2s ease;
  }

  .notification-btn:active,
  .user-btn:active {
    transform: scale(0.95);
    background-color: rgba(251, 191, 36, 0.3);
  }

  /* Enhanced notification badge */
  .notification-badge {
    top: 0.2rem;
    right: 0.2rem;
    font-size: 0.65rem;
    padding: 0.15rem 0.35rem;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Improved notification menu positioning */
  .notification-menu {
    min-width: min(320px, calc(100vw - 2rem));
    max-width: calc(100vw - 2rem);
    right: 0;
    max-height: 70vh;
    border-radius: 16px;
    margin-top: 0.75rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  }

  .notification-header {
    padding: 1rem 1.25rem;
    border-radius: 16px 16px 0 0;
  }

  .notification-list {
    max-height: calc(70vh - 120px);
  }

  .notification-item {
    padding: 0.875rem 1.25rem;
    border-radius: 0;
  }

  .notification-item:first-child {
    border-radius: 0;
  }
}
