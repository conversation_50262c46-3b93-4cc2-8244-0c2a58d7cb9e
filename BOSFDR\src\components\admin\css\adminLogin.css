/* Enhanced Mobile-First Admin Login Styles - Blue & Yellow Theme */
.admin-login {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #3b82f6 100%);
}

/* Enhanced card styles */
.card {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  border: none;
  width: 100%;
  max-width: 400px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.card-header {
  border-radius: 20px 20px 0 0 !important;
  background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
  color: white;
  padding: 2rem 1.5rem 1.5rem;
  border: none;
  text-align: center;
}

.card-header h3 {
  margin: 0;
  font-size: clamp(1.25rem, 4vw, 1.5rem);
  font-weight: 600;
}

.card-body {
  padding: 2rem 1.5rem !important;
}

/* Enhanced Input Group Styles */
.input-group {
  margin-bottom: 1.5rem;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.input-group:focus-within {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(30, 64, 175, 0.15);
}

.input-group-text {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #e9ecef;
  border-right: none;
  color: #6c757d;
  padding: 1rem;
  min-width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.input-group .form-control {
  border: 2px solid #e9ecef;
  border-left: none;
  padding: 1rem 1.25rem;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
}

.input-group .form-control:focus {
  border-color: #1e40af;
  box-shadow: none;
  background: #eff6ff;
}

.input-group .form-control:focus + .input-group-text,
.input-group:focus-within .input-group-text {
  border-color: #1e40af;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
}

/* Enhanced Button Styles */
.btn-success {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  border: none;
  border-radius: 12px;
  padding: 1rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 1rem;
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4);
  position: relative;
  overflow: hidden;
  color: #1e3a8a;
}

.btn-success::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-success:hover::before {
  left: 100%;
}

.btn-success:hover {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(251, 191, 36, 0.5);
  color: #1e3a8a;
}

.btn-success:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(251, 191, 36, 0.4);
}

.btn-outline-secondary {
  border-radius: 0 12px 12px 0;
  border-left: none;
  padding: 1rem;
  transition: all 0.3s ease;
}

/* Enhanced Form Styles */
.form-control {
  border-radius: 12px;
  padding: 1rem 1.25rem;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
  font-size: 1rem;
  background: white;
}

.form-control:focus {
  border-color: #1e40af;
  box-shadow: 0 0 0 0.25rem rgba(30, 64, 175, 0.15);
  background: #eff6ff;
  transform: translateY(-1px);
}

.form-control::placeholder {
  color: #adb5bd;
  font-size: 0.95rem;
}

.form-check {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

.form-check-input {
  width: 1.25rem;
  height: 1.25rem;
  margin-top: 0.125rem;
  border-radius: 6px;
  border: 2px solid #ced4da;
  transition: all 0.3s ease;
}

.form-check-input:checked {
  background-color: #fbbf24;
  border-color: #fbbf24;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.4);
}

.form-check-label {
  font-size: 0.95rem;
  color: #495057;
  margin-left: 0.5rem;
}

/* Enhanced Alert Styles */
.alert {
  border-radius: 12px;
  border: none;
  padding: 1rem 1.25rem;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.alert-danger {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
  border-left: 4px solid #dc3545;
}

.alert-success {
  background: linear-gradient(135deg, #d1edff 0%, #b8daff 100%);
  color: #0c5460;
  border-left: 4px solid #28a745;
}

/* Enhanced Modal Styles */
.modal-content {
  border-radius: 20px;
  border: none;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

.modal-header {
  border-bottom: 2px solid #e9ecef;
  border-radius: 20px 20px 0 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1.5rem 2rem;
}

.modal-body {
  padding: 2rem;
}

/* Enhanced Link Styles */
a {
  color: #1e40af;
  transition: all 0.3s ease;
  text-decoration: none;
  font-weight: 500;
}

a:hover {
  color: #1e3a8a;
  text-decoration: underline;
  text-underline-offset: 3px;
}

/* Loading spinner */
.spinner-border-sm {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.5rem;
}

/* Enhanced Animation */
.card {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Status info card */
.border-warning {
  border-color: #ffc107 !important;
  border-width: 2px !important;
  border-radius: 12px !important;
}

/* Password visibility toggle */
.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.5rem;
  z-index: 10;
  transition: color 0.3s ease;
}

.password-toggle:hover {
  color: #1e40af;
}

/* Enhanced Mobile Responsive Design */

/* Tablet and larger screens */
@media (min-width: 769px) {
  .admin-login {
    padding: 2rem;
  }

  .card {
    max-width: 450px;
  }

  .card-body {
    padding: 2.5rem 2rem !important;
  }
}

/* Mobile and tablet portrait */
@media (max-width: 768px) {
  .admin-login {
    padding: 1rem;
    min-height: 100vh;
    align-items: flex-start;
    padding-top: 2rem;
  }

  .card {
    max-width: 100%;
    width: 100%;
    margin: 0;
  }

  .card-header {
    padding: 1.5rem 1.25rem 1.25rem;
  }

  .card-header h3 {
    font-size: clamp(1.1rem, 5vw, 1.3rem);
  }

  .card-body {
    padding: 1.5rem 1.25rem !important;
  }
}
