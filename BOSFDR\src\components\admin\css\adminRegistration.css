/* Enhanced Mobile-First Admin Registration Styles - Blue & Yellow Theme */
.admin-registration {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #3b82f6 100%);
}

/* Enhanced card styles */
.card {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  border: none;
  width: 100%;
  max-width: 600px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.card-header {
  border-radius: 20px 20px 0 0 !important;
  background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
  color: white;
  padding: 2rem 1.5rem 1.5rem;
  border: none;
  text-align: center;
}

.card-header h3 {
  margin: 0;
  font-size: clamp(1.25rem, 4vw, 1.5rem);
  font-weight: 600;
}

.card-body {
  padding: 2rem 1.5rem !important;
}

/* Step Indicator Styles */
.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e9ecef;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
  border: 2px solid #e9ecef;
}

.step-label {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
  transition: all 0.3s ease;
}

.step-indicator.active .step-number {
  background: #fbbf24;
  color: #1e3a8a;
  border-color: #fbbf24;
  transform: scale(1.1);
}

.step-indicator.active .step-label {
  color: #1e40af;
  font-weight: 600;
}

.step-indicator.completed .step-number {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.step-indicator.completed .step-label {
  color: #28a745;
}

.step-line {
  height: 2px;
  background: #e9ecef;
  flex: 1;
  margin: 0 1rem;
  margin-top: 20px;
  transition: all 0.3s ease;
}

.step-line.active {
  background: #fbbf24;
}

/* Enhanced Form Styles */
.form-control, .form-select {
  border-radius: 12px;
  padding: 1rem 1.25rem;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
  font-size: 1rem;
  background: white;
}

.form-control:focus, .form-select:focus {
  border-color: #1e40af;
  box-shadow: 0 0 0 0.25rem rgba(30, 64, 175, 0.15);
  background: #eff6ff;
  transform: translateY(-1px);
}

.form-control::placeholder {
  color: #adb5bd;
  font-size: 0.95rem;
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

/* Enhanced Input Group Styles */
.input-group {
  margin-bottom: 1.5rem;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.input-group:focus-within {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(30, 64, 175, 0.15);
}

.input-group .form-control {
  border-left: none;
}

.input-group .btn {
  border-radius: 0 12px 12px 0;
  border-left: none;
}

/* Enhanced Button Styles */
.btn-success {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  border: none;
  border-radius: 12px;
  padding: 1rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4);
  position: relative;
  overflow: hidden;
  color: #1e3a8a;
}

.btn-success::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-success:hover::before {
  left: 100%;
}

.btn-success:hover {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(251, 191, 36, 0.5);
  color: #1e3a8a;
}

.btn-success:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(251, 191, 36, 0.4);
}

.btn-outline-secondary {
  border-radius: 12px;
  border: 2px solid #6c757d;
  padding: 1rem 2rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
  background: #6c757d;
  border-color: #6c757d;
  transform: translateY(-2px);
}

/* Enhanced Alert Styles */
.alert {
  border-radius: 12px;
  border: none;
  padding: 1rem 1.25rem;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.alert-danger {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
  border-left: 4px solid #dc3545;
}

.alert-success {
  background: linear-gradient(135deg, #d1edff 0%, #b8daff 100%);
  color: #0c5460;
  border-left: 4px solid #28a745;
}

/* Step Content Animation */
.step-content {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Animation */
.card {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Loading spinner */
.spinner-border-sm {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.5rem;
}

/* Enhanced Link Styles */
a {
  color: #1e40af;
  transition: all 0.3s ease;
  text-decoration: none;
  font-weight: 500;
}

a:hover {
  color: #1e3a8a;
  text-decoration: underline;
  text-underline-offset: 3px;
}

/* Enhanced Mobile Responsive Design */

/* Tablet and larger screens */
@media (min-width: 769px) {
  .admin-registration {
    padding: 2rem;
  }

  .card {
    max-width: 650px;
  }

  .card-body {
    padding: 2.5rem 2rem !important;
  }

  .step-number {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }
}

/* Mobile and tablet portrait */
@media (max-width: 768px) {
  .admin-registration {
    padding: 1rem;
    min-height: 100vh;
    align-items: flex-start;
    padding-top: 2rem;
  }

  .card {
    max-width: 100%;
    width: 100%;
    margin: 0;
  }

  .card-header {
    padding: 1.5rem 1.25rem 1.25rem;
  }

  .card-header h3 {
    font-size: clamp(1.1rem, 5vw, 1.3rem);
  }

  .card-body {
    padding: 1.5rem 1.25rem !important;
  }

  .step-number {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }

  .step-label {
    font-size: 0.8rem;
  }

  .step-line {
    margin: 0 0.5rem;
    margin-top: 17px;
  }

  .btn-success, .btn-outline-secondary {
    padding: 0.875rem 1.5rem;
    font-size: 0.95rem;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .admin-registration {
    padding: 0.75rem;
    padding-top: 1.5rem;
  }

  .card-header {
    padding: 1.25rem 1rem 1rem;
  }

  .card-body {
    padding: 1.25rem 1rem !important;
  }

  .step-number {
    width: 30px;
    height: 30px;
    font-size: 0.8rem;
  }

  .step-label {
    font-size: 0.75rem;
  }

  .step-line {
    margin: 0 0.25rem;
    margin-top: 15px;
  }
}
