/* Admin Sidebar Styles - Professional Blue & Yellow Theme */
.dashboard-sidebar {
  width: 280px;
  height: 100vh;
  background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 50%, #3b82f6 100%);
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 4px 0 20px rgba(30, 58, 138, 0.25);
  border-right: 3px solid #fbbf24;
  overflow: hidden;
  will-change: width;
  display: flex;
  flex-direction: column;
}

/* Collapsed state */
.dashboard-sidebar.collapsed {
  width: 72px;
}

/* Collapsed logo adjustments */
.dashboard-sidebar.collapsed .sidebar-logo {
  justify-content: center;
  padding: 1rem 0;
  min-height: 80px;
  display: flex;
  align-items: center;
}

.dashboard-sidebar.collapsed .logo-content {
  justify-content: center;
  gap: 0;
  width: 100%;
  display: flex;
  align-items: center;
}

.dashboard-sidebar.collapsed .logo-image-container {
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.dashboard-sidebar.collapsed .logo-text {
  opacity: 0;
  transform: translateX(-20px);
  width: 0;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.dashboard-sidebar.collapsed .mobile-close-btn {
  display: none;
}

/* Logo Section */
.sidebar-logo {
  padding: 1.5rem 1.5rem 1rem;
  border-bottom: 2px solid rgba(251, 191, 36, 0.3);
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.15) 0%, rgba(245, 158, 11, 0.1) 100%);
  position: relative;
  min-height: 90px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.logo-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  min-width: 0;
}

.logo-image-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.logo-image {
  width: 45px;
  height: 45px;
  border-radius: 12px;
  object-fit: cover;
  border: 2px solid #fbbf24;
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  display: block;
}

/* Collapsed logo image adjustments */
.dashboard-sidebar.collapsed .logo-image {
  margin: 0 auto;
  display: block;
}

.logo-image:hover {
  transform: scale(1.05);
}

.logo-text {
  flex: 1;
  min-width: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.collapsed .logo-text:not(.mobile-show) {
  opacity: 0;
  transform: translateX(-20px);
  width: 0;
  overflow: hidden;
}

.logo-title {
  font-size: 1.1rem;
  font-weight: 700;
  margin: 0;
  color: #fbbf24;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.logo-subtitle {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0.25rem 0 0 0;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mobile-close-btn {
  display: none;
  background: none;
  border: none;
  color: #fbbf24;
  font-size: 1.2rem;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 1rem;
}

.mobile-close-btn:hover {
  background-color: rgba(251, 191, 36, 0.2);
  transform: scale(1.1);
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  padding: 1.5rem 0;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
}

.nav-item {
  margin-bottom: 0.25rem;
  padding: 0 1rem;
}

/* Collapsed navigation adjustments */
.dashboard-sidebar.collapsed .nav-item {
  padding: 0 0.5rem;
  margin-bottom: 0.5rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.875rem 1.25rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: visible;
  background: transparent;
  border: 2px solid transparent;
  gap: 1rem;
  min-height: 52px;
  cursor: pointer;
}

/* Collapsed nav link adjustments */
.dashboard-sidebar.collapsed .nav-link {
  justify-content: center;
  padding: 1rem 0;
  gap: 0;
  border-radius: 16px;
  margin: 0;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.15) 0%, rgba(245, 158, 11, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 10px;
}

.nav-link:hover::before {
  opacity: 1;
}

.nav-link:hover {
  color: #fbbf24;
  border-color: rgba(251, 191, 36, 0.4);
  box-shadow: 0 4px 20px rgba(251, 191, 36, 0.2);
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.15) 0%, rgba(245, 158, 11, 0.1) 100%);
}

.nav-link:hover .nav-icon {
  color: #fbbf24;
  transform: scale(1.1);
}

.nav-link:hover .nav-text {
  color: #fbbf24;
}

/* Expanded hover effect */
.dashboard-sidebar:not(.collapsed) .nav-link:hover {
  transform: translateX(8px);
}

/* Collapsed hover effect */
.dashboard-sidebar.collapsed .nav-link:hover {
  transform: scale(1.05);
  border-radius: 16px;
}

.nav-link.active {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #1e3a8a;
  border-color: #fcd34d;
  box-shadow: 0 6px 25px rgba(251, 191, 36, 0.5);
}

/* Expanded active effect */
.dashboard-sidebar:not(.collapsed) .nav-link.active {
  transform: translateX(8px);
}

/* Collapsed active effect */
.dashboard-sidebar.collapsed .nav-link.active {
  transform: scale(1.05);
  border-radius: 16px;
}

.nav-link.active .nav-icon {
  color: #1e3a8a;
  transform: scale(1.1);
}

.nav-link.active .nav-text {
  color: #1e3a8a;
  font-weight: 700;
}

.nav-link.active::before {
  opacity: 0;
}

/* Icon Container */
.nav-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.nav-icon {
  font-size: 1.2rem;
  width: 24px;
  height: 24px;
  text-align: center;
  flex-shrink: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Collapsed badge for icons */
.collapsed-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #1e3a8a;
  font-size: 0.65rem;
  font-weight: 700;
  padding: 0.125rem 0.25rem;
  border-radius: 8px;
  min-width: 16px;
  height: 16px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  z-index: 10;
}

/* Active indicator for collapsed state */
.active-indicator {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: linear-gradient(180deg, #fbbf24 0%, #f59e0b 100%);
  border-radius: 2px 0 0 2px;
  box-shadow: 0 0 8px rgba(251, 191, 36, 0.6);
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  min-width: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard-sidebar.collapsed .nav-content {
  opacity: 0;
  transform: translateX(-20px);
  width: 0;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.nav-text {
  font-weight: 600;
  font-size: 0.95rem;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard-sidebar.collapsed .nav-text {
  opacity: 0;
  transform: translateX(-20px);
  width: 0;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.nav-badge {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #1e3a8a;
  font-size: 0.75rem;
  font-weight: 700;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  min-width: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.nav-link:hover .nav-badge {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.4);
}

/* Urgent badge styles */
.nav-badge.urgent {
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
  color: white;
  animation: pulse-urgent 2s infinite;
}

.nav-badge.urgent-badge {
  background: linear-gradient(135deg, #ff6b7a 0%, #ff4757 100%);
  color: white;
  margin-left: 0.25rem;
  font-size: 0.7rem;
  padding: 0.2rem 0.4rem;
}

.nav-badges {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Urgent indicators */
.urgent-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.6rem;
  animation: pulse-urgent 2s infinite;
  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);
}

.nav-link.has-urgent {
  position: relative;
}

.nav-link.has-urgent::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(to bottom, #ff4757, #ff6b7a);
  border-radius: 0 2px 2px 0;
}

/* Urgent pulse animation */
@keyframes pulse-urgent {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 71, 87, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 71, 87, 0);
  }
}

/* Bottom Section */
.sidebar-bottom {
  padding: 1rem;
  border-top: 2px solid rgba(251, 191, 36, 0.3);
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%);
  flex-shrink: 0;
  margin-top: auto;
}

/* Collapsed bottom section */
.dashboard-sidebar.collapsed .sidebar-bottom {
  padding: 0.75rem 0.5rem;
}

/* Enhanced Logout Section */
.logout-section {
  position: relative;
}

/* Enhanced Logout Button */
.logout-btn {
  width: 100%;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border: 2px solid #f87171;
  border-radius: 16px;
  padding: 1rem 1.25rem;
  color: white;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-family: inherit;
  text-align: left;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

/* Collapsed logout button */
.logout-btn.collapsed {
  padding: 1rem;
  justify-content: center;
  gap: 0;
  border-radius: 20px;
  min-height: 56px;
}

/* Logout button hover effects */
.logout-btn:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  border-color: #ef4444;
  color: white;
  box-shadow: 0 8px 30px rgba(239, 68, 68, 0.6);
  transform: translateY(-2px);
}

/* Expanded logout hover effect */
.dashboard-sidebar:not(.collapsed) .logout-btn:hover {
  transform: translateY(-2px) translateX(4px);
}

/* Collapsed logout hover effect */
.dashboard-sidebar.collapsed .logout-btn:hover {
  transform: translateY(-2px) scale(1.05);
}

/* Logout button active state */
.logout-btn:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.2);
}

/* Logout Icon Container */
.logout-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  flex-shrink: 0;
  transition: all 0.3s ease;
  overflow: hidden;
}

.logout-btn:hover .logout-icon-container {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

/* Logout Icon */
.logout-icon {
  font-size: 1.1rem;
  color: white;
  transition: all 0.3s ease;
  z-index: 2;
  position: relative;
}

.logout-btn:hover .logout-icon {
  color: white;
  transform: scale(1.1);
}

/* Logout Ripple Effect */
.logout-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
  opacity: 0;
}

.logout-btn:hover .logout-ripple {
  width: 40px;
  height: 40px;
  opacity: 1;
}

/* Logout Content */
.logout-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  min-width: 0;
  transition: all 0.3s ease;
}

.logout-text {
  font-size: 0.95rem;
  font-weight: 600;
  color: inherit;
  line-height: 1.2;
  transition: all 0.3s ease;
}

.logout-subtitle {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  line-height: 1.2;
  transition: all 0.3s ease;
}

.logout-btn:hover .logout-subtitle {
  color: rgba(255, 255, 255, 0.9);
}

/* Logout Arrow */
.logout-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.logout-btn:hover .logout-arrow {
  color: white;
  transform: translateX(4px);
}

/* Collapsed state adjustments */
.dashboard-sidebar.collapsed .logout-content,
.dashboard-sidebar.collapsed .logout-arrow {
  opacity: 0;
  transform: translateX(-20px);
  width: 0;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

/* Enhanced tooltip for collapsed logout */
.dashboard-sidebar.collapsed .logout-btn:hover::after {
  content: 'Logout - Sign out safely';
  position: absolute;
  left: calc(100% + 12px);
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  white-space: nowrap;
  z-index: 1001;
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.2);
  opacity: 0;
  animation: fadeInLogoutTooltip 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  pointer-events: none;
  letter-spacing: 0.025em;
}

/* Logout tooltip arrow */
.dashboard-sidebar.collapsed .logout-btn:hover::before {
  content: '';
  position: absolute;
  left: calc(100% + 4px);
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 6px 8px 6px 0;
  border-color: transparent #ef4444 transparent transparent;
  z-index: 1002;
  opacity: 0;
  animation: fadeInLogoutTooltip 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes fadeInLogoutTooltip {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

/* Scrollbar Styling */
.sidebar-nav::-webkit-scrollbar {
  width: 6px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: rgba(251, 191, 36, 0.1);
  border-radius: 3px;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(251, 191, 36, 0.3);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(251, 191, 36, 0.5);
}



/* Enhanced Tooltip for collapsed state */
.dashboard-sidebar.collapsed .nav-link:hover::after {
  content: attr(title);
  position: absolute;
  left: calc(100% + 12px);
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
  color: #fbbf24;
  padding: 0.625rem 0.875rem;
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 600;
  white-space: nowrap;
  z-index: 1001;
  box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
  border: 1px solid #fbbf24;
  opacity: 0;
  animation: fadeInTooltip 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  pointer-events: none;
  letter-spacing: 0.025em;
}

/* Tooltip arrow */
.dashboard-sidebar.collapsed .nav-link:hover::before {
  content: '';
  position: absolute;
  left: calc(100% + 4px);
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 6px 8px 6px 0;
  border-color: transparent #1e40af transparent transparent;
  z-index: 1002;
  opacity: 0;
  animation: fadeInTooltip 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes fadeInTooltip {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

/* Mobile Overlay */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(30, 58, 138, 0.4);
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1), visibility 0.3s ease;
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
  touch-action: none;
}

.mobile-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Enhanced Mobile-First Responsive Design */

/* Tablet and larger screens */
@media (min-width: 769px) {
  .dashboard-sidebar {
    position: fixed;
    transform: translateX(0);
  }

  .mobile-overlay {
    display: none;
  }
}

/* Mobile and tablet portrait */
@media (max-width: 768px) {
  .dashboard-sidebar {
    position: fixed;
    top: 60px;
    left: 0;
    width: 100%;
    max-width: 320px;
    height: calc(100vh - 60px);
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1002;
    box-shadow: 4px 0 30px rgba(30, 58, 138, 0.4);
  }

  .dashboard-sidebar:not(.collapsed) {
    transform: translateX(0);
  }

  .dashboard-sidebar.collapsed {
    transform: translateX(-100%);
    width: 100%;
    max-width: 320px;
  }

  .mobile-close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    min-height: 40px;
  }

  /* Show close button on mobile even when collapsed */
  .dashboard-sidebar.collapsed .mobile-close-btn {
    display: flex;
  }

  /* Override collapsed styles on mobile */
  .dashboard-sidebar .logo-text,
  .dashboard-sidebar .nav-text,
  .dashboard-sidebar .nav-content {
    opacity: 1 !important;
    transform: translateX(0) !important;
    width: auto !important;
    overflow: visible !important;
    pointer-events: auto !important;
  }

  /* Hide collapsed badges on mobile */
  .collapsed-badge {
    display: none !important;
  }

  /* Hide active indicators on mobile */
  .active-indicator {
    display: none !important;
  }

  .nav-item {
    padding: 0 0.75rem;
    margin-bottom: 0.25rem;
  }

  .nav-link {
    padding: 0.875rem 1rem;
    min-height: 52px;
    gap: 1rem;
    justify-content: flex-start;
  }

  .sidebar-logo {
    padding: 1rem 1.25rem 0.75rem;
    min-height: 80px;
  }

  .logo-image {
    width: 40px;
    height: 40px;
  }

  .logo-title {
    font-size: 1rem;
  }

  .logo-subtitle {
    font-size: 0.7rem;
  }

  /* Disable tooltips on mobile */
  .dashboard-sidebar .nav-link:hover::after,
  .dashboard-sidebar .nav-link:hover::before {
    display: none !important;
  }
}