/* Mobile Utilities for Admin Components */

/* Touch-friendly button sizes */
@media (max-width: 768px) {
  .btn {
    min-height: 44px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }
  
  .btn-sm {
    min-height: 38px;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }
  
  .btn-lg {
    min-height: 50px;
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
  }
}

/* Enhanced form controls for mobile */
@media (max-width: 768px) {
  .form-control,
  .form-select {
    min-height: 44px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }
  
  .form-control:focus,
  .form-select:focus {
    box-shadow: 0 0 0 0.25rem rgba(33, 37, 41, 0.25);
  }
  
  .input-group-text {
    min-height: 44px;
    padding: 0.75rem;
  }
}

/* Mobile navigation adjustments */
@media (max-width: 991.98px) {
  .navbar-toggler {
    padding: 0.5rem;
    font-size: 1.25rem;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
  }
  
  .navbar-toggler:focus {
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25);
  }
  
  .navbar-collapse {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Mobile card adjustments */
@media (max-width: 768px) {
  .card {
    margin-bottom: 1rem;
    border-radius: 12px;
  }
  
  .card-header {
    padding: 1rem;
    border-radius: 12px 12px 0 0 !important;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .card-footer {
    padding: 1rem;
    border-radius: 0 0 12px 12px !important;
  }
}

/* Mobile table responsiveness */
@media (max-width: 768px) {
  .table-responsive {
    border: none;
  }
  
  .table {
    font-size: 0.875rem;
  }
  
  .table th,
  .table td {
    padding: 0.5rem;
    vertical-align: middle;
  }
  
  .table-sm th,
  .table-sm td {
    padding: 0.25rem;
  }
}

/* Mobile modal adjustments */
@media (max-width: 768px) {
  .modal-dialog {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }
  
  .modal-content {
    border-radius: 12px;
  }
  
  .modal-header {
    padding: 1rem;
    border-radius: 12px 12px 0 0;
  }
  
  .modal-body {
    padding: 1rem;
  }
  
  .modal-footer {
    padding: 1rem;
    border-radius: 0 0 12px 12px;
  }
}

/* Mobile dropdown adjustments */
@media (max-width: 768px) {
  .dropdown-menu {
    border-radius: 12px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }
  
  .dropdown-item {
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }
  
  .dropdown-header {
    padding: 0.75rem 1rem 0.5rem;
    font-size: 0.9rem;
  }
}

/* Mobile alert adjustments */
@media (max-width: 768px) {
  .alert {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 12px;
    font-size: 0.95rem;
  }
  
  .alert-dismissible .btn-close {
    padding: 1rem;
  }
}

/* Mobile spacing utilities */
@media (max-width: 768px) {
  .mobile-p-1 { padding: 0.25rem !important; }
  .mobile-p-2 { padding: 0.5rem !important; }
  .mobile-p-3 { padding: 1rem !important; }
  .mobile-p-4 { padding: 1.5rem !important; }
  
  .mobile-m-1 { margin: 0.25rem !important; }
  .mobile-m-2 { margin: 0.5rem !important; }
  .mobile-m-3 { margin: 1rem !important; }
  .mobile-m-4 { margin: 1.5rem !important; }
  
  .mobile-mb-1 { margin-bottom: 0.25rem !important; }
  .mobile-mb-2 { margin-bottom: 0.5rem !important; }
  .mobile-mb-3 { margin-bottom: 1rem !important; }
  .mobile-mb-4 { margin-bottom: 1.5rem !important; }
  
  .mobile-mt-1 { margin-top: 0.25rem !important; }
  .mobile-mt-2 { margin-top: 0.5rem !important; }
  .mobile-mt-3 { margin-top: 1rem !important; }
  .mobile-mt-4 { margin-top: 1.5rem !important; }
}

/* Mobile text utilities */
@media (max-width: 768px) {
  .mobile-text-sm { font-size: 0.875rem !important; }
  .mobile-text-base { font-size: 1rem !important; }
  .mobile-text-lg { font-size: 1.125rem !important; }
  .mobile-text-xl { font-size: 1.25rem !important; }
  
  .mobile-text-center { text-align: center !important; }
  .mobile-text-left { text-align: left !important; }
  .mobile-text-right { text-align: right !important; }
}

/* Mobile display utilities */
@media (max-width: 768px) {
  .mobile-d-none { display: none !important; }
  .mobile-d-block { display: block !important; }
  .mobile-d-flex { display: flex !important; }
  .mobile-d-grid { display: grid !important; }
}

/* Mobile flex utilities */
@media (max-width: 768px) {
  .mobile-flex-column { flex-direction: column !important; }
  .mobile-flex-row { flex-direction: row !important; }
  .mobile-justify-center { justify-content: center !important; }
  .mobile-justify-between { justify-content: space-between !important; }
  .mobile-align-center { align-items: center !important; }
  .mobile-align-start { align-items: flex-start !important; }
}

/* Mobile grid utilities */
@media (max-width: 768px) {
  .mobile-col-12 {
    flex: 0 0 100% !important;
    max-width: 100% !important;
  }
  
  .mobile-col-6 {
    flex: 0 0 50% !important;
    max-width: 50% !important;
  }
  
  .mobile-col-4 {
    flex: 0 0 33.333333% !important;
    max-width: 33.333333% !important;
  }
  
  .mobile-col-3 {
    flex: 0 0 25% !important;
    max-width: 25% !important;
  }
}

/* Mobile overflow utilities */
@media (max-width: 768px) {
  .mobile-overflow-hidden { overflow: hidden !important; }
  .mobile-overflow-auto { overflow: auto !important; }
  .mobile-overflow-scroll { overflow: scroll !important; }
  
  .mobile-overflow-x-hidden { overflow-x: hidden !important; }
  .mobile-overflow-x-auto { overflow-x: auto !important; }
  .mobile-overflow-x-scroll { overflow-x: scroll !important; }
  
  .mobile-overflow-y-hidden { overflow-y: hidden !important; }
  .mobile-overflow-y-auto { overflow-y: auto !important; }
  .mobile-overflow-y-scroll { overflow-y: scroll !important; }
}

/* Mobile position utilities */
@media (max-width: 768px) {
  .mobile-position-relative { position: relative !important; }
  .mobile-position-absolute { position: absolute !important; }
  .mobile-position-fixed { position: fixed !important; }
  .mobile-position-sticky { position: sticky !important; }
}

/* Mobile width utilities */
@media (max-width: 768px) {
  .mobile-w-25 { width: 25% !important; }
  .mobile-w-50 { width: 50% !important; }
  .mobile-w-75 { width: 75% !important; }
  .mobile-w-100 { width: 100% !important; }
  .mobile-w-auto { width: auto !important; }
}

/* Mobile height utilities */
@media (max-width: 768px) {
  .mobile-h-25 { height: 25% !important; }
  .mobile-h-50 { height: 50% !important; }
  .mobile-h-75 { height: 75% !important; }
  .mobile-h-100 { height: 100% !important; }
  .mobile-h-auto { height: auto !important; }
}

/* Touch-friendly interactive elements */
@media (max-width: 768px) {
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .mobile-clickable {
    cursor: pointer;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    tap-highlight-color: rgba(0, 0, 0, 0.1);
  }
  
  .mobile-no-tap-highlight {
    -webkit-tap-highlight-color: transparent;
    tap-highlight-color: transparent;
  }
}

/* Mobile scrolling improvements */
@media (max-width: 768px) {
  .mobile-smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
  
  .mobile-scroll-snap-x {
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
  }
  
  .mobile-scroll-snap-y {
    scroll-snap-type: y mandatory;
    -webkit-overflow-scrolling: touch;
  }
  
  .mobile-scroll-snap-start {
    scroll-snap-align: start;
  }
  
  .mobile-scroll-snap-center {
    scroll-snap-align: center;
  }
  
  .mobile-scroll-snap-end {
    scroll-snap-align: end;
  }
}

/* Mobile safe area adjustments for notched devices */
@media (max-width: 768px) {
  .mobile-safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .mobile-safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .mobile-safe-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .mobile-safe-right {
    padding-right: env(safe-area-inset-right);
  }
  
  .mobile-safe-all {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}
