<template>
  <div class="barangay-clearance-request">
    <!-- Header -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-main">
          <h1 class="page-title">
            <i class="fas fa-certificate"></i>
            Barangay Clearance Request
          </h1>
          <p class="page-description">
            Apply for your Barangay Clearance certificate online
          </p>
        </div>
        <button class="back-btn" @click="goBack">
          <i class="fas fa-arrow-left"></i>
          Back
        </button>
      </div>
    </div>

    <!-- Progress Steps -->
    <div class="progress-steps">
      <div class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
        <div class="step-number">1</div>
        <span class="step-label">Upload Documents</span>
      </div>
      <div class="step" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
        <div class="step-number">2</div>
        <span class="step-label">Purpose & Details</span>
      </div>
      <div class="step" :class="{ active: currentStep >= 3, completed: currentStep > 3 }">
        <div class="step-number">3</div>
        <span class="step-label">Payment</span>
      </div>
      <div class="step" :class="{ active: currentStep >= 4 }">
        <div class="step-number">4</div>
        <span class="step-label">Review & Submit</span>
      </div>
    </div>

    <!-- Form Container -->
    <div class="form-container">
      <form @submit.prevent="handleSubmit">
        
        <!-- Step 1: Personal Information -->
        <div v-if="currentStep === 1" class="form-step">
          <div class="step-header">
            <h2>Personal Information</h2>
            <p>Your profile information will be used for this request</p>
          </div>

          <div class="profile-preview">
            <div class="profile-card">
              <div class="profile-info">
                <h3>{{ getFullName() }}</h3>
                <div class="info-grid">
                  <div class="info-item">
                    <label>Email:</label>
                    <span>{{ (clientData?.email || clientData?.profile?.email) || 'Not provided' }}</span>
                  </div>
                  <div class="info-item">
                    <label>Phone:</label>
                    <span>{{ (clientData?.phone_number || clientData?.profile?.phone_number) || 'Not provided' }}</span>
                  </div>
                  <div class="info-item">
                    <label>Address:</label>
                    <span>{{ getFullAddress() }}</span>
                  </div>
                  <div class="info-item">
                    <label>Date of Birth:</label>
                    <span>{{ formatDate(clientData?.birth_date || clientData?.profile?.birth_date) }}</span>
                  </div>
                  <div class="info-item">
                    <label>Gender:</label>
                    <span>{{ (clientData?.gender || clientData?.profile?.gender) || 'Not provided' }}</span>
                  </div>
                  <div class="info-item">
                    <label>Civil Status:</label>
                    <span>{{ getCivilStatusName(clientData?.civil_status_id || clientData?.profile?.civil_status_id) }}</span>
                  </div>
                  <div class="info-item">
                    <label>Nationality:</label>
                    <span>{{ (clientData?.nationality || clientData?.profile?.nationality) || 'Not provided' }}</span>
                  </div>
                  <div class="info-item">
                    <label>Years of Residency:</label>
                    <span>{{ getResidencyDisplay() }}</span>
                  </div>
                </div>
              </div>
              <div class="profile-actions">
                <button type="button" class="update-profile-btn" @click="updateProfile">
                  <i class="fas fa-edit"></i>
                  Update Profile
                </button>
              </div>
            </div>
          </div>

          <!-- Required Documents Upload -->
          <div class="form-section">
            <h3><i class="fas fa-upload"></i> Required Documents</h3>
            <p class="section-description">Please upload the following required documents as per government regulations:</p>

            <!-- Valid Government ID -->
            <div class="document-upload-group">
              <label class="document-label">
                <i class="fas fa-id-card"></i>
                Valid Government ID *
                <span class="document-info">(Driver's License, Voter's ID, Passport, etc.)</span>
              </label>
              <div class="file-upload-area" @click="triggerFileInput('government_id')" @dragover.prevent @drop.prevent="handleFileDrop($event, 'government_id')">
                <input
                  ref="governmentIdInput"
                  type="file"
                  accept="image/*,.pdf"
                  @change="handleFileSelect($event, 'government_id')"
                  style="display: none"
                  required
                />
                <div v-if="!uploadedFiles.government_id" class="upload-placeholder">
                  <i class="fas fa-cloud-upload-alt"></i>
                  <p>Click to upload or drag and drop</p>
                  <small>JPG, PNG, PDF (Max 5MB)</small>
                </div>
                <div v-else class="uploaded-file">
                  <i class="fas fa-file-check"></i>
                  <span>{{ uploadedFiles.government_id.name }}</span>
                  <button type="button" @click.stop="removeFile('government_id')" class="remove-file">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Proof of Residency -->
            <div class="document-upload-group">
              <label class="document-label">
                <i class="fas fa-home"></i>
                Proof of Residency *
                <span class="document-info">(Utility Bill, Lease Agreement, Barangay Certificate)</span>
              </label>
              <div class="file-upload-area" @click="triggerFileInput('proof_of_residency')" @dragover.prevent @drop.prevent="handleFileDrop($event, 'proof_of_residency')">
                <input
                  ref="proofOfResidencyInput"
                  type="file"
                  accept="image/*,.pdf"
                  @change="handleFileSelect($event, 'proof_of_residency')"
                  style="display: none"
                  required
                />
                <div v-if="!uploadedFiles.proof_of_residency" class="upload-placeholder">
                  <i class="fas fa-cloud-upload-alt"></i>
                  <p>Click to upload or drag and drop</p>
                  <small>JPG, PNG, PDF (Max 5MB)</small>
                </div>
                <div v-else class="uploaded-file">
                  <i class="fas fa-file-check"></i>
                  <span>{{ uploadedFiles.proof_of_residency.name }}</span>
                  <button type="button" @click.stop="removeFile('proof_of_residency')" class="remove-file">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Community Tax Certificate (Optional) -->
            <div class="document-upload-group">
              <label class="document-label">
                <i class="fas fa-certificate"></i>
                Community Tax Certificate (Cedula)
                <span class="document-info optional">(Optional - if available)</span>
              </label>
              <div class="file-upload-area" @click="triggerFileInput('cedula')" @dragover.prevent @drop.prevent="handleFileDrop($event, 'cedula')">
                <input
                  ref="cedulaInput"
                  type="file"
                  accept="image/*,.pdf"
                  @change="handleFileSelect($event, 'cedula')"
                  style="display: none"
                />
                <div v-if="!uploadedFiles.cedula" class="upload-placeholder">
                  <i class="fas fa-cloud-upload-alt"></i>
                  <p>Click to upload or drag and drop</p>
                  <small>JPG, PNG, PDF (Max 5MB)</small>
                </div>
                <div v-else class="uploaded-file">
                  <i class="fas fa-file-check"></i>
                  <span>{{ uploadedFiles.cedula.name }}</span>
                  <button type="button" @click.stop="removeFile('cedula')" class="remove-file">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Legal Notice -->
          <div class="form-section">
            <div class="legal-notice">
              <h3><i class="fas fa-info-circle"></i> Important Notice</h3>
              <p>This barangay clearance certifies that you are a resident in good standing with no pending legal cases or disputes within the barangay. Only information required by law is collected.</p>
              <div class="data-privacy-note">
                <small><i class="fas fa-shield-alt"></i> Your personal information is protected under the Data Privacy Act of 2012.</small>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 2: Purpose and Details -->
        <div v-if="currentStep === 2" class="form-step">
          <div class="step-header">
            <h2>Purpose and Additional Details</h2>
            <p>Please provide the purpose and any additional information</p>
          </div>

          <div class="form-section">
            <div class="form-group">
              <label for="purpose_category">Purpose Category *</label>
              <select
                id="purpose_category"
                v-model="formData.purpose_category_id"
                required
                @change="onPurposeChange"
              >
                <option value="">Select purpose</option>
                <option
                  v-for="category in purposeCategories"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.category_name }}
                </option>
              </select>
            </div>

            <div class="form-group">
              <label for="purpose_details">Purpose Details *</label>
              <textarea
                id="purpose_details"
                v-model="formData.purpose_details"
                rows="3"
                required
                placeholder="Please provide specific details about the purpose of this clearance"
              ></textarea>
            </div>

            <div class="form-group">
              <label for="pending_cases">Pending Cases Declaration *</label>
              <div class="radio-group">
                <label class="radio-option">
                  <input
                    type="radio"
                    v-model="formData.has_pending_cases"
                    :value="false"
                    required
                  />
                  <span class="radio-custom"></span>
                  No pending cases
                </label>
                <label class="radio-option">
                  <input
                    type="radio"
                    v-model="formData.has_pending_cases"
                    :value="true"
                    required
                  />
                  <span class="radio-custom"></span>
                  Has pending cases
                </label>
              </div>
            </div>

            <div v-if="formData.has_pending_cases" class="form-group">
              <label for="pending_cases_details">Pending Cases Details *</label>
              <textarea
                id="pending_cases_details"
                v-model="formData.pending_cases_details"
                rows="3"
                required
                placeholder="Please provide details about pending cases"
              ></textarea>
            </div>

            <div class="form-group">
              <label for="voter_registration">Voter Registration Status</label>
              <div class="radio-group">
                <label class="radio-option">
                  <input
                    type="radio"
                    v-model="formData.is_registered_voter"
                    :value="true"
                  />
                  <span class="radio-custom"></span>
                  Registered voter
                </label>
                <label class="radio-option">
                  <input
                    type="radio"
                    v-model="formData.is_registered_voter"
                    :value="false"
                  />
                  <span class="radio-custom"></span>
                  Not registered
                </label>
              </div>
            </div>

            <div class="form-group">
              <label for="additional_notes">Additional Notes</label>
              <textarea
                id="additional_notes"
                v-model="formData.additional_notes"
                rows="2"
                placeholder="Any additional information or special requests"
              ></textarea>
            </div>
          </div>
        </div>

        <!-- Step 3: Payment Method -->
        <div v-if="currentStep === 3" class="form-step">
          <div class="step-header">
            <h2>Payment Information</h2>
            <p>Choose your preferred payment method</p>
          </div>

          <!-- Fee Summary -->
          <div class="fee-summary">
            <div class="fee-card">
              <h3>Fee Breakdown</h3>
              <div class="fee-items">
                <div class="fee-item">
                  <span>Barangay Clearance Fee</span>
                  <span>₱{{ formatCurrency(baseFee) }}</span>
                </div>
                <div class="fee-item total">
                  <span>Total Amount</span>
                  <span>₱{{ formatCurrency(totalFee) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Payment Methods -->
          <div class="form-section">
            <h3>Select Payment Method</h3>
            <div class="payment-methods">
              <div
                v-for="method in paymentMethods"
                :key="method.id"
                class="payment-option"
                :class="{ selected: formData.payment_method_id === method.id }"
                @click="selectPaymentMethod(method.id)"
              >
                <div class="payment-icon">
                  <i :class="getPaymentIcon(method.method_code)"></i>
                </div>
                <div class="payment-info">
                  <h4>{{ method.method_name }}</h4>
                  <p v-if="method.description">{{ method.description }}</p>
                </div>
                <div class="payment-radio">
                  <input
                    type="radio"
                    :value="method.id"
                    v-model="formData.payment_method_id"
                    required
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 4: Review and Submit -->
        <div v-if="currentStep === 4" class="form-step">
          <div class="step-header">
            <h2>Review Your Request</h2>
            <p>Please review all information before submitting</p>
          </div>

          <div class="review-sections">
            <!-- Personal Information Review -->
            <div class="review-section">
              <h3>Personal Information</h3>
              <div class="review-grid">
                <div class="review-item">
                  <label>Full Name:</label>
                  <span>{{ getFullName() }}</span>
                </div>
                <div class="review-item">
                  <label>Address:</label>
                  <span>{{ getFullAddress() }}</span>
                </div>
                <div class="review-item">
                  <label>Phone:</label>
                  <span>{{ (clientData?.phone_number || clientData?.profile?.phone_number) || 'Not provided' }}</span>
                </div>
              </div>
            </div>

            <!-- Cedula Information Review -->
            <div class="review-section">
              <h3>Cedula Information</h3>
              <div class="review-grid">
                <div class="review-item">
                  <label>Cedula Number:</label>
                  <span>{{ formData.cedula_number }}</span>
                </div>
                <div class="review-item">
                  <label>Date Issued:</label>
                  <span>{{ formData.cedula_date_issued }}</span>
                </div>
                <div class="review-item">
                  <label>Place Issued:</label>
                  <span>{{ formData.cedula_place_issued }}</span>
                </div>
              </div>
            </div>

            <!-- Purpose Review -->
            <div class="review-section">
              <h3>Purpose & Details</h3>
              <div class="review-grid">
                <div class="review-item">
                  <label>Purpose:</label>
                  <span>{{ getPurposeCategoryName() }}</span>
                </div>
                <div class="review-item">
                  <label>Details:</label>
                  <span>{{ formData.purpose_details }}</span>
                </div>
                <div class="review-item">
                  <label>Pending Cases:</label>
                  <span>{{ formData.has_pending_cases ? 'Yes' : 'No' }}</span>
                </div>
                <div v-if="formData.has_pending_cases && formData.pending_cases_details" class="review-item">
                  <label>Case Details:</label>
                  <span>{{ formData.pending_cases_details }}</span>
                </div>
              </div>
            </div>

            <!-- Payment Review -->
            <div class="review-section">
              <h3>Payment Information</h3>
              <div class="review-grid">
                <div class="review-item">
                  <label>Payment Method:</label>
                  <span>{{ getPaymentMethodName() }}</span>
                </div>
                <div class="review-item">
                  <label>Total Amount:</label>
                  <span class="amount">₱{{ formatCurrency(totalFee) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Terms and Conditions -->
          <div class="terms-section">
            <label class="checkbox-option">
              <input
                type="checkbox"
                v-model="formData.agree_to_terms"
                required
              />
              <span class="checkbox-custom"></span>
              I agree to the <a href="#" @click.prevent="showTerms">terms and conditions</a> and certify that all information provided is true and accurate.
            </label>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button
            v-if="currentStep > 1"
            type="button"
            class="btn-secondary"
            @click="previousStep"
          >
            <i class="fas fa-chevron-left"></i>
            Previous
          </button>
          
          <button
            v-if="currentStep < 4"
            type="button"
            class="btn-primary"
            @click="nextStep"
            :disabled="!canProceedToNextStep()"
          >
            Next
            <i class="fas fa-chevron-right"></i>
          </button>
          
          <button
            v-if="currentStep === 4"
            type="submit"
            class="btn-submit"
            :disabled="submitting || !formData.agree_to_terms"
          >
            <template v-if="submitting">
              <i class="fas fa-spinner fa-spin"></i>
              Submitting...
            </template>
            <template v-else>
              <i class="fas fa-paper-plane"></i>
              Submit Request
            </template>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import documentRequestService from '@/services/documentRequestService';
import clientAuthService from '@/services/clientAuthService';

export default {
  name: 'BarangayClearanceRequest',
  data() {
    return {
      currentStep: 1,
      submitting: false,
      purposeCategories: [],
      paymentMethods: [],
      baseFee: 150.00,
      totalFee: 150.00,
      formData: {
        document_type_id: 2, // Barangay Clearance
        purpose_category_id: '',
        purpose_details: '',
        has_pending_cases: false,
        pending_cases_details: '',
        payment_method_id: '',
        agree_to_terms: false
      },
      uploadedFiles: {
        government_id: null,
        proof_of_residency: null,
        cedula: null
      },
      uploadErrors: {},
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedFileTypes: ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'],
      clientData: null, // Fresh profile data
    };
  },
  computed: {
    // Keep the old method as fallback
    cachedClientData() {
      return clientAuthService.getCurrentUser();
    }
  },
  async mounted() {
    await this.loadFormData();
  },
  methods: {
    async loadFormData() {
      try {
        // Load fresh profile data first
        console.log('Loading fresh profile data...');
        const profileResponse = await clientAuthService.getProfile();
        if (profileResponse.success) {
          this.clientData = profileResponse.data;
          console.log('Fresh profile data loaded:', this.clientData);
        } else {
          // Fallback to cached data
          this.clientData = this.cachedClientData;
          console.log('Using cached profile data:', this.clientData);
        }

        const [purposeResponse, paymentResponse] = await Promise.all([
          documentRequestService.getPurposeCategories(),
          documentRequestService.getPaymentMethods()
        ]);

        this.purposeCategories = purposeResponse.data || [];
        this.paymentMethods = paymentResponse.data || [];

      } catch (error) {
        console.error('Error loading form data:', error);
        // Fallback to cached data on error
        this.clientData = this.cachedClientData;
        this.$toast?.error('Failed to load some form data');
      }
    },

    getFullName() {
      // Try fresh data first, then fallback to cached data structure
      const profile = this.clientData || this.clientData?.profile;
      if (!profile) return 'N/A';
      return `${profile.first_name || ''} ${profile.middle_name || ''} ${profile.last_name || ''}`.trim();
    },

    getFullAddress() {
      // Try fresh data first, then fallback to cached data structure
      const profile = this.clientData || this.clientData?.profile;
      if (!profile) return 'Not provided';

      const parts = [
        profile.house_number,
        profile.street,
        profile.subdivision,
        profile.barangay,
        profile.city_municipality || profile.city,
        profile.province
      ].filter(Boolean);

      return parts.length > 0 ? parts.join(', ') : 'Not provided';
    },

    getCivilStatusName(statusId) {
      const statuses = {
        1: 'Single',
        2: 'Married',
        3: 'Divorced',
        4: 'Widowed',
        5: 'Separated'
      };
      return statuses[statusId] || 'Not provided';
    },

    getResidencyDisplay() {
      const profile = this.clientData || this.clientData?.profile;
      if (!profile) return 'Not provided';

      const years = profile.years_of_residency;
      const months = profile.months_of_residency;

      if (!years && !months) return 'Not provided';

      const parts = [];
      if (years) parts.push(`${years} year${years > 1 ? 's' : ''}`);
      if (months) parts.push(`${months} month${months > 1 ? 's' : ''}`);

      return parts.join(' and ');
    },

    formatDate(dateString) {
      if (!dateString) return 'Not provided';
      return new Date(dateString).toLocaleDateString();
    },

    formatCurrency(amount) {
      return parseFloat(amount).toFixed(2);
    },

    canProceedToNextStep() {
      switch (this.currentStep) {
        case 1:
          // Step 1: Required documents must be uploaded
          return this.uploadedFiles.government_id && this.uploadedFiles.proof_of_residency;
        case 2:
          return this.formData.purpose_category_id &&
                 this.formData.purpose_details &&
                 this.formData.has_pending_cases !== null;
        case 3:
          return this.formData.payment_method_id;
        default:
          return true;
      }
    },

    nextStep() {
      if (this.canProceedToNextStep() && this.currentStep < 4) {
        this.currentStep++;
      }
    },

    previousStep() {
      if (this.currentStep > 1) {
        this.currentStep--;
      }
    },

    onPurposeChange() {
      // Could implement dynamic fee calculation based on purpose
    },

    redirectToCedula() {
      // Redirect to Cedula application page
      this.$router.push('/client/cedula-request');
    },

    selectPaymentMethod(methodId) {
      this.formData.payment_method_id = methodId;
    },

    getPaymentIcon(methodCode) {
      const icons = {
        'CASH': 'fas fa-money-bill',
        'PAYMONGO_CARD': 'fas fa-credit-card',
        'PAYMONGO_GCASH': 'fab fa-google-pay',
        'PAYMONGO_GRABPAY': 'fas fa-mobile-alt',
        'PAYMONGO_PAYMAYA': 'fas fa-wallet'
      };
      return icons[methodCode] || 'fas fa-credit-card';
    },

    getPurposeCategoryName() {
      const category = this.purposeCategories.find(c => c.id === this.formData.purpose_category_id);
      return category?.category_name || '';
    },

    getPaymentMethodName() {
      const method = this.paymentMethods.find(m => m.id === this.formData.payment_method_id);
      return method?.method_name || '';
    },

    async handleSubmit() {
      if (!this.formData.agree_to_terms) return;

      try {
        this.submitting = true;

        // Prepare request data with proper validation
        const requestData = {
          document_type_id: parseInt(this.formData.document_type_id) || 2,
          purpose_category_id: parseInt(this.formData.purpose_category_id) || 1,
          purpose_details: this.formData.purpose_details && this.formData.purpose_details.length >= 10
            ? this.formData.purpose_details
            : 'Barangay Clearance request for official purposes',
          payment_method_id: parseInt(this.formData.payment_method_id) || null,
          delivery_method: 'pickup',
          priority: 'normal',
          // Barangay Clearance specific fields (legally required)
          has_pending_cases: Boolean(this.formData.has_pending_cases),
          pending_cases_details: this.formData.pending_cases_details || null,
          cedula_number: this.formData.cedula_number || null,
          cedula_date_issued: this.formData.cedula_date_issued || null,
          cedula_place_issued: this.formData.cedula_place_issued || null,
          total_fee: this.totalFee || 150.00
        };

        console.log('Submitting request data:', requestData);

        const response = await documentRequestService.submitRequest(requestData);

        const requestId = response.data.id;
        console.log('Request created with ID:', requestId);

        // Upload documents if any are selected
        const hasDocuments = this.uploadedFiles.government_id ||
                            this.uploadedFiles.proof_of_residency ||
                            this.uploadedFiles.cedula;

        if (hasDocuments) {
          console.log('Uploading documents...');
          await this.uploadDocumentsToRequest(requestId);
        }

        this.$toast?.success('Request submitted successfully!');
        this.$router.push({
          name: 'RequestDetails',
          params: { id: requestId }
        });

      } catch (error) {
        console.error('Error submitting request:', error);
        console.error('Error details:', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message
        });

        let errorMessage = 'Failed to submit request';
        if (error.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error.response?.data?.errors) {
          errorMessage = error.response.data.errors.map(e => e.msg).join(', ');
        } else if (error.message) {
          errorMessage = error.message;
        }

        this.$toast?.error(errorMessage);
      } finally {
        this.submitting = false;
      }
    },

    async uploadDocumentsToRequest(requestId) {
      try {
        const filesToUpload = [];

        // Collect files to upload
        Object.entries(this.uploadedFiles).forEach(([type, file]) => {
          if (file) {
            filesToUpload.push({ type, file });
          }
        });

        if (filesToUpload.length === 0) {
          return;
        }

        // Upload documents using the service
        const uploadResponse = await documentRequestService.uploadDocuments(requestId, filesToUpload);

        if (uploadResponse.success) {
          console.log('Documents uploaded successfully:', uploadResponse.data);
          this.$toast?.success(`${uploadResponse.data.total_uploaded} document(s) uploaded successfully`);
        } else {
          console.error('Document upload failed:', uploadResponse);
          this.$toast?.warning('Request submitted but some documents failed to upload');
        }

      } catch (error) {
        console.error('Document upload error:', error);
        this.$toast?.warning('Request submitted but document upload failed. You can upload documents later.');
      }
    },

    goBack() {
      this.$router.push({ name: 'NewDocumentRequest' });
    },

    updateProfile() {
      // TODO: Navigate to profile update page
      console.log('Update profile');
    },

    // File handling methods
    triggerFileInput(fileType) {
      // Convert snake_case to camelCase for ref names
      const refNameMap = {
        'government_id': 'governmentIdInput',
        'proof_of_residency': 'proofOfResidencyInput',
        'cedula': 'cedulaInput'
      };

      const inputRef = refNameMap[fileType];

      if (!inputRef) {
        console.error(`Unknown file type: ${fileType}`);
        return;
      }

      // Add safety check for ref existence
      if (this.$refs[inputRef]) {
        this.$refs[inputRef].click();
      } else {
        console.warn(`File input ref '${inputRef}' not found`);
        // Try again after next tick
        this.$nextTick(() => {
          if (this.$refs[inputRef]) {
            this.$refs[inputRef].click();
          } else {
            console.error(`File input ref '${inputRef}' still not found after nextTick`);
          }
        });
      }
    },

    handleFileSelect(event, fileType) {
      const file = event.target.files[0];
      if (file) {
        this.validateAndSetFile(file, fileType);
      }
    },

    handleFileDrop(event, fileType) {
      const file = event.dataTransfer.files[0];
      if (file) {
        this.validateAndSetFile(file, fileType);
      }
    },

    validateAndSetFile(file, fileType) {
      // Clear previous errors (Vue 3 compatible)
      delete this.uploadErrors[fileType];

      // Validate file size
      if (file.size > this.maxFileSize) {
        this.uploadErrors[fileType] = 'File size must be less than 5MB';
        this.$toast?.error(`File size must be less than 5MB`);
        return;
      }

      // Validate file type
      if (!this.allowedFileTypes.includes(file.type)) {
        this.uploadErrors[fileType] = 'Only JPG, PNG, and PDF files are allowed';
        this.$toast?.error('Only JPG, PNG, and PDF files are allowed');
        return;
      }

      // Set the file (Vue 3 compatible)
      this.uploadedFiles[fileType] = file;
      this.$toast?.success(`${file.name} uploaded successfully`);
    },

    removeFile(fileType) {
      // Vue 3 compatible reactive updates
      this.uploadedFiles[fileType] = null;
      delete this.uploadErrors[fileType];

      // Convert snake_case to camelCase for ref names
      const refNameMap = {
        'government_id': 'governmentIdInput',
        'proof_of_residency': 'proofOfResidencyInput',
        'cedula': 'cedulaInput'
      };

      const inputRef = refNameMap[fileType];

      // Clear the input with safety check
      if (inputRef && this.$refs[inputRef]) {
        this.$refs[inputRef].value = '';
      } else {
        console.warn(`File input ref '${inputRef}' not found during removal`);
      }
    },

    showTerms() {
      // TODO: Show terms and conditions modal
      console.log('Show terms');
    }
  }
};
</script>

<style scoped>
.barangay-clearance-request {
  padding: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1a365d;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.page-title i {
  color: #3182ce;
}

.page-description {
  font-size: 1rem;
  color: #4a5568;
  margin: 0;
}

.back-btn {
  background: #e2e8f0;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  color: #4a5568;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.back-btn:hover {
  background: #cbd5e0;
  color: #2d3748;
}

.progress-steps {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
  position: relative;
}

.progress-steps::before {
  content: '';
  position: absolute;
  top: 1.5rem;
  left: 25%;
  right: 25%;
  height: 2px;
  background: #e2e8f0;
  z-index: 1;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
  z-index: 2;
}

.step-number {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: #e2e8f0;
  color: #a0aec0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-bottom: 0.5rem;
  transition: all 0.3s;
}

.step.active .step-number {
  background: #3182ce;
  color: white;
}

.step.completed .step-number {
  background: #38a169;
  color: white;
}

.step-label {
  font-size: 0.875rem;
  color: #718096;
  text-align: center;
}

.step.active .step-label {
  color: #3182ce;
  font-weight: 500;
}

.form-container {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.form-step {
  min-height: 400px;
}

.step-header {
  text-align: center;
  margin-bottom: 2rem;
}

.step-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 0.5rem;
}

.step-header p {
  color: #4a5568;
  margin: 0;
}

.profile-preview {
  margin-bottom: 2rem;
}

.profile-card {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.profile-info h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 1rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #718096;
}

.info-item span {
  color: #2d3748;
}

.update-profile-btn {
  background: #3182ce;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.update-profile-btn:hover {
  background: #2c5aa0;
}

.form-section {
  margin-bottom: 2rem;
}

.form-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 1rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #2d3748;
  font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3182ce;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
}

.radio-option:hover {
  background: #f7fafc;
}

.radio-option input[type="radio"] {
  display: none;
}

.radio-custom {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid #e2e8f0;
  border-radius: 50%;
  position: relative;
  transition: all 0.2s;
}

.radio-option input[type="radio"]:checked + .radio-custom {
  border-color: #3182ce;
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0.5rem;
  height: 0.5rem;
  background: #3182ce;
  border-radius: 50%;
}

.fee-summary {
  margin-bottom: 2rem;
}

.fee-card {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
}

.fee-card h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 1rem;
}

.fee-items {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.fee-item.total {
  border-top: 1px solid #e2e8f0;
  padding-top: 1rem;
  font-weight: 600;
  font-size: 1.125rem;
  color: #1a365d;
}

.payment-methods {
  display: grid;
  gap: 1rem;
}

.payment-option {
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.payment-option:hover {
  border-color: #cbd5e0;
}

.payment-option.selected {
  border-color: #3182ce;
  background: #ebf8ff;
}

.payment-icon {
  width: 3rem;
  height: 3rem;
  background: #f7fafc;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: #4a5568;
}

.payment-option.selected .payment-icon {
  background: #3182ce;
  color: white;
}

.payment-info {
  flex: 1;
}

.payment-info h4 {
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 0.25rem;
}

.payment-info p {
  color: #718096;
  font-size: 0.875rem;
  margin: 0;
}

.payment-radio input {
  width: 1.25rem;
  height: 1.25rem;
}

.review-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-bottom: 2rem;
}

.review-section {
  background: #f7fafc;
  border-radius: 0.75rem;
  padding: 1.5rem;
}

.review-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 1rem;
}

.review-grid {
  display: grid;
  gap: 1rem;
}

.review-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e2e8f0;
}

.review-item:last-child {
  border-bottom: none;
}

.review-item label {
  font-weight: 500;
  color: #4a5568;
  min-width: 120px;
}

.review-item span {
  color: #2d3748;
  text-align: right;
  flex: 1;
}

.review-item .amount {
  font-weight: 600;
  color: #38a169;
  font-size: 1.125rem;
}

.terms-section {
  margin-bottom: 2rem;
  padding: 1rem;
  background: #fffaf0;
  border: 1px solid #fed7aa;
  border-radius: 0.5rem;
}

.checkbox-option {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  line-height: 1.5;
}

.checkbox-option input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid #e2e8f0;
  border-radius: 0.25rem;
  position: relative;
  flex-shrink: 0;
  margin-top: 0.125rem;
  transition: all 0.2s;
}

.checkbox-option input[type="checkbox"]:checked + .checkbox-custom {
  border-color: #3182ce;
  background: #3182ce;
}

.checkbox-option input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.875rem;
  font-weight: bold;
}

.checkbox-option a {
  color: #3182ce;
  text-decoration: underline;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid #e2e8f0;
}

.btn-secondary,
.btn-primary,
.btn-submit {
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
}

.btn-secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-secondary:hover {
  background: #cbd5e0;
}

.btn-primary {
  background: #3182ce;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2c5aa0;
}

.btn-submit {
  background: #38a169;
  color: white;
}

.btn-submit:hover:not(:disabled) {
  background: #2f855a;
}

.btn-primary:disabled,
.btn-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .barangay-clearance-request {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .progress-steps {
    flex-wrap: wrap;
    gap: 1rem;
  }

  .progress-steps::before {
    display: none;
  }

  .form-container {
    padding: 1.5rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .profile-card {
    flex-direction: column;
    gap: 1rem;
  }

  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .review-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .review-item span {
    text-align: left;
  }
}

/* Legal Notice Styles */
.legal-notice {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 1px solid #2196f3;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.legal-notice h3 {
  color: #1976d2;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legal-notice p {
  color: #424242;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.data-privacy-note {
  background: rgba(76, 175, 80, 0.1);
  border-left: 4px solid #4caf50;
  padding: 0.75rem;
  border-radius: 4px;
}

.data-privacy-note small {
  color: #2e7d32;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Document Upload Styles */
.document-upload-group {
  margin-bottom: 1.5rem;
}

.document-label {
  display: block;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.document-label i {
  margin-right: 0.5rem;
  color: #3498db;
}

.document-info {
  font-weight: 400;
  color: #7f8c8d;
  font-size: 0.875rem;
  display: block;
  margin-top: 0.25rem;
}

.document-info.optional {
  color: #27ae60;
}

.file-upload-area {
  border: 2px dashed #bdc3c7;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.file-upload-area:hover {
  border-color: #3498db;
  background: #e3f2fd;
}

.file-upload-area.dragover {
  border-color: #2ecc71;
  background: #e8f5e8;
}

.upload-placeholder i {
  font-size: 2rem;
  color: #95a5a6;
  margin-bottom: 0.5rem;
}

.upload-placeholder p {
  margin: 0.5rem 0;
  color: #2c3e50;
  font-weight: 500;
}

.upload-placeholder small {
  color: #7f8c8d;
}

.uploaded-file {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #27ae60;
  font-weight: 500;
}

.uploaded-file i {
  color: #27ae60;
}

.remove-file {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 0.5rem;
  transition: background 0.3s ease;
}

.remove-file:hover {
  background: #c0392b;
}

.upload-error {
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

@media (max-width: 768px) {
  .file-upload-area {
    padding: 1.5rem 1rem;
  }

  .upload-placeholder i {
    font-size: 1.5rem;
  }
}
</style>
