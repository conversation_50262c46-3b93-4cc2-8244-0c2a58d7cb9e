<template>
  <div class="cedula-request">
    <!-- Header -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-main">
          <h1 class="page-title">
            <i class="fas fa-id-card"></i>
            <PERSON><PERSON><PERSON> (Community Tax Certificate) Request
          </h1>
          <p class="page-description">
            Apply for your Community Tax Certificate online
          </p>
        </div>
        <button class="back-btn" @click="goBack">
          <i class="fas fa-arrow-left"></i>
          Back
        </button>
      </div>
    </div>

    <!-- Progress Steps -->
    <div class="progress-steps">
      <div class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
        <div class="step-number">1</div>
        <span class="step-label">Personal Info</span>
      </div>
      <div class="step" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
        <div class="step-number">2</div>
        <span class="step-label">Income & Assets</span>
      </div>
      <div class="step" :class="{ active: currentStep >= 3, completed: currentStep > 3 }">
        <div class="step-number">3</div>
        <span class="step-label">Payment</span>
      </div>
      <div class="step" :class="{ active: currentStep >= 4 }">
        <div class="step-number">4</div>
        <span class="step-label">Review & Submit</span>
      </div>
    </div>

    <!-- Form Container -->
    <div class="form-container">
      <form @submit.prevent="handleSubmit">
        
        <!-- Step 1: Personal Information -->
        <div v-if="currentStep === 1" class="form-step">
          <div class="step-header">
            <h2>Personal Information</h2>
            <p>Your profile information will be used for this request</p>
          </div>

          <div class="profile-preview">
            <div class="profile-card">
              <div class="profile-info">
                <h3>{{ getFullName() }}</h3>
                <div class="info-grid">
                  <div class="info-item">
                    <label>Email:</label>
                    <span>{{ (clientData?.email || clientData?.profile?.email) || 'Not provided' }}</span>
                  </div>
                  <div class="info-item">
                    <label>Phone:</label>
                    <span>{{ (clientData?.phone_number || clientData?.profile?.phone_number) || 'Not provided' }}</span>
                  </div>
                  <div class="info-item">
                    <label>Address:</label>
                    <span>{{ getFullAddress() }}</span>
                  </div>
                  <div class="info-item">
                    <label>Date of Birth:</label>
                    <span>{{ formatDate(clientData?.birth_date || clientData?.profile?.birth_date) }}</span>
                  </div>
                  <div class="info-item">
                    <label>Gender:</label>
                    <span>{{ formatGender(clientData?.gender || clientData?.profile?.gender) || 'Not provided' }}</span>
                  </div>
                  <div class="info-item">
                    <label>Civil Status:</label>
                    <span>{{ getCivilStatusName(clientData?.civil_status_id || clientData?.profile?.civil_status_id) }}</span>
                  </div>
                  <div class="info-item">
                    <label>Nationality:</label>
                    <span>{{ (clientData?.nationality || clientData?.profile?.nationality) || 'Not provided' }}</span>
                  </div>
                  <div class="info-item">
                    <label>Years of Residency:</label>
                    <span>{{ getResidencyDisplay() }}</span>
                  </div>
                  <div class="info-item">
                    <label>Citizenship:</label>
                    <span>{{ clientData?.profile?.citizenship || 'Filipino' }}</span>
                  </div>
                </div>
              </div>
              <div class="profile-actions">
                <button type="button" class="update-profile-btn" @click="updateProfile">
                  <i class="fas fa-edit"></i>
                  Update Profile
                </button>
              </div>
            </div>
          </div>

          <!-- Purpose Information -->
          <div class="form-section">
            <h3>Purpose of Cedula</h3>
            <div class="form-group">
              <label for="purpose_category">Purpose Category *</label>
              <select
                id="purpose_category"
                v-model="formData.purpose_category_id"
                required
              >
                <option value="">Select purpose</option>
                <option
                  v-for="category in purposeCategories"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.category_name }}
                </option>
              </select>
            </div>

            <div class="form-group">
              <label for="purpose_details">Purpose Details *</label>
              <textarea
                id="purpose_details"
                v-model="formData.purpose_details"
                rows="3"
                required
                placeholder="Please provide specific details about the purpose of this cedula"
              ></textarea>
            </div>
          </div>
        </div>

        <!-- Step 2: Income and Assets Information -->
        <div v-if="currentStep === 2" class="form-step">
          <div class="step-header">
            <h2>Income and Assets Information</h2>
            <p>Please provide accurate information for tax computation</p>
          </div>

          <!-- Income Information -->
          <div class="form-section">
            <h3>Income Information</h3>
            <div class="form-grid">
              <div class="form-group">
                <label for="annual_income">Annual Income (₱) *</label>
                <input
                  id="annual_income"
                  v-model.number="formData.annual_income"
                  type="number"
                  min="0"
                  step="0.01"
                  required
                  placeholder="0.00"
                  @input="calculateTax"
                />
              </div>
              <div class="form-group">
                <label for="income_source">Primary Income Source *</label>
                <select
                  id="income_source"
                  v-model="formData.income_source"
                  required
                >
                  <option value="">Select income source</option>
                  <option value="Employment">Employment/Salary</option>
                  <option value="Business">Business</option>
                  <option value="Professional">Professional Practice</option>
                  <option value="Agriculture">Agriculture</option>
                  <option value="Pension">Pension</option>
                  <option value="Investment">Investment</option>
                  <option value="Other">Other</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Business Information (if applicable) -->
          <div v-if="formData.income_source === 'Business' || formData.income_source === 'Professional'" class="form-section">
            <h3>Business/Professional Information</h3>
            <div class="form-grid">
              <div class="form-group">
                <label for="business_name">Business/Practice Name</label>
                <input
                  id="business_name"
                  v-model="formData.business_name"
                  type="text"
                  placeholder="Name of business or professional practice"
                />
              </div>
              <div class="form-group">
                <label for="business_address">Business Address</label>
                <textarea
                  id="business_address"
                  v-model="formData.business_address"
                  rows="2"
                  placeholder="Complete business address"
                ></textarea>
              </div>
              <div class="form-group">
                <label for="business_nature">Nature of Business/Profession</label>
                <input
                  id="business_nature"
                  v-model="formData.business_nature"
                  type="text"
                  placeholder="e.g., Retail, Consulting, Medical Practice"
                />
              </div>
            </div>
          </div>

          <!-- Property Information -->
          <div class="form-section">
            <h3>Property Information</h3>
            <div class="form-group">
              <label for="has_real_property">Do you own real property? *</label>
              <div class="radio-group">
                <label class="radio-option">
                  <input
                    type="radio"
                    v-model="formData.has_real_property"
                    :value="false"
                    required
                  />
                  <span class="radio-custom"></span>
                  No
                </label>
                <label class="radio-option">
                  <input
                    type="radio"
                    v-model="formData.has_real_property"
                    :value="true"
                    required
                  />
                  <span class="radio-custom"></span>
                  Yes
                </label>
              </div>
            </div>

            <div v-if="formData.has_real_property" class="form-grid">
              <div class="form-group">
                <label for="property_value">Assessed Property Value (₱) *</label>
                <input
                  id="property_value"
                  v-model.number="formData.property_value"
                  type="number"
                  min="0"
                  step="0.01"
                  required
                  placeholder="0.00"
                  @input="calculateTax"
                />
              </div>
              <div class="form-group">
                <label for="property_location">Property Location</label>
                <textarea
                  id="property_location"
                  v-model="formData.property_location"
                  rows="2"
                  placeholder="Complete property address"
                ></textarea>
              </div>
            </div>
          </div>

          <!-- Personal Property Information -->
          <div class="form-section">
            <h3>Personal Property Information</h3>
            <div class="form-group">
              <label for="has_personal_property">Do you own personal property worth ₱1,000 or more? *</label>
              <div class="radio-group">
                <label class="radio-option">
                  <input
                    type="radio"
                    v-model="formData.has_personal_property"
                    :value="false"
                    required
                  />
                  <span class="radio-custom"></span>
                  No
                </label>
                <label class="radio-option">
                  <input
                    type="radio"
                    v-model="formData.has_personal_property"
                    :value="true"
                    required
                  />
                  <span class="radio-custom"></span>
                  Yes
                </label>
              </div>
            </div>

            <div v-if="formData.has_personal_property" class="form-group">
              <label for="personal_property_value">Personal Property Value (₱) *</label>
              <input
                id="personal_property_value"
                v-model.number="formData.personal_property_value"
                type="number"
                min="1000"
                step="0.01"
                required
                placeholder="1000.00"
                @input="calculateTax"
              />
              <small class="field-note">Include vehicles, jewelry, equipment, etc. worth ₱1,000 or more</small>
            </div>
          </div>

          <!-- Business/Professional Information -->
          <div class="form-section">
            <h3>Business/Professional Information</h3>
            <div class="form-group">
              <label for="has_business">Do you own a business or practice a profession? *</label>
              <div class="radio-group">
                <label class="radio-option">
                  <input
                    type="radio"
                    v-model="formData.has_business"
                    :value="false"
                    required
                  />
                  <span class="radio-custom"></span>
                  No
                </label>
                <label class="radio-option">
                  <input
                    type="radio"
                    v-model="formData.has_business"
                    :value="true"
                    required
                  />
                  <span class="radio-custom"></span>
                  Yes
                </label>
              </div>
            </div>

            <div v-if="formData.has_business" class="form-grid">
              <div class="form-group">
                <label for="business_gross_receipts">Annual Gross Receipts/Professional Fees (₱) *</label>
                <input
                  id="business_gross_receipts"
                  v-model.number="formData.business_gross_receipts"
                  type="number"
                  min="0"
                  step="0.01"
                  required
                  placeholder="0.00"
                  @input="calculateTax"
                />
              </div>
              <div class="form-group">
                <label for="business_type">Type of Business/Profession</label>
                <select
                  id="business_type"
                  v-model="formData.business_type"
                >
                  <option value="">Select type</option>
                  <option value="retail">Retail Business</option>
                  <option value="wholesale">Wholesale Business</option>
                  <option value="manufacturing">Manufacturing</option>
                  <option value="services">Service Business</option>
                  <option value="professional">Professional Practice</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Tax Computation -->
          <div class="tax-computation">
            <div class="tax-card">
              <h3>Tax Computation</h3>
              <div class="tax-breakdown">
                <div class="tax-item">
                  <span>Basic Community Tax:</span>
                  <span>₱{{ formatCurrency(basicTax) }}</span>
                </div>
                <div class="tax-item">
                  <span>Income Tax ({{ getIncomeRate() }}%):</span>
                  <span>₱{{ formatCurrency(incomeTax) }}</span>
                </div>
                <div v-if="formData.has_real_property || formData.has_personal_property" class="tax-item">
                  <span>Property Tax:</span>
                  <span>₱{{ formatCurrency(propertyTax) }}</span>
                </div>
                <div v-if="formData.has_business" class="tax-item">
                  <span>Business Tax ({{ getBusinessRate() }}%):</span>
                  <span>₱{{ formatCurrency(businessTax) }}</span>
                </div>
                <div class="tax-item total">
                  <span>Total Cedula Tax:</span>
                  <span>₱{{ formatCurrency(totalTax) }}</span>
                </div>
              </div>
              <div class="tax-note">
                <i class="fas fa-info-circle"></i>
                <p>Tax computation is based on current rates and may be subject to verification.</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 3: Payment Method -->
        <div v-if="currentStep === 3" class="form-step">
          <div class="step-header">
            <h2>Payment Information</h2>
            <p>Choose your preferred payment method</p>
          </div>

          <!-- Fee Summary -->
          <div class="fee-summary">
            <div class="fee-card">
              <h3>Fee Breakdown</h3>
              <div class="fee-items">
                <div class="fee-item">
                  <span>Cedula Tax</span>
                  <span>₱{{ formatCurrency(totalTax) }}</span>
                </div>
                <div class="fee-item">
                  <span>Processing Fee</span>
                  <span>₱{{ formatCurrency(processingFee) }}</span>
                </div>
                <div class="fee-item total">
                  <span>Total Amount</span>
                  <span>₱{{ formatCurrency(totalFee) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Payment Methods -->
          <div class="form-section">
            <h3>Select Payment Method</h3>
            <div class="payment-methods">
              <div
                v-for="method in paymentMethods"
                :key="method.id"
                class="payment-option"
                :class="{ selected: formData.payment_method_id === method.id }"
                @click="selectPaymentMethod(method.id)"
              >
                <div class="payment-icon">
                  <i :class="getPaymentIcon(method.method_code)"></i>
                </div>
                <div class="payment-info">
                  <h4>{{ method.method_name }}</h4>
                  <p v-if="method.description">{{ method.description }}</p>
                </div>
                <div class="payment-radio">
                  <input
                    type="radio"
                    :value="method.id"
                    v-model="formData.payment_method_id"
                    required
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 4: Review and Submit -->
        <div v-if="currentStep === 4" class="form-step">
          <div class="step-header">
            <h2>Review Your Request</h2>
            <p>Please review all information before submitting</p>
          </div>

          <div class="review-sections">
            <!-- Personal Information Review -->
            <div class="review-section">
              <h3>Personal Information</h3>
              <div class="review-grid">
                <div class="review-item">
                  <label>Full Name:</label>
                  <span>{{ getFullName() }}</span>
                </div>
                <div class="review-item">
                  <label>Purpose:</label>
                  <span>{{ getPurposeCategoryName() }}</span>
                </div>
                <div class="review-item">
                  <label>Purpose Details:</label>
                  <span>{{ formData.purpose_details }}</span>
                </div>
              </div>
            </div>

            <!-- Income & Assets Review -->
            <div class="review-section">
              <h3>Income & Assets Information</h3>
              <div class="review-grid">
                <div class="review-item">
                  <label>Annual Income:</label>
                  <span>₱{{ formatCurrency(formData.annual_income) }}</span>
                </div>
                <div class="review-item">
                  <label>Income Source:</label>
                  <span>{{ formData.income_source }}</span>
                </div>
                <div v-if="formData.business_name" class="review-item">
                  <label>Business/Practice:</label>
                  <span>{{ formData.business_name }}</span>
                </div>
                <div class="review-item">
                  <label>Real Property:</label>
                  <span>{{ formData.has_real_property ? 'Yes' : 'No' }}</span>
                </div>
                <div v-if="formData.has_real_property" class="review-item">
                  <label>Property Value:</label>
                  <span>₱{{ formatCurrency(formData.property_value) }}</span>
                </div>
              </div>
            </div>

            <!-- Tax Computation Review -->
            <div class="review-section">
              <h3>Tax Computation</h3>
              <div class="review-grid">
                <div class="review-item">
                  <label>Basic Community Tax:</label>
                  <span>₱{{ formatCurrency(basicTax) }}</span>
                </div>
                <div class="review-item">
                  <label>Income Tax:</label>
                  <span>₱{{ formatCurrency(incomeTax) }}</span>
                </div>
                <div v-if="formData.has_real_property" class="review-item">
                  <label>Property Tax:</label>
                  <span>₱{{ formatCurrency(propertyTax) }}</span>
                </div>
                <div class="review-item">
                  <label>Cedula Tax:</label>
                  <span class="amount">₱{{ formatCurrency(totalTax) }}</span>
                </div>
              </div>
            </div>

            <!-- Payment Review -->
            <div class="review-section">
              <h3>Payment Information</h3>
              <div class="review-grid">
                <div class="review-item">
                  <label>Payment Method:</label>
                  <span>{{ getPaymentMethodName() }}</span>
                </div>
                <div class="review-item">
                  <label>Total Amount:</label>
                  <span class="amount">₱{{ formatCurrency(totalFee) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Terms and Conditions -->
          <div class="terms-section">
            <label class="checkbox-option">
              <input
                type="checkbox"
                v-model="formData.agree_to_terms"
                required
              />
              <span class="checkbox-custom"></span>
              I certify that all information provided is true and accurate to the best of my knowledge, and I agree to the <a href="#" @click.prevent="showTerms">terms and conditions</a>.
            </label>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button
            v-if="currentStep > 1"
            type="button"
            class="btn-secondary"
            @click="previousStep"
          >
            <i class="fas fa-chevron-left"></i>
            Previous
          </button>
          
          <button
            v-if="currentStep < 4"
            type="button"
            class="btn-primary"
            @click="nextStep"
            :disabled="!canProceedToNextStep()"
          >
            Next
            <i class="fas fa-chevron-right"></i>
          </button>
          
          <button
            v-if="currentStep === 4"
            type="submit"
            class="btn-submit"
            :disabled="submitButtonDisabled"
            @click="handleSubmitClick"
          >
            <template v-if="submitting">
              <i class="fas fa-spinner fa-spin"></i>
              Submitting...
            </template>
            <template v-else>
              <i class="fas fa-paper-plane"></i>
              Submit Request
            </template>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import documentRequestService from '@/services/documentRequestService';
import clientAuthService from '@/services/clientAuthService';

export default {
  name: 'CedulaRequest',
  data() {
    return {
      currentStep: 1,
      submitting: false,
      purposeCategories: [],
      paymentMethods: [],
      processingFee: 5.00, // Processing fee
      basicTax: 5.00, // Basic community tax
      formData: {
        document_type_id: 1, // Cedula
        purpose_category_id: '',
        purpose_details: '',
        annual_income: 0,
        income_source: '',
        business_name: '',
        business_address: '',
        business_nature: '',
        has_real_property: false,
        property_value: 0,
        property_location: '',
        has_personal_property: false,
        personal_property_value: 0,
        has_business: false,
        business_gross_receipts: 0,
        business_type: '',
        payment_method_id: '',
        agree_to_terms: false
      },
      clientData: null // Fresh profile data
    };
  },
  computed: {
    // Keep the old method as fallback
    cachedClientData() {
      return clientAuthService.getCurrentUser();
    },

    // Debug computed property for submit button state
    submitButtonDisabled() {
      const disabled = this.submitting || !this.formData.agree_to_terms;
      console.log('🔘 Submit button disabled:', disabled, {
        submitting: this.submitting,
        agree_to_terms: this.formData.agree_to_terms,
        currentStep: this.currentStep
      });
      return disabled;
    },
    incomeTax() {
      const income = this.formData.annual_income || 0;
      const rate = this.getIncomeRate() / 100;
      const tax = income * rate;
      return Math.min(tax, 5000); // Maximum income tax
    },
    propertyTax() {
      let tax = 0;

      // Real property tax
      if (this.formData.has_real_property) {
        const realValue = this.formData.property_value || 0;
        const realRate = this.getPropertyRate() / 100;
        tax += realValue * realRate;
      }

      // Personal property tax
      if (this.formData.has_personal_property) {
        const personalValue = this.formData.personal_property_value || 0;
        const personalRate = this.getPersonalPropertyRate() / 100;
        tax += personalValue * personalRate;
      }

      return Math.min(tax, 5000); // Maximum property tax
    },
    businessTax() {
      if (!this.formData.has_business) return 0;
      const receipts = this.formData.business_gross_receipts || 0;
      const rate = this.getBusinessRate() / 100;
      const tax = receipts * rate;
      return Math.min(tax, 5000); // Maximum business tax
    },
    totalTax() {
      return this.basicTax + this.incomeTax + this.propertyTax + this.businessTax;
    },
    totalFee() {
      return this.totalTax + this.processingFee;
    }
  },
  async mounted() {
    console.log('🎯 CedulaRequest component mounted');

    // Check authentication status
    const token = localStorage.getItem('clientToken');
    const clientData = localStorage.getItem('clientData');
    console.log('🔑 Client token exists:', !!token);
    console.log('👤 Client data exists:', !!clientData);

    if (clientData) {
      try {
        const parsedData = JSON.parse(clientData);
        console.log('👤 Client data:', parsedData);
      } catch (e) {
        console.error('❌ Error parsing client data:', e);
      }
    }

    await this.loadFormData();
  },
  methods: {
    async loadFormData() {
      try {
        // Load fresh profile data first
        console.log('Loading fresh profile data...');
        const profileResponse = await clientAuthService.getProfile();
        if (profileResponse.success) {
          this.clientData = profileResponse.data;
          console.log('Fresh profile data loaded:', this.clientData);
        } else {
          // Fallback to cached data
          this.clientData = this.cachedClientData;
          console.log('Using cached profile data:', this.clientData);
        }

        const [purposeResponse, paymentResponse] = await Promise.all([
          documentRequestService.getPurposeCategories(),
          documentRequestService.getPaymentMethods()
        ]);

        this.purposeCategories = purposeResponse.data || [];
        this.paymentMethods = paymentResponse.data || [];

      } catch (error) {
        console.error('Error loading form data:', error);
        // Fallback to cached data on error
        this.clientData = this.cachedClientData;
        this.showToast('Error', 'Failed to load some form data', 'error');
      }
    },

    getFullName() {
      // Try fresh data first, then fallback to cached data structure
      const profile = this.clientData || this.clientData?.profile;
      if (!profile) return 'N/A';
      return `${profile.first_name || ''} ${profile.middle_name || ''} ${profile.last_name || ''}`.trim();
    },

    getFullAddress() {
      // Try fresh data first, then fallback to cached data structure
      const profile = this.clientData || this.clientData?.profile;
      if (!profile) return 'Not provided';

      const parts = [
        profile.house_number,
        profile.street,
        profile.subdivision,
        profile.barangay,
        profile.city_municipality || profile.city,
        profile.province
      ].filter(Boolean);

      return parts.length > 0 ? parts.join(', ') : 'Not provided';
    },

    formatGender(gender) {
      if (!gender) return 'Not provided';
      return gender.charAt(0).toUpperCase() + gender.slice(1);
    },

    getCivilStatusName(statusId) {
      const statuses = {
        1: 'Single',
        2: 'Married',
        3: 'Divorced',
        4: 'Widowed',
        5: 'Separated'
      };
      return statuses[statusId] || 'Not provided';
    },

    getResidencyDisplay() {
      // Try fresh data first, then fallback to cached data structure
      const profile = this.clientData || this.clientData?.profile;
      if (!profile) return 'Not provided';

      const years = profile.years_of_residency;
      const months = profile.months_of_residency;

      if (!years && !months) return 'Not provided';

      const parts = [];
      if (years) parts.push(`${years} year${years > 1 ? 's' : ''}`);
      if (months) parts.push(`${months} month${months > 1 ? 's' : ''}`);

      return parts.join(' and ');
    },

    formatDate(dateString) {
      if (!dateString) return 'Not provided';
      return new Date(dateString).toLocaleDateString();
    },

    formatCurrency(amount) {
      return parseFloat(amount || 0).toFixed(2);
    },

    getIncomeRate() {
      const income = this.formData.annual_income || 0;
      if (income <= 10000) return 0.5;
      if (income <= 50000) return 1.0;
      if (income <= 100000) return 1.5;
      return 2.0;
    },

    getPropertyRate() {
      const value = this.formData.property_value || 0;
      if (value <= 100000) return 0.5;
      if (value <= 500000) return 1.0;
      return 1.5;
    },

    getPersonalPropertyRate() {
      const value = this.formData.personal_property_value || 0;
      if (value <= 10000) return 0.25;
      if (value <= 50000) return 0.5;
      return 1.0;
    },

    getBusinessRate() {
      const receipts = this.formData.business_gross_receipts || 0;
      if (receipts <= 50000) return 0.5;
      if (receipts <= 200000) return 1.0;
      if (receipts <= 500000) return 1.5;
      return 2.0;
    },

    calculateTax() {
      // Tax calculation is handled by computed properties
      this.$forceUpdate();
    },

    canProceedToNextStep() {
      const result = (() => {
        switch (this.currentStep) {
          case 1:
            return this.formData.purpose_category_id && this.formData.purpose_details;
          case 2:
            return this.formData.annual_income >= 0 &&
                   this.formData.income_source &&
                   this.formData.has_real_property !== null &&
                   this.formData.has_personal_property !== null &&
                   this.formData.has_business !== null;
          case 3:
            return this.formData.payment_method_id;
          default:
            return true;
        }
      })();

      console.log(`🔍 canProceedToNextStep (step ${this.currentStep}):`, result);
      if (this.currentStep === 1) {
        console.log('  - purpose_category_id:', this.formData.purpose_category_id);
        console.log('  - purpose_details:', this.formData.purpose_details);
      } else if (this.currentStep === 2) {
        console.log('  - annual_income:', this.formData.annual_income);
        console.log('  - income_source:', this.formData.income_source);
        console.log('  - has_real_property:', this.formData.has_real_property);
        console.log('  - has_personal_property:', this.formData.has_personal_property);
        console.log('  - has_business:', this.formData.has_business);
      } else if (this.currentStep === 3) {
        console.log('  - payment_method_id:', this.formData.payment_method_id);
      }

      return result;
    },

    nextStep() {
      console.log('🔄 nextStep called, current step:', this.currentStep);
      const canProceed = this.canProceedToNextStep();
      console.log('🔍 Can proceed:', canProceed);

      if (canProceed && this.currentStep < 4) {
        this.currentStep++;
        console.log('✅ Moved to step:', this.currentStep);
      } else {
        console.log('❌ Cannot proceed to next step');
      }
    },

    previousStep() {
      if (this.currentStep > 1) {
        this.currentStep--;
      }
    },

    selectPaymentMethod(methodId) {
      this.formData.payment_method_id = methodId;
    },

    getPaymentIcon(methodCode) {
      const icons = {
        'CASH': 'fas fa-money-bill',
        'PAYMONGO_CARD': 'fas fa-credit-card',
        'PAYMONGO_GCASH': 'fab fa-google-pay',
        'PAYMONGO_GRABPAY': 'fas fa-mobile-alt',
        'PAYMONGO_PAYMAYA': 'fas fa-wallet'
      };
      return icons[methodCode] || 'fas fa-credit-card';
    },

    getPurposeCategoryName() {
      const category = this.purposeCategories.find(c => c.id === this.formData.purpose_category_id);
      return category?.category_name || '';
    },

    getPaymentMethodName() {
      const method = this.paymentMethods.find(m => m.id === this.formData.payment_method_id);
      return method?.method_name || '';
    },

    handleSubmitClick(event) {
      console.log('🔥 Submit button clicked!', event);
      console.log('📋 Form data:', this.formData);
      console.log('✅ Agree to terms:', this.formData.agree_to_terms);
      console.log('🔄 Current step:', this.currentStep);
      console.log('🚫 Button disabled:', this.submitting || !this.formData.agree_to_terms);

      // Don't prevent default here, let the form submission happen naturally
    },

    async handleSubmit() {
      console.log('🚀 handleSubmit called');
      console.log('📋 Form data:', this.formData);
      console.log('✅ Agree to terms:', this.formData.agree_to_terms);

      if (!this.formData.agree_to_terms) {
        console.log('❌ Terms not agreed to');
        this.showToast('Error', 'Please agree to the terms and conditions', 'error');
        return;
      }

      try {
        console.log('🔄 Starting submission process...');
        this.submitting = true;

        // Check authentication
        const token = localStorage.getItem('clientToken');
        console.log('🔑 Client token exists:', !!token);

        if (!token) {
          this.showToast('Error', 'Please log in to submit a request', 'error');
          this.submitting = false;
          return;
        }

        // Ensure required fields are properly formatted
        const requestData = {
          ...this.formData,
          // Ensure IDs are integers
          document_type_id: parseInt(this.formData.document_type_id),
          purpose_category_id: parseInt(this.formData.purpose_category_id),
          payment_method_id: this.formData.payment_method_id ? parseInt(this.formData.payment_method_id) : undefined,
          // Ensure numeric fields are numbers
          annual_income: parseFloat(this.formData.annual_income) || 0,
          // Fix field mapping for cedula
          property_value: parseFloat(this.formData.property_value) || 0,
          personal_property_value: parseFloat(this.formData.personal_property_value) || 0,
          business_gross_receipts: parseFloat(this.formData.business_gross_receipts) || 0,
          // Computed values
          computed_tax: this.totalTax,
          total_fee: this.totalFee
        };

        console.log('📤 Request data to submit:', requestData);

        // Validate required fields
        if (!requestData.purpose_category_id) {
          this.showToast('Error', 'Please select a purpose category', 'error');
          this.submitting = false;
          return;
        }
        if (!requestData.purpose_details || requestData.purpose_details.length < 10) {
          this.showToast('Error', 'Purpose details must be at least 10 characters long', 'error');
          this.submitting = false;
          return;
        }
        if (!requestData.payment_method_id) {
          this.showToast('Error', 'Please select a payment method', 'error');
          this.submitting = false;
          return;
        }

        console.log('📡 Making API call...');
        const response = await documentRequestService.submitRequest(requestData);
        console.log('✅ API response:', response);

        this.showToast('Success', 'Cedula request submitted successfully!', 'success');
        this.$router.push({
          name: 'RequestDetails',
          params: { id: response.data.id }
        });

      } catch (error) {
        console.error('❌ Error submitting request:', error);
        console.error('❌ Error response:', error.response);
        console.error('❌ Error data:', error.response?.data);

        let errorMessage = 'Failed to submit request';
        if (error.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error.response?.data?.error) {
          errorMessage = error.response.data.error;
        } else if (error.message) {
          errorMessage = error.message;
        }

        this.showToast('Error', errorMessage, 'error');
      } finally {
        console.log('🏁 Submission process completed');
        this.submitting = false;
      }
    },

    goBack() {
      this.$router.push({ name: 'NewDocumentRequest' });
    },

    updateProfile() {
      // TODO: Navigate to profile update page
      console.log('Update profile');
    },

    showTerms() {
      // TODO: Show terms and conditions modal
      console.log('Show terms');
    },

    showToast(title, message, type = 'info') {
      // Log to console for debugging
      console.log(`[${type.toUpperCase()}] ${title}: ${message}`);

      // Create a simple toast notification
      const toast = document.createElement('div');
      toast.className = `toast-notification toast-${type}`;
      toast.innerHTML = `
        <div class="toast-header">
          <strong>${title}</strong>
          <button type="button" class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
        <div class="toast-body">${message}</div>
      `;

      // Add toast styles if not already added
      if (!document.getElementById('toast-styles')) {
        const styles = document.createElement('style');
        styles.id = 'toast-styles';
        styles.textContent = `
          .toast-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            min-width: 300px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 9999;
            animation: slideIn 0.3s ease;
          }
          .toast-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px 8px;
            border-bottom: 1px solid #e9ecef;
          }
          .toast-body {
            padding: 8px 16px 12px;
            color: #6c757d;
          }
          .toast-close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #6c757d;
          }
          .toast-success { border-left: 4px solid #28a745; }
          .toast-error { border-left: 4px solid #dc3545; }
          .toast-info { border-left: 4px solid #17a2b8; }
          .toast-warning { border-left: 4px solid #ffc107; }
          @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
          }
        `;
        document.head.appendChild(styles);
      }

      // Add toast to page
      document.body.appendChild(toast);

      // Auto-remove after 5 seconds
      setTimeout(() => {
        if (toast.parentElement) {
          toast.style.animation = 'slideIn 0.3s ease reverse';
          setTimeout(() => toast.remove(), 300);
        }
      }, 5000);
    }
  }
};
</script>

<style scoped>
/* Import shared styles from BarangayClearanceRequest */
.cedula-request {
  padding: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1a365d;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.page-title i {
  color: #3182ce;
}

.page-description {
  font-size: 1rem;
  color: #4a5568;
  margin: 0;
}

.back-btn {
  background: #e2e8f0;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  color: #4a5568;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.back-btn:hover {
  background: #cbd5e0;
  color: #2d3748;
}

.progress-steps {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
  position: relative;
}

.progress-steps::before {
  content: '';
  position: absolute;
  top: 1.5rem;
  left: 25%;
  right: 25%;
  height: 2px;
  background: #e2e8f0;
  z-index: 1;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
  z-index: 2;
}

.step-number {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: #e2e8f0;
  color: #a0aec0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-bottom: 0.5rem;
  transition: all 0.3s;
}

.step.active .step-number {
  background: #3182ce;
  color: white;
}

.step.completed .step-number {
  background: #38a169;
  color: white;
}

.step-label {
  font-size: 0.875rem;
  color: #718096;
  text-align: center;
}

.step.active .step-label {
  color: #3182ce;
  font-weight: 500;
}

.form-container {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.form-step {
  min-height: 400px;
}

.step-header {
  text-align: center;
  margin-bottom: 2rem;
}

.step-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 0.5rem;
}

.step-header p {
  color: #4a5568;
  margin: 0;
}

.profile-preview {
  margin-bottom: 2rem;
}

.profile-card {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.profile-info h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 1rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #718096;
}

.info-item span {
  color: #2d3748;
}

.update-profile-btn {
  background: #3182ce;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.update-profile-btn:hover {
  background: #2c5aa0;
}

.form-section {
  margin-bottom: 2rem;
}

.form-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 1rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #2d3748;
  font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3182ce;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
}

.radio-option:hover {
  background: #f7fafc;
}

.radio-option input[type="radio"] {
  display: none;
}

.radio-custom {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid #e2e8f0;
  border-radius: 50%;
  position: relative;
  transition: all 0.2s;
}

.radio-option input[type="radio"]:checked + .radio-custom {
  border-color: #3182ce;
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0.5rem;
  height: 0.5rem;
  background: #3182ce;
  border-radius: 50%;
}

/* Tax Computation Specific Styles */
.tax-computation {
  margin-top: 2rem;
}

.tax-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.tax-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-align: center;
}

.tax-breakdown {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.tax-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.tax-item:last-child {
  border-bottom: none;
}

.tax-item.total {
  border-top: 2px solid rgba(255, 255, 255, 0.3);
  padding-top: 1rem;
  font-weight: 600;
  font-size: 1.125rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-top: 0.5rem;
}

.tax-note {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
}

.tax-note i {
  color: #ffd700;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.tax-note p {
  margin: 0;
  line-height: 1.4;
}

.fee-summary {
  margin-bottom: 2rem;
}

.fee-card {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
}

.fee-card h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 1rem;
}

.fee-items {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.fee-item.total {
  border-top: 1px solid #e2e8f0;
  padding-top: 1rem;
  font-weight: 600;
  font-size: 1.125rem;
  color: #1a365d;
}

.payment-methods {
  display: grid;
  gap: 1rem;
}

.payment-option {
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.payment-option:hover {
  border-color: #cbd5e0;
}

.payment-option.selected {
  border-color: #3182ce;
  background: #ebf8ff;
}

.payment-icon {
  width: 3rem;
  height: 3rem;
  background: #f7fafc;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: #4a5568;
}

.payment-option.selected .payment-icon {
  background: #3182ce;
  color: white;
}

.payment-info {
  flex: 1;
}

.payment-info h4 {
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 0.25rem;
}

.payment-info p {
  color: #718096;
  font-size: 0.875rem;
  margin: 0;
}

.payment-radio input {
  width: 1.25rem;
  height: 1.25rem;
}

.review-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-bottom: 2rem;
}

.review-section {
  background: #f7fafc;
  border-radius: 0.75rem;
  padding: 1.5rem;
}

.review-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 1rem;
}

.review-grid {
  display: grid;
  gap: 1rem;
}

.review-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e2e8f0;
}

.review-item:last-child {
  border-bottom: none;
}

.review-item label {
  font-weight: 500;
  color: #4a5568;
  min-width: 120px;
}

.review-item span {
  color: #2d3748;
  text-align: right;
  flex: 1;
}

.review-item .amount {
  font-weight: 600;
  color: #38a169;
  font-size: 1.125rem;
}

.terms-section {
  margin-bottom: 2rem;
  padding: 1rem;
  background: #fffaf0;
  border: 1px solid #fed7aa;
  border-radius: 0.5rem;
}

.checkbox-option {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  line-height: 1.5;
}

.checkbox-option input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid #e2e8f0;
  border-radius: 0.25rem;
  position: relative;
  flex-shrink: 0;
  margin-top: 0.125rem;
  transition: all 0.2s;
}

.checkbox-option input[type="checkbox"]:checked + .checkbox-custom {
  border-color: #3182ce;
  background: #3182ce;
}

.checkbox-option input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.875rem;
  font-weight: bold;
}

.checkbox-option a {
  color: #3182ce;
  text-decoration: underline;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid #e2e8f0;
}

.btn-secondary,
.btn-primary,
.btn-submit {
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
}

.btn-secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-secondary:hover {
  background: #cbd5e0;
}

.btn-primary {
  background: #3182ce;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2c5aa0;
}

.btn-submit {
  background: #38a169;
  color: white;
}

.btn-submit:hover:not(:disabled) {
  background: #2f855a;
}

.btn-primary:disabled,
.btn-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .cedula-request {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .progress-steps {
    flex-wrap: wrap;
    gap: 1rem;
  }

  .progress-steps::before {
    display: none;
  }

  .form-container {
    padding: 1.5rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .profile-card {
    flex-direction: column;
    gap: 1rem;
  }

  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .review-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .review-item span {
    text-align: left;
  }

  .tax-card {
    padding: 1.5rem;
  }

  .tax-breakdown {
    gap: 0.75rem;
  }
}
</style>
