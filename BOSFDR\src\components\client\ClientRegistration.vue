<template>
  <div class="client-registration">
    <div class="container-fluid min-vh-100 d-flex align-items-center justify-content-center bg-light">
      <div class="row w-100 justify-content-center">
        <div class="col-12 col-md-8 col-lg-6 col-xl-5">
          <div class="card shadow-lg border-0">
            <div class="card-header bg-primary text-white text-center py-4">
              <h3 class="mb-0">
                <i class="fas fa-user-plus me-2"></i>
                Client Registration
              </h3>
              <p class="mb-0 mt-2">Barangay Bula Management System</p>
            </div>
            
            <div class="card-body p-4">
              <!-- Registration Steps Indicator -->
              <div class="row mb-4">
                <div class="col-12">
                  <div class="d-flex justify-content-between align-items-center">
                    <div class="step-indicator" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
                      <div class="step-number">1</div>
                      <div class="step-label">Account</div>
                    </div>
                    <div class="step-line" :class="{ active: currentStep > 1 }"></div>
                    <div class="step-indicator" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
                      <div class="step-number">2</div>
                      <div class="step-label">Profile</div>
                    </div>
                    <div class="step-line" :class="{ active: currentStep > 2 }"></div>
                    <div class="step-indicator" :class="{ active: currentStep >= 3, completed: currentStep > 3 }">
                      <div class="step-number">3</div>
                      <div class="step-label">Verify</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Step 1: Account Creation -->
              <div v-if="currentStep === 1" class="step-content">
                <h5 class="mb-3">Create Your Account</h5>
                <form @submit.prevent="submitAccountForm">
                  <div class="mb-3">
                    <label for="username" class="form-label">Username</label>
                    <input
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.username }"
                      id="username"
                      v-model="accountForm.username"
                      @blur="validateField('username')"
                      @input="clearFieldError('username')"
                      placeholder="Enter your username"
                      required
                    >
                    <div v-if="errors.username" class="invalid-feedback">
                      {{ errors.username }}
                    </div>
                  </div>

                  <div class="mb-3">
                    <label for="email" class="form-label">Email Address</label>
                    <input
                      type="email"
                      class="form-control"
                      :class="{ 'is-invalid': errors.email }"
                      id="email"
                      v-model="accountForm.email"
                      @blur="validateField('email')"
                      @input="clearFieldError('email')"
                      placeholder="Enter your email address"
                      required
                    >
                    <div v-if="errors.email" class="invalid-feedback">
                      {{ errors.email }}
                    </div>
                  </div>

                  <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <div class="input-group">
                      <input
                        :type="showPassword ? 'text' : 'password'"
                        class="form-control"
                        :class="{ 'is-invalid': errors.password }"
                        id="password"
                        v-model="accountForm.password"
                        @blur="validateField('password')"
                        @input="clearFieldError('password')"
                        placeholder="Enter your password"
                        required
                      >
                      <button
                        type="button"
                        class="btn btn-outline-secondary"
                        @click="showPassword = !showPassword"
                      >
                        <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                      </button>
                    </div>
                    <div v-if="errors.password" class="invalid-feedback d-block">
                      {{ errors.password }}
                    </div>
                    <div class="form-text">
                      Password must be at least 8 characters with uppercase, lowercase, and number.
                    </div>
                  </div>

                  <div class="mb-4">
                    <label for="confirmPassword" class="form-label">Confirm Password</label>
                    <input
                      type="password"
                      class="form-control"
                      :class="{ 'is-invalid': errors.confirmPassword }"
                      id="confirmPassword"
                      v-model="accountForm.confirmPassword"
                      @blur="validateField('confirmPassword')"
                      @input="clearFieldError('confirmPassword')"
                      placeholder="Confirm your password"
                      required
                    >
                    <div v-if="errors.confirmPassword" class="invalid-feedback">
                      {{ errors.confirmPassword }}
                    </div>
                  </div>

                  <div class="d-grid gap-2">
                    <button
                      type="submit"
                      class="btn btn-primary"
                      :disabled="loading"
                    >
                      <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                      {{ loading ? 'Creating Account...' : 'Create Account' }}
                    </button>
                  </div>
                </form>
              </div>

              <!-- Step 2: Profile Information -->
              <div v-if="currentStep === 2" class="step-content">
                <h5 class="mb-3">Complete Your Profile</h5>
                <form @submit.prevent="submitProfileForm">
                  <!-- Personal Information -->
                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="firstName" class="form-label">First Name</label>
                      <input
                        type="text"
                        class="form-control"
                        :class="{ 'is-invalid': errors.first_name }"
                        id="firstName"
                        v-model="profileForm.first_name"
                        @blur="validateField('first_name')"
                        @input="clearFieldError('first_name')"
                        required
                      >
                      <div v-if="errors.first_name" class="invalid-feedback">
                        {{ errors.first_name }}
                      </div>
                    </div>
                    <div class="col-md-6 mb-3">
                      <label for="lastName" class="form-label">Last Name</label>
                      <input
                        type="text"
                        class="form-control"
                        :class="{ 'is-invalid': errors.last_name }"
                        id="lastName"
                        v-model="profileForm.last_name"
                        @blur="validateField('last_name')"
                        @input="clearFieldError('last_name')"
                        required
                      >
                      <div v-if="errors.last_name" class="invalid-feedback">
                        {{ errors.last_name }}
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="middleName" class="form-label">Middle Name (Optional)</label>
                      <input
                        type="text"
                        class="form-control"
                        id="middleName"
                        v-model="profileForm.middle_name"
                      >
                    </div>
                    <div class="col-md-6 mb-3">
                      <label for="suffix" class="form-label">Suffix (Optional)</label>
                      <input
                        type="text"
                        class="form-control"
                        id="suffix"
                        v-model="profileForm.suffix"
                        placeholder="Jr., Sr., III, etc."
                      >
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="birthDate" class="form-label">Birth Date</label>
                      <input
                        type="date"
                        class="form-control"
                        :class="{ 'is-invalid': errors.birth_date }"
                        id="birthDate"
                        v-model="profileForm.birth_date"
                        @blur="validateField('birth_date')"
                        @input="clearFieldError('birth_date')"
                        required
                      >
                      <div v-if="errors.birth_date" class="invalid-feedback">
                        {{ errors.birth_date }}
                      </div>
                    </div>
                    <div class="col-md-6 mb-3">
                      <label for="gender" class="form-label">Gender</label>
                      <select
                        class="form-select"
                        :class="{ 'is-invalid': errors.gender }"
                        id="gender"
                        v-model="profileForm.gender"
                        @change="clearFieldError('gender')"
                        required
                      >
                        <option value="">Select Gender</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                      </select>
                      <div v-if="errors.gender" class="invalid-feedback">
                        {{ errors.gender }}
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="civilStatus" class="form-label">Civil Status</label>
                      <select
                        class="form-select"
                        :class="{ 'is-invalid': errors.civil_status_id }"
                        id="civilStatus"
                        v-model="profileForm.civil_status_id"
                        @change="clearFieldError('civil_status_id')"
                        required
                      >
                        <option value="">Select Civil Status</option>
                        <option value="1">Single</option>
                        <option value="2">Married</option>
                        <option value="3">Divorced</option>
                        <option value="4">Widowed</option>
                        <option value="5">Separated</option>
                      </select>
                      <div v-if="errors.civil_status_id" class="invalid-feedback">
                        {{ errors.civil_status_id }}
                      </div>
                    </div>
                    <div class="col-md-6 mb-3">
                      <label for="phoneNumber" class="form-label">Phone Number</label>
                      <input
                        type="tel"
                        class="form-control"
                        :class="{ 'is-invalid': errors.phone_number }"
                        id="phoneNumber"
                        v-model="profileForm.phone_number"
                        @blur="validateField('phone_number')"
                        @input="clearFieldError('phone_number')"
                        placeholder="09123456789"
                        required
                      >
                      <div v-if="errors.phone_number" class="invalid-feedback">
                        {{ errors.phone_number }}
                      </div>
                    </div>
                  </div>

                  <!-- Address Information -->
                  <h6 class="mt-4 mb-3">Address Information</h6>
                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="barangay" class="form-label">Barangay</label>
                      <input
                        type="text"
                        class="form-control"
                        :class="{ 'is-invalid': errors.barangay }"
                        id="barangay"
                        v-model="profileForm.barangay"
                        @blur="validateField('barangay')"
                        @input="clearFieldError('barangay')"
                        required
                      >
                      <div v-if="errors.barangay" class="invalid-feedback">
                        {{ errors.barangay }}
                      </div>
                    </div>
                    <div class="col-md-6 mb-3">
                      <label for="cityMunicipality" class="form-label">City/Municipality</label>
                      <input
                        type="text"
                        class="form-control"
                        :class="{ 'is-invalid': errors.city_municipality }"
                        id="cityMunicipality"
                        v-model="profileForm.city_municipality"
                        @blur="validateField('city_municipality')"
                        @input="clearFieldError('city_municipality')"
                        required
                      >
                      <div v-if="errors.city_municipality" class="invalid-feedback">
                        {{ errors.city_municipality }}
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="province" class="form-label">Province</label>
                      <input
                        type="text"
                        class="form-control"
                        :class="{ 'is-invalid': errors.province }"
                        id="province"
                        v-model="profileForm.province"
                        @blur="validateField('province')"
                        @input="clearFieldError('province')"
                        required
                      >
                      <div v-if="errors.province" class="invalid-feedback">
                        {{ errors.province }}
                      </div>
                    </div>
                    <div class="col-md-6 mb-3">
                      <label for="houseNumber" class="form-label">House Number (Optional)</label>
                      <input
                        type="text"
                        class="form-control"
                        id="houseNumber"
                        v-model="profileForm.house_number"
                        placeholder="e.g., 123, Blk 5 Lot 10"
                      >
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="street" class="form-label">Street (Optional)</label>
                      <input
                        type="text"
                        class="form-control"
                        id="street"
                        v-model="profileForm.street"
                        placeholder="e.g., Main Street, Rizal Avenue"
                      >
                    </div>
                    <div class="col-md-6 mb-3">
                      <label for="subdivision" class="form-label">Subdivision (Optional)</label>
                      <input
                        type="text"
                        class="form-control"
                        id="subdivision"
                        v-model="profileForm.subdivision"
                        placeholder="e.g., Green Valley, Sunrise Village"
                      >
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="postalCode" class="form-label">Postal Code (Optional)</label>
                      <input
                        type="text"
                        class="form-control"
                        id="postalCode"
                        v-model="profileForm.postal_code"
                        placeholder="e.g., 9500"
                        maxlength="10"
                      >
                    </div>
                    <div class="col-md-6 mb-3">
                      <label for="nationality" class="form-label">Nationality</label>
                      <input
                        type="text"
                        class="form-control"
                        :class="{ 'is-invalid': errors.nationality }"
                        id="nationality"
                        v-model="profileForm.nationality"
                        @blur="validateField('nationality')"
                        @input="clearFieldError('nationality')"
                        required
                      >
                      <div v-if="errors.nationality" class="invalid-feedback">
                        {{ errors.nationality }}
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="yearsOfResidency" class="form-label">Years of Residency (Optional)</label>
                      <input
                        type="number"
                        class="form-control"
                        id="yearsOfResidency"
                        v-model.number="profileForm.years_of_residency"
                        placeholder="e.g., 5"
                        min="0"
                        max="100"
                      >
                    </div>
                    <div class="col-md-6 mb-3">
                      <label for="monthsOfResidency" class="form-label">Additional Months (Optional)</label>
                      <input
                        type="number"
                        class="form-control"
                        id="monthsOfResidency"
                        v-model.number="profileForm.months_of_residency"
                        placeholder="e.g., 6"
                        min="0"
                        max="11"
                      >
                    </div>
                  </div>

                  <div class="d-grid gap-2 d-md-flex justify-content-md-between">
                    <button
                      type="button"
                      class="btn btn-outline-secondary"
                      @click="goToPreviousStep"
                    >
                      <i class="fas fa-arrow-left me-2"></i>
                      Back
                    </button>
                    <button
                      type="submit"
                      class="btn btn-primary"
                      :disabled="loading"
                    >
                      <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                      {{ loading ? 'Saving Profile...' : 'Complete Registration' }}
                    </button>
                  </div>
                </form>
              </div>

              <!-- Step 3: Email Verification -->
              <div v-if="currentStep === 3" class="step-content">
                <div class="text-center">
                  <div class="mb-4">
                    <i class="fas fa-envelope-open-text text-primary" style="font-size: 3rem;"></i>
                  </div>
                  <h5 class="mb-3">Verify Your Email</h5>
                  <p class="text-muted mb-4">
                    We've sent a verification code to <strong>{{ accountForm.email }}</strong>
                  </p>
                </div>

                <form @submit.prevent="submitVerificationForm">
                  <div class="mb-3">
                    <label for="otp" class="form-label">Verification Code</label>
                    <input
                      type="text"
                      class="form-control text-center"
                      :class="{ 'is-invalid': errors.otp }"
                      id="otp"
                      v-model="verificationForm.otp"
                      @input="clearFieldError('otp')"
                      placeholder="Enter 6-digit code"
                      maxlength="6"
                      required
                    >
                    <div v-if="errors.otp" class="invalid-feedback">
                      {{ errors.otp }}
                    </div>
                  </div>

                  <div class="d-grid gap-2 mb-3">
                    <button
                      type="submit"
                      class="btn btn-primary"
                      :disabled="loading"
                    >
                      <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                      {{ loading ? 'Verifying...' : 'Verify Email' }}
                    </button>
                  </div>

                  <div class="text-center">
                    <p class="text-muted mb-2">Didn't receive the code?</p>
                    <button
                      type="button"
                      class="btn btn-link p-0"
                      @click="resendVerificationCode"
                      :disabled="resendLoading || resendCooldown > 0"
                    >
                      <span v-if="resendLoading" class="spinner-border spinner-border-sm me-2"></span>
                      {{ resendCooldown > 0 ? `Resend in ${resendCooldown}s` : 'Resend Code' }}
                    </button>
                  </div>
                </form>
              </div>

              <!-- Success Message -->
              <div v-if="currentStep === 4" class="step-content text-center">
                <div class="mb-4">
                  <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                </div>
                <h4 class="text-success mb-3">Registration Successful!</h4>
                <p class="text-muted mb-4">
                  Your account has been created and verified successfully. 
                  You can now log in to access the barangay services.
                </p>
                <div class="d-grid gap-2">
                  <button
                    type="button"
                    class="btn btn-primary"
                    @click="goToLogin"
                  >
                    <i class="fas fa-sign-in-alt me-2"></i>
                    Go to Login
                  </button>
                </div>
              </div>

              <!-- Error Alert -->
              <div v-if="errorMessage" class="alert alert-danger mt-3" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                {{ errorMessage }}
              </div>

              <!-- Success Alert -->
              <div v-if="successMessage" class="alert alert-success mt-3" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                {{ successMessage }}
              </div>
            </div>

            <div class="card-footer text-center py-3">
              <p class="mb-0 text-muted">
                Already have an account? 
                <router-link to="/client/login" class="text-decoration-none">
                  Sign in here
                </router-link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script src="./js/clientRegistration.js"></script>

<style scoped src="./css/clientRegistration.css"></style>
