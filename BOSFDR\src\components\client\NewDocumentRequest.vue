<template>
  <div class="new-request-page">
    <!-- Header -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-main">
          <h1 class="page-title">
            <i class="fas fa-plus-circle"></i>
            New Document Request
          </h1>
          <p class="page-description">
            Choose the type of document you want to request from Barangay Bula
          </p>
        </div>
        <button class="back-btn" @click="goBack">
          <i class="fas fa-arrow-left"></i>
          Back to Dashboard
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p>Loading available services...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <div class="error-content">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Unable to Load Services</h3>
        <p>{{ error }}</p>
        <button class="retry-btn" @click="loadDocumentTypes">
          <i class="fas fa-redo"></i>
          Try Again
        </button>
      </div>
    </div>

    <!-- Document Types -->
    <div v-else class="document-types-container">
      <div class="document-types-grid">
        <div
          v-for="documentType in documentTypes"
          :key="documentType.id"
          class="document-card"
          @click="selectDocumentType(documentType)"
          :class="{ 'disabled': !documentType.is_active }"
        >
          <div class="document-icon">
            <i :class="getDocumentIcon(documentType.type_name)"></i>
          </div>
          
          <div class="document-content">
            <h3 class="document-title">{{ documentType.type_name }}</h3>
            <p class="document-description">{{ documentType.description }}</p>
            
            <div class="document-details">
              <div class="fee-info">
                <span class="fee-label">Base Fee:</span>
                <span class="fee-amount">₱{{ formatCurrency(documentType.base_fee) }}</span>
              </div>
              
              <div class="processing-time">
                <i class="fas fa-clock"></i>
                <span>{{ getProcessingTime(documentType.type_name) }}</span>
              </div>
            </div>
          </div>

          <div class="document-action">
            <span v-if="!documentType.is_active" class="status-badge unavailable">
              Unavailable
            </span>
            <i v-else class="fas fa-chevron-right"></i>
          </div>
        </div>
      </div>

      <!-- Information Section -->
      <div class="info-section">
        <div class="info-card">
          <div class="info-header">
            <i class="fas fa-info-circle"></i>
            <h3>Important Information</h3>
          </div>
          <div class="info-content">
            <ul class="info-list">
              <li>
                <i class="fas fa-check"></i>
                Ensure your profile information is complete and accurate
              </li>
              <li>
                <i class="fas fa-check"></i>
                Have your valid ID and supporting documents ready
              </li>
              <li>
                <i class="fas fa-check"></i>
                Processing time may vary depending on document verification
              </li>
              <li>
                <i class="fas fa-check"></i>
                You can pay online using various payment methods
              </li>
            </ul>
          </div>
        </div>

        <div class="help-card">
          <div class="help-header">
            <i class="fas fa-headset"></i>
            <h3>Need Help?</h3>
          </div>
          <div class="help-content">
            <p>If you have questions about document requirements or the application process:</p>
            <div class="help-actions">
              <button class="help-btn" @click="openHelp">
                <i class="fas fa-question-circle"></i>
                View FAQ
              </button>
              <button class="contact-btn" @click="contactSupport">
                <i class="fas fa-phone"></i>
                Contact Support
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import documentRequestService from '@/services/documentRequestService';

export default {
  name: 'NewDocumentRequest',
  data() {
    return {
      documentTypes: [],
      loading: true,
      error: null
    };
  },
  async mounted() {
    await this.loadDocumentTypes();
  },
  methods: {
    async loadDocumentTypes() {
      try {
        this.loading = true;
        this.error = null;
        
        const response = await documentRequestService.getDocumentTypes();
        this.documentTypes = response.data || [];
        
      } catch (error) {
        console.error('Error loading document types:', error);
        this.error = error.response?.data?.message || 'Failed to load available services';
      } finally {
        this.loading = false;
      }
    },

    selectDocumentType(documentType) {
      if (!documentType.is_active) return;

      // Navigate to specific document request form
      const routeName = this.getRouteForDocumentType(documentType.type_name);
      if (routeName) {
        this.$router.push({ 
          name: routeName,
          params: { documentTypeId: documentType.id }
        });
      }
    },

    getRouteForDocumentType(typeName) {
      const routes = {
        'Barangay Clearance': 'BarangayClearanceRequest',
        'Cedula': 'CedulaRequest'
      };
      return routes[typeName];
    },

    getDocumentIcon(typeName) {
      const icons = {
        'Barangay Clearance': 'fas fa-certificate',
        'Cedula': 'fas fa-id-card'
      };
      return icons[typeName] || 'fas fa-file-alt';
    },

    getProcessingTime(typeName) {
      const times = {
        'Barangay Clearance': '3-5 business days',
        'Cedula': '1-2 business days'
      };
      return times[typeName] || '3-5 business days';
    },

    formatCurrency(amount) {
      return parseFloat(amount).toFixed(2);
    },

    goBack() {
      this.$router.push({ name: 'ClientDashboard' });
    },

    openHelp() {
      // TODO: Implement help/FAQ modal or page
      console.log('Opening help...');
    },

    contactSupport() {
      // TODO: Implement contact support functionality
      console.log('Contacting support...');
    }
  }
};
</script>

<style scoped>
.new-request-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1a365d;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.page-title i {
  color: #3182ce;
}

.page-description {
  font-size: 1.1rem;
  color: #4a5568;
  margin: 0;
}

.back-btn {
  background: #e2e8f0;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  color: #4a5568;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.back-btn:hover {
  background: #cbd5e0;
  color: #2d3748;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner i {
  font-size: 3rem;
  color: #3182ce;
  margin-bottom: 1rem;
}

.error-content {
  max-width: 400px;
}

.error-content i {
  font-size: 3rem;
  color: #e53e3e;
  margin-bottom: 1rem;
}

.retry-btn {
  background: #3182ce;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.document-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.document-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 1rem;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.document-card:hover:not(.disabled) {
  border-color: #3182ce;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(49, 130, 206, 0.1);
}

.document-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.document-icon {
  flex-shrink: 0;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #3182ce, #2c5aa0);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.document-content {
  flex: 1;
}

.document-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 0.5rem;
}

.document-description {
  color: #4a5568;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.document-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.fee-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.fee-label {
  color: #718096;
  font-size: 0.9rem;
}

.fee-amount {
  font-weight: 600;
  color: #38a169;
  font-size: 1.1rem;
}

.processing-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #718096;
  font-size: 0.9rem;
}

.document-action {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.document-action i {
  color: #a0aec0;
  font-size: 1.25rem;
}

.status-badge.unavailable {
  background: #fed7d7;
  color: #c53030;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 500;
}

.info-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.info-card, .help-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
}

.info-header, .help-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.info-header h3, .help-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a365d;
  margin: 0;
}

.info-header i {
  color: #3182ce;
}

.help-header i {
  color: #38a169;
}

.info-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-list li {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  color: #4a5568;
  line-height: 1.5;
}

.info-list i {
  color: #38a169;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.help-content p {
  color: #4a5568;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.help-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.help-btn, .contact-btn {
  background: transparent;
  border: 1px solid #e2e8f0;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4a5568;
}

.help-btn:hover {
  border-color: #3182ce;
  color: #3182ce;
}

.contact-btn:hover {
  border-color: #38a169;
  color: #38a169;
}

@media (max-width: 768px) {
  .new-request-page {
    padding: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .document-types-grid {
    grid-template-columns: 1fr;
  }
  
  .document-card {
    flex-direction: column;
    text-align: center;
  }
  
  .info-section {
    grid-template-columns: 1fr;
  }
}
</style>
