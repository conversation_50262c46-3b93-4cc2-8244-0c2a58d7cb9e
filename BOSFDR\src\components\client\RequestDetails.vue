<template>
  <div class="request-details-page">
    <!-- Header -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-main">
          <h1 class="page-title">
            <i class="fas fa-file-alt"></i>
            Request Details
          </h1>
          <p class="page-description">
            View detailed information about your document request
          </p>
        </div>
        <div class="header-actions">
          <button class="back-btn" @click="goBack">
            <i class="fas fa-arrow-left"></i>
            Back to Requests
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p>Loading request details...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <div class="error-content">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Unable to Load Request</h3>
        <p>{{ error }}</p>
        <button class="retry-btn" @click="loadRequestDetails">
          <i class="fas fa-redo"></i>
          Try Again
        </button>
      </div>
    </div>

    <!-- Request Details -->
    <div v-else-if="request" class="request-container">
      <!-- Request Overview -->
      <div class="request-overview">
        <div class="overview-header">
          <div class="request-type">
            <i :class="getDocumentIcon(request.document_type)"></i>
            <div>
              <h2>{{ request.document_type }}</h2>
              <p class="request-id">Request #{{ request.id.toString().padStart(6, '0') }}</p>
            </div>
          </div>
          <div class="request-status">
            <span class="status-badge" :class="getStatusClass(request.status)">
              {{ formatStatus(request.status) }}
            </span>
          </div>
        </div>

        <div class="overview-grid">
          <div class="overview-item">
            <label>Submitted Date:</label>
            <span>{{ formatDate(request.created_at) }}</span>
          </div>
          <div class="overview-item">
            <label>Last Updated:</label>
            <span>{{ formatDate(request.updated_at) }}</span>
          </div>
          <div class="overview-item">
            <label>Purpose:</label>
            <span>{{ request.purpose_category || 'Not specified' }}</span>
          </div>
          <div class="overview-item">
            <label>Total Fee:</label>
            <span class="amount">₱{{ formatCurrency(request.total_fee) }}</span>
          </div>
        </div>

        <!-- Progress Timeline -->
        <div class="progress-timeline">
          <h3>Request Progress</h3>

          <!-- Main Timeline -->
          <div class="timeline">
            <div
              v-for="(step, index) in getMainTimelineSteps()"
              :key="index"
              class="timeline-item"
              :class="{
                active: step.status === request.status,
                completed: isStepCompleted(step.status, request.status)
              }"
            >
              <div class="timeline-icon">
                <i :class="step.icon"></i>
              </div>
              <div class="timeline-content">
                <h4>{{ step.title }}</h4>
                <p>{{ step.description }}</p>
                <span v-if="step.status === request.status" class="timeline-date">
                  {{ formatDate(request.updated_at) }}
                </span>
              </div>
            </div>
          </div>

          <!-- Rejection Branch (only show if rejected) -->
          <div v-if="request.status === 'rejected'" class="rejection-branch">
            <div class="branch-connector"></div>
            <div class="timeline-item rejected active">
              <div class="timeline-icon">
                <i class="fas fa-times-circle"></i>
              </div>
              <div class="timeline-content">
                <h4>Request Rejected</h4>
                <p>Your request has been rejected by our staff</p>
                <span class="timeline-date">{{ formatDate(request.updated_at) }}</span>
                <div v-if="request.rejection_reason" class="rejection-reason">
                  <i class="fas fa-info-circle"></i>
                  <strong>Reason:</strong> {{ request.rejection_reason }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Request Information -->
      <div class="request-info-section">
        <h3>Request Information</h3>
        
        <!-- Personal Information -->
        <div class="info-card">
          <h4>Personal Information</h4>
          <div class="info-grid">
            <div class="info-item">
              <label>Full Name:</label>
              <span>{{ getFullName() }}</span>
            </div>
            <div class="info-item" v-if="getClientEmail()">
              <label>Email:</label>
              <span>{{ getClientEmail() }}</span>
            </div>
            <div class="info-item">
              <label>Phone:</label>
              <span>{{ getClientPhone() }}</span>
            </div>
            <div class="info-item">
              <label>Date of Birth:</label>
              <span>{{ formatDate(getClientBirthDate()) || 'Not provided' }}</span>
            </div>
            <div class="info-item">
              <label>Gender:</label>
              <span>{{ formatGender(getClientGender()) || 'Not provided' }}</span>
            </div>
            <div class="info-item">
              <label>Civil Status:</label>
              <span>{{ getCivilStatusName(getClientCivilStatus()) || 'Not provided' }}</span>
            </div>
            <div class="info-item">
              <label>Nationality:</label>
              <span>{{ getClientNationality() || 'Not provided' }}</span>
            </div>
            <div class="info-item">
              <label>Address:</label>
              <span>{{ getFullAddress() }}</span>
            </div>
            <div class="info-item">
              <label>Years of Residency:</label>
              <span>{{ getResidencyDisplay() || 'Not provided' }}</span>
            </div>
          </div>
        </div>

        <!-- Document Specific Information -->
        <div class="info-card">
          <h4>{{ request.document_type }} Details</h4>
          <div class="info-grid">
            <div class="info-item">
              <label>Purpose Category:</label>
              <span>{{ request.purpose_category || 'Not specified' }}</span>
            </div>
            <div class="info-item">
              <label>Purpose Details:</label>
              <span>{{ request.purpose_details || 'General purpose' }}</span>
            </div>

            <!-- Barangay Clearance specific fields -->
            <template v-if="request.document_type === 'Barangay Clearance'">
              <div class="info-item" v-if="getEmergencyContact()">
                <label>Emergency Contact:</label>
                <span>{{ getEmergencyContact() }}</span>
              </div>
              <div class="info-item" v-if="getEmergencyPhone()">
                <label>Emergency Phone:</label>
                <span>{{ getEmergencyPhone() }}</span>
              </div>
              <div class="info-item">
                <label>Pending Cases:</label>
                <span>{{ request.has_pending_cases ? 'Yes' : 'No' }}</span>
              </div>
            </template>

            <!-- Cedula specific fields -->
            <template v-if="request.document_type === 'Cedula'">
              <div class="info-item">
                <label>Annual Income:</label>
                <span>₱{{ formatCurrency(request.annual_income) }}</span>
              </div>
              <div class="info-item">
                <label>Income Source:</label>
                <span>{{ request.income_source || 'Not specified' }}</span>
              </div>
              <div class="info-item">
                <label>Real Property:</label>
                <span>{{ request.has_real_property ? 'Yes' : 'No' }}</span>
              </div>
              <div v-if="request.has_real_property" class="info-item">
                <label>Property Value:</label>
                <span>₱{{ formatCurrency(request.property_value) }}</span>
              </div>
              <div v-if="request.business_name" class="info-item">
                <label>Business Name:</label>
                <span>{{ request.business_name }}</span>
              </div>
            </template>
          </div>
        </div>

        <!-- Payment Information -->
        <div class="info-card">
          <h4>Payment Information</h4>
          <div class="info-grid">
            <div class="info-item">
              <label>Payment Method:</label>
              <span>{{ request.payment_method || 'Not selected' }}</span>
            </div>
            <div class="info-item">
              <label>Payment Status:</label>
              <span class="payment-status" :class="getPaymentStatusClass(request.payment_status)">
                {{ formatPaymentStatus(request.payment_status) }}
              </span>
            </div>
            <div class="info-item">
              <label>Total Fee:</label>
              <span class="amount">₱{{ formatCurrency(request.total_fee) }}</span>
            </div>
            <div v-if="request.payment_date" class="info-item">
              <label>Payment Date:</label>
              <span>{{ formatDate(request.payment_date) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="request-actions">
        <button 
          v-if="canCancelRequest(request.status)"
          class="action-btn cancel-btn"
          @click="cancelRequest"
        >
          <i class="fas fa-times"></i>
          Cancel Request
        </button>

        <button 
          v-if="needsPayment(request)"
          class="action-btn pay-btn"
          @click="processPayment"
        >
          <i class="fas fa-credit-card"></i>
          Pay Now
        </button>

        <button 
          v-if="canDownload(request.status)"
          class="action-btn download-btn"
          @click="downloadDocument"
        >
          <i class="fas fa-download"></i>
          Download Document
        </button>

        <button class="action-btn print-btn" @click="printDetails">
          <i class="fas fa-print"></i>
          Print Details
        </button>
      </div>

      <!-- Admin Notes (if any) -->
      <div v-if="request.admin_notes" class="admin-notes">
        <h3>Admin Notes</h3>
        <div class="notes-content">
          <p>{{ request.admin_notes }}</p>
          <span class="notes-date">{{ formatDate(request.updated_at) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import documentRequestService from '@/services/documentRequestService';
import clientAuthService from '@/services/clientAuthService';

export default {
  name: 'RequestDetails',
  data() {
    return {
      request: null,
      loading: true,
      error: null,
      progressSteps: [
        {
          status: 'pending',
          title: 'Request Submitted',
          description: 'Your request has been submitted and is waiting for review',
          icon: 'fas fa-paper-plane'
        },
        {
          status: 'under_review',
          title: 'Under Review',
          description: 'Your request is being reviewed by our staff',
          icon: 'fas fa-search'
        },
        {
          status: 'approved',
          title: 'Approved',
          description: 'Your request has been approved and payment is required',
          icon: 'fas fa-check'
        },
        {
          status: 'processing',
          title: 'Processing',
          description: 'Your document is being prepared',
          icon: 'fas fa-cog'
        },
        {
          status: 'ready_for_pickup',
          title: 'Ready for Pickup',
          description: 'Your document is ready for pickup',
          icon: 'fas fa-box'
        },
        {
          status: 'completed',
          title: 'Completed',
          description: 'Request has been completed successfully',
          icon: 'fas fa-check-circle'
        }
      ]
    };
  },
  computed: {
    clientData() {
      return clientAuthService.getCurrentUser();
    }
  },
  async mounted() {
    await this.loadRequestDetails();
  },
  methods: {
    async loadRequestDetails() {
      try {
        this.loading = true;
        this.error = null;
        
        const requestId = this.$route.params.id;
        const response = await documentRequestService.getRequestDetails(requestId);
        this.request = response.data;
        
      } catch (error) {
        console.error('Error loading request details:', error);
        this.error = error.response?.data?.message || 'Failed to load request details';
      } finally {
        this.loading = false;
      }
    },

    getDocumentIcon(type) {
      const icons = {
        'Barangay Clearance': 'fas fa-certificate',
        'Cedula': 'fas fa-id-card'
      };
      return icons[type] || 'fas fa-file-alt';
    },

    getStatusClass(status) {
      const classes = {
        'pending': 'status-pending',
        'under_review': 'status-review',
        'approved': 'status-approved',
        'processing': 'status-processing',
        'ready_for_pickup': 'status-ready',
        'completed': 'status-completed',
        'rejected': 'status-rejected',
        'cancelled': 'status-cancelled'
      };
      return classes[status] || 'status-unknown';
    },

    formatStatus(status) {
      const statusMap = {
        'pending': 'Pending',
        'under_review': 'Under Review',
        'approved': 'Approved',
        'processing': 'Processing',
        'ready_for_pickup': 'Ready for Pickup',
        'completed': 'Completed',
        'rejected': 'Rejected',
        'cancelled': 'Cancelled'
      };
      return statusMap[status] || status;
    },

    getPaymentStatusClass(status) {
      const classes = {
        'pending': 'payment-pending',
        'paid': 'payment-paid',
        'failed': 'payment-failed',
        'refunded': 'payment-refunded'
      };
      return classes[status] || 'payment-unknown';
    },

    formatPaymentStatus(status) {
      const statusMap = {
        'pending': 'Pending Payment',
        'paid': 'Paid',
        'failed': 'Payment Failed',
        'refunded': 'Refunded'
      };
      return statusMap[status] || 'Not Required';
    },

    getMainTimelineSteps() {
      // Return only the main progression steps (no rejected)
      return this.progressSteps;
    },

    isStepCompleted(stepStatus, currentStatus) {
      // Don't mark any steps as completed if the request is rejected
      if (currentStatus === 'rejected') {
        // Only mark steps before "under_review" as completed for rejected requests
        const completedBeforeRejection = ['pending'];
        return completedBeforeRejection.includes(stepStatus);
      }

      const statusOrder = ['pending', 'under_review', 'approved', 'processing', 'ready_for_pickup', 'completed'];
      const stepIndex = statusOrder.indexOf(stepStatus);
      const currentIndex = statusOrder.indexOf(currentStatus);
      return stepIndex < currentIndex;
    },

    getFullName() {
      const client = this.request?.client || this.clientData?.profile;
      if (!client) return 'N/A';
      return `${client.first_name || ''} ${client.middle_name || ''} ${client.last_name || ''}`.trim();
    },

    getClientEmail() {
      const email = this.request?.email || this.request?.client?.email || this.clientData?.profile?.email;
      return email && email.trim() ? email : null;
    },

    getClientPhone() {
      const phone = this.request?.phone_number || this.request?.client?.phone_number || this.clientData?.profile?.phone_number;
      return phone && phone.trim() ? phone : 'Contact information not available';
    },

    getEmergencyContact() {
      const contact = this.request?.emergency_contact_name;
      return contact && contact.trim() ? contact : null;
    },

    getEmergencyPhone() {
      const phone = this.request?.emergency_contact_phone;
      return phone && phone.trim() ? phone : null;
    },

    getFullAddress() {
      // Try multiple sources for address data
      const client = this.request?.client || this.clientData || this.request;
      if (!client) return 'Address not available';

      const parts = [
        client.house_number,
        client.street,
        client.subdivision,
        client.barangay,
        client.city_municipality || client.city,
        client.province
      ].filter(part => part && part.trim());

      return parts.length > 0 ? parts.join(', ') : 'Address not available';
    },

    // New helper methods for complete client information
    getClientBirthDate() {
      return this.request?.birth_date || this.request?.client?.birth_date || this.clientData?.birth_date;
    },

    getClientGender() {
      return this.request?.gender || this.request?.client?.gender || this.clientData?.gender;
    },

    getClientCivilStatus() {
      return this.request?.civil_status_id || this.request?.client?.civil_status_id || this.clientData?.civil_status_id;
    },

    getClientNationality() {
      return this.request?.nationality || this.request?.client?.nationality || this.clientData?.nationality;
    },

    formatGender(gender) {
      if (!gender) return 'Not provided';
      return gender.charAt(0).toUpperCase() + gender.slice(1);
    },

    getCivilStatusName(statusId) {
      const statuses = {
        1: 'Single',
        2: 'Married',
        3: 'Divorced',
        4: 'Widowed',
        5: 'Separated'
      };
      return statuses[statusId] || 'Not provided';
    },

    getResidencyDisplay() {
      const client = this.request?.client || this.clientData || this.request;
      if (!client) return 'Not provided';

      const years = client.years_of_residency;
      const months = client.months_of_residency;

      if (!years && !months) return 'Not provided';

      const parts = [];
      if (years) parts.push(`${years} year${years > 1 ? 's' : ''}`);
      if (months) parts.push(`${months} month${months > 1 ? 's' : ''}`);

      return parts.join(' and ');
    },

    formatDate(dateString) {
      if (!dateString) return 'N/A';
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    formatCurrency(amount) {
      return parseFloat(amount || 0).toFixed(2);
    },

    canCancelRequest(status) {
      // Enhanced cancellation rules - allow cancellation until payment is confirmed
      const cancellableStatuses = [
        'pending',
        'under_review',
        'additional_info_required',
        'approved',
        'payment_pending',
        'payment_failed'
      ];
      return cancellableStatuses.includes(status);
    },

    needsPayment(request) {
      // Check if request needs payment based on enhanced workflow
      const paymentRequiredStatuses = ['approved', 'payment_pending', 'payment_failed'];
      const unpaidStatuses = ['pending', 'failed', null, undefined, ''];

      return paymentRequiredStatuses.includes(request.status) &&
             unpaidStatuses.includes(request.payment_status) &&
             request.payment_method_id && // Must have a payment method selected
             request.is_online_payment; // Only show for online payment methods
    },

    canDownload(status) {
      return status === 'completed';
    },

    async cancelRequest() {
      // Enhanced cancellation with reason input
      const reason = prompt(
        'Please provide a reason for cancelling this request (optional):',
        ''
      );

      // If user clicked cancel on the prompt, don't proceed
      if (reason === null) return;

      // Confirm cancellation
      if (!confirm('Are you sure you want to cancel this request?')) return;

      try {
        console.log('🚫 Cancelling request:', this.request.id, 'Reason:', reason);

        const cancellationReason = reason.trim() || 'Cancelled by user';
        await documentRequestService.cancelRequest(this.request.id, cancellationReason);

        this.$toast?.success('Request cancelled successfully. Administrators have been notified.');
        this.request.status = 'cancelled';

        // Refresh request details to show updated status
        await this.loadRequestDetails();

        console.log('✅ Request cancelled successfully');
      } catch (error) {
        console.error('❌ Error cancelling request:', error);

        // Show more specific error messages
        if (error.response?.data?.message) {
          this.$toast?.error(error.response.data.message);
        } else if (error.message?.includes('cannot be cancelled')) {
          this.$toast?.error('This request cannot be cancelled at its current stage');
        } else {
          this.$toast?.error('Failed to cancel request. Please try again.');
        }
      }
    },

    async processPayment() {
      try {
        // Check if request has a payment method selected
        if (!this.request.payment_method_id) {
          this.showToast('Error', 'No payment method selected for this request', 'error');
          return;
        }

        // Check if it's an online payment method
        if (!this.request.is_online_payment) {
          this.showToast('Info', 'This request uses in-person payment. Please pay at the barangay office.', 'info');
          return;
        }

        // Show loading state
        this.showToast('Info', 'Initiating payment...', 'info');

        // Get current user data from clientAuthService
        const currentUser = this.$clientAuth.getCurrentUser();

        // Prepare payment data
        const paymentData = {
          request_id: this.request.id,
          payment_method_id: this.request.payment_method_id,
          customer_email: currentUser?.email || this.request.email
        };

        // Import paymentService dynamically if not already imported
        const { default: paymentService } = await import('@/services/paymentService');

        // Initiate payment through PayMongo
        const response = await paymentService.initiatePayment(paymentData);

        if (response.success && response.data.checkout_url) {
          // Redirect to PayMongo checkout page
          this.showToast('Success', 'Redirecting to payment page...', 'success');
          paymentService.redirectToPayMongo(response.data.checkout_url);
        } else {
          throw new Error(response.message || 'Failed to initiate payment');
        }

      } catch (error) {
        console.error('Payment initiation error:', error);
        const errorMessage = error.response?.data?.message || error.message || 'Failed to initiate payment';
        this.showToast('Error', errorMessage, 'error');
      }
    },

    downloadDocument() {
      // TODO: Implement document download
      console.log('Download document for request:', this.request.id);
    },

    printDetails() {
      window.print();
    },

    goBack() {
      this.$router.push({ name: 'MyRequests' });
    },

    showToast(title, message, type = 'info') {
      // Log to console for debugging
      console.log(`[${type.toUpperCase()}] ${title}: ${message}`);

      // Create a simple toast notification
      const toast = document.createElement('div');
      toast.className = `toast-notification toast-${type}`;
      toast.innerHTML = `
        <div class="toast-header">
          <strong>${title}</strong>
          <button type="button" class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
        <div class="toast-body">${message}</div>
      `;

      // Add toast styles if not already added
      if (!document.getElementById('toast-styles')) {
        const styles = document.createElement('style');
        styles.id = 'toast-styles';
        styles.textContent = `
          .toast-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            min-width: 300px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 9999;
            animation: slideIn 0.3s ease;
          }
          .toast-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px 8px;
            border-bottom: 1px solid #e9ecef;
          }
          .toast-body {
            padding: 8px 16px 12px;
            color: #6c757d;
          }
          .toast-close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #6c757d;
          }
          .toast-success { border-left: 4px solid #28a745; }
          .toast-error { border-left: 4px solid #dc3545; }
          .toast-info { border-left: 4px solid #17a2b8; }
          .toast-warning { border-left: 4px solid #ffc107; }
          @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
          }
        `;
        document.head.appendChild(styles);
      }

      // Add toast to page
      document.body.appendChild(toast);

      // Auto-remove after 5 seconds
      setTimeout(() => {
        if (toast.parentElement) {
          toast.style.animation = 'slideIn 0.3s ease reverse';
          setTimeout(() => toast.remove(), 300);
        }
      }, 5000);
    }
  }
};
</script>

<style scoped>
.request-details-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1a365d;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.page-title i {
  color: #3182ce;
}

.page-description {
  font-size: 1rem;
  color: #4a5568;
  margin: 0;
}

.back-btn {
  background: #e2e8f0;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  color: #4a5568;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.back-btn:hover {
  background: #cbd5e0;
  color: #2d3748;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner i {
  font-size: 2rem;
  color: #3182ce;
  margin-bottom: 1rem;
}

.error-content {
  max-width: 400px;
}

.error-content i {
  font-size: 3rem;
  color: #e53e3e;
  margin-bottom: 1rem;
}

.error-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.error-content p {
  color: #718096;
  margin-bottom: 1.5rem;
}

.retry-btn {
  background: #3182ce;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.retry-btn:hover {
  background: #2c5aa0;
}

.request-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.request-overview {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.request-type {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.request-type i {
  font-size: 2rem;
  color: #3182ce;
}

.request-type h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 0.25rem;
}

.request-id {
  font-family: 'Courier New', monospace;
  color: #718096;
  font-size: 0.875rem;
  margin: 0;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-pending { background: #fef5e7; color: #d69e2e; }
.status-review { background: #ebf8ff; color: #3182ce; }
.status-approved { background: #f0fff4; color: #38a169; }
.status-processing { background: #e6fffa; color: #319795; }
.status-ready { background: #f0f9ff; color: #0ea5e9; }
.status-completed { background: #f0fff4; color: #22c55e; }
.status-rejected { background: #fef2f2; color: #ef4444; }
.status-cancelled { background: #f8fafc; color: #64748b; }

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.overview-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.overview-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.overview-item span {
  color: #2d3748;
  font-weight: 500;
}

.amount {
  color: #38a169 !important;
  font-weight: 600 !important;
  font-size: 1.125rem !important;
}

.progress-timeline {
  margin-top: 2rem;
}

.progress-timeline h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 1.5rem;
}

.timeline {
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 1.5rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e2e8f0;
}

.timeline-item {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding-bottom: 2rem;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-icon {
  width: 3rem;
  height: 3rem;
  background: #e2e8f0;
  color: #a0aec0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  position: relative;
  z-index: 2;
  flex-shrink: 0;
}

.timeline-item.completed .timeline-icon {
  background: #38a169;
  color: white;
}

.timeline-item.active .timeline-icon {
  background: #3182ce;
  color: white;
  animation: pulse 2s infinite;
}

/* Rejected status - higher specificity to override completed */
.timeline-item.rejected .timeline-icon {
  background: #e53e3e !important;
  color: white !important;
  animation: none !important;
}

/* Rejection Branch Styling */
.rejection-branch {
  position: relative;
  margin-top: 1rem;
  padding-left: 2rem;
}

.branch-connector {
  position: absolute;
  left: 1.5rem;
  top: -1rem;
  width: 2px;
  height: 1rem;
  background: #e53e3e;
}

.branch-connector::before {
  content: '';
  position: absolute;
  top: 1rem;
  left: -4px;
  width: 10px;
  height: 2px;
  background: #e53e3e;
}

.rejection-branch .timeline-item {
  padding-bottom: 0;
}

.rejection-branch .rejection-reason {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: #fef2f2;
  border-left: 4px solid #e53e3e;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.rejection-branch .rejection-reason i {
  color: #e53e3e;
  margin-right: 0.5rem;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(49, 130, 206, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(49, 130, 206, 0); }
  100% { box-shadow: 0 0 0 0 rgba(49, 130, 206, 0); }
}

.timeline-content {
  flex: 1;
  padding-top: 0.25rem;
}

.timeline-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 0.25rem;
}

.timeline-content p {
  color: #4a5568;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.timeline-date {
  font-size: 0.75rem;
  color: #718096;
  font-style: italic;
}

.request-info-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.request-info-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 0;
}

.info-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.info-card h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1a365d;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item label {
  font-size: 0.75rem;
  font-weight: 500;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.info-item span {
  color: #2d3748;
  font-weight: 500;
}

.payment-status {
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  display: inline-block;
}

.payment-pending { background: #fef5e7; color: #d69e2e; }
.payment-paid { background: #f0fff4; color: #38a169; }
.payment-failed { background: #fef2f2; color: #e53e3e; }
.payment-refunded { background: #f0f9ff; color: #0ea5e9; }

.request-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  padding: 2rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.action-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
  min-width: 140px;
  justify-content: center;
}

.cancel-btn {
  background: #e53e3e;
  color: white;
}

.cancel-btn:hover {
  background: #c53030;
}

.pay-btn {
  background: #38a169;
  color: white;
}

.pay-btn:hover {
  background: #2f855a;
}

.download-btn {
  background: #3182ce;
  color: white;
}

.download-btn:hover {
  background: #2c5aa0;
}

.print-btn {
  background: #718096;
  color: white;
}

.print-btn:hover {
  background: #4a5568;
}

.admin-notes {
  background: #fffaf0;
  border: 1px solid #fed7aa;
  border-radius: 1rem;
  padding: 1.5rem;
}

.admin-notes h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #c05621;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-notes h3::before {
  content: '📝';
}

.notes-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.notes-content p {
  color: #744210;
  margin: 0;
  line-height: 1.5;
}

.notes-date {
  font-size: 0.75rem;
  color: #a16207;
  font-style: italic;
}

@media print {
  .page-header,
  .request-actions {
    display: none;
  }

  .request-details-page {
    padding: 0;
  }

  .request-container {
    gap: 1rem;
  }

  .request-overview,
  .info-card {
    box-shadow: none;
    border: 1px solid #e2e8f0;
  }
}

@media (max-width: 768px) {
  .request-details-page {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .overview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .timeline-item {
    gap: 0.75rem;
  }

  .timeline-icon {
    width: 2.5rem;
    height: 2.5rem;
  }

  .timeline::before {
    left: 1.25rem;
  }

  .request-actions {
    flex-direction: column;
    padding: 1.5rem;
  }

  .action-btn {
    min-width: auto;
  }
}
</style>
