/* Client Dashboard Styles */
.client-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
  color: #1a202c;
  line-height: 1.6;
  /* Performance optimizations */
  contain: layout style paint;
  will-change: auto;
}

/* CSS Custom Properties for theming */
:root {
  --primary-color: #1e3a8a;
  --primary-light: #1e40af;
  --accent-color: #fbbf24;
  --accent-light: #f59e0b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --text-primary: #1a202c;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #e2e8f0;
  --border-color: rgba(30, 58, 138, 0.1);
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --bg-primary: #1e293b;
    --bg-secondary: #334155;
    --bg-tertiary: #475569;
    --border-color: rgba(148, 163, 184, 0.2);
  }

  .client-dashboard {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: var(--text-primary);
  }
}

/* Accessibility Improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus management */
*:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Skip to main content link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
  }

  .stat-card,
  .action-card,
  .chart-card {
    border: 2px solid var(--border-color);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .loading-spinner i {
    animation: none;
  }

  .progress-circle {
    transition: none;
  }
}

/* Print styles */
@media print {
  .client-dashboard {
    background: white !important;
    color: black !important;
  }

  .sidebar,
  .mobile-overlay,
  .refresh-button,
  .search-section,
  .action-card {
    display: none !important;
  }

  .main-content {
    margin-left: 0 !important;
  }

  .stat-card,
  .chart-card {
    break-inside: avoid;
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.loading-spinner {
  font-size: 3rem;
  color: #1e3a8a;
  margin-bottom: 1rem;
}

.loading-state p {
  font-size: 1.1rem;
  color: #6c757d;
  margin: 0;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  padding: 2rem;
}

.error-icon {
  font-size: 4rem;
  color: #dc3545;
  margin-bottom: 1rem;
}

.error-state h3 {
  color: #333;
  margin-bottom: 0.5rem;
}

.error-state p {
  color: #6c757d;
  margin-bottom: 1.5rem;
}

.retry-button {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.retry-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
}

/* Last Updated Info */
.last-updated {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #6c757d;
}

.refresh-button {
  background: none;
  border: 1px solid #dee2e6;
  color: #6c757d;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-button:hover:not(:disabled) {
  background-color: #f8f9fa;
  border-color: #1e3a8a;
  color: #1e3a8a;
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Progress bars for stats */
.stat-progress {
  margin-top: 0.75rem;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: #e2e8f0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 100%);
  border-radius: 3px;
  transition: width 0.8s ease;
}

.progress-fill.success {
  background: linear-gradient(90deg, #10b981 0%, #34d399 100%);
}

.progress-text {
  font-size: 0.75rem;
  color: var(--text-muted);
  font-weight: 500;
}

/* Stat trends */
.stat-trend {
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Make stat cards clickable */
.stat-card[role="button"] {
  cursor: pointer;
}

.stat-card[role="button"]:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Client Dashboard Root Container */
.client-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

/* Layout Container */
.layout-container {
  display: flex;
  min-height: 100vh;
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: 280px;
  margin-top: 70px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: calc(100vh - 70px);
  overflow-x: hidden;
  overflow-y: auto;
  will-change: margin-left;
  position: relative;
}

.main-content.sidebar-collapsed {
  margin-left: 70px;
}

.content-wrapper {
  padding: 2rem;
  max-width: 100%;
  min-height: 100%;
}

/* Dashboard Content */
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Welcome Section */
.welcome-section {
  margin-bottom: 1rem;
}

.welcome-card {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  color: white;
  padding: 2.5rem;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 8px 32px rgba(30, 58, 138, 0.2);
  border: 2px solid #fbbf24;
  position: relative;
  overflow: hidden;
}

.welcome-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%);
}

.welcome-content {
  flex: 1;
}

.welcome-title {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.welcome-text {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.account-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-active {
  background-color: #28a745;
  color: white;
}

.status-pending {
  background-color: #ffc107;
  color: #212529;
}

.status-suspended {
  background-color: #dc3545;
  color: white;
}

.status-inactive {
  background-color: #6c757d;
  color: white;
}

.welcome-avatar {
  font-size: 4rem;
  opacity: 0.3;
}

/* Dashboard Insights */
.dashboard-insights {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.insight-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
}

.insight-item i {
  color: #fbbf24;
}

/* Data Visualization Section */
.visualization-section {
  margin-bottom: 2rem;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

@media (max-width: 768px) {
  .charts-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.chart-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 2rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.chart-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.chart-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

/* Donut Chart */
.donut-chart {
  position: relative;
  width: 200px;
  height: 200px;
}

.donut-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.progress-circle {
  transition: stroke-dashoffset 1s ease;
}

.chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.chart-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.chart-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.chart-legend {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-color.completed {
  background-color: #10b981;
}

.legend-color.pending {
  background-color: #f59e0b;
}

/* Bar Chart */
.bar-chart {
  width: 100%;
  max-width: 300px;
}

.bar-chart-bars {
  display: flex;
  align-items: end;
  gap: 0.5rem;
  height: 150px;
  margin-bottom: 0.5rem;
}

.bar-item {
  flex: 1;
  background: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-light) 100%);
  border-radius: 4px 4px 0 0;
  min-height: 20px;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
}

.bar-item:hover {
  opacity: 0.8;
  transform: scaleY(1.05);
}

.bar-value {
  position: absolute;
  top: -1.5rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-secondary);
  white-space: nowrap;
}

.bar-chart-labels {
  display: flex;
  gap: 0.5rem;
}

.bar-label {
  flex: 1;
  text-align: center;
  font-size: 0.8rem;
  color: var(--text-muted);
  font-weight: 500;
}

/* Search and Filter Section */
.search-section {
  margin-bottom: 2rem;
}

.search-container {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .search-container {
    flex-direction: column;
    align-items: stretch;
  }
}

.search-input-group {
  position: relative;
  flex: 1;
  min-width: 300px;
}

@media (max-width: 768px) {
  .search-input-group {
    min-width: auto;
  }
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 0.9rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.clear-search {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.clear-search:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
}

.filter-group {
  display: flex;
  gap: 0.75rem;
}

.filter-select {
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

/* Search Results */
.search-results {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-top: 1rem;
}

.search-results-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem 0;
}

.search-results-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.search-result-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.search-result-item:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
  transform: translateX(4px);
}

.result-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.result-content {
  flex: 1;
}

.result-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.25rem 0;
}

.result-description {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.result-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.8rem;
}

.result-status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.result-status.status-pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.result-status.status-completed {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.result-status.status-processing {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.result-date {
  color: var(--text-muted);
}

.search-results-more {
  margin-top: 1rem;
  text-align: center;
}

.view-all-button {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-all-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.no-search-results {
  text-align: center;
  padding: 2rem;
  color: var(--text-muted);
}

.no-search-results i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  opacity: 0.5;
}

/* Stats Section */
.stats-section {
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

@media (min-width: 1400px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .actions-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Enhanced responsive design */
@media (max-width: 768px) {
  .content-wrapper {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .actions-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .welcome-card {
    padding: 1.5rem;
    flex-direction: column;
    text-align: center;
  }

  .welcome-title {
    font-size: 1.5rem;
  }

  .dashboard-insights {
    justify-content: center;
  }

  .stat-card {
    padding: 1.5rem;
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .stat-number {
    font-size: 1.8rem;
  }

  .action-card {
    padding: 1.5rem;
    text-align: center;
  }

  .action-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    margin: 0 auto 1rem auto;
  }

  .chart-card {
    padding: 1.5rem;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .donut-chart {
    width: 150px;
    height: 150px;
  }

  .chart-value {
    font-size: 1.5rem;
  }

  .last-updated {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    font-size: 0.8rem;
  }
}

.stat-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(30, 58, 138, 0.08);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(30, 58, 138, 0.1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  /* Performance optimizations */
  contain: layout style paint;
  transform: translateZ(0); /* Force hardware acceleration */
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1e3a8a 0%, #fbbf24 100%);
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 16px 48px rgba(30, 58, 138, 0.15);
  border-color: rgba(251, 191, 36, 0.3);
}

/* Accessibility improvements */
.stat-card:focus-within {
  outline: 2px solid #1e3a8a;
  outline-offset: 2px;
}

.stat-icon {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  color: #fbbf24;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  box-shadow: 0 8px 24px rgba(30, 58, 138, 0.25);
  border: 2px solid #fbbf24;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.stat-card:hover .stat-icon::before {
  opacity: 1;
  animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.stat-icon.pending {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #1e3a8a;
}

.stat-icon.completed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.stat-icon.payments {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2.2rem;
  font-weight: 800;
  margin: 0;
  color: #1a202c;
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.stat-label {
  font-size: 0.95rem;
  color: #64748b;
  margin: 0;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Quick Actions Section */
.quick-actions-section {
  margin-bottom: 1rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
  color: #333;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.action-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(30, 58, 138, 0.08);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1.5rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(30, 58, 138, 0.1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  text-align: left;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%);
}

.action-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 16px 48px rgba(251, 191, 36, 0.15);
  border-color: rgba(30, 58, 138, 0.3);
}

.action-card:focus {
  outline: 2px solid #1e3a8a;
  outline-offset: 2px;
}

.action-card:active {
  transform: translateY(-4px) scale(1.01);
}

.action-icon {
  width: 70px;
  height: 70px;
  border-radius: 18px;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #1e3a8a;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  box-shadow: 0 8px 24px rgba(251, 191, 36, 0.25);
  border: 2px solid #1e3a8a;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.action-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.action-card:hover .action-icon::before {
  opacity: 1;
  animation: shimmer 1.5s ease-in-out;
}

.action-card:hover .action-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 32px rgba(251, 191, 36, 0.4);
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: #1a202c;
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.action-description {
  font-size: 0.95rem;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: #333;
}

.action-description {
  font-size: 0.85rem;
  color: #6c757d;
  margin: 0;
  line-height: 1.4;
}

/* Section Headers */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.refresh-activity-btn {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-muted);
  padding: 0.5rem;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-activity-btn:hover:not(:disabled) {
  background-color: var(--bg-secondary);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.refresh-activity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Activity Section */
.activity-section {
  margin-bottom: 2rem;
}

.activity-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

@media (max-width: 1024px) {
  .activity-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.activity-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.activity-main {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h4 {
  font-size: 1.1rem;
  margin: 0 0 0.5rem 0;
  color: #495057;
}

.empty-state p {
  font-size: 0.9rem;
  margin: 0;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 16px;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
  overflow: hidden;
}

.activity-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #1e3a8a 0%, #fbbf24 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.activity-item:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-color: rgba(30, 58, 138, 0.1);
  transform: translateX(4px);
}

.activity-item:hover::before {
  opacity: 1;
}

.activity-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  flex-shrink: 0;
  border: 2px solid rgba(30, 58, 138, 0.1);
  transition: all 0.3s ease;
}

.activity-item:hover .activity-icon {
  transform: scale(1.1);
  border-color: rgba(30, 58, 138, 0.2);
}

.activity-details {
  flex: 1;
}

.activity-title {
  font-size: 0.95rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: #333;
}

.activity-description {
  font-size: 0.85rem;
  color: #6c757d;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.activity-time {
  font-size: 0.8rem;
  color: #adb5bd;
}

/* Account Info */
.account-info {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  height: fit-content;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-label {
  font-size: 0.8rem;
  color: #6c757d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 0.9rem;
  color: #333;
  font-weight: 500;
}

.info-value-with-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-value-with-status span {
  font-size: 0.9rem;
  color: #333;
  font-weight: 500;
}

.verified {
  color: #28a745;
}

.unverified {
  color: #ffc107;
}

/* Page Content (for other menu items) */
.page-content {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.page-header {
  margin-bottom: 2rem;
  text-align: center;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #333;
}

.page-description {
  font-size: 1rem;
  color: #6c757d;
  margin: 0;
}

.page-body {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.coming-soon {
  text-align: center;
  color: #6c757d;
}

.coming-soon i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.3;
}

.coming-soon h3 {
  font-size: 1.5rem;
  margin: 0 0 0.5rem 0;
  color: #495057;
}

.coming-soon p {
  font-size: 1rem;
  margin: 0;
}

/* Enhanced Mobile-First Responsive Design */

/* Large Desktop */
@media (min-width: 1400px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }

  .actions-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }

  .content-wrapper {
    padding: 3rem;
    max-width: 1400px;
    margin: 0 auto;
  }
}

/* Desktop */
@media (max-width: 1200px) {
  .activity-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.5rem;
  }

  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  .content-wrapper {
    padding: 2rem;
  }
}

/* Tablet */
@media (max-width: 992px) {
  .main-content {
    margin-left: 70px; /* Collapsed sidebar by default on tablets */
    transition: margin-left 0.3s ease;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }

  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }

  .welcome-card {
    padding: 2rem;
    flex-direction: row;
  }

  .content-wrapper {
    padding: 1.5rem;
  }

  /* Improve touch targets for tablets */
  .stat-card,
  .action-card {
    min-height: 120px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .stat-card:hover,
  .action-card:hover {
    transform: translateY(-2px);
  }
}

/* Mobile Landscape and Small Tablets */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    margin-top: 60px;
    height: calc(100vh - 60px);
    width: 100%;
    transition: none;
    overflow-y: auto;
  }

  .main-content.sidebar-collapsed {
    margin-left: 0;
  }

  .content-wrapper {
    padding: 1rem;
    min-height: 100%;
  }

  /* Enhanced welcome section for mobile */
  .welcome-card {
    padding: 1.5rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .welcome-title {
    font-size: clamp(1.25rem, 4vw, 1.5rem);
    line-height: 1.3;
  }

  .welcome-text {
    font-size: clamp(0.875rem, 3vw, 1rem);
    line-height: 1.5;
  }

  .welcome-avatar {
    font-size: 3rem;
    order: -1;
  }

  /* Improved stats grid for mobile */
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .stat-card {
    padding: 1.25rem;
    min-height: 100px;
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
    margin: 0 auto;
  }

  .stat-number {
    font-size: clamp(1.5rem, 5vw, 2rem);
  }

  .stat-label {
    font-size: clamp(0.75rem, 2.5vw, 0.875rem);
  }

  /* Enhanced actions grid */
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .action-card {
    padding: 1.25rem;
    flex-direction: column;
    text-align: center;
    min-height: 120px;
    gap: 0.75rem;
  }

  .action-icon {
    width: 50px;
    height: 50px;
    font-size: 1.4rem;
    margin: 0 auto 0.5rem;
  }

  .action-title {
    font-size: clamp(0.875rem, 3vw, 1rem);
  }

  .action-description {
    font-size: clamp(0.75rem, 2.5vw, 0.875rem);
    line-height: 1.4;
  }

  /* Activity section improvements */
  .activity-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .activity-item {
    padding: 1rem;
    border-radius: 12px;
    background: #f8f9fa;
    margin-bottom: 0.75rem;
  }

  .activity-icon {
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
  }

  /* Account info improvements */
  .account-info {
    padding: 1.25rem;
  }

  .info-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
  }

  .info-item:last-child {
    border-bottom: none;
  }
}

/* Mobile Portrait */
@media (max-width: 480px) {
  .main-content {
    margin-top: 56px;
    height: calc(100vh - 56px);
    overflow-y: auto;
  }

  .content-wrapper {
    padding: 0.75rem;
  }

  /* Single column layout for small screens */
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .actions-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  /* Compact welcome card */
  .welcome-card {
    padding: 1.25rem;
    gap: 0.75rem;
  }

  .welcome-title {
    font-size: clamp(1.1rem, 5vw, 1.25rem);
  }

  .welcome-text {
    font-size: clamp(0.8rem, 3.5vw, 0.9rem);
  }

  .welcome-avatar {
    font-size: 2.5rem;
  }

  /* Compact stat cards */
  .stat-card {
    padding: 1rem;
    min-height: 80px;
    gap: 0.5rem;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .stat-number {
    font-size: clamp(1.25rem, 6vw, 1.5rem);
  }

  .stat-label {
    font-size: clamp(0.7rem, 3vw, 0.8rem);
  }

  /* Compact action cards */
  .action-card {
    padding: 1rem;
    min-height: 100px;
    gap: 0.5rem;
  }

  .action-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
  }

  .action-title {
    font-size: clamp(0.8rem, 3.5vw, 0.9rem);
  }

  .action-description {
    font-size: clamp(0.7rem, 3vw, 0.8rem);
  }

  /* Compact activity items */
  .activity-item {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .activity-icon {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .activity-title {
    font-size: clamp(0.8rem, 3.5vw, 0.9rem);
  }

  .activity-description {
    font-size: clamp(0.7rem, 3vw, 0.8rem);
  }

  .activity-time {
    font-size: clamp(0.65rem, 2.5vw, 0.75rem);
  }

  /* Account info compact */
  .account-info {
    padding: 1rem;
  }

  .info-item {
    padding: 0.5rem 0;
  }

  .info-label {
    font-size: clamp(0.7rem, 2.5vw, 0.8rem);
  }

  .info-value {
    font-size: clamp(0.8rem, 3vw, 0.9rem);
  }
}

/* Extra Small Devices */
@media (max-width: 360px) {
  .content-wrapper {
    padding: 0.5rem;
  }

  .welcome-card {
    padding: 1rem;
  }

  .stat-card,
  .action-card {
    padding: 0.875rem;
  }

  .dashboard-content {
    gap: 1rem;
  }

  .section-title {
    font-size: clamp(1rem, 4vw, 1.125rem);
  }
}

/* Landscape orientation optimizations */
@media (max-height: 500px) and (orientation: landscape) {
  .welcome-card {
    padding: 1rem;
  }

  .content-wrapper {
    padding: 1rem;
  }

  .dashboard-content {
    gap: 1rem;
  }

  .stats-grid,
  .actions-grid {
    gap: 0.75rem;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dashboard-content > * {
  animation: fadeInUp 0.5s ease forwards;
}

.dashboard-content > *:nth-child(1) { animation-delay: 0.1s; }
.dashboard-content > *:nth-child(2) { animation-delay: 0.2s; }
.dashboard-content > *:nth-child(3) { animation-delay: 0.3s; }
.dashboard-content > *:nth-child(4) { animation-delay: 0.4s; }

/* Scrollbar styling moved to clientSidebar.css */
