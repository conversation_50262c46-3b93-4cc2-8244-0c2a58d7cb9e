/* Client Header Styles - Modern Blue & Yellow Theme */
.dashboard-header {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  color: white;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  box-shadow: 0 4px 20px rgba(30, 58, 138, 0.15);
  position: fixed;
  top: 0;
  left: 280px;
  right: 0;
  z-index: 1001;
  border-bottom: 3px solid #fbbf24;
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: left;
}

/* Header positioning when sidebar is collapsed */
.dashboard-header.sidebar-collapsed {
  left: 70px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

/* Header Left Section */
.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 1.3rem;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.sidebar-toggle:hover {
  background-color: rgba(251, 191, 36, 0.2);
  border-color: #fbbf24;
  transform: scale(1.05);
}

.page-title h1 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: white;
  line-height: 1.2;
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

/* Notification Dropdown */
.notification-dropdown {
  position: relative;
}

.notification-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.notification-btn:hover {
  background-color: rgba(251, 191, 36, 0.2);
  border-color: #fbbf24;
  transform: scale(1.05);
}

.notification-badge {
  position: absolute;
  top: 0.3rem;
  right: 0.3rem;
  background-color: #ef4444;
  color: white;
  font-size: 0.7rem;
  padding: 0.2rem 0.4rem;
  border-radius: 12px;
  min-width: 18px;
  text-align: center;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

/* Notification Menu */
.notification-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  padding: 0;
  min-width: 350px;
  max-width: 400px;
  z-index: 1002;
  margin-top: 0.5rem;
  border: 2px solid #f1f5f9;
  overflow: hidden;
}

.notification-header {
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 2px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-header h6 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3a8a;
}

.notification-count {
  font-size: 0.8rem;
  color: #6b7280;
  background: #fbbf24;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: 500;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.no-notifications {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.no-notifications i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  opacity: 0.5;
}

.no-notifications p {
  margin: 0;
  font-size: 0.9rem;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.notification-item:hover {
  background-color: #f8fafc;
}

.notification-item.unread {
  background-color: #fef3c7;
  border-left: 4px solid #fbbf24;
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  color: #fbbf24;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 1rem;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: #1f2937;
}

.notification-message {
  font-size: 0.8rem;
  color: #6b7280;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.notification-time {
  font-size: 0.75rem;
  color: #9ca3af;
}

.notification-footer {
  padding: 1rem 1.5rem;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  text-align: center;
}

.notification-footer a {
  color: #1e3a8a;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: color 0.2s ease;
}

.notification-footer a:hover {
  color: #fbbf24;
}

/* User Dropdown */
.user-dropdown {
  position: relative;
}

.user-dropdown.active .dropdown-arrow {
  transform: rotate(180deg);
}

.user-btn {
  background: none;
  border: none;
  color: white;
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1.25rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  background: rgba(255, 255, 255, 0.1);
}

.user-btn:hover {
  background-color: rgba(251, 191, 36, 0.2);
  border-color: #fbbf24;
  transform: translateY(-2px);
}

.user-avatar i {
  font-size: 2rem;
  color: #fbbf24;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.user-name {
  font-weight: 700;
  font-size: 0.95rem;
  line-height: 1.2;
  color: #fbbf24;
}

.user-role {
  font-size: 0.75rem;
  opacity: 0.9;
}

.dropdown-arrow {
  font-size: 0.8rem;
  transition: transform 0.2s ease;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  padding: 0.5rem 0;
  min-width: 220px;
  z-index: 1002;
  margin-top: 0.5rem;
  border: 2px solid #f1f5f9;
  overflow: hidden;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: #374151;
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.dropdown-item:hover {
  background-color: #f8fafc;
  color: #1e3a8a;
  text-decoration: none;
}

.dropdown-item i {
  margin-right: 0.75rem;
  width: 16px;
  color: #6b7280;
}

.dropdown-item:hover i {
  color: #fbbf24;
}

.dropdown-divider {
  height: 1px;
  background-color: #e5e7eb;
  margin: 0.5rem 0;
}

/* Enhanced Mobile-First Responsive Design */

/* Tablet and larger screens */
@media (min-width: 769px) {
  .dashboard-header {
    left: 280px;
  }

  .dashboard-header.sidebar-collapsed {
    left: 70px;
  }
}

/* Mobile and tablet portrait */
@media (max-width: 768px) {
  .dashboard-header {
    padding: 0 1rem;
    height: 60px;
    left: 0;
    right: 0;
    width: 100%;
    position: fixed;
    z-index: 1001;
    box-shadow: 0 2px 20px rgba(30, 58, 138, 0.2);
  }

  .dashboard-header.sidebar-collapsed {
    left: 0;
  }

  .header-content {
    max-width: 100%;
    overflow: hidden;
  }

  .header-left {
    gap: 0.75rem;
    flex: 1;
    min-width: 0;
  }

  .page-title {
    flex: 1;
    min-width: 0;
  }

  .page-title h1 {
    font-size: clamp(1rem, 4vw, 1.2rem);
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0;
  }

  /* Enhanced touch targets */
  .sidebar-toggle {
    padding: 0.625rem;
    font-size: 1.2rem;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    transition: all 0.2s ease;
  }

  .sidebar-toggle:active {
    transform: scale(0.95);
    background-color: rgba(251, 191, 36, 0.3);
  }

  /* Hide user info on mobile */
  .user-info {
    display: none;
  }

  .header-actions {
    gap: 0.5rem;
    flex-shrink: 0;
  }

  /* Enhanced notification and user buttons */
  .notification-btn,
  .user-btn {
    padding: 0.625rem;
    font-size: 1.1rem;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    transition: all 0.2s ease;
  }

  .notification-btn:active,
  .user-btn:active {
    transform: scale(0.95);
    background-color: rgba(251, 191, 36, 0.3);
  }

  /* Enhanced notification badge */
  .notification-badge {
    top: 0.2rem;
    right: 0.2rem;
    font-size: 0.65rem;
    padding: 0.15rem 0.35rem;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Improved notification menu positioning */
  .notification-menu {
    min-width: min(320px, calc(100vw - 2rem));
    max-width: calc(100vw - 2rem);
    right: 0;
    max-height: 70vh;
    border-radius: 16px;
    margin-top: 0.75rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  }

  .notification-header {
    padding: 1rem 1.25rem;
    border-radius: 16px 16px 0 0;
  }

  .notification-list {
    max-height: calc(70vh - 120px);
  }

  .notification-item {
    padding: 0.875rem 1.25rem;
    border-radius: 0;
  }

  .notification-item:first-child {
    border-radius: 0;
  }

  .notification-item:last-child {
    border-radius: 0 0 16px 16px;
  }

  /* Improved dropdown menu */
  .dropdown-menu {
    min-width: 180px;
    right: 0;
    border-radius: 16px;
    margin-top: 0.75rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  }

  .dropdown-item {
    padding: 0.875rem 1.25rem;
    font-size: 0.95rem;
    border-radius: 0;
  }

  .dropdown-item:first-child {
    border-radius: 16px 16px 0 0;
  }

  .dropdown-item:last-child {
    border-radius: 0 0 16px 16px;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .dashboard-header {
    padding: 0 0.75rem;
    height: 56px;
  }

  .header-left {
    gap: 0.5rem;
  }

  .page-title h1 {
    font-size: clamp(0.9rem, 4.5vw, 1.1rem);
  }

  .sidebar-toggle {
    padding: 0.5rem;
    font-size: 1.1rem;
    min-width: 40px;
    min-height: 40px;
  }

  .notification-btn,
  .user-btn {
    padding: 0.5rem;
    font-size: 1rem;
    min-width: 40px;
    min-height: 40px;
  }

  .header-actions {
    gap: 0.375rem;
  }

  /* Full-width notification menu on small screens */
  .notification-menu {
    min-width: calc(100vw - 1.5rem);
    max-width: calc(100vw - 1.5rem);
    right: -0.75rem;
    left: 0.75rem;
    max-height: 60vh;
    margin-top: 0.5rem;
  }

  .notification-header {
    padding: 0.875rem 1rem;
  }

  .notification-header h6 {
    font-size: 0.95rem;
  }

  .notification-list {
    max-height: calc(60vh - 100px);
  }

  .notification-item {
    padding: 0.75rem 1rem;
    gap: 0.75rem;
  }

  .notification-icon {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }

  .notification-title {
    font-size: 0.85rem;
  }

  .notification-message {
    font-size: 0.75rem;
  }

  .notification-time {
    font-size: 0.7rem;
  }

  /* Compact dropdown menu */
  .dropdown-menu {
    min-width: 160px;
    right: -0.5rem;
  }

  .dropdown-item {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .dropdown-item i {
    width: 14px;
    margin-right: 0.5rem;
  }
}

/* Extra small devices */
@media (max-width: 360px) {
  .dashboard-header {
    padding: 0 0.5rem;
    height: 54px;
  }

  .header-left {
    gap: 0.375rem;
  }

  .page-title h1 {
    font-size: clamp(0.85rem, 5vw, 1rem);
  }

  .sidebar-toggle,
  .notification-btn,
  .user-btn {
    padding: 0.4rem;
    min-width: 36px;
    min-height: 36px;
    font-size: 0.95rem;
  }

  .header-actions {
    gap: 0.25rem;
  }

  .notification-badge {
    font-size: 0.6rem;
    padding: 0.1rem 0.3rem;
    min-width: 14px;
    height: 14px;
  }

  .notification-menu {
    right: -0.5rem;
    left: 0.5rem;
  }

  .dropdown-menu {
    min-width: 140px;
    right: -0.25rem;
  }
}

/* Landscape orientation optimizations */
@media (max-height: 500px) and (orientation: landscape) {
  .dashboard-header {
    height: 50px;
    padding: 0 1rem;
  }

  .sidebar-toggle,
  .notification-btn,
  .user-btn {
    padding: 0.4rem;
    min-width: 36px;
    min-height: 36px;
  }

  .page-title h1 {
    font-size: 0.95rem;
  }

  .notification-menu {
    max-height: 50vh;
  }

  .notification-list {
    max-height: calc(50vh - 80px);
  }
}

/* Scrollbar for notification list */
.notification-list::-webkit-scrollbar {
  width: 4px;
}

.notification-list::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.notification-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.notification-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
