/* Enhanced Mobile-First Client Login Styles */
.client-login {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Enhanced card styles */
.card {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: none;
  width: 100%;
  max-width: 400px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.card-header {
  border-radius: 20px 20px 0 0 !important;
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  color: white;
  padding: 2rem 1.5rem 1.5rem;
  border: none;
  text-align: center;
}

.card-header h4 {
  margin: 0;
  font-size: clamp(1.25rem, 4vw, 1.5rem);
  font-weight: 600;
}

.card-body {
  padding: 2rem 1.5rem !important;
}

/* Enhanced Input Group Styles */
.input-group {
  margin-bottom: 1.5rem;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.input-group:focus-within {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 123, 255, 0.15);
}

.input-group-text {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #e9ecef;
  border-right: none;
  color: #6c757d;
  padding: 1rem;
  min-width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.input-group .form-control {
  border: 2px solid #e9ecef;
  border-left: none;
  padding: 1rem 1.25rem;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
}

.input-group .form-control:focus {
  border-color: #007bff;
  box-shadow: none;
  background: #f8faff;
}

.input-group .form-control:focus + .input-group-text,
.input-group:focus-within .input-group-text {
  border-color: #007bff;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #007bff;
}

/* Enhanced Button Styles */
.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  border-radius: 12px;
  padding: 1rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 1rem;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(0, 123, 255, 0.3);
}

.btn-outline-secondary {
  border-radius: 0 12px 12px 0;
  border-left: none;
  padding: 1rem;
  transition: all 0.3s ease;
}

/* Enhanced Form Styles */
.form-control {
  border-radius: 12px;
  padding: 1rem 1.25rem;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
  font-size: 1rem;
  background: white;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.15);
  background: #f8faff;
  transform: translateY(-1px);
}

.form-control::placeholder {
  color: #adb5bd;
  font-size: 0.95rem;
}

.form-check {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

.form-check-input {
  width: 1.25rem;
  height: 1.25rem;
  margin-top: 0.125rem;
  border-radius: 6px;
  border: 2px solid #ced4da;
  transition: all 0.3s ease;
}

.form-check-input:checked {
  background-color: #007bff;
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.form-check-label {
  font-size: 0.95rem;
  color: #495057;
  margin-left: 0.5rem;
}

/* Enhanced Alert Styles */
.alert {
  border-radius: 12px;
  border: none;
  padding: 1rem 1.25rem;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.alert-danger {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
  border-left: 4px solid #dc3545;
}

.alert-success {
  background: linear-gradient(135deg, #d1edff 0%, #b8daff 100%);
  color: #0c5460;
  border-left: 4px solid #28a745;
}

/* Enhanced Modal Styles */
.modal-content {
  border-radius: 20px;
  border: none;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

.modal-header {
  border-bottom: 2px solid #e9ecef;
  border-radius: 20px 20px 0 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1.5rem 2rem;
}

.modal-body {
  padding: 2rem;
}

/* Enhanced Link Styles */
a {
  color: #007bff;
  transition: all 0.3s ease;
  text-decoration: none;
  font-weight: 500;
}

a:hover {
  color: #0056b3;
  text-decoration: underline;
  text-underline-offset: 3px;
}

/* Loading spinner */
.spinner-border-sm {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.5rem;
}

/* Enhanced Animation */
.card {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Status info card */
.border-warning {
  border-color: #ffc107 !important;
  border-width: 2px !important;
  border-radius: 12px !important;
}

/* Password visibility toggle */
.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.5rem;
  z-index: 10;
  transition: color 0.3s ease;
}

.password-toggle:hover {
  color: #007bff;
}

/* Enhanced Mobile Responsive Design */

/* Tablet and larger screens */
@media (min-width: 769px) {
  .client-login {
    padding: 2rem;
  }

  .card {
    max-width: 450px;
  }

  .card-body {
    padding: 2.5rem 2rem !important;
  }
}

/* Mobile and tablet portrait */
@media (max-width: 768px) {
  .client-login {
    padding: 1rem;
    min-height: 100vh;
    align-items: flex-start;
    padding-top: 2rem;
  }

  .card {
    max-width: 100%;
    width: 100%;
    margin: 0;
  }

  .card-header {
    padding: 1.5rem 1.25rem 1.25rem;
  }

  .card-header h4 {
    font-size: clamp(1.1rem, 5vw, 1.3rem);
  }

  .card-body {
    padding: 1.5rem 1.25rem !important;
  }

  /* Enhanced touch targets */
  .input-group {
    margin-bottom: 1.25rem;
  }

  .input-group-text {
    padding: 1rem 0.875rem;
    min-width: 48px;
    font-size: 1rem;
  }

  .input-group .form-control {
    padding: 1rem 1.125rem;
    font-size: 1rem;
  }

  .form-control {
    padding: 1rem 1.125rem;
    font-size: 1rem;
    min-height: 48px;
  }

  .btn-primary {
    padding: 1rem 1.5rem;
    font-size: 1rem;
    min-height: 48px;
  }

  .form-check-input {
    width: 1.125rem;
    height: 1.125rem;
  }

  .form-check-label {
    font-size: 0.9rem;
  }

  /* Modal adjustments */
  .modal-content {
    margin: 1rem;
    border-radius: 16px;
  }

  .modal-header {
    padding: 1.25rem 1.5rem;
    border-radius: 16px 16px 0 0;
  }

  .modal-body {
    padding: 1.5rem;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .client-login {
    padding: 0.75rem;
    padding-top: 1.5rem;
  }

  .card-header {
    padding: 1.25rem 1rem 1rem;
  }

  .card-header h4 {
    font-size: clamp(1rem, 5.5vw, 1.2rem);
  }

  .card-body {
    padding: 1.25rem 1rem !important;
  }

  .input-group {
    margin-bottom: 1rem;
    border-radius: 10px;
  }

  .input-group-text {
    padding: 0.875rem 0.75rem;
    min-width: 44px;
    font-size: 0.95rem;
  }

  .input-group .form-control,
  .form-control {
    padding: 0.875rem 1rem;
    font-size: 0.95rem;
    min-height: 44px;
  }

  .btn-primary {
    padding: 0.875rem 1.25rem;
    font-size: 0.95rem;
    min-height: 44px;
    border-radius: 10px;
  }

  .form-check {
    margin: 1.25rem 0;
    padding-left: 1.75rem;
  }

  .form-check-input {
    width: 1rem;
    height: 1rem;
  }

  .form-check-label {
    font-size: 0.85rem;
  }

  .alert {
    padding: 0.875rem 1rem;
    font-size: 0.9rem;
    border-radius: 10px;
  }

  /* Modal adjustments for small screens */
  .modal-content {
    margin: 0.5rem;
    border-radius: 12px;
  }

  .modal-header {
    padding: 1rem 1.25rem;
    border-radius: 12px 12px 0 0;
  }

  .modal-body {
    padding: 1.25rem;
  }
}

/* Extra small devices */
@media (max-width: 360px) {
  .client-login {
    padding: 0.5rem;
    padding-top: 1rem;
  }

  .card {
    border-radius: 16px;
  }

  .card-header {
    padding: 1rem 0.875rem 0.875rem;
    border-radius: 16px 16px 0 0;
  }

  .card-header h4 {
    font-size: clamp(0.95rem, 6vw, 1.1rem);
  }

  .card-body {
    padding: 1rem 0.875rem !important;
  }

  .input-group,
  .form-control,
  .btn-primary,
  .alert {
    border-radius: 8px;
  }

  .input-group-text {
    padding: 0.75rem 0.625rem;
    min-width: 40px;
    font-size: 0.9rem;
  }

  .input-group .form-control,
  .form-control {
    padding: 0.75rem 0.875rem;
    font-size: 0.9rem;
    min-height: 40px;
  }

  .btn-primary {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    min-height: 40px;
  }

  .form-check {
    margin: 1rem 0;
    padding-left: 1.5rem;
  }

  .form-check-input {
    width: 0.875rem;
    height: 0.875rem;
  }

  .form-check-label {
    font-size: 0.8rem;
  }

  .alert {
    padding: 0.75rem 0.875rem;
    font-size: 0.85rem;
  }
}

/* Landscape orientation optimizations */
@media (max-height: 500px) and (orientation: landscape) {
  .client-login {
    padding: 0.5rem;
    align-items: center;
    padding-top: 0.5rem;
  }

  .card {
    max-width: 400px;
  }

  .card-header {
    padding: 1rem 1.25rem 0.75rem;
  }

  .card-body {
    padding: 1rem 1.25rem !important;
  }

  .input-group {
    margin-bottom: 0.875rem;
  }

  .form-check {
    margin: 1rem 0;
  }

  .btn-primary {
    margin-top: 0.75rem;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .card {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  .btn-primary {
    box-shadow: 0 3px 12px rgba(0, 123, 255, 0.35);
  }

  .input-group:focus-within {
    box-shadow: 0 3px 15px rgba(0, 123, 255, 0.18);
  }
}
