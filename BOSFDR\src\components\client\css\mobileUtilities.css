/* Mobile-Specific Utilities and Performance Optimizations */

/* ===== MOBILE PERFORMANCE OPTIMIZATIONS ===== */

/* Hardware acceleration for smooth animations */
.gpu-accelerated,
.sidebar-toggle,
.nav-link,
.action-card,
.stat-card,
.btn,
.modal {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize scrolling performance */
.scrollable {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===== TOUCH-FRIENDLY INTERACTIONS ===== */

/* Enhanced touch targets */
.touch-target {
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: manipulation;
}

/* Touch feedback animations */
.touch-feedback {
  position: relative;
  overflow: hidden;
}

.touch-feedback::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
  pointer-events: none;
}

.touch-feedback:active::before {
  width: 200px;
  height: 200px;
}

/* Prevent text selection on touch */
.no-select {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* ===== MOBILE LAYOUT UTILITIES ===== */

/* Flexible containers */
.mobile-container {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  padding: 0 1rem;
}

.mobile-full-width {
  width: 100% !important;
  max-width: 100% !important;
}

.mobile-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Mobile spacing utilities */
.mobile-p-1 { padding: 0.25rem !important; }
.mobile-p-2 { padding: 0.5rem !important; }
.mobile-p-3 { padding: 1rem !important; }
.mobile-p-4 { padding: 1.5rem !important; }

.mobile-m-1 { margin: 0.25rem !important; }
.mobile-m-2 { margin: 0.5rem !important; }
.mobile-m-3 { margin: 1rem !important; }
.mobile-m-4 { margin: 1.5rem !important; }

/* Mobile text utilities */
.mobile-text-xs { font-size: 0.75rem !important; }
.mobile-text-sm { font-size: 0.875rem !important; }
.mobile-text-base { font-size: 1rem !important; }
.mobile-text-lg { font-size: 1.125rem !important; }

/* ===== MOBILE ANIMATIONS ===== */

/* Slide animations optimized for mobile */
@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Bounce animation for touch feedback */
@keyframes mobileBounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Pulse animation for notifications */
@keyframes mobilePulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Shake animation for errors */
@keyframes mobileShake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* Animation classes */
.animate-slide-in-left {
  animation: slideInFromLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in-right {
  animation: slideInFromRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in-bottom {
  animation: slideInFromBottom 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-bounce {
  animation: mobileBounce 0.6s ease;
}

.animate-pulse {
  animation: mobilePulse 2s infinite;
}

.animate-shake {
  animation: mobileShake 0.5s ease-in-out;
}

/* ===== MOBILE LOADING STATES ===== */

/* Mobile-optimized loading spinner */
.mobile-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid rgba(0, 123, 255, 0.2);
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Skeleton loading for mobile */
.mobile-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: mobileSkeletonLoading 1.5s infinite;
  border-radius: 8px;
}

@keyframes mobileSkeletonLoading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* ===== MOBILE FORM ENHANCEMENTS ===== */

/* Enhanced mobile input styling */
.mobile-input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 12px;
  border: 2px solid #e9ecef;
  padding: 1rem;
  font-size: 16px; /* Prevents zoom on iOS */
  transition: all 0.3s ease;
  background: white;
}

.mobile-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
  transform: translateY(-1px);
}

/* Mobile button enhancements */
.mobile-btn {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: none;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  min-height: 48px;
  touch-action: manipulation;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.mobile-btn:active {
  transform: scale(0.98);
}

/* ===== MOBILE ACCESSIBILITY ===== */

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card,
  .btn,
  .form-control {
    border-width: 2px !important;
  }
  
  .text-muted {
    color: #000 !important;
  }
}

/* Focus indicators for mobile */
.mobile-focus:focus {
  outline: 3px solid #007bff;
  outline-offset: 2px;
}

/* ===== MOBILE SAFE AREAS ===== */

/* Support for devices with notches */
@supports (padding: max(0px)) {
  .mobile-safe-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  .mobile-safe-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
  
  .mobile-safe-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }
  
  .mobile-safe-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}

/* ===== MOBILE BREAKPOINT UTILITIES ===== */

/* Show/hide utilities for different screen sizes */
@media (max-width: 480px) {
  .hide-xs { display: none !important; }
  .show-xs { display: block !important; }
}

@media (max-width: 768px) {
  .hide-sm { display: none !important; }
  .show-sm { display: block !important; }
}

@media (max-width: 992px) {
  .hide-md { display: none !important; }
  .show-md { display: block !important; }
}
