import clientAuthService from '@/services/clientAuthService';
import { validators, formatApiErrors, clearFieldError } from '@/utils/validation';

export default {
  name: 'ClientRegistration',
  data() {
    return {
      currentStep: 1,
      loading: false,
      resendLoading: false,
      resendCooldown: 0,
      showPassword: false,
      errorMessage: '',
      successMessage: '',
      accountId: null,
      
      // Account form data
      accountForm: {
        username: '',
        email: '',
        password: '',
        confirmPassword: ''
      },
      
      // Profile form data
      profileForm: {
        first_name: '',
        middle_name: '',
        last_name: '',
        suffix: '',
        birth_date: '',
        gender: '',
        civil_status_id: '',
        nationality: 'Filipino',
        phone_number: '',
        email: '',
        house_number: '',
        street: '',
        subdivision: '',
        barangay: '',
        city_municipality: '',
        province: '',
        postal_code: '',
        years_of_residency: null,
        months_of_residency: null
      },
      
      // Verification form data
      verificationForm: {
        otp: ''
      },
      
      // Form errors
      errors: {}
    };
  },
  
  methods: {
    // Validate individual field
    validateField(fieldName) {
      let error = null;
      const value = this.getFieldValue(fieldName);
      
      switch (fieldName) {
        case 'username':
          error = validators.required(value, 'Username') || validators.username(value);
          break;
        case 'email':
          error = validators.required(value, 'Email') || validators.email(value);
          break;
        case 'password':
          error = validators.required(value, 'Password') || validators.password(value);
          break;
        case 'confirmPassword':
          error = validators.required(value, 'Confirm Password') || 
                  validators.confirmPassword(value, this.accountForm.password);
          break;
        case 'first_name':
          error = validators.required(value, 'First Name') || validators.name(value, 'First Name');
          break;
        case 'last_name':
          error = validators.required(value, 'Last Name') || validators.name(value, 'Last Name');
          break;
        case 'birth_date':
          error = validators.required(value, 'Birth Date') || validators.birthDate(value);
          break;
        case 'gender':
          error = validators.required(value, 'Gender');
          break;
        case 'civil_status_id':
          error = validators.required(value, 'Civil Status');
          break;
        case 'nationality':
          error = validators.required(value, 'Nationality') || validators.name(value, 'Nationality');
          break;
        case 'phone_number':
          error = validators.required(value, 'Phone Number') || validators.phoneNumber(value);
          break;
        case 'barangay':
          error = validators.required(value, 'Barangay') || validators.address(value, 'Barangay');
          break;
        case 'city_municipality':
          error = validators.required(value, 'City/Municipality') || 
                  validators.address(value, 'City/Municipality');
          break;
        case 'province':
          error = validators.required(value, 'Province') || validators.address(value, 'Province');
          break;
        case 'otp':
          error = validators.required(value, 'Verification Code') || validators.otp(value);
          break;
      }
      
      if (error) {
        this.errors = { ...this.errors, [fieldName]: error };
      } else {
        this.clearFieldError(fieldName);
      }
      
      return !error;
    },
    
    // Get field value from appropriate form
    getFieldValue(fieldName) {
      if (Object.prototype.hasOwnProperty.call(this.accountForm, fieldName)) {
        return this.accountForm[fieldName];
      } else if (Object.prototype.hasOwnProperty.call(this.profileForm, fieldName)) {
        return this.profileForm[fieldName];
      } else if (Object.prototype.hasOwnProperty.call(this.verificationForm, fieldName)) {
        return this.verificationForm[fieldName];
      }
      return '';
    },
    
    // Clear field error
    clearFieldError(fieldName) {
      this.errors = clearFieldError(this.errors, fieldName);
    },
    
    // Clear all messages
    clearMessages() {
      this.errorMessage = '';
      this.successMessage = '';
    },
    
    // Submit account form (Step 1)
    async submitAccountForm() {
      this.clearMessages();
      
      // Validate all account fields
      const fieldsToValidate = ['username', 'email', 'password', 'confirmPassword'];
      let isValid = true;
      
      fieldsToValidate.forEach(field => {
        if (!this.validateField(field)) {
          isValid = false;
        }
      });
      
      if (!isValid) {
        this.errorMessage = 'Please fix the errors above';
        return;
      }
      
      this.loading = true;
      
      try {
        const response = await clientAuthService.registerAccount({
          username: this.accountForm.username,
          email: this.accountForm.email,
          password: this.accountForm.password,
          confirmPassword: this.accountForm.confirmPassword
        });
        
        if (response.success) {
          this.accountId = response.data.accountId;
          this.profileForm.email = this.accountForm.email;
          this.successMessage = response.message;
          this.currentStep = 2;
        }
      } catch (error) {
        const errorData = clientAuthService.parseError(error);
        this.errorMessage = errorData.message;
        
        if (errorData.errors && errorData.errors.length > 0) {
          this.errors = { ...this.errors, ...formatApiErrors(errorData.errors) };
        }
      } finally {
        this.loading = false;
      }
    },
    
    // Submit profile form (Step 2)
    async submitProfileForm() {
      this.clearMessages();
      
      // Validate all required profile fields
      const fieldsToValidate = [
        'first_name', 'last_name', 'birth_date', 'gender',
        'civil_status_id', 'nationality', 'phone_number', 'barangay',
        'city_municipality', 'province'
      ];
      
      let isValid = true;
      
      fieldsToValidate.forEach(field => {
        if (!this.validateField(field)) {
          isValid = false;
        }
      });
      
      if (!isValid) {
        this.errorMessage = 'Please fix the errors above';
        return;
      }
      
      this.loading = true;
      
      try {
        const response = await clientAuthService.completeRegistration(this.accountId, {
          ...this.profileForm,
          civil_status_id: parseInt(this.profileForm.civil_status_id)
        });
        
        if (response.success) {
          this.successMessage = response.message;
          this.currentStep = 3;
          
          // Start resend cooldown
          this.startResendCooldown();
        }
      } catch (error) {
        const errorData = clientAuthService.parseError(error);
        this.errorMessage = errorData.message;
        
        if (errorData.errors && errorData.errors.length > 0) {
          this.errors = { ...this.errors, ...formatApiErrors(errorData.errors) };
        }
      } finally {
        this.loading = false;
      }
    },
    
    // Submit verification form (Step 3)
    async submitVerificationForm() {
      this.clearMessages();
      
      if (!this.validateField('otp')) {
        this.errorMessage = 'Please enter a valid verification code';
        return;
      }
      
      this.loading = true;
      
      try {
        const response = await clientAuthService.verifyEmail(
          this.accountForm.email,
          this.verificationForm.otp
        );
        
        if (response.success) {
          this.successMessage = response.message;
          this.currentStep = 4;
        }
      } catch (error) {
        const errorData = clientAuthService.parseError(error);
        this.errorMessage = errorData.message;
      } finally {
        this.loading = false;
      }
    },
    
    // Resend verification code
    async resendVerificationCode() {
      if (this.resendCooldown > 0) return;
      
      this.resendLoading = true;
      this.clearMessages();
      
      try {
        const response = await clientAuthService.resendVerificationEmail(this.accountForm.email);
        
        if (response.success) {
          this.successMessage = 'Verification code sent successfully';
          this.startResendCooldown();
        }
      } catch (error) {
        const errorData = clientAuthService.parseError(error);
        this.errorMessage = errorData.message;
      } finally {
        this.resendLoading = false;
      }
    },
    
    // Start resend cooldown timer
    startResendCooldown() {
      this.resendCooldown = 60;
      const timer = setInterval(() => {
        this.resendCooldown--;
        if (this.resendCooldown <= 0) {
          clearInterval(timer);
        }
      }, 1000);
    },
    
    // Go to previous step
    goToPreviousStep() {
      if (this.currentStep > 1) {
        this.currentStep--;
        this.clearMessages();
      }
    },
    
    // Go to login page
    goToLogin() {
      this.$router.push('/client/login');
    }
  }
};
