/* General Styles */
body {
  margin: 0;
  font-family: Arial, sans-serif;
  background-color: #e4e7eb;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  flex-direction: column;
}

/* Logo */
.logo {
  margin-bottom: 10px;
}

/* System Title */
.system-title {
  font-size: 12px;
  margin-bottom: 20px;
  text-align: center;
}

/* Login Container */
.login-container {
  background-color: #2e2e2e;
  padding: 30px;
  border: 1px solid #f28500;
  border-radius: 8px;
  width: 300px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
}

/* Header */
.login-container h2 {
  text-align: center;
  margin-bottom: 20px;
  font-size: 16px;
}

/* Input Groups */
.input-group {
  margin-bottom: 15px;
}

/* Input Field with Icon */
.input-icon {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.input-icon i {
  padding: 10px;
  background-color: #ddd;
  font-style: normal;
}

.input-icon input {
  border: none;
  padding: 10px;
  width: 100%;
  font-size: 14px;
  outline: none;
}

/* Login Button */
.login-btn {
  background-color: #f28500;
  border: none;
  color: white;
  padding: 10px;
  width: 100%;
  font-size: 14px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

/* Links */
.links {
  margin-top: 10px;
  text-align: center;
  font-size: 12px;
}

.links a {
  color: #a8c7ff;
  text-decoration: none;
  margin: 0 5px;
}

.links a:hover {
  text-decoration: underline;
}
