// src/main.js
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'

// Import Tailwind CSS
import './assets/css/tailwind.css'

// Import Bootstrap’s CSS
import 'bootstrap/dist/css/bootstrap.min.css'

// Import Bootstrap's JS
import 'bootstrap/dist/js/bootstrap.bundle.min.js'

// Import Font-Awesome’s JS
import '@fortawesome/fontawesome-free/js/all.js'

// Import and initialize auth services
import clientAuthService from './services/clientAuthService'
import adminAuthService from './services/adminAuthService'

// Initialize authentication services with error handling
try {
  if (clientAuthService && typeof clientAuthService.initializeAuth === 'function') {
    clientAuthService.initializeAuth()
  }
  if (adminAuthService && typeof adminAuthService.initializeAuth === 'function') {
    adminAuthService.initializeAuth()
  }
} catch (error) {
  console.warn('Auth service initialization failed:', error)
}

const app = createApp(App)

// Add global error handler
app.config.errorHandler = (err, instance, info) => {
  console.error('Global error:', err)
  console.error('Component instance:', instance)
  console.error('Error info:', info)
}

// Add global warning handler
app.config.warnHandler = (msg, instance, trace) => {
  console.warn('Global warning:', msg)
  console.warn('Component instance:', instance)
  console.warn('Trace:', trace)
}

// Make auth services available globally
app.config.globalProperties.$clientAuth = clientAuthService
app.config.globalProperties.$adminAuth = adminAuthService

app.use(router)
app.mount('#app')

console.log('Vue app mounted successfully')