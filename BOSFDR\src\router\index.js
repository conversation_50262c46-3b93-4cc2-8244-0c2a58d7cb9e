import { createRouter, createWebHistory } from 'vue-router';
import clientAuthService from '@/services/clientAuthService';
import adminAuthService from '@/services/adminAuthService';

// Import components with error handling
const AdminLogin = () => import('@/components/admin/AdminLogin.vue');
const AdminRegistration = () => import('@/components/admin/AdminRegistration.vue');
const AdminDashboard = () => import('@/components/admin/AdminDashboard.vue');
const AdminTest = () => import('@/components/admin/AdminTest.vue');
const ClientLogin = () => import('@/components/client/ClientLogin.vue');
const ClientRegistration = () => import('@/components/client/ClientRegistration.vue');
const ClientDashboard = () => import('@/components/client/ClientDashboard.vue');

const routes = [
  // Default route - redirect to client login
  {
    path: '/',
    redirect: '/client/login'
  },
  
  // Client routes
  {
    path: '/client/login',
    name: 'ClientLogin',
    component: ClientLogin,
    meta: { 
      title: 'Client Login',
      requiresGuest: true // Only accessible when not logged in
    }
  },
  {
    path: '/client/register',
    name: 'ClientRegistration',
    component: ClientRegistration,
    meta: { 
      title: 'Client Registration',
      requiresGuest: true
    }
  },
  {
    path: '/client/dashboard',
    name: 'ClientDashboard',
    component: ClientDashboard,
    meta: {
      title: 'Client Dashboard',
      requiresAuth: true // Only accessible when logged in
    }
  },
  {
    path: '/client/request/new',
    name: 'NewDocumentRequest',
    component: () => import('@/components/client/NewDocumentRequest.vue'),
    meta: {
      title: 'New Document Request',
      requiresAuth: true
    }
  },
  {
    path: '/client/request/barangay-clearance',
    name: 'BarangayClearanceRequest',
    component: () => import('@/components/client/BarangayClearanceRequest.vue'),
    meta: {
      title: 'Barangay Clearance Request',
      requiresAuth: true
    }
  },
  {
    path: '/client/request/cedula',
    name: 'CedulaRequest',
    component: () => import('@/components/client/CedulaRequest.vue'),
    meta: {
      title: 'Cedula Request',
      requiresAuth: true
    }
  },
  {
    path: '/client/requests',
    name: 'MyRequests',
    component: () => import('@/components/client/MyRequests.vue'),
    meta: {
      title: 'My Requests',
      requiresAuth: true
    }
  },
  {
    path: '/client/request/:id',
    name: 'RequestDetails',
    component: () => import('@/components/client/RequestDetails.vue'),
    meta: {
      title: 'Request Details',
      requiresAuth: true
    }
  },
  
  // Admin routes
  {
    path: '/admin/test',
    name: 'AdminTest',
    component: AdminTest,
    meta: {
      title: 'Admin Test'
    }
  },
  {
    path: '/admin/login',
    name: 'AdminLogin',
    component: AdminLogin,
    meta: {
      title: 'Admin Login',
      requiresAdminGuest: true // Only accessible when admin is not logged in
    }
  },
  {
    path: '/admin/register',
    name: 'AdminRegistration',
    component: AdminRegistration,
    meta: {
      title: 'Admin Registration',
      requiresAdminGuest: true
    }
  },
  {
    path: '/admin/dashboard',
    name: 'AdminDashboard',
    component: AdminDashboard,
    meta: {
      title: 'Admin Dashboard',
      requiresAdminAuth: true // Only accessible when admin is logged in
    }
  },
  {
    path: '/admin/users',
    name: 'AdminUsers',
    component: () => import('@/components/admin/AdminUsers.vue'),
    meta: {
      title: 'Manage Users',
      requiresAdminAuth: true
    }
  },
  {
    path: '/admin/requests',
    name: 'AdminRequests',
    component: () => import('@/components/admin/AdminRequests.vue'),
    meta: {
      title: 'View Requests',
      requiresAdminAuth: true
    }
  },
  {
    path: '/admin/reports',
    name: 'AdminReports',
    component: () => import('@/components/admin/AdminReports.vue'),
    meta: {
      title: 'Generate Reports',
      requiresAdminAuth: true
    }
  },
  {
    path: '/admin/settings',
    name: 'AdminSettings',
    component: () => import('@/components/admin/AdminSettings.vue'),
    meta: {
      title: 'System Settings',
      requiresAdminAuth: true,
      requiresRole: 'admin' // Only admin role can access settings
    }
  },
  {
    path: '/admin/profile',
    name: 'AdminProfile',
    component: () => import('@/components/admin/AdminProfile.vue'),
    meta: {
      title: 'Admin Profile',
      requiresAdminAuth: true
    }
  },
  {
    path: '/admin/activity-logs',
    name: 'AdminActivityLogs',
    component: () => import('@/components/admin/AdminActivityLogs.vue'),
    meta: {
      title: 'Activity Logs',
      requiresAdminAuth: true
    }
  },
  {
    path: '/admin/audit-logs',
    name: 'AdminAuditLogs',
    component: () => import('@/components/admin/AdminActivityLogs.vue'), // Reuse ActivityLogs for now
    meta: {
      title: 'Audit Logs',
      requiresAdminAuth: true
    }
  },
  
  // Catch-all route for 404
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/components/NotFound.vue'),
    meta: { 
      title: 'Page Not Found'
    }
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(_, __, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  }
});

// Navigation guards
router.beforeEach((to, _, next) => {
  try {
    console.log('Navigating to:', to.path, to.name);

    // Set page title
    document.title = to.meta.title ? `${to.meta.title} - Barangay Bula` : 'Barangay Bula';

    // Skip auth checks for public routes
    if (!to.meta.requiresAuth && !to.meta.requiresAdminAuth && !to.meta.requiresGuest && !to.meta.requiresAdminGuest) {
      console.log('Public route, skipping auth checks');
      next();
      return;
    }

    // Check authentication requirements with error handling
    let isClientLoggedIn = false;
    let isAdminLoggedIn = false;

    try {
      isClientLoggedIn = clientAuthService && typeof clientAuthService.isLoggedIn === 'function'
        ? clientAuthService.isLoggedIn()
        : false;
    } catch (error) {
      console.warn('Client auth check failed:', error);
      isClientLoggedIn = false;
    }

    try {
      isAdminLoggedIn = adminAuthService && typeof adminAuthService.isAuthenticated === 'function'
        ? adminAuthService.isAuthenticated() // Use enhanced authentication check
        : false;
    } catch (error) {
      console.warn('Admin auth check failed:', error);
      isAdminLoggedIn = false;
    }

    console.log('Auth status - Client:', isClientLoggedIn, 'Admin:', isAdminLoggedIn);

    // Admin authentication checks
    if (to.meta.requiresAdminAuth && !isAdminLoggedIn) {
      console.log('Admin auth required but not logged in, redirecting to admin login');
      next('/admin/login');
      return;
    }

    // Admin role-based authorization checks
    if (to.meta.requiresRole && isAdminLoggedIn) {
      const hasRequiredRole = adminAuthService.hasRole(to.meta.requiresRole);
      if (!hasRequiredRole) {
        console.log(`Admin role '${to.meta.requiresRole}' required but not authorized, redirecting to dashboard`);
        next('/admin/dashboard');
        return;
      }
    }

    if (to.meta.requiresAdminGuest && isAdminLoggedIn) {
      console.log('Admin guest route but admin is logged in, redirecting to dashboard');
      next('/admin/dashboard');
      return;
    }

    // Client authentication checks
    if (to.meta.requiresAuth && !isClientLoggedIn) {
      console.log('Client auth required but not logged in, redirecting to client login');
      next('/client/login');
      return;
    }

    if (to.meta.requiresGuest && isClientLoggedIn) {
      console.log('Client guest route but client is logged in, redirecting to dashboard');
      next('/client/dashboard');
      return;
    }

    console.log('Navigation successful to:', to.path);
    next();
  } catch (error) {
    console.error('Navigation guard error:', error);
    // Continue navigation even if there's an error to prevent infinite loops
    next();
  }
});

export default router;
