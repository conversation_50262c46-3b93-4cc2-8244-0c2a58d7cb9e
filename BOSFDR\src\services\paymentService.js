import api from './api';

/**
 * Payment Service for PayMongo Integration
 * Handles online payments and payment verification
 */
class PaymentService {
  
  /**
   * Get PayMongo configuration for frontend
   * @returns {Promise<Object>} PayMongo configuration
   */
  async getPaymentConfig() {
    try {
      const response = await api.get('/payments/config');
      return response.data;
    } catch (error) {
      console.error('Failed to get payment config:', error);
      throw error;
    }
  }

  /**
   * Initiate payment process
   * @param {Object} paymentData - Payment information
   * @param {number} paymentData.request_id - Document request ID
   * @param {number} paymentData.payment_method_id - Payment method ID
   * @param {string} paymentData.customer_email - Customer email (optional)
   * @returns {Promise<Object>} Payment initiation response
   */
  async initiatePayment(paymentData) {
    try {
      const response = await api.post('/payments/initiate', paymentData);
      return response.data;
    } catch (error) {
      console.error('Failed to initiate payment:', error);
      throw error;
    }
  }

  /**
   * Get payment status
   * @param {string} transactionId - Transaction ID
   * @returns {Promise<Object>} Payment status
   */
  async getPaymentStatus(transactionId) {
    try {
      const response = await api.get(`/payments/status/${transactionId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get payment status:', error);
      throw error;
    }
  }

  /**
   * Get payment verification queue (Admin only)
   * @returns {Promise<Object>} Pending payment verifications
   */
  async getPaymentVerificationQueue() {
    try {
      const response = await api.get('/admin/documents/payment-verification-queue');
      return response.data;
    } catch (error) {
      console.error('Failed to get payment verification queue:', error);
      throw error;
    }
  }

  /**
   * Verify in-person payment (Admin only)
   * @param {number} requestId - Document request ID
   * @param {Object} paymentDetails - Payment verification details
   * @param {number} paymentDetails.amount_received - Amount received
   * @param {number} paymentDetails.payment_method_id - Payment method ID
   * @param {string} paymentDetails.receipt_number - Receipt number (optional)
   * @param {string} paymentDetails.notes - Verification notes (optional)
   * @returns {Promise<Object>} Verification response
   */
  async verifyInPersonPayment(requestId, paymentDetails) {
    try {
      const response = await api.post(`/admin/documents/requests/${requestId}/verify-payment`, paymentDetails);
      return response.data;
    } catch (error) {
      console.error('Failed to verify in-person payment:', error);
      throw error;
    }
  }

  /**
   * Handle PayMongo checkout redirect
   * @param {string} checkoutUrl - PayMongo checkout URL
   */
  redirectToPayMongo(checkoutUrl) {
    if (checkoutUrl) {
      window.location.href = checkoutUrl;
    } else {
      throw new Error('Invalid checkout URL');
    }
  }

  /**
   * Format amount for display
   * @param {number} amount - Amount in PHP
   * @returns {string} Formatted amount
   */
  formatAmount(amount) {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  }

  /**
   * Get payment method display name
   * @param {string} methodCode - Payment method code
   * @returns {string} Display name
   */
  getPaymentMethodDisplayName(methodCode) {
    const methodNames = {
      'CASH': 'Cash Payment',
      'PAYMONGO_CARD': 'Credit/Debit Card',
      'PAYMONGO_GCASH': 'GCash',
      'PAYMONGO_GRABPAY': 'GrabPay',
      'PAYMONGO_PAYMAYA': 'Maya (PayMaya)',
      'PAYMONGO_BANK': 'Bank Transfer'
    };
    return methodNames[methodCode] || methodCode;
  }

  /**
   * Check if payment method is online
   * @param {string} methodCode - Payment method code
   * @returns {boolean} True if online payment method
   */
  isOnlinePaymentMethod(methodCode) {
    return methodCode && methodCode.startsWith('PAYMONGO_');
  }

  /**
   * Get payment status display info
   * @param {string} status - Payment status
   * @returns {Object} Status display information
   */
  getPaymentStatusDisplay(status) {
    const statusInfo = {
      'pending': { text: 'Pending', color: 'warning', icon: 'clock' },
      'succeeded': { text: 'Paid', color: 'success', icon: 'check-circle' },
      'failed': { text: 'Failed', color: 'danger', icon: 'x-circle' },
      'cancelled': { text: 'Cancelled', color: 'secondary', icon: 'x' }
    };
    return statusInfo[status] || { text: status, color: 'secondary', icon: 'help-circle' };
  }

  /**
   * Calculate processing fee
   * @param {number} baseAmount - Base amount
   * @param {number} feePercentage - Fee percentage
   * @param {number} feeFixed - Fixed fee
   * @returns {number} Processing fee
   */
  calculateProcessingFee(baseAmount, feePercentage = 0, feeFixed = 0) {
    const percentageFee = (baseAmount * feePercentage) / 100;
    return percentageFee + feeFixed;
  }

  /**
   * Calculate total amount including fees
   * @param {number} baseAmount - Base amount
   * @param {number} feePercentage - Fee percentage
   * @param {number} feeFixed - Fixed fee
   * @returns {Object} Amount breakdown
   */
  calculateTotalAmount(baseAmount, feePercentage = 0, feeFixed = 0) {
    const processingFee = this.calculateProcessingFee(baseAmount, feePercentage, feeFixed);
    const totalAmount = baseAmount + processingFee;
    
    return {
      baseAmount,
      processingFee,
      totalAmount,
      formattedBase: this.formatAmount(baseAmount),
      formattedFee: this.formatAmount(processingFee),
      formattedTotal: this.formatAmount(totalAmount)
    };
  }

  /**
   * Validate payment data before submission
   * @param {Object} paymentData - Payment data to validate
   * @returns {Object} Validation result
   */
  validatePaymentData(paymentData) {
    const errors = [];

    if (!paymentData.request_id) {
      errors.push('Request ID is required');
    }

    if (!paymentData.payment_method_id) {
      errors.push('Payment method is required');
    }

    if (paymentData.customer_email && !this.isValidEmail(paymentData.customer_email)) {
      errors.push('Invalid email format');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate email format
   * @param {string} email - Email to validate
   * @returns {boolean} True if valid email
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Handle payment errors with user-friendly messages
   * @param {Error} error - Error object
   * @returns {string} User-friendly error message
   */
  getErrorMessage(error) {
    if (error.response?.data?.message) {
      return error.response.data.message;
    }
    
    if (error.response?.status === 400) {
      return 'Invalid payment information. Please check your details and try again.';
    }
    
    if (error.response?.status === 401) {
      return 'You need to be logged in to make a payment.';
    }
    
    if (error.response?.status === 404) {
      return 'Payment service not found. Please contact support.';
    }
    
    if (error.response?.status >= 500) {
      return 'Payment service is temporarily unavailable. Please try again later.';
    }
    
    return error.message || 'An unexpected error occurred during payment processing.';
  }
}

export default new PaymentService();
