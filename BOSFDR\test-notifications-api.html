<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Notifications API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 200px; }
    </style>
</head>
<body>
    <h1>🔔 Test Notifications API</h1>
    
    <div class="section">
        <h3>1. Login</h3>
        <input type="text" id="username" placeholder="Username" value="admin12345">
        <input type="password" id="password" placeholder="Password" value="12345QWERTqwert">
        <button onclick="login()">Login as Admin</button>
        <div id="login-log" class="log"></div>
    </div>

    <div class="section">
        <h3>2. Test API Endpoints</h3>
        <button onclick="testUnreadCount()">Test Unread Count</button>
        <button onclick="testGetNotifications()">Test Get Notifications</button>
        <button onclick="testSendTestNotification()">Send Test Notification</button>
        <div id="api-log" class="log"></div>
    </div>

    <div class="section">
        <h3>3. Notification Display</h3>
        <div id="notifications-display"></div>
    </div>

    <script>
        let adminToken = null;
        const API_BASE = 'http://localhost:7000/api';

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            element.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            element.scrollTop = element.scrollHeight;
        }

        async function login() {
            try {
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                log('login-log', 'Attempting login...', 'info');

                const response = await fetch(`${API_BASE}/admin/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    adminToken = data.data.token;
                    log('login-log', '✅ Login successful!', 'success');
                    log('login-log', `Token: ${adminToken.substring(0, 20)}...`, 'info');
                } else {
                    log('login-log', `❌ Login failed: ${data.message}`, 'error');
                }
            } catch (error) {
                log('login-log', `❌ Login error: ${error.message}`, 'error');
            }
        }

        async function testUnreadCount() {
            if (!adminToken) {
                log('api-log', '❌ Please login first', 'error');
                return;
            }

            try {
                log('api-log', 'Testing unread count endpoint...', 'info');

                const response = await fetch(`${API_BASE}/notifications/unread-count`, {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`
                    }
                });

                const data = await response.json();
                log('api-log', `✅ Unread count response: ${JSON.stringify(data, null, 2)}`, 'success');

                if (data.data && typeof data.data.count !== 'undefined') {
                    log('api-log', `📊 Unread count: ${data.data.count}`, 'info');
                }
            } catch (error) {
                log('api-log', `❌ Unread count error: ${error.message}`, 'error');
            }
        }

        async function testGetNotifications() {
            if (!adminToken) {
                log('api-log', '❌ Please login first', 'error');
                return;
            }

            try {
                log('api-log', 'Testing get notifications endpoint...', 'info');

                const response = await fetch(`${API_BASE}/notifications?page=1&limit=10`, {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`
                    }
                });

                const data = await response.json();
                log('api-log', `✅ Notifications response: ${JSON.stringify(data, null, 2)}`, 'success');

                if (data.data && data.data.notifications) {
                    log('api-log', `📊 Found ${data.data.notifications.length} notifications`, 'info');
                    displayNotifications(data.data.notifications);
                }
            } catch (error) {
                log('api-log', `❌ Get notifications error: ${error.message}`, 'error');
            }
        }

        async function testSendTestNotification() {
            if (!adminToken) {
                log('api-log', '❌ Please login first', 'error');
                return;
            }

            try {
                log('api-log', 'Sending test notification...', 'info');

                const response = await fetch(`${API_BASE}/notifications/test`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        title: 'Test Notification',
                        message: 'This is a test notification to debug the notification panel issue.',
                        type: 'test',
                        priority: 'normal'
                    })
                });

                const data = await response.json();
                log('api-log', `✅ Test notification response: ${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                log('api-log', `❌ Send test notification error: ${error.message}`, 'error');
            }
        }

        function displayNotifications(notifications) {
            const container = document.getElementById('notifications-display');
            container.innerHTML = '<h4>Notifications:</h4>';

            if (notifications.length === 0) {
                container.innerHTML += '<p>No notifications found</p>';
                return;
            }

            notifications.forEach((notif, index) => {
                const div = document.createElement('div');
                div.style.border = '1px solid #ddd';
                div.style.padding = '10px';
                div.style.margin = '5px 0';
                div.style.borderRadius = '5px';
                if (!notif.is_read) {
                    div.style.backgroundColor = '#f0f8ff';
                    div.style.borderLeft = '4px solid #007bff';
                }

                div.innerHTML = `
                    <strong>${notif.title || 'No Title'}</strong><br>
                    <span>${notif.message || 'No Message'}</span><br>
                    <small>Type: ${notif.type || 'Unknown'} | Read: ${notif.is_read ? 'Yes' : 'No'} | Created: ${notif.created_at || 'Unknown'}</small>
                `;
                container.appendChild(div);
            });
        }
    </script>
</body>
</html>
