-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 13, 2025 at 09:26 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `barangay_management_system`
--

DELIMITER $$
--
-- Functions
--
CREATE DEFINER=`root`@`localhost` FUNCTION `CalculateAge` (`birth_date` DATE) RETURNS INT(11) DETERMINISTIC READS SQL DATA BEGIN
    RETURN YEAR(CURDATE()) - YEAR(birth_date) - (DATE_FORMAT(CURDATE(), '%m%d') < DATE_FORMAT(birth_date, '%m%d'));
END$$

CREATE DEFINER=`root`@`localhost` FUNCTION `CalculateProcessingFee` (`amount` DECIMAL(10,2), `payment_method_id` INT) RETURNS DECIMAL(10,2) DETERMINISTIC READS SQL DATA BEGIN
    DECLARE fee_percentage DECIMAL(5,2);
    DECLARE fee_fixed DECIMAL(10,2);
    DECLARE total_fee DECIMAL(10,2);

    -- Get fee structure for payment method
    SELECT processing_fee_percentage, processing_fee_fixed
    INTO fee_percentage, fee_fixed
    FROM payment_methods
    WHERE id = payment_method_id;

    -- Calculate total processing fee
    SET total_fee = (amount * fee_percentage / 100) + fee_fixed;

    RETURN total_fee;
END$$

CREATE DEFINER=`root`@`localhost` FUNCTION `GenerateRequestNumber` (`doc_type` VARCHAR(10)) RETURNS VARCHAR(50) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DETERMINISTIC READS SQL DATA BEGIN
    DECLARE next_seq INT;
    DECLARE current_year VARCHAR(4);
    DECLARE request_num VARCHAR(50);

    SET current_year = YEAR(CURDATE());

    -- Get next sequence number for the year
    SELECT COALESCE(MAX(CAST(SUBSTRING(request_number, -6) AS UNSIGNED)), 0) + 1
    INTO next_seq
    FROM document_requests
    WHERE request_number LIKE CONCAT(doc_type, '-', current_year, '-%');

    -- Format: DOC-YYYY-NNNNNN (e.g., CED-2024-000001)
    SET request_num = CONCAT(doc_type, '-', current_year, '-', LPAD(next_seq, 6, '0'));

    RETURN request_num;
END$$

CREATE DEFINER=`root`@`localhost` FUNCTION `GenerateTransactionId` () RETURNS VARCHAR(100) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DETERMINISTIC READS SQL DATA BEGIN
    DECLARE transaction_id VARCHAR(100);
    DECLARE timestamp_str VARCHAR(20);
    DECLARE random_suffix VARCHAR(10);

    SET timestamp_str = UNIX_TIMESTAMP();
    SET random_suffix = LPAD(FLOOR(RAND() * 1000000), 6, '0');
    SET transaction_id = CONCAT('TXN-', timestamp_str, '-', random_suffix);

    RETURN transaction_id;
END$$

DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `admin_employee_accounts`
--

CREATE TABLE `admin_employee_accounts` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `role` enum('admin','employee') NOT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `last_login` timestamp NULL DEFAULT NULL,
  `password_changed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_employee_accounts`
--

INSERT INTO `admin_employee_accounts` (`id`, `username`, `password_hash`, `role`, `status`, `last_login`, `password_changed_at`, `created_at`, `updated_at`) VALUES
(32, 'admin12345', '$2a$12$ubLUsH2fvM9Xcpr0PNxpnuf.lql4qc35RZkB8UeJNhruKUsjWU5Ta', 'admin', 'active', '2025-07-13 02:48:37', '2025-07-07 14:08:18', '2025-07-07 14:08:18', '2025-07-13 02:48:37');

-- --------------------------------------------------------

--
-- Table structure for table `admin_employee_profiles`
--

CREATE TABLE `admin_employee_profiles` (
  `id` int(11) NOT NULL,
  `account_id` int(11) NOT NULL,
  `employee_id` varchar(20) DEFAULT NULL,
  `first_name` varchar(100) NOT NULL,
  `middle_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) NOT NULL,
  `suffix` varchar(10) DEFAULT NULL,
  `phone_number` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `profile_picture` varchar(255) DEFAULT NULL,
  `position` varchar(100) DEFAULT NULL,
  `department` varchar(100) DEFAULT NULL,
  `hire_date` date DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_employee_profiles`
--

INSERT INTO `admin_employee_profiles` (`id`, `account_id`, `employee_id`, `first_name`, `middle_name`, `last_name`, `suffix`, `phone_number`, `email`, `profile_picture`, `position`, `department`, `hire_date`, `created_at`, `updated_at`) VALUES
(27, 32, '************', 'Roco', 'Joma', 'Manalo', 'Jr', '***********', '<EMAIL>', NULL, 'Dept Head', 'CMO', '2016-01-06', '2025-07-07 14:08:44', '2025-07-07 14:08:44');

-- --------------------------------------------------------

--
-- Table structure for table `audit_logs`
--

CREATE TABLE `audit_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `user_type` enum('admin','employee','client') NOT NULL,
  `action` varchar(100) NOT NULL,
  `table_name` varchar(100) DEFAULT NULL,
  `record_id` int(11) DEFAULT NULL,
  `old_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`old_values`)),
  `new_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`new_values`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `barangay_clearance_applications`
--

CREATE TABLE `barangay_clearance_applications` (
  `id` int(11) NOT NULL,
  `request_id` int(11) NOT NULL,
  `has_pending_cases` tinyint(1) DEFAULT 0,
  `pending_cases_details` text DEFAULT NULL,
  `voter_registration_number` varchar(50) DEFAULT NULL,
  `precinct_number` varchar(20) DEFAULT NULL,
  `emergency_contact_name` varchar(200) DEFAULT NULL,
  `emergency_contact_relationship` varchar(50) DEFAULT NULL,
  `emergency_contact_phone` varchar(20) DEFAULT NULL,
  `emergency_contact_address` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `barangay_clearance_applications`
--

INSERT INTO `barangay_clearance_applications` (`id`, `request_id`, `has_pending_cases`, `pending_cases_details`, `voter_registration_number`, `precinct_number`, `emergency_contact_name`, `emergency_contact_relationship`, `emergency_contact_phone`, `emergency_contact_address`, `created_at`, `updated_at`) VALUES
(3, 12, 0, NULL, NULL, NULL, 'Test Contact', 'Friend', '***********', 'Test Address', '2025-07-08 03:32:41', '2025-07-08 03:32:41'),
(4, 13, 0, NULL, NULL, NULL, 'Roco Joma Manalo', 'Friend', '***********', '19 Lacumbre City', '2025-07-08 03:36:39', '2025-07-08 03:36:39');

-- --------------------------------------------------------

--
-- Table structure for table `cedula_applications`
--

CREATE TABLE `cedula_applications` (
  `id` int(11) NOT NULL,
  `request_id` int(11) NOT NULL,
  `occupation` varchar(100) DEFAULT NULL,
  `employer_name` varchar(200) DEFAULT NULL,
  `employer_address` text DEFAULT NULL,
  `monthly_income` decimal(12,2) DEFAULT NULL,
  `annual_income` decimal(12,2) DEFAULT NULL,
  `business_name` varchar(200) DEFAULT NULL,
  `business_address` text DEFAULT NULL,
  `business_type` varchar(100) DEFAULT NULL,
  `business_income` decimal(12,2) DEFAULT NULL,
  `has_real_property` tinyint(1) DEFAULT 0,
  `property_assessed_value` decimal(15,2) DEFAULT NULL,
  `property_location` text DEFAULT NULL,
  `tin_number` varchar(20) DEFAULT NULL,
  `previous_ctc_number` varchar(50) DEFAULT NULL,
  `previous_ctc_date_issued` date DEFAULT NULL,
  `previous_ctc_place_issued` varchar(100) DEFAULT NULL,
  `computed_tax` decimal(10,2) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cedula_applications`
--

INSERT INTO `cedula_applications` (`id`, `request_id`, `occupation`, `employer_name`, `employer_address`, `monthly_income`, `annual_income`, `business_name`, `business_address`, `business_type`, `business_income`, `has_real_property`, `property_assessed_value`, `property_location`, `tin_number`, `previous_ctc_number`, `previous_ctc_date_issued`, `previous_ctc_place_issued`, `computed_tax`, `created_at`, `updated_at`) VALUES
(1, 5, 'Employment', NULL, NULL, 0.00, 10000.00, NULL, NULL, NULL, 0.00, 0, 0.00, NULL, NULL, NULL, NULL, NULL, 50.00, '2025-07-08 01:42:26', '2025-07-08 01:42:26'),
(2, 9, 'Employment', NULL, NULL, 0.00, 10000.00, NULL, NULL, NULL, 0.00, 0, 0.00, NULL, NULL, NULL, NULL, NULL, 50.00, '2025-07-08 03:14:36', '2025-07-08 03:14:36'),
(3, 15, 'Employment', NULL, NULL, 0.00, 20000.00, NULL, NULL, NULL, 0.00, 0, 0.00, NULL, NULL, NULL, NULL, NULL, 150.00, '2025-07-09 03:51:11', '2025-07-09 03:51:11');

-- --------------------------------------------------------

--
-- Table structure for table `civil_status`
--

CREATE TABLE `civil_status` (
  `id` int(11) NOT NULL,
  `status_name` varchar(20) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `civil_status`
--

INSERT INTO `civil_status` (`id`, `status_name`, `created_at`) VALUES
(1, 'Single', '2025-06-08 08:27:00'),
(2, 'Married', '2025-06-08 08:27:00'),
(3, 'Divorced', '2025-06-08 08:27:00'),
(4, 'Widowed', '2025-06-08 08:27:00'),
(5, 'Separated', '2025-06-08 08:27:00');

-- --------------------------------------------------------

--
-- Table structure for table `client_accounts`
--

CREATE TABLE `client_accounts` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `status` enum('active','inactive','suspended','pending_verification') DEFAULT 'pending_verification',
  `email_verified` tinyint(1) DEFAULT 0,
  `phone_verified` tinyint(1) DEFAULT 0,
  `last_login` timestamp NULL DEFAULT NULL,
  `password_changed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `client_accounts`
--

INSERT INTO `client_accounts` (`id`, `username`, `password_hash`, `status`, `email_verified`, `phone_verified`, `last_login`, `password_changed_at`, `created_at`, `updated_at`) VALUES
(12, 'revo4438', '$2a$12$ttZAfTsFN4YJDbHAryWLd.fQpHkuloLE0idBNUW6LvMUbKVPxQWG.', 'active', 1, 0, '2025-07-13 02:48:57', '2025-07-07 14:07:40', '2025-07-07 14:07:40', '2025-07-13 02:48:57');

-- --------------------------------------------------------

--
-- Table structure for table `client_profiles`
--

CREATE TABLE `client_profiles` (
  `id` int(11) NOT NULL,
  `account_id` int(11) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `middle_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) NOT NULL,
  `suffix` varchar(10) DEFAULT NULL,
  `birth_date` date NOT NULL,
  `gender` enum('male','female') NOT NULL,
  `civil_status_id` int(11) NOT NULL,
  `nationality` varchar(50) DEFAULT 'Filipino',
  `phone_number` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `house_number` varchar(20) DEFAULT NULL,
  `street` varchar(100) DEFAULT NULL,
  `subdivision` varchar(100) DEFAULT NULL,
  `barangay` varchar(100) NOT NULL,
  `city_municipality` varchar(100) NOT NULL,
  `province` varchar(100) NOT NULL,
  `postal_code` varchar(10) DEFAULT NULL,
  `years_of_residency` int(11) DEFAULT NULL,
  `months_of_residency` int(11) DEFAULT NULL,
  `profile_picture` varchar(255) DEFAULT NULL,
  `is_verified` tinyint(1) DEFAULT 0,
  `verified_by` int(11) DEFAULT NULL,
  `verified_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `client_profiles`
--

INSERT INTO `client_profiles` (`id`, `account_id`, `first_name`, `middle_name`, `last_name`, `suffix`, `birth_date`, `gender`, `civil_status_id`, `nationality`, `phone_number`, `email`, `house_number`, `street`, `subdivision`, `barangay`, `city_municipality`, `province`, `postal_code`, `years_of_residency`, `months_of_residency`, `profile_picture`, `is_verified`, `verified_by`, `verified_at`, `created_at`, `updated_at`) VALUES
(11, 12, 'Jerome', 'Joma', 'Revo', 'Jr', '2005-06-08', 'male', 1, 'Filipino', '***********', '<EMAIL>', '', 'Molave', '', 'Lagao', 'Gensan', 'South Cotabato', '', NULL, NULL, NULL, 0, NULL, NULL, '2025-07-07 14:07:47', '2025-07-09 03:32:34');

-- --------------------------------------------------------

--
-- Table structure for table `document_requests`
--

CREATE TABLE `document_requests` (
  `id` int(11) NOT NULL,
  `request_number` varchar(50) NOT NULL,
  `client_id` int(11) NOT NULL,
  `document_type_id` int(11) NOT NULL,
  `purpose_category_id` int(11) NOT NULL,
  `purpose_details` text NOT NULL,
  `status_id` int(11) NOT NULL,
  `priority` enum('normal','urgent') DEFAULT 'normal',
  `processed_by` int(11) DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `base_fee` decimal(10,2) NOT NULL,
  `additional_fees` decimal(10,2) DEFAULT 0.00,
  `processing_fee` decimal(10,2) DEFAULT 0.00,
  `payment_method_id` int(11) DEFAULT NULL,
  `payment_status` enum('pending','processing','paid','failed','refunded','cancelled') DEFAULT 'pending',
  `payment_reference` varchar(100) DEFAULT NULL,
  `payment_provider_reference` varchar(100) DEFAULT NULL,
  `paid_at` timestamp NULL DEFAULT NULL,
  `delivery_method` enum('pickup','delivery') DEFAULT 'pickup',
  `delivery_address` text DEFAULT NULL,
  `delivery_fee` decimal(10,2) DEFAULT 0.00,
  `requested_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `target_completion_date` date DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `document_requests`
--

INSERT INTO `document_requests` (`id`, `request_number`, `client_id`, `document_type_id`, `purpose_category_id`, `purpose_details`, `status_id`, `priority`, `processed_by`, `approved_by`, `processed_at`, `approved_at`, `base_fee`, `additional_fees`, `processing_fee`, `payment_method_id`, `payment_status`, `payment_reference`, `payment_provider_reference`, `paid_at`, `delivery_method`, `delivery_address`, `delivery_fee`, `requested_at`, `target_completion_date`, `completed_at`, `created_at`, `updated_at`) VALUES
(5, 'CED-2025-000001', 12, 1, 5, 'It serves as proof that you are a resident of a certain city or municipality, which is often needed when applying for a job or processing government documents.', 8, 'normal', NULL, 32, NULL, '2025-07-09 04:43:19', 50.00, 0.00, 10.60, 3, 'pending', NULL, NULL, NULL, 'pickup', NULL, 0.00, '2025-07-08 01:42:26', NULL, NULL, '2025-07-08 01:42:26', '2025-07-13 05:24:49'),
(9, 'CED-2025-000002', 12, 1, 3, 'To travel abroad, vacation', 8, 'normal', NULL, 32, NULL, '2025-07-09 04:49:21', 50.00, 0.00, 10.60, 3, 'pending', NULL, NULL, NULL, 'pickup', NULL, 0.00, '2025-07-08 03:14:36', NULL, NULL, '2025-07-08 03:14:36', '2025-07-13 05:25:07'),
(12, 'BC-2025-000001', 12, 2, 2, 'Test Automotive Shop, Relocation', 4, 'normal', NULL, 32, NULL, '2025-07-09 16:16:22', 50.00, 0.00, 5.75, 6, 'pending', NULL, NULL, NULL, 'pickup', NULL, 0.00, '2025-07-08 03:32:41', NULL, NULL, '2025-07-08 03:32:41', '2025-07-09 16:16:22'),
(13, 'BC-2025-000002', 12, 2, 2, 'The Automotive Shop, Relocation will be located at the Marcos High Way', 4, 'normal', NULL, 32, NULL, '2025-07-09 16:09:48', 50.00, 0.00, 16.75, 2, 'pending', NULL, NULL, NULL, 'pickup', NULL, 0.00, '2025-07-08 03:36:39', NULL, NULL, '2025-07-08 03:36:39', '2025-07-09 16:09:48'),
(15, 'CED-2025-000003', 12, 1, 3, 'For vacation in Thailand', 4, 'normal', NULL, 32, NULL, '2025-07-09 15:08:42', 150.00, 0.00, 5.45, 6, 'pending', NULL, NULL, NULL, 'pickup', NULL, 0.00, '2025-07-09 03:51:11', NULL, NULL, '2025-07-09 03:51:11', '2025-07-09 15:08:42');

-- --------------------------------------------------------

--
-- Table structure for table `document_types`
--

CREATE TABLE `document_types` (
  `id` int(11) NOT NULL,
  `type_name` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `base_fee` decimal(10,2) NOT NULL DEFAULT 0.00,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `document_types`
--

INSERT INTO `document_types` (`id`, `type_name`, `description`, `base_fee`, `is_active`, `created_at`) VALUES
(1, 'Cedula', 'Community Tax Certificate', 30.00, 1, '2025-06-08 08:27:00'),
(2, 'Barangay Clearance', 'Certificate of Good Moral Character', 50.00, 1, '2025-06-08 08:27:00');

-- --------------------------------------------------------

--
-- Table structure for table `generated_documents`
--

CREATE TABLE `generated_documents` (
  `id` int(11) NOT NULL,
  `request_id` int(11) NOT NULL,
  `document_number` varchar(100) NOT NULL,
  `document_path` varchar(500) DEFAULT NULL,
  `qr_code_data` text DEFAULT NULL,
  `issued_date` date NOT NULL,
  `expiry_date` date DEFAULT NULL,
  `is_valid` tinyint(1) DEFAULT 1,
  `issued_by` int(11) NOT NULL,
  `authorized_signatory` varchar(200) DEFAULT NULL,
  `security_hash` varchar(255) DEFAULT NULL,
  `verification_code` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `user_type` enum('admin','client') NOT NULL,
  `type` varchar(50) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`data`)),
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `is_read` tinyint(1) DEFAULT 0,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `otps`
--

CREATE TABLE `otps` (
  `id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `otp_code` varchar(10) NOT NULL,
  `purpose` enum('registration','password_reset','email_verification','login') DEFAULT 'registration',
  `expires_at` datetime NOT NULL,
  `is_used` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `otps`
--

INSERT INTO `otps` (`id`, `email`, `otp_code`, `purpose`, `expires_at`, `is_used`, `created_at`, `updated_at`) VALUES
(4, '<EMAIL>', '438352', 'email_verification', '2025-06-19 20:22:08', 1, '2025-06-19 12:12:08', '2025-06-19 12:12:33'),
(20, '<EMAIL>', '228822', 'registration', '2025-06-22 22:12:55', 0, '2025-06-22 14:02:55', '2025-06-22 14:02:55'),
(21, '<EMAIL>', '721360', 'registration', '2025-06-22 22:13:01', 0, '2025-06-22 14:03:01', '2025-06-22 14:03:01'),
(22, '<EMAIL>', '141759', 'registration', '2025-06-22 22:13:06', 0, '2025-06-22 14:03:06', '2025-06-22 14:03:06'),
(27, '<EMAIL>', '340158', 'registration', '2025-06-22 22:31:25', 1, '2025-06-22 14:21:25', '2025-06-22 14:21:28'),
(30, '<EMAIL>', '897346', 'email_verification', '2025-06-22 22:42:16', 1, '2025-06-22 14:32:16', '2025-06-22 14:32:34'),
(33, '<EMAIL>', '193441', 'registration', '2025-06-23 02:08:10', 1, '2025-06-22 17:58:10', '2025-06-22 17:58:25'),
(34, '<EMAIL>', '944107', 'registration', '2025-06-23 02:08:15', 0, '2025-06-22 17:58:15', '2025-06-22 17:58:15'),
(42, '<EMAIL>', '128206', 'registration', '2025-07-07 21:59:50', 1, '2025-07-07 13:49:50', '2025-07-07 13:51:07'),
(44, '<EMAIL>', '441449', 'registration', '2025-07-07 22:06:41', 0, '2025-07-07 13:56:41', '2025-07-07 13:56:41'),
(45, '<EMAIL>', '805744', 'registration', '2025-07-07 22:06:47', 1, '2025-07-07 13:56:47', '2025-07-07 13:57:11'),
(46, '<EMAIL>', '130254', 'registration', '2025-07-07 22:07:24', 1, '2025-07-07 13:57:24', '2025-07-07 13:58:13'),
(47, '<EMAIL>', '555409', 'registration', '2025-07-07 22:09:23', 1, '2025-07-07 13:59:23', '2025-07-07 14:02:38'),
(48, '<EMAIL>', '039587', 'registration', '2025-07-07 22:09:28', 1, '2025-07-07 13:59:28', '2025-07-07 13:59:55'),
(49, '<EMAIL>', '569324', 'registration', '2025-07-07 22:10:01', 1, '2025-07-07 14:00:01', '2025-07-07 14:00:18'),
(50, '<EMAIL>', '999999', 'registration', '2025-07-07 22:12:38', 1, '2025-07-07 14:02:38', '2025-07-07 14:02:38'),
(51, '<EMAIL>', '123456', 'registration', '2025-07-07 22:32:38', 0, '2025-07-07 14:02:38', '2025-07-07 14:02:38'),
(52, '<EMAIL>', '999888', 'registration', '2025-07-07 22:34:26', 1, '2025-07-07 14:04:26', '2025-07-07 14:04:26'),
(53, '<EMAIL>', '853270', 'email_verification', '2025-07-07 22:17:47', 1, '2025-07-07 14:07:47', '2025-07-07 14:07:58'),
(54, '<EMAIL>', '364235', 'registration', '2025-07-07 22:18:44', 1, '2025-07-07 14:08:44', '2025-07-07 14:09:00'),
(55, '<EMAIL>', '835585', 'registration', '2025-07-07 22:18:48', 0, '2025-07-07 14:08:48', '2025-07-07 14:08:48');

-- --------------------------------------------------------

--
-- Table structure for table `payment_methods`
--

CREATE TABLE `payment_methods` (
  `id` int(11) NOT NULL,
  `method_name` varchar(50) NOT NULL,
  `method_code` varchar(20) NOT NULL,
  `description` text DEFAULT NULL,
  `is_online` tinyint(1) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `processing_fee_percentage` decimal(5,2) DEFAULT 0.00,
  `processing_fee_fixed` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `requires_verification` tinyint(1) DEFAULT 0 COMMENT 'Whether payment method requires manual verification'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `payment_methods`
--

INSERT INTO `payment_methods` (`id`, `method_name`, `method_code`, `description`, `is_online`, `is_active`, `processing_fee_percentage`, `processing_fee_fixed`, `created_at`, `requires_verification`) VALUES
(1, 'Cash Payment', 'CASH', 'Pay in cash at barangay office', 0, 1, 0.00, 0.00, '2025-06-08 08:27:00', 1),
(2, 'PayMongo - Credit/Debit Card', 'PAYMONGO_CARD', 'Pay online using credit or debit card via PayMongo', 1, 1, 3.50, 15.00, '2025-06-08 08:27:00', 0),
(3, 'PayMongo - GCash', 'PAYMONGO_GCASH', 'Pay using GCash via PayMongo', 1, 1, 2.00, 10.00, '2025-06-08 08:27:00', 0),
(4, 'PayMongo - GrabPay', 'PAYMONGO_GRABPAY', 'Pay using GrabPay via PayMongo', 1, 1, 2.00, 10.00, '2025-06-08 08:27:00', 0),
(5, 'PayMongo - PayMaya', 'PAYMONGO_PAYMAYA', 'Pay using PayMaya via PayMongo', 1, 1, 2.00, 10.00, '2025-06-08 08:27:00', 0),
(6, 'PayMongo - Bank Transfer', 'PAYMONGO_BANK', 'Pay via online bank transfer through PayMongo', 1, 1, 1.50, 5.00, '2025-06-08 08:27:00', 0);

-- --------------------------------------------------------

--
-- Table structure for table `payment_transactions`
--

CREATE TABLE `payment_transactions` (
  `id` int(11) NOT NULL,
  `request_id` int(11) NOT NULL,
  `payment_method_id` int(11) NOT NULL,
  `transaction_id` varchar(100) NOT NULL,
  `external_transaction_id` varchar(100) DEFAULT NULL,
  `paymongo_payment_intent_id` varchar(100) DEFAULT NULL,
  `paymongo_payment_method_id` varchar(100) DEFAULT NULL,
  `paymongo_source_id` varchar(100) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `processing_fee` decimal(10,2) DEFAULT 0.00,
  `net_amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'PHP',
  `status` enum('pending','processing','succeeded','failed','cancelled','refunded') DEFAULT 'pending',
  `failure_reason` text DEFAULT NULL,
  `payment_description` text DEFAULT NULL,
  `customer_email` varchar(100) DEFAULT NULL,
  `customer_phone` varchar(20) DEFAULT NULL,
  `webhook_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`webhook_data`)),
  `callback_url` varchar(500) DEFAULT NULL,
  `success_url` varchar(500) DEFAULT NULL,
  `cancel_url` varchar(500) DEFAULT NULL,
  `initiated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `completed_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `verified_by` int(11) DEFAULT NULL COMMENT 'Admin who verified in-person payment',
  `verified_at` timestamp NULL DEFAULT NULL COMMENT 'When payment was verified',
  `receipt_number` varchar(100) DEFAULT NULL COMMENT 'Physical receipt number for cash payments',
  `verification_notes` text DEFAULT NULL COMMENT 'Additional notes from payment verification'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payment_verifications`
--

CREATE TABLE `payment_verifications` (
  `id` int(11) NOT NULL,
  `request_id` int(11) NOT NULL,
  `payment_method_id` int(11) NOT NULL,
  `amount_received` decimal(10,2) NOT NULL,
  `receipt_number` varchar(100) DEFAULT NULL,
  `verification_notes` text DEFAULT NULL,
  `proof_image_path` varchar(500) DEFAULT NULL,
  `verified_by` int(11) NOT NULL,
  `verified_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payment_webhooks`
--

CREATE TABLE `payment_webhooks` (
  `id` int(11) NOT NULL,
  `webhook_id` varchar(100) NOT NULL,
  `event_type` varchar(100) NOT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `payment_transaction_id` int(11) DEFAULT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`payload`)),
  `signature` varchar(500) DEFAULT NULL,
  `processed` tinyint(1) DEFAULT 0,
  `processed_at` timestamp NULL DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `retry_count` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `pickup_schedules`
--

CREATE TABLE `pickup_schedules` (
  `id` int(11) NOT NULL,
  `request_id` int(11) NOT NULL,
  `scheduled_date` date NOT NULL,
  `scheduled_time_start` time NOT NULL,
  `scheduled_time_end` time NOT NULL,
  `pickup_notes` text DEFAULT NULL,
  `scheduled_by` int(11) NOT NULL,
  `client_confirmed` tinyint(1) DEFAULT 0,
  `client_confirmed_at` timestamp NULL DEFAULT NULL,
  `actual_pickup_at` timestamp NULL DEFAULT NULL,
  `picked_up_by_name` varchar(200) DEFAULT NULL,
  `picked_up_by_id_type` varchar(50) DEFAULT NULL,
  `picked_up_by_id_number` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `purpose_categories`
--

CREATE TABLE `purpose_categories` (
  `id` int(11) NOT NULL,
  `category_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `purpose_categories`
--

INSERT INTO `purpose_categories` (`id`, `category_name`, `description`, `is_active`, `created_at`) VALUES
(1, 'Employment', 'For job application or employment purposes', 1, '2025-06-08 08:27:00'),
(2, 'Business Registration', 'For business license or registration', 1, '2025-06-08 08:27:00'),
(3, 'Travel/Visa', 'For travel documents or visa application', 1, '2025-06-08 08:27:00'),
(4, 'School Enrollment', 'For school admission or enrollment', 1, '2025-06-08 08:27:00'),
(5, 'Government Transaction', 'For other government-related transactions', 1, '2025-06-08 08:27:00'),
(6, 'Bank Account', 'For opening bank accounts', 1, '2025-06-08 08:27:00'),
(7, 'Insurance', 'For insurance applications', 1, '2025-06-08 08:27:00'),
(8, 'Legal Proceedings', 'For court or legal matters', 1, '2025-06-08 08:27:00'),
(9, 'Loan Application', 'For loan or credit applications', 1, '2025-06-08 08:27:00'),
(10, 'Other', 'Other purposes not listed above', 1, '2025-06-08 08:27:00');

-- --------------------------------------------------------

--
-- Table structure for table `request_status`
--

CREATE TABLE `request_status` (
  `id` int(11) NOT NULL,
  `status_name` varchar(30) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `request_status`
--

INSERT INTO `request_status` (`id`, `status_name`, `description`, `created_at`) VALUES
(1, 'pending', 'Request submitted and pending review', '2025-06-08 08:27:00'),
(2, 'under_review', 'Request is being reviewed by staff', '2025-06-08 08:27:00'),
(3, 'additional_info_required', 'Additional information or documents needed', '2025-06-08 08:27:00'),
(4, 'approved', 'Request approved and ready for processing', '2025-06-08 08:27:00'),
(5, 'processing', 'Document is being prepared', '2025-06-08 08:27:00'),
(6, 'ready_for_pickup', 'Document is ready for pickup', '2025-06-08 08:27:00'),
(7, 'completed', 'Request completed successfully', '2025-06-08 08:27:00'),
(8, 'cancelled', 'Request cancelled by client', '2025-06-08 08:27:00'),
(9, 'rejected', 'Request rejected by authority', '2025-06-08 08:27:00'),
(10, 'payment_pending', 'Approved request awaiting payment', '2025-07-13 07:24:38'),
(11, 'payment_confirmed', 'Payment verified and confirmed', '2025-07-13 07:24:38'),
(12, 'payment_failed', 'Payment failed or expired', '2025-07-13 07:24:38'),
(13, 'pickup_scheduled', 'Pickup appointment scheduled', '2025-07-13 07:24:38');

-- --------------------------------------------------------

--
-- Table structure for table `request_status_history`
--

CREATE TABLE `request_status_history` (
  `id` int(11) NOT NULL,
  `request_id` int(11) NOT NULL,
  `old_status_id` int(11) DEFAULT NULL,
  `new_status_id` int(11) NOT NULL,
  `changed_by` int(11) NOT NULL,
  `change_reason` text DEFAULT NULL,
  `changed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `request_status_history`
--

INSERT INTO `request_status_history` (`id`, `request_id`, `old_status_id`, `new_status_id`, `changed_by`, `change_reason`, `changed_at`) VALUES
(5, 12, 1, 8, 32, 'Testing cancel functionality', '2025-07-08 03:46:28'),
(6, 5, 1, 9, 32, 'Missing required documents for API test', '2025-07-08 16:55:40'),
(7, 13, 1, 2, 32, 'Moving to under review', '2025-07-08 16:58:50'),
(8, 13, 2, 4, 32, 'API test approval from under_review', '2025-07-08 16:59:02'),
(11, 9, 1, 2, 32, 'Moving to review for approval test', '2025-07-08 17:12:55'),
(12, 9, 2, 4, 32, 'Approved for testing purposes', '2025-07-08 17:12:56'),
(13, 9, 4, 9, 32, 'Missing required documents - test rejection', '2025-07-08 17:12:57'),
(14, 13, 4, 9, 32, 'cvdgfdfdb', '2025-07-08 17:16:59'),
(16, 15, 1, 4, 32, 'Quick approval from admin interface', '2025-07-09 04:03:03'),
(17, 5, 1, 4, 32, 'Quick approval from admin interface', '2025-07-09 04:43:19'),
(18, 9, 1, 2, 32, 'Reviewing ', '2025-07-09 04:48:27'),
(19, 9, 2, 4, 32, 'Quick approval from admin interface', '2025-07-09 04:49:21'),
(20, 12, 1, 4, 32, 'Quick approval from admin interface', '2025-07-09 05:01:42'),
(21, 15, 1, 4, 32, 'Quick approval from admin interface', '2025-07-09 15:08:42'),
(22, 13, 1, 4, 32, 'Quick approval from admin interface', '2025-07-09 16:09:48'),
(23, 12, 1, 4, 32, 'Quick approval from admin interface', '2025-07-09 16:16:22'),
(24, 5, 1, 8, 32, 'Cancelled by user', '2025-07-13 05:24:49'),
(25, 9, 2, 8, 32, 'Cancelled by user', '2025-07-13 05:25:07');

-- --------------------------------------------------------

--
-- Table structure for table `supporting_documents`
--

CREATE TABLE `supporting_documents` (
  `id` int(11) NOT NULL,
  `request_id` int(11) NOT NULL,
  `document_name` varchar(200) NOT NULL,
  `document_type` varchar(100) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int(11) DEFAULT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `uploaded_by` int(11) NOT NULL,
  `is_verified` tinyint(1) DEFAULT 0,
  `verified_by` int(11) DEFAULT NULL,
  `verified_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `system_settings`
--

CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('string','number','boolean','json') DEFAULT 'string',
  `description` text DEFAULT NULL,
  `is_public` tinyint(1) DEFAULT 0,
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `system_settings`
--

INSERT INTO `system_settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `description`, `is_public`, `updated_by`, `updated_at`) VALUES
(1, 'system_name', 'Barangay Management System', 'string', 'Name of the system', 1, NULL, '2025-06-08 08:27:00'),
(2, 'barangay_name', 'Barangay Sample', 'string', 'Name of the barangay', 1, NULL, '2025-06-08 08:27:00'),
(3, 'barangay_address', 'Sample Address, City, Province', 'string', 'Complete address of barangay', 1, NULL, '2025-06-08 08:27:00'),
(4, 'barangay_contact', '+63 123 456 7890', 'string', 'Contact number of barangay', 1, NULL, '2025-06-08 08:27:00'),
(5, 'barangay_email', '<EMAIL>', 'string', 'Email address of barangay', 1, NULL, '2025-06-08 08:27:00'),
(6, 'cedula_base_fee', '30.00', 'number', 'Base fee for cedula', 0, NULL, '2025-06-08 08:27:00'),
(7, 'clearance_base_fee', '50.00', 'number', 'Base fee for barangay clearance', 0, NULL, '2025-06-08 08:27:00'),
(8, 'processing_days_cedula', '3', 'number', 'Standard processing days for cedula', 1, NULL, '2025-06-08 08:27:00'),
(9, 'processing_days_clearance', '5', 'number', 'Standard processing days for clearance', 1, NULL, '2025-06-08 08:27:00'),
(10, 'max_file_upload_size', '5242880', 'number', 'Maximum file upload size in bytes (5MB)', 0, NULL, '2025-06-08 08:27:00'),
(11, 'allowed_file_types', '[\"jpg\",\"jpeg\",\"png\",\"pdf\",\"doc\",\"docx\"]', 'json', 'Allowed file types for upload', 0, NULL, '2025-06-08 08:27:00'),
(12, 'paymongo_public_key', 'pk_test_your_public_key_here', 'string', 'PayMongo public key for frontend', 0, NULL, '2025-06-08 08:27:00'),
(13, 'paymongo_secret_key', 'sk_test_your_secret_key_here', 'string', 'PayMongo secret key for backend', 0, NULL, '2025-06-08 08:27:00'),
(14, 'paymongo_webhook_secret', 'whsec_your_webhook_secret_here', 'string', 'PayMongo webhook secret for verification', 0, NULL, '2025-06-08 08:27:00'),
(15, 'enable_online_payments', 'true', 'boolean', 'Enable or disable online payment options', 1, NULL, '2025-06-08 08:27:00'),
(16, 'payment_timeout_minutes', '30', 'number', 'Payment session timeout in minutes', 0, NULL, '2025-06-08 08:27:00');

-- --------------------------------------------------------

--
-- Stand-in structure for view `v_client_complete`
-- (See below for the actual view)
--
CREATE TABLE `v_client_complete` (
`account_id` int(11)
,`username` varchar(50)
,`account_status` enum('active','inactive','suspended','pending_verification')
,`email_verified` tinyint(1)
,`phone_verified` tinyint(1)
,`profile_id` int(11)
,`full_name` varchar(313)
,`first_name` varchar(100)
,`middle_name` varchar(100)
,`last_name` varchar(100)
,`suffix` varchar(10)
,`birth_date` date
,`age` int(6)
,`gender` enum('male','female')
,`civil_status` varchar(20)
,`nationality` varchar(50)
,`phone_number` varchar(20)
,`email` varchar(100)
,`complete_address` text
,`barangay` varchar(100)
,`city_municipality` varchar(100)
,`province` varchar(100)
,`years_of_residency` int(11)
,`months_of_residency` int(11)
,`is_verified` tinyint(1)
,`verified_at` timestamp
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `v_document_requests_complete`
-- (See below for the actual view)
--
CREATE TABLE `v_document_requests_complete` (
`request_id` int(11)
,`request_number` varchar(50)
,`document_type` varchar(50)
,`purpose_category` varchar(100)
,`purpose_details` text
,`current_status` varchar(30)
,`priority` enum('normal','urgent')
,`total_fee` decimal(12,2)
,`base_fee` decimal(10,2)
,`additional_fees` decimal(10,2)
,`processing_fee` decimal(10,2)
,`payment_status` enum('pending','processing','paid','failed','refunded','cancelled')
,`payment_method` varchar(50)
,`is_online_payment` tinyint(1)
,`payment_reference` varchar(100)
,`payment_provider_reference` varchar(100)
,`paid_at` timestamp
,`delivery_method` enum('pickup','delivery')
,`requested_at` timestamp
,`target_completion_date` date
,`completed_at` timestamp
,`client_name` varchar(313)
,`client_phone` varchar(20)
,`client_email` varchar(100)
,`client_address` text
,`processed_by_name` varchar(201)
,`approved_by_name` varchar(201)
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `v_payment_audit_trail`
-- (See below for the actual view)
--
CREATE TABLE `v_payment_audit_trail` (
`transaction_id` int(11)
,`reference_number` varchar(100)
,`request_number` varchar(50)
,`document_name` varchar(50)
,`payment_method` varchar(50)
,`amount` decimal(10,2)
,`processing_fee` decimal(10,2)
,`net_amount` decimal(10,2)
,`payment_status` enum('pending','processing','succeeded','failed','cancelled','refunded')
,`initiated_at` timestamp
,`verified_at` timestamp
,`receipt_number` varchar(100)
,`verification_notes` text
,`client_name` varchar(201)
,`verified_by_name` varchar(201)
,`payment_type` varchar(9)
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `v_payment_transactions_complete`
-- (See below for the actual view)
--
CREATE TABLE `v_payment_transactions_complete` (
`transaction_id` int(11)
,`internal_transaction_id` varchar(100)
,`external_transaction_id` varchar(100)
,`paymongo_payment_intent_id` varchar(100)
,`request_number` varchar(50)
,`request_id` int(11)
,`client_name` varchar(313)
,`client_email` varchar(100)
,`client_phone` varchar(20)
,`payment_method` varchar(50)
,`payment_method_code` varchar(20)
,`amount` decimal(10,2)
,`processing_fee` decimal(10,2)
,`net_amount` decimal(10,2)
,`currency` varchar(3)
,`payment_status` enum('pending','processing','succeeded','failed','cancelled','refunded')
,`failure_reason` text
,`initiated_at` timestamp
,`completed_at` timestamp
,`expires_at` timestamp
,`document_type` varchar(50)
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `v_payment_verification_queue`
-- (See below for the actual view)
--
CREATE TABLE `v_payment_verification_queue` (
`request_id` int(11)
,`request_number` varchar(50)
,`status_id` int(11)
,`status_name` varchar(30)
,`document_name` varchar(50)
,`base_fee` decimal(10,2)
,`payment_method` varchar(50)
,`requires_verification` tinyint(1)
,`client_name` varchar(201)
,`client_email` varchar(50)
,`request_date` timestamp
,`approved_at` timestamp
,`payment_status` enum('pending','processing','paid','failed','refunded','cancelled')
,`transaction_id` varchar(100)
,`payment_amount` decimal(10,2)
,`transaction_status` enum('pending','processing','succeeded','failed','cancelled','refunded')
,`verified_by` int(11)
,`verified_at` timestamp
,`receipt_number` varchar(100)
,`verified_by_name` varchar(201)
);

-- --------------------------------------------------------

--
-- Structure for view `v_client_complete`
--
DROP TABLE IF EXISTS `v_client_complete`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_client_complete`  AS SELECT `ca`.`id` AS `account_id`, `ca`.`username` AS `username`, `ca`.`status` AS `account_status`, `ca`.`email_verified` AS `email_verified`, `ca`.`phone_verified` AS `phone_verified`, `cp`.`id` AS `profile_id`, concat(`cp`.`first_name`,case when `cp`.`middle_name` is not null then concat(' ',`cp`.`middle_name`) else '' end,' ',`cp`.`last_name`,case when `cp`.`suffix` is not null then concat(' ',`cp`.`suffix`) else '' end) AS `full_name`, `cp`.`first_name` AS `first_name`, `cp`.`middle_name` AS `middle_name`, `cp`.`last_name` AS `last_name`, `cp`.`suffix` AS `suffix`, `cp`.`birth_date` AS `birth_date`, year(curdate()) - year(`cp`.`birth_date`) - (date_format(curdate(),'%m%d') < date_format(`cp`.`birth_date`,'%m%d')) AS `age`, `cp`.`gender` AS `gender`, `cs`.`status_name` AS `civil_status`, `cp`.`nationality` AS `nationality`, `cp`.`phone_number` AS `phone_number`, `cp`.`email` AS `email`, concat_ws(', ',nullif(concat_ws(' ',`cp`.`house_number`,`cp`.`street`),''),nullif(`cp`.`subdivision`,''),`cp`.`barangay`,`cp`.`city_municipality`,`cp`.`province`,nullif(`cp`.`postal_code`,'')) AS `complete_address`, `cp`.`barangay` AS `barangay`, `cp`.`city_municipality` AS `city_municipality`, `cp`.`province` AS `province`, `cp`.`years_of_residency` AS `years_of_residency`, `cp`.`months_of_residency` AS `months_of_residency`, `cp`.`is_verified` AS `is_verified`, `cp`.`verified_at` AS `verified_at` FROM ((`client_accounts` `ca` join `client_profiles` `cp` on(`ca`.`id` = `cp`.`account_id`)) join `civil_status` `cs` on(`cp`.`civil_status_id` = `cs`.`id`)) ;

-- --------------------------------------------------------

--
-- Structure for view `v_document_requests_complete`
--
DROP TABLE IF EXISTS `v_document_requests_complete`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_document_requests_complete`  AS SELECT `dr`.`id` AS `request_id`, `dr`.`request_number` AS `request_number`, `dt`.`type_name` AS `document_type`, `pc`.`category_name` AS `purpose_category`, `dr`.`purpose_details` AS `purpose_details`, `rs`.`status_name` AS `current_status`, `dr`.`priority` AS `priority`, `dr`.`base_fee`+ `dr`.`additional_fees` + `dr`.`processing_fee` AS `total_fee`, `dr`.`base_fee` AS `base_fee`, `dr`.`additional_fees` AS `additional_fees`, `dr`.`processing_fee` AS `processing_fee`, `dr`.`payment_status` AS `payment_status`, `pm`.`method_name` AS `payment_method`, `pm`.`is_online` AS `is_online_payment`, `dr`.`payment_reference` AS `payment_reference`, `dr`.`payment_provider_reference` AS `payment_provider_reference`, `dr`.`paid_at` AS `paid_at`, `dr`.`delivery_method` AS `delivery_method`, `dr`.`requested_at` AS `requested_at`, `dr`.`target_completion_date` AS `target_completion_date`, `dr`.`completed_at` AS `completed_at`, `vc`.`full_name` AS `client_name`, `vc`.`phone_number` AS `client_phone`, `vc`.`email` AS `client_email`, `vc`.`complete_address` AS `client_address`, concat(`aep_processed`.`first_name`,' ',`aep_processed`.`last_name`) AS `processed_by_name`, concat(`aep_approved`.`first_name`,' ',`aep_approved`.`last_name`) AS `approved_by_name` FROM (((((((((`document_requests` `dr` join `document_types` `dt` on(`dr`.`document_type_id` = `dt`.`id`)) join `purpose_categories` `pc` on(`dr`.`purpose_category_id` = `pc`.`id`)) join `request_status` `rs` on(`dr`.`status_id` = `rs`.`id`)) join `v_client_complete` `vc` on(`dr`.`client_id` = `vc`.`account_id`)) left join `payment_methods` `pm` on(`dr`.`payment_method_id` = `pm`.`id`)) left join `admin_employee_accounts` `aea_processed` on(`dr`.`processed_by` = `aea_processed`.`id`)) left join `admin_employee_profiles` `aep_processed` on(`aea_processed`.`id` = `aep_processed`.`account_id`)) left join `admin_employee_accounts` `aea_approved` on(`dr`.`approved_by` = `aea_approved`.`id`)) left join `admin_employee_profiles` `aep_approved` on(`aea_approved`.`id` = `aep_approved`.`account_id`)) ;

-- --------------------------------------------------------

--
-- Structure for view `v_payment_audit_trail`
--
DROP TABLE IF EXISTS `v_payment_audit_trail`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_payment_audit_trail`  AS SELECT `pt`.`id` AS `transaction_id`, `pt`.`transaction_id` AS `reference_number`, `dr`.`request_number` AS `request_number`, `dt`.`type_name` AS `document_name`, `pm`.`method_name` AS `payment_method`, `pt`.`amount` AS `amount`, `pt`.`processing_fee` AS `processing_fee`, `pt`.`net_amount` AS `net_amount`, `pt`.`status` AS `payment_status`, `pt`.`created_at` AS `initiated_at`, `pt`.`verified_at` AS `verified_at`, `pt`.`receipt_number` AS `receipt_number`, `pt`.`verification_notes` AS `verification_notes`, concat(`cp`.`first_name`,' ',`cp`.`last_name`) AS `client_name`, concat(`ap`.`first_name`,' ',`ap`.`last_name`) AS `verified_by_name`, CASE WHEN `pm`.`is_online` = 1 THEN 'Online' ELSE 'In-Person' END AS `payment_type` FROM (((((((`payment_transactions` `pt` join `document_requests` `dr` on(`pt`.`request_id` = `dr`.`id`)) join `document_types` `dt` on(`dr`.`document_type_id` = `dt`.`id`)) join `payment_methods` `pm` on(`pt`.`payment_method_id` = `pm`.`id`)) join `client_accounts` `ca` on(`dr`.`client_id` = `ca`.`id`)) join `client_profiles` `cp` on(`ca`.`id` = `cp`.`account_id`)) left join `admin_employee_accounts` `admin` on(`pt`.`verified_by` = `admin`.`id`)) left join `admin_employee_profiles` `ap` on(`admin`.`id` = `ap`.`account_id`)) ORDER BY `pt`.`created_at` DESC ;

-- --------------------------------------------------------

--
-- Structure for view `v_payment_transactions_complete`
--
DROP TABLE IF EXISTS `v_payment_transactions_complete`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_payment_transactions_complete`  AS SELECT `pt`.`id` AS `transaction_id`, `pt`.`transaction_id` AS `internal_transaction_id`, `pt`.`external_transaction_id` AS `external_transaction_id`, `pt`.`paymongo_payment_intent_id` AS `paymongo_payment_intent_id`, `dr`.`request_number` AS `request_number`, `dr`.`id` AS `request_id`, `vc`.`full_name` AS `client_name`, `vc`.`email` AS `client_email`, `vc`.`phone_number` AS `client_phone`, `pm`.`method_name` AS `payment_method`, `pm`.`method_code` AS `payment_method_code`, `pt`.`amount` AS `amount`, `pt`.`processing_fee` AS `processing_fee`, `pt`.`net_amount` AS `net_amount`, `pt`.`currency` AS `currency`, `pt`.`status` AS `payment_status`, `pt`.`failure_reason` AS `failure_reason`, `pt`.`initiated_at` AS `initiated_at`, `pt`.`completed_at` AS `completed_at`, `pt`.`expires_at` AS `expires_at`, `dt`.`type_name` AS `document_type` FROM ((((`payment_transactions` `pt` join `document_requests` `dr` on(`pt`.`request_id` = `dr`.`id`)) join `payment_methods` `pm` on(`pt`.`payment_method_id` = `pm`.`id`)) join `document_types` `dt` on(`dr`.`document_type_id` = `dt`.`id`)) join `v_client_complete` `vc` on(`dr`.`client_id` = `vc`.`account_id`)) ;

-- --------------------------------------------------------

--
-- Structure for view `v_payment_verification_queue`
--
DROP TABLE IF EXISTS `v_payment_verification_queue`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_payment_verification_queue`  AS SELECT `dr`.`id` AS `request_id`, `dr`.`request_number` AS `request_number`, `dr`.`status_id` AS `status_id`, `rs`.`status_name` AS `status_name`, `dt`.`type_name` AS `document_name`, `dt`.`base_fee` AS `base_fee`, `pm`.`method_name` AS `payment_method`, `pm`.`requires_verification` AS `requires_verification`, concat(`cp`.`first_name`,' ',`cp`.`last_name`) AS `client_name`, `ca`.`username` AS `client_email`, `dr`.`created_at` AS `request_date`, `dr`.`approved_at` AS `approved_at`, `dr`.`payment_status` AS `payment_status`, `pt`.`transaction_id` AS `transaction_id`, `pt`.`amount` AS `payment_amount`, `pt`.`status` AS `transaction_status`, `pt`.`verified_by` AS `verified_by`, `pt`.`verified_at` AS `verified_at`, `pt`.`receipt_number` AS `receipt_number`, concat(`ap`.`first_name`,' ',`ap`.`last_name`) AS `verified_by_name` FROM ((((((((`document_requests` `dr` join `request_status` `rs` on(`dr`.`status_id` = `rs`.`id`)) join `document_types` `dt` on(`dr`.`document_type_id` = `dt`.`id`)) join `payment_methods` `pm` on(`dr`.`payment_method_id` = `pm`.`id`)) join `client_accounts` `ca` on(`dr`.`client_id` = `ca`.`id`)) join `client_profiles` `cp` on(`ca`.`id` = `cp`.`account_id`)) left join `payment_transactions` `pt` on(`dr`.`id` = `pt`.`request_id`)) left join `admin_employee_accounts` `admin` on(`pt`.`verified_by` = `admin`.`id`)) left join `admin_employee_profiles` `ap` on(`admin`.`id` = `ap`.`account_id`)) WHERE `dr`.`status_id` = 4 AND `pm`.`requires_verification` = 1 AND (`dr`.`payment_status` = 'pending' OR `dr`.`payment_status` is null) ORDER BY `dr`.`created_at` ASC ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_employee_accounts`
--
ALTER TABLE `admin_employee_accounts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_role` (`role`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `admin_employee_profiles`
--
ALTER TABLE `admin_employee_profiles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `employee_id` (`employee_id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `account_id` (`account_id`),
  ADD KEY `idx_employee_id` (`employee_id`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_full_name` (`last_name`,`first_name`);

--
-- Indexes for table `audit_logs`
--
ALTER TABLE `audit_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `barangay_clearance_applications`
--
ALTER TABLE `barangay_clearance_applications`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `request_id` (`request_id`),
  ADD KEY `idx_voter_registration` (`voter_registration_number`);

--
-- Indexes for table `cedula_applications`
--
ALTER TABLE `cedula_applications`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `request_id` (`request_id`),
  ADD KEY `idx_tin_number` (`tin_number`),
  ADD KEY `idx_occupation` (`occupation`);

--
-- Indexes for table `civil_status`
--
ALTER TABLE `civil_status`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `status_name` (`status_name`);

--
-- Indexes for table `client_accounts`
--
ALTER TABLE `client_accounts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `client_profiles`
--
ALTER TABLE `client_profiles`
  ADD PRIMARY KEY (`id`),
  ADD KEY `account_id` (`account_id`),
  ADD KEY `civil_status_id` (`civil_status_id`),
  ADD KEY `verified_by` (`verified_by`),
  ADD KEY `idx_full_name` (`last_name`,`first_name`),
  ADD KEY `idx_birth_date` (`birth_date`),
  ADD KEY `idx_barangay` (`barangay`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_phone` (`phone_number`);

--
-- Indexes for table `document_requests`
--
ALTER TABLE `document_requests`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `request_number` (`request_number`),
  ADD KEY `purpose_category_id` (`purpose_category_id`),
  ADD KEY `processed_by` (`processed_by`),
  ADD KEY `approved_by` (`approved_by`),
  ADD KEY `idx_request_number` (`request_number`),
  ADD KEY `idx_client_id` (`client_id`),
  ADD KEY `idx_document_type` (`document_type_id`),
  ADD KEY `idx_status` (`status_id`),
  ADD KEY `idx_payment_status` (`payment_status`),
  ADD KEY `idx_payment_method` (`payment_method_id`),
  ADD KEY `idx_requested_at` (`requested_at`);

--
-- Indexes for table `document_types`
--
ALTER TABLE `document_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `type_name` (`type_name`);

--
-- Indexes for table `generated_documents`
--
ALTER TABLE `generated_documents`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `document_number` (`document_number`),
  ADD KEY `request_id` (`request_id`),
  ADD KEY `issued_by` (`issued_by`),
  ADD KEY `idx_document_number` (`document_number`),
  ADD KEY `idx_verification_code` (`verification_code`),
  ADD KEY `idx_issued_date` (`issued_date`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_notifications` (`user_id`,`user_type`,`created_at`),
  ADD KEY `idx_unread_notifications` (`user_type`,`is_read`,`created_at`),
  ADD KEY `idx_notification_type` (`type`,`created_at`),
  ADD KEY `idx_priority` (`priority`,`created_at`),
  ADD KEY `idx_notifications_user_unread` (`user_id`,`user_type`,`is_read`,`created_at`),
  ADD KEY `idx_notifications_broadcast` (`user_type`,`is_read`,`created_at`);

--
-- Indexes for table `otps`
--
ALTER TABLE `otps`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_email_purpose` (`email`,`purpose`),
  ADD KEY `idx_otp_code` (`otp_code`),
  ADD KEY `idx_expires_at` (`expires_at`);

--
-- Indexes for table `payment_methods`
--
ALTER TABLE `payment_methods`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `method_name` (`method_name`),
  ADD UNIQUE KEY `method_code` (`method_code`);

--
-- Indexes for table `payment_transactions`
--
ALTER TABLE `payment_transactions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `transaction_id` (`transaction_id`),
  ADD KEY `payment_method_id` (`payment_method_id`),
  ADD KEY `idx_transaction_id` (`transaction_id`),
  ADD KEY `idx_external_transaction_id` (`external_transaction_id`),
  ADD KEY `idx_paymongo_payment_intent` (`paymongo_payment_intent_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_request_id` (`request_id`),
  ADD KEY `idx_initiated_at` (`initiated_at`),
  ADD KEY `idx_verified_by` (`verified_by`),
  ADD KEY `idx_verified_at` (`verified_at`),
  ADD KEY `idx_receipt_number` (`receipt_number`);

--
-- Indexes for table `payment_verifications`
--
ALTER TABLE `payment_verifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `request_id` (`request_id`),
  ADD KEY `payment_method_id` (`payment_method_id`),
  ADD KEY `verified_by` (`verified_by`),
  ADD KEY `idx_verified_at` (`verified_at`);

--
-- Indexes for table `payment_webhooks`
--
ALTER TABLE `payment_webhooks`
  ADD PRIMARY KEY (`id`),
  ADD KEY `payment_transaction_id` (`payment_transaction_id`),
  ADD KEY `idx_webhook_id` (`webhook_id`),
  ADD KEY `idx_event_type` (`event_type`),
  ADD KEY `idx_transaction_id` (`transaction_id`),
  ADD KEY `idx_processed` (`processed`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `pickup_schedules`
--
ALTER TABLE `pickup_schedules`
  ADD PRIMARY KEY (`id`),
  ADD KEY `request_id` (`request_id`),
  ADD KEY `scheduled_by` (`scheduled_by`),
  ADD KEY `idx_scheduled_date` (`scheduled_date`),
  ADD KEY `idx_actual_pickup` (`actual_pickup_at`);

--
-- Indexes for table `purpose_categories`
--
ALTER TABLE `purpose_categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `request_status`
--
ALTER TABLE `request_status`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `status_name` (`status_name`);

--
-- Indexes for table `request_status_history`
--
ALTER TABLE `request_status_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `old_status_id` (`old_status_id`),
  ADD KEY `new_status_id` (`new_status_id`),
  ADD KEY `changed_by` (`changed_by`),
  ADD KEY `idx_request_id` (`request_id`),
  ADD KEY `idx_changed_at` (`changed_at`);

--
-- Indexes for table `supporting_documents`
--
ALTER TABLE `supporting_documents`
  ADD PRIMARY KEY (`id`),
  ADD KEY `uploaded_by` (`uploaded_by`),
  ADD KEY `verified_by` (`verified_by`),
  ADD KEY `idx_request_id` (`request_id`),
  ADD KEY `idx_document_type` (`document_type`);

--
-- Indexes for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `idx_setting_key` (`setting_key`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_employee_accounts`
--
ALTER TABLE `admin_employee_accounts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=33;

--
-- AUTO_INCREMENT for table `admin_employee_profiles`
--
ALTER TABLE `admin_employee_profiles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `audit_logs`
--
ALTER TABLE `audit_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `barangay_clearance_applications`
--
ALTER TABLE `barangay_clearance_applications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `cedula_applications`
--
ALTER TABLE `cedula_applications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `civil_status`
--
ALTER TABLE `civil_status`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `client_accounts`
--
ALTER TABLE `client_accounts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `client_profiles`
--
ALTER TABLE `client_profiles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `document_requests`
--
ALTER TABLE `document_requests`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `document_types`
--
ALTER TABLE `document_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `generated_documents`
--
ALTER TABLE `generated_documents`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `otps`
--
ALTER TABLE `otps`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=56;

--
-- AUTO_INCREMENT for table `payment_methods`
--
ALTER TABLE `payment_methods`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `payment_transactions`
--
ALTER TABLE `payment_transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payment_verifications`
--
ALTER TABLE `payment_verifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payment_webhooks`
--
ALTER TABLE `payment_webhooks`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pickup_schedules`
--
ALTER TABLE `pickup_schedules`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `purpose_categories`
--
ALTER TABLE `purpose_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `request_status`
--
ALTER TABLE `request_status`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `request_status_history`
--
ALTER TABLE `request_status_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT for table `supporting_documents`
--
ALTER TABLE `supporting_documents`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `system_settings`
--
ALTER TABLE `system_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `admin_employee_profiles`
--
ALTER TABLE `admin_employee_profiles`
  ADD CONSTRAINT `admin_employee_profiles_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `admin_employee_accounts` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `barangay_clearance_applications`
--
ALTER TABLE `barangay_clearance_applications`
  ADD CONSTRAINT `barangay_clearance_applications_ibfk_1` FOREIGN KEY (`request_id`) REFERENCES `document_requests` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `cedula_applications`
--
ALTER TABLE `cedula_applications`
  ADD CONSTRAINT `cedula_applications_ibfk_1` FOREIGN KEY (`request_id`) REFERENCES `document_requests` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `client_profiles`
--
ALTER TABLE `client_profiles`
  ADD CONSTRAINT `client_profiles_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `client_accounts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `client_profiles_ibfk_2` FOREIGN KEY (`civil_status_id`) REFERENCES `civil_status` (`id`),
  ADD CONSTRAINT `client_profiles_ibfk_3` FOREIGN KEY (`verified_by`) REFERENCES `admin_employee_accounts` (`id`);

--
-- Constraints for table `document_requests`
--
ALTER TABLE `document_requests`
  ADD CONSTRAINT `document_requests_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client_accounts` (`id`),
  ADD CONSTRAINT `document_requests_ibfk_2` FOREIGN KEY (`document_type_id`) REFERENCES `document_types` (`id`),
  ADD CONSTRAINT `document_requests_ibfk_3` FOREIGN KEY (`purpose_category_id`) REFERENCES `purpose_categories` (`id`),
  ADD CONSTRAINT `document_requests_ibfk_4` FOREIGN KEY (`status_id`) REFERENCES `request_status` (`id`),
  ADD CONSTRAINT `document_requests_ibfk_5` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods` (`id`),
  ADD CONSTRAINT `document_requests_ibfk_6` FOREIGN KEY (`processed_by`) REFERENCES `admin_employee_accounts` (`id`),
  ADD CONSTRAINT `document_requests_ibfk_7` FOREIGN KEY (`approved_by`) REFERENCES `admin_employee_accounts` (`id`);

--
-- Constraints for table `generated_documents`
--
ALTER TABLE `generated_documents`
  ADD CONSTRAINT `generated_documents_ibfk_1` FOREIGN KEY (`request_id`) REFERENCES `document_requests` (`id`),
  ADD CONSTRAINT `generated_documents_ibfk_2` FOREIGN KEY (`issued_by`) REFERENCES `admin_employee_accounts` (`id`);

--
-- Constraints for table `payment_transactions`
--
ALTER TABLE `payment_transactions`
  ADD CONSTRAINT `fk_payment_verified_by` FOREIGN KEY (`verified_by`) REFERENCES `admin_employee_accounts` (`id`),
  ADD CONSTRAINT `payment_transactions_ibfk_1` FOREIGN KEY (`request_id`) REFERENCES `document_requests` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payment_transactions_ibfk_2` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods` (`id`);

--
-- Constraints for table `payment_verifications`
--
ALTER TABLE `payment_verifications`
  ADD CONSTRAINT `payment_verifications_ibfk_1` FOREIGN KEY (`request_id`) REFERENCES `document_requests` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payment_verifications_ibfk_2` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods` (`id`),
  ADD CONSTRAINT `payment_verifications_ibfk_3` FOREIGN KEY (`verified_by`) REFERENCES `admin_employee_accounts` (`id`);

--
-- Constraints for table `payment_webhooks`
--
ALTER TABLE `payment_webhooks`
  ADD CONSTRAINT `payment_webhooks_ibfk_1` FOREIGN KEY (`payment_transaction_id`) REFERENCES `payment_transactions` (`id`);

--
-- Constraints for table `pickup_schedules`
--
ALTER TABLE `pickup_schedules`
  ADD CONSTRAINT `pickup_schedules_ibfk_1` FOREIGN KEY (`request_id`) REFERENCES `document_requests` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pickup_schedules_ibfk_2` FOREIGN KEY (`scheduled_by`) REFERENCES `admin_employee_accounts` (`id`);

--
-- Constraints for table `request_status_history`
--
ALTER TABLE `request_status_history`
  ADD CONSTRAINT `request_status_history_ibfk_1` FOREIGN KEY (`request_id`) REFERENCES `document_requests` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `request_status_history_ibfk_2` FOREIGN KEY (`old_status_id`) REFERENCES `request_status` (`id`),
  ADD CONSTRAINT `request_status_history_ibfk_3` FOREIGN KEY (`new_status_id`) REFERENCES `request_status` (`id`),
  ADD CONSTRAINT `request_status_history_ibfk_4` FOREIGN KEY (`changed_by`) REFERENCES `admin_employee_accounts` (`id`);

--
-- Constraints for table `supporting_documents`
--
ALTER TABLE `supporting_documents`
  ADD CONSTRAINT `supporting_documents_ibfk_1` FOREIGN KEY (`request_id`) REFERENCES `document_requests` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `supporting_documents_ibfk_2` FOREIGN KEY (`uploaded_by`) REFERENCES `client_accounts` (`id`),
  ADD CONSTRAINT `supporting_documents_ibfk_3` FOREIGN KEY (`verified_by`) REFERENCES `admin_employee_accounts` (`id`);

--
-- Constraints for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD CONSTRAINT `system_settings_ibfk_1` FOREIGN KEY (`updated_by`) REFERENCES `admin_employee_accounts` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
