# Server Configuration
NODE_ENV=development
PORT=3000
FRONTEND_URL=http://localhost:3000

# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password_here
DB_NAME=barangay_management_system
DB_PORT=3306

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_complex
JWT_EXPIRE=30d

# Security
BCRYPT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# Email Configuration (Gmail SMTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_16_character_app_password
EMAIL_FROM_NAME=Barangay Management System
EMAIL_FROM_ADDRESS=<EMAIL>

# OTP Configuration
OTP_EXPIRY_MINUTES=10
OTP_LENGTH=6

# PayMongo Configuration
PAYMONGO_PUBLIC_KEY=pk_test_VnW6ygvKAx3JCnhmSPBFxyn
PAYMONGO_SECRET_KEY=sk_test_wi8qaYjt74YtvpUEeFKpZsg1
PAYMONGO_WEBHOOK_SECRET=whsec_your_webhook_secret_here
PAYMONGO_BASE_URL=https://api.paymongo.com/v1
ENABLE_ONLINE_PAYMENTS=true
PAYMENT_TIMEOUT_MINUTES=30
