# 🚀 Complete Payment Workflow Setup Guide

## 📋 Status Flow Overview

```
1. pending → Customer submits request
2. payment_pending → Request approved, waiting for payment
3. payment_confirmed → Payment received (PayMongo webhook)
4. under_review → <PERSON>min reviews the request
5. approved → <PERSON>min approves the request
6. processing → Document is being prepared
7. ready_for_pickup → Document ready for collection
8. completed → Request completed successfully
```

## 🔧 Setup Instructions

### Step 1: PayMongo Webhook Setup

#### For Development (Local Testing):
```bash
# 1. Install ngrok globally
npm install -g ngrok

# 2. Start your backend server
npm start

# 3. In another terminal, expose your local server
ngrok http 7000

# 4. Copy the HTTPS URL (e.g., https://abc123.ngrok.io)

# 5. Update your .env file
echo "WEBHOOK_URL=https://abc123.ngrok.io/api/payments/webhook" >> .env

# 6. Run the webhook setup script
node setup_paymongo_webhook.js
```

#### For Production:
```bash
# Update .env with your production URL
WEBHOOK_URL=https://yourdomain.com/api/payments/webhook

# Run the webhook setup script
node setup_paymongo_webhook.js
```

### Step 2: Test the Complete Workflow

```bash
# Run the automated workflow test
node test_payment_workflow.js
```

## 🔄 Manual Testing Steps

### 1. Customer Payment Process
1. **Login as client** → `revo4438` / `password123`
2. **Submit a document request** → Barangay Clearance
3. **Choose PayMongo payment** → Bank Transfer
4. **Complete payment** → Click "Pay Now" and pay via PayMongo
5. **Webhook triggers** → Status automatically changes to `payment_confirmed`

### 2. Admin Review Process
1. **Login as admin** → `admin` / `admin123`
2. **Go to Admin Requests** → View all requests
3. **Find paid request** → Status should be `payment_confirmed`
4. **Review request** → Change status to `under_review`
5. **Approve request** → Change status to `approved`
6. **Process document** → Change status to `processing`
7. **Mark ready** → Change status to `ready_for_pickup`
8. **Complete request** → Change status to `completed`

## 🎯 Status Transition Rules

### Automatic Transitions (System):
- `payment_pending` → `payment_confirmed` (PayMongo webhook)
- `payment_failed` → `payment_pending` (Failed payment retry)

### Manual Transitions (Admin):
- `payment_confirmed` → `under_review` → `approved` → `processing` → `ready_for_pickup` → `completed`
- Any status → `rejected` (with reason)
- Any status → `cancelled` (client request)

## 🔍 Monitoring & Debugging

### Check Payment Status:
```sql
SELECT 
  id, request_number, status_name, payment_status, paid_at
FROM document_requests dr
JOIN request_status rs ON dr.status_id = rs.id
WHERE client_id = 12
ORDER BY id DESC;
```

### Check Webhook Logs:
```sql
SELECT * FROM payment_webhooks 
ORDER BY created_at DESC 
LIMIT 10;
```

### Check Payment Transactions:
```sql
SELECT * FROM payment_transactions 
WHERE request_id = [REQUEST_ID]
ORDER BY created_at DESC;
```

## 🚨 Troubleshooting

### Webhook Not Working:
1. Check if webhook URL is accessible from internet
2. Verify PayMongo webhook secret in .env
3. Check server logs for webhook errors
4. Test webhook manually with curl

### Payment Not Updating Status:
1. Check if webhook endpoint is registered with PayMongo
2. Verify webhook signature validation
3. Check database for webhook records
4. Review payment transaction logs

### Status Transitions Blocked:
1. Verify admin permissions
2. Check status transition rules in code
3. Ensure required fields are provided
4. Review status history for conflicts

## 📊 Key Files Modified

- `paymentController.js` → Webhook handling & status updates
- `AdminRequests.vue` → Admin workflow interface
- `MyRequests.vue` → Client payment interface
- `paymongoService.js` → PayMongo API integration

## 🎉 Success Indicators

✅ **Payment Link Creation** → No 400 errors, shows correct amount
✅ **PayMongo Payment** → Customer can complete payment
✅ **Webhook Processing** → Status changes to `payment_confirmed`
✅ **Admin Workflow** → Can transition through all statuses
✅ **Status History** → All changes logged with reasons
✅ **Email Notifications** → Sent at key status changes

## 🔗 Useful Commands

```bash
# Test webhook setup
node setup_paymongo_webhook.js --dev-help

# Run complete workflow test
node test_payment_workflow.js

# Check database status
node -e "
const { executeQuery } = require('./src/config/database');
executeQuery('SELECT * FROM request_status ORDER BY id')
  .then(console.table)
  .then(() => process.exit(0));
"

# Restart server with logs
npm start | tee server.log
```
