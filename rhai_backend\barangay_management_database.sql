-- =====================================================
-- BARANGAY MANAGEMENT SYSTEM - COMPLETE DATABASE SCHEMA
-- Version: 2.0 with PayMongo Integration
-- =====================================================

-- Create database
CREATE DATABASE IF NOT EXISTS barangay_management_system;
USE barangay_management_system;

-- =====================================================
-- REFERENCE/LOOKUP TABLES
-- =====================================================

-- Civil Status lookup table
CREATE TABLE civil_status (
    id INT PRIMARY KEY AUTO_INCREMENT,
    status_name VARCHAR(20) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Document types lookup table
CREATE TABLE document_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    type_name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    base_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Request status lookup table
CREATE TABLE request_status (
    id INT PRIMARY KEY AUTO_INCREMENT,
    status_name VARCHAR(30) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Purpose categories lookup table
CREATE TABLE purpose_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payment methods lookup table (PayMongo Integration)
CREATE TABLE payment_methods (
    id INT PRIMARY KEY AUTO_INCREMENT,
    method_name VARCHAR(50) NOT NULL UNIQUE,
    method_code VARCHAR(20) NOT NULL UNIQUE,
    description TEXT,
    is_online BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    processing_fee_percentage DECIMAL(5,2) DEFAULT 0.00,
    processing_fee_fixed DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- USER MANAGEMENT TABLES
-- =====================================================

-- Admin and Employee accounts
CREATE TABLE admin_employee_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'employee') NOT NULL,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    last_login TIMESTAMP NULL,
    password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status)
);

-- Admin and Employee profiles
CREATE TABLE admin_employee_profiles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_id INT NOT NULL,
    employee_id VARCHAR(20) UNIQUE,
    first_name VARCHAR(100) NOT NULL,
    middle_name VARCHAR(100),
    last_name VARCHAR(100) NOT NULL,
    suffix VARCHAR(10),
    phone_number VARCHAR(20),
    email VARCHAR(100) UNIQUE,
    profile_picture VARCHAR(255),
    position VARCHAR(100),
    department VARCHAR(100),
    hire_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (account_id) REFERENCES admin_employee_accounts(id) ON DELETE CASCADE,
    INDEX idx_employee_id (employee_id),
    INDEX idx_email (email),
    INDEX idx_full_name (last_name, first_name)
);

-- Client accounts
CREATE TABLE client_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    status ENUM('active', 'inactive', 'suspended', 'pending_verification') DEFAULT 'pending_verification',
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_status (status)
);

-- Client profiles
CREATE TABLE client_profiles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_id INT NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    middle_name VARCHAR(100),
    last_name VARCHAR(100) NOT NULL,
    suffix VARCHAR(10),
    birth_date DATE NOT NULL,
    gender ENUM('male', 'female') NOT NULL,
    civil_status_id INT NOT NULL,
    nationality VARCHAR(50) DEFAULT 'Filipino',
    phone_number VARCHAR(20) NOT NULL,
    email VARCHAR(100),
    
    -- Address information
    house_number VARCHAR(20),
    street VARCHAR(100),
    subdivision VARCHAR(100),
    barangay VARCHAR(100) NOT NULL,
    city_municipality VARCHAR(100) NOT NULL,
    province VARCHAR(100) NOT NULL,
    postal_code VARCHAR(10),
    
    -- Residency information
    years_of_residency INT,
    months_of_residency INT,
    
    -- Profile picture and verification
    profile_picture VARCHAR(255),
    is_verified BOOLEAN DEFAULT FALSE,
    verified_by INT NULL,
    verified_at TIMESTAMP NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (account_id) REFERENCES client_accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (civil_status_id) REFERENCES civil_status(id),
    FOREIGN KEY (verified_by) REFERENCES admin_employee_accounts(id),
    
    INDEX idx_full_name (last_name, first_name),
    INDEX idx_birth_date (birth_date),
    INDEX idx_barangay (barangay),
    INDEX idx_email (email),
    INDEX idx_phone (phone_number)
);

-- =====================================================
-- DOCUMENT REQUEST TABLES (WITH PAYMENT INTEGRATION)
-- =====================================================

-- Main document requests table
CREATE TABLE document_requests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    request_number VARCHAR(50) NOT NULL UNIQUE,
    client_id INT NOT NULL,
    document_type_id INT NOT NULL,
    purpose_category_id INT NOT NULL,
    purpose_details TEXT NOT NULL,
    
    -- Request status and processing
    status_id INT NOT NULL,
    priority ENUM('normal', 'urgent') DEFAULT 'normal',
    
    -- Processing information
    processed_by INT NULL,
    approved_by INT NULL,
    processed_at TIMESTAMP NULL,
    approved_at TIMESTAMP NULL,
    
    -- Fees and payment (PayMongo Integration)
    base_fee DECIMAL(10,2) NOT NULL,
    additional_fees DECIMAL(10,2) DEFAULT 0.00,
    processing_fee DECIMAL(10,2) DEFAULT 0.00,
    payment_method_id INT NULL,
    payment_status ENUM('pending', 'processing', 'paid', 'failed', 'refunded', 'cancelled') DEFAULT 'pending',
    payment_reference VARCHAR(100),
    payment_provider_reference VARCHAR(100),
    paid_at TIMESTAMP NULL,
    
    -- Delivery information
    delivery_method ENUM('pickup', 'delivery') DEFAULT 'pickup',
    delivery_address TEXT,
    delivery_fee DECIMAL(10,2) DEFAULT 0.00,
    
    -- Timestamps
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    target_completion_date DATE,
    completed_at TIMESTAMP NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_id) REFERENCES client_accounts(id),
    FOREIGN KEY (document_type_id) REFERENCES document_types(id),
    FOREIGN KEY (purpose_category_id) REFERENCES purpose_categories(id),
    FOREIGN KEY (status_id) REFERENCES request_status(id),
    FOREIGN KEY (payment_method_id) REFERENCES payment_methods(id),
    FOREIGN KEY (processed_by) REFERENCES admin_employee_accounts(id),
    FOREIGN KEY (approved_by) REFERENCES admin_employee_accounts(id),
    
    INDEX idx_request_number (request_number),
    INDEX idx_client_id (client_id),
    INDEX idx_document_type (document_type_id),
    INDEX idx_status (status_id),
    INDEX idx_payment_status (payment_status),
    INDEX idx_payment_method (payment_method_id),
    INDEX idx_requested_at (requested_at)
);

-- =====================================================
-- PAYMENT TRANSACTIONS TABLE (PayMongo Integration)
-- =====================================================

-- Payment transactions for detailed payment tracking
CREATE TABLE payment_transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    request_id INT NOT NULL,
    payment_method_id INT NOT NULL,

    -- Transaction details
    transaction_id VARCHAR(100) NOT NULL UNIQUE,
    external_transaction_id VARCHAR(100),

    -- PayMongo specific fields
    paymongo_payment_intent_id VARCHAR(100),
    paymongo_payment_method_id VARCHAR(100),
    paymongo_source_id VARCHAR(100),

    -- Amount details
    amount DECIMAL(10,2) NOT NULL,
    processing_fee DECIMAL(10,2) DEFAULT 0.00,
    net_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'PHP',

    -- Transaction status
    status ENUM('pending', 'processing', 'succeeded', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
    failure_reason TEXT,

    -- Payment details
    payment_description TEXT,
    customer_email VARCHAR(100),
    customer_phone VARCHAR(20),

    -- Webhook and callback data
    webhook_data JSON,
    callback_url VARCHAR(500),
    success_url VARCHAR(500),
    cancel_url VARCHAR(500),

    -- Timestamps
    initiated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (request_id) REFERENCES document_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (payment_method_id) REFERENCES payment_methods(id),

    INDEX idx_transaction_id (transaction_id),
    INDEX idx_external_transaction_id (external_transaction_id),
    INDEX idx_paymongo_payment_intent (paymongo_payment_intent_id),
    INDEX idx_status (status),
    INDEX idx_request_id (request_id),
    INDEX idx_initiated_at (initiated_at)
);

-- =====================================================
-- DOCUMENT-SPECIFIC TABLES
-- =====================================================

-- Cedula applications (extends document_requests)
CREATE TABLE cedula_applications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    request_id INT NOT NULL UNIQUE,

    -- Employment/Occupation information
    occupation VARCHAR(100),
    employer_name VARCHAR(200),
    employer_address TEXT,
    monthly_income DECIMAL(12,2),
    annual_income DECIMAL(12,2),

    -- Business information (if self-employed)
    business_name VARCHAR(200),
    business_address TEXT,
    business_type VARCHAR(100),
    business_income DECIMAL(12,2),

    -- Property information
    has_real_property BOOLEAN DEFAULT FALSE,
    property_assessed_value DECIMAL(15,2),
    property_location TEXT,

    -- Tax information
    tin_number VARCHAR(20),
    previous_ctc_number VARCHAR(50),
    previous_ctc_date_issued DATE,
    previous_ctc_place_issued VARCHAR(100),

    -- Computed tax amount
    computed_tax DECIMAL(10,2),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (request_id) REFERENCES document_requests(id) ON DELETE CASCADE,
    INDEX idx_tin_number (tin_number),
    INDEX idx_occupation (occupation)
);

-- Barangay clearance applications (extends document_requests)
CREATE TABLE barangay_clearance_applications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    request_id INT NOT NULL UNIQUE,

    -- Character reference information
    has_pending_cases BOOLEAN DEFAULT FALSE,
    pending_cases_details TEXT,

    -- Additional verification
    voter_registration_number VARCHAR(50),
    precinct_number VARCHAR(20),

    -- Emergency contact
    emergency_contact_name VARCHAR(200),
    emergency_contact_relationship VARCHAR(50),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_address TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (request_id) REFERENCES document_requests(id) ON DELETE CASCADE,
    INDEX idx_voter_registration (voter_registration_number)
);

-- =====================================================
-- SUPPORTING DOCUMENTS TABLES
-- =====================================================

-- Uploaded supporting documents
CREATE TABLE supporting_documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    request_id INT NOT NULL,
    document_name VARCHAR(200) NOT NULL,
    document_type VARCHAR(100) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT,
    mime_type VARCHAR(100),
    uploaded_by INT NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    verified_by INT NULL,
    verified_at TIMESTAMP NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (request_id) REFERENCES document_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES client_accounts(id),
    FOREIGN KEY (verified_by) REFERENCES admin_employee_accounts(id),

    INDEX idx_request_id (request_id),
    INDEX idx_document_type (document_type)
);

-- Generated certificates/documents
CREATE TABLE generated_documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    request_id INT NOT NULL,
    document_number VARCHAR(100) NOT NULL UNIQUE,
    document_path VARCHAR(500),
    qr_code_data TEXT,

    -- Document validity
    issued_date DATE NOT NULL,
    expiry_date DATE,
    is_valid BOOLEAN DEFAULT TRUE,

    -- Issuing authority
    issued_by INT NOT NULL,
    authorized_signatory VARCHAR(200),

    -- Security features
    security_hash VARCHAR(255),
    verification_code VARCHAR(50),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (request_id) REFERENCES document_requests(id),
    FOREIGN KEY (issued_by) REFERENCES admin_employee_accounts(id),

    INDEX idx_document_number (document_number),
    INDEX idx_verification_code (verification_code),
    INDEX idx_issued_date (issued_date)
);

-- =====================================================
-- AUDIT AND LOGGING TABLES
-- =====================================================

-- Request status history
CREATE TABLE request_status_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    request_id INT NOT NULL,
    old_status_id INT,
    new_status_id INT NOT NULL,
    changed_by INT NOT NULL,
    change_reason TEXT,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (request_id) REFERENCES document_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (old_status_id) REFERENCES request_status(id),
    FOREIGN KEY (new_status_id) REFERENCES request_status(id),
    FOREIGN KEY (changed_by) REFERENCES admin_employee_accounts(id),

    INDEX idx_request_id (request_id),
    INDEX idx_changed_at (changed_at)
);

-- System audit log
CREATE TABLE audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    user_type ENUM('admin', 'employee', 'client') NOT NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100),
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- SYSTEM CONFIGURATION TABLES
-- =====================================================

-- System settings
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (updated_by) REFERENCES admin_employee_accounts(id),
    INDEX idx_setting_key (setting_key)
);

-- =====================================================
-- INSERT INITIAL DATA
-- =====================================================

-- Insert civil status options
INSERT INTO civil_status (status_name) VALUES
('Single'), ('Married'), ('Divorced'), ('Widowed'), ('Separated');

-- Insert document types
INSERT INTO document_types (type_name, description, base_fee) VALUES
('Cedula', 'Community Tax Certificate', 30.00),
('Barangay Clearance', 'Certificate of Good Moral Character', 50.00);

-- Insert request status options
INSERT INTO request_status (status_name, description) VALUES
('pending', 'Request submitted and pending review'),
('under_review', 'Request is being reviewed by staff'),
('additional_info_required', 'Additional information or documents needed'),
('approved', 'Request approved and ready for processing'),
('processing', 'Document is being prepared'),
('ready_for_pickup', 'Document is ready for pickup'),
('completed', 'Request completed successfully'),
('cancelled', 'Request cancelled by client'),
('rejected', 'Request rejected by authority');

-- Insert purpose categories
INSERT INTO purpose_categories (category_name, description) VALUES
('Employment', 'For job application or employment purposes'),
('Business Registration', 'For business license or registration'),
('Travel/Visa', 'For travel documents or visa application'),
('School Enrollment', 'For school admission or enrollment'),
('Government Transaction', 'For other government-related transactions'),
('Bank Account', 'For opening bank accounts'),
('Insurance', 'For insurance applications'),
('Legal Proceedings', 'For court or legal matters'),
('Loan Application', 'For loan or credit applications'),
('Other', 'Other purposes not listed above');

-- Insert payment methods (Cash and PayMongo options)
INSERT INTO payment_methods (method_name, method_code, description, is_online, is_active, processing_fee_percentage, processing_fee_fixed) VALUES
('Cash Payment', 'CASH', 'Pay in cash at barangay office', FALSE, TRUE, 0.00, 0.00),
('PayMongo - Credit/Debit Card', 'PAYMONGO_CARD', 'Pay online using credit or debit card via PayMongo', TRUE, TRUE, 3.50, 15.00),
('PayMongo - GCash', 'PAYMONGO_GCASH', 'Pay using GCash via PayMongo', TRUE, TRUE, 2.00, 10.00),
('PayMongo - GrabPay', 'PAYMONGO_GRABPAY', 'Pay using GrabPay via PayMongo', TRUE, TRUE, 2.00, 10.00),
('PayMongo - PayMaya', 'PAYMONGO_PAYMAYA', 'Pay using PayMaya via PayMongo', TRUE, TRUE, 2.00, 10.00),
('PayMongo - Bank Transfer', 'PAYMONGO_BANK', 'Pay via online bank transfer through PayMongo', TRUE, TRUE, 1.50, 5.00);

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('system_name', 'Barangay Management System', 'string', 'Name of the system', TRUE),
('barangay_name', 'Barangay Sample', 'string', 'Name of the barangay', TRUE),
('barangay_address', 'Sample Address, City, Province', 'string', 'Complete address of barangay', TRUE),
('barangay_contact', '+63 123 456 7890', 'string', 'Contact number of barangay', TRUE),
('barangay_email', '<EMAIL>', 'string', 'Email address of barangay', TRUE),
('cedula_base_fee', '30.00', 'number', 'Base fee for cedula', FALSE),
('clearance_base_fee', '50.00', 'number', 'Base fee for barangay clearance', FALSE),
('processing_days_cedula', '3', 'number', 'Standard processing days for cedula', TRUE),
('processing_days_clearance', '5', 'number', 'Standard processing days for clearance', TRUE),
('max_file_upload_size', '5242880', 'number', 'Maximum file upload size in bytes (5MB)', FALSE),
('allowed_file_types', '["jpg","jpeg","png","pdf","doc","docx"]', 'json', 'Allowed file types for upload', FALSE),
('paymongo_public_key', 'pk_test_your_public_key_here', 'string', 'PayMongo public key for frontend', FALSE),
('paymongo_secret_key', 'sk_test_your_secret_key_here', 'string', 'PayMongo secret key for backend', FALSE),
('paymongo_webhook_secret', 'whsec_your_webhook_secret_here', 'string', 'PayMongo webhook secret for verification', FALSE),
('enable_online_payments', 'true', 'boolean', 'Enable or disable online payment options', TRUE),
('payment_timeout_minutes', '30', 'number', 'Payment session timeout in minutes', FALSE);

-- Insert sample admin account (password: admin123)
INSERT INTO admin_employee_accounts (username, password_hash, role, status) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active');

-- Insert admin profile
INSERT INTO admin_employee_profiles (account_id, employee_id, first_name, middle_name, last_name, phone_number, email, position, department, hire_date) VALUES
(1, 'EMP-001', 'Juan', 'Santos', 'Cruz', '+63 ************', '<EMAIL>', 'Barangay Captain', 'Administration', '2020-01-15');
