const { executeQuery } = require('./src/config/database');

async function checkAdminStructure() {
  try {
    console.log('🔍 Checking admin table structure...');
    
    // Check admin_employee_accounts structure
    const structure = await executeQuery('DESCRIBE admin_employee_accounts');
    console.log('📋 admin_employee_accounts structure:');
    console.table(structure);
    
    // Get sample admin data
    const admins = await executeQuery('SELECT * FROM admin_employee_accounts WHERE status = "active" LIMIT 2');
    console.log('\n👥 Sample admin data:');
    console.table(admins);
    
    // Check admin_employee_profiles structure
    const profileStructure = await executeQuery('DESCRIBE admin_employee_profiles');
    console.log('\n📋 admin_employee_profiles structure:');
    console.table(profileStructure);
    
    // Get sample profile data
    const profiles = await executeQuery('SELECT * FROM admin_employee_profiles LIMIT 2');
    console.log('\n👤 Sample profile data:');
    console.table(profiles);
    
  } catch (error) {
    console.error('❌ Error checking admin structure:', error.message);
  }
}

if (require.main === module) {
  checkAdminStructure();
}

module.exports = { checkAdminStructure };
