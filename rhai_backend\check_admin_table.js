const { executeQuery } = require('./src/config/database');

async function checkAdminTable() {
  try {
    console.log('🔍 Checking admin table...');
    
    // Check all tables
    const tables = await executeQuery('SHOW TABLES');
    console.log('📋 All tables:');
    tables.forEach(table => {
      const tableName = Object.values(table)[0];
      console.log(`   - ${tableName}`);
    });
    
    // Find admin table
    const adminTables = tables.filter(table => {
      const tableName = Object.values(table)[0];
      return tableName.toLowerCase().includes('admin');
    });
    
    console.log('\n👥 Admin-related tables:');
    if (adminTables.length === 0) {
      console.log('   ❌ No admin tables found');
    } else {
      adminTables.forEach(table => {
        const tableName = Object.values(table)[0];
        console.log(`   ✅ ${tableName}`);
      });
    }
    
    // Try to query the correct admin table
    const possibleAdminTables = ['admin_accounts', 'admins', 'admin_users', 'users'];
    
    for (const tableName of possibleAdminTables) {
      try {
        const result = await executeQuery(`SELECT COUNT(*) as count FROM ${tableName} WHERE status = 'active'`);
        console.log(`\n✅ Found admin table: ${tableName}`);
        console.log(`   👥 Active admins: ${result[0].count}`);
        
        // Get admin details
        const admins = await executeQuery(`SELECT id, username, email FROM ${tableName} WHERE status = 'active' LIMIT 5`);
        console.log('   📋 Admin details:');
        admins.forEach(admin => {
          console.log(`      - ID: ${admin.id}, Username: ${admin.username}, Email: ${admin.email || 'N/A'}`);
        });
        
        return tableName;
      } catch (error) {
        // Table doesn't exist, continue
      }
    }
    
    console.log('\n❌ No valid admin table found');
    return null;
    
  } catch (error) {
    console.error('❌ Error checking admin table:', error.message);
  }
}

if (require.main === module) {
  checkAdminTable();
}

module.exports = { checkAdminTable };
