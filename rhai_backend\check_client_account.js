const { executeQuery } = require('./src/config/database');

async function checkClientAccount() {
  try {
    console.log('🔍 Checking client account...');
    
    // Check client accounts
    const accounts = await executeQuery(`
      SELECT ca.id, ca.username, cp.email, cp.first_name, cp.last_name
      FROM client_accounts ca
      LEFT JOIN client_profiles cp ON ca.id = cp.account_id
      WHERE ca.username = 'revo4438'
      LIMIT 1
    `);
    
    if (accounts.length === 0) {
      console.log('❌ No client account found with username "revo4438"');
      
      // Show available accounts
      const allAccounts = await executeQuery(`
        SELECT ca.id, ca.username, cp.email, cp.first_name, cp.last_name
        FROM client_accounts ca
        LEFT JOIN client_profiles cp ON ca.id = cp.account_id
        LIMIT 5
      `);
      
      console.log('\n📋 Available client accounts:');
      allAccounts.forEach(acc => {
        console.log(`   ID: ${acc.id}, Username: ${acc.username}, Email: ${acc.email || 'N/A'}, Name: ${acc.first_name || 'N/A'} ${acc.last_name || 'N/A'}`);
      });
      
    } else {
      const account = accounts[0];
      console.log('✅ Client account found:');
      console.log(`   ID: ${account.id}`);
      console.log(`   Username: ${account.username}`);
      console.log(`   Email: ${account.email || 'N/A'}`);
      console.log(`   Name: ${account.first_name || 'N/A'} ${account.last_name || 'N/A'}`);
      
      // Check their document requests
      const requests = await executeQuery(`
        SELECT id, request_number, status_id, payment_status, base_fee, processing_fee
        FROM document_requests 
        WHERE client_id = ?
        ORDER BY id DESC
        LIMIT 3
      `, [account.id]);
      
      console.log('\n📄 Recent document requests:');
      if (requests.length === 0) {
        console.log('   ❌ No document requests found');
      } else {
        requests.forEach(req => {
          const totalFee = parseFloat(req.base_fee) + parseFloat(req.processing_fee);
          console.log(`   Request #${req.id} (${req.request_number}): Status ${req.status_id}, Payment: ${req.payment_status}, Fee: ₱${totalFee.toFixed(2)}`);
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Error checking client account:', error.message);
  }
}

if (require.main === module) {
  checkClientAccount();
}

module.exports = { checkClientAccount };
