const { executeQuery } = require('./src/config/database');

async function checkNotificationsTable() {
  try {
    console.log('🔍 Checking notifications table structure...');
    
    // Check notifications table structure
    const structure = await executeQuery('DESCRIBE notifications');
    console.log('📋 notifications table structure:');
    structure.forEach(field => {
      console.log(`   ${field.Field} - ${field.Type} ${field.Null === 'YES' ? '(nullable)' : '(required)'}`);
    });
    
    // Get sample notification data
    const notifications = await executeQuery('SELECT * FROM notifications LIMIT 3');
    console.log('\n📨 Sample notification data:');
    if (notifications.length === 0) {
      console.log('   ❌ No notifications found');
    } else {
      console.table(notifications);
    }
    
  } catch (error) {
    console.error('❌ Error checking notifications table:', error.message);
  }
}

if (require.main === module) {
  checkNotificationsTable();
}

module.exports = { checkNotificationsTable };
