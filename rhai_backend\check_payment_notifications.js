const { executeQuery } = require('./src/config/database');

async function checkPaymentNotifications() {
  try {
    console.log('🔍 Checking payment notifications...');
    
    // Get latest payment notifications
    const notifications = await executeQuery(`
      SELECT * FROM notifications 
      WHERE type = 'payment_confirmed' 
      ORDER BY created_at DESC 
      LIMIT 3
    `);
    
    console.log('📨 Latest payment notifications:');
    if (notifications.length === 0) {
      console.log('   ❌ No payment notifications found');
    } else {
      notifications.forEach((notif, index) => {
        console.log(`\n📧 Notification #${index + 1}:`);
        console.log(`   ID: ${notif.id}`);
        console.log(`   Recipient: ${notif.recipient_type} #${notif.recipient_id}`);
        console.log(`   Title: ${notif.title}`);
        console.log(`   Message: ${notif.message}`);
        console.log(`   Priority: ${notif.priority}`);
        console.log(`   Read: ${notif.is_read ? 'Yes' : 'No'}`);
        console.log(`   Created: ${notif.created_at}`);
        
        if (notif.data) {
          try {
            const data = JSON.parse(notif.data);
            console.log(`   Data:`, data);
          } catch (e) {
            console.log(`   Data: ${notif.data}`);
          }
        }
      });
    }
    
    // Check document request status
    console.log('\n📄 Checking document request status...');
    const requests = await executeQuery(`
      SELECT id, request_number, status_id, payment_status, paid_at
      FROM document_requests 
      WHERE id IN (99, 100)
      ORDER BY id DESC
    `);
    
    console.log('📋 Document requests status:');
    requests.forEach(req => {
      console.log(`   Request #${req.id} (${req.request_number}): Status ${req.status_id}, Payment: ${req.payment_status}, Paid: ${req.paid_at || 'Not paid'}`);
    });
    
  } catch (error) {
    console.error('❌ Error checking notifications:', error.message);
  }
}

if (require.main === module) {
  checkPaymentNotifications();
}

module.exports = { checkPaymentNotifications };
