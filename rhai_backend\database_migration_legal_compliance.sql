-- =====================================================
-- DATABASE MIGRATION FOR LEGAL COMPLIANCE
-- Removes unnecessary fields and adds required fields
-- Based on Philippine Local Government Code research
-- =====================================================

USE barangay_management_system;

-- =====================================================
-- 1. BARANGAY CLEARANCE - REMOVE UNNECESSARY FIELDS
-- =====================================================

-- Check current structure
SELECT 'BEFORE: Barangay Clearance Applications Structure' as info;
DESCRIBE barangay_clearance_applications;

-- Remove unnecessary fields that violate Data Privacy Act
-- These fields are NOT required by Local Government Code
ALTER TABLE barangay_clearance_applications 
DROP COLUMN IF EXISTS voter_registration_number,
DROP COLUMN IF EXISTS precinct_number,
DROP COLUMN IF EXISTS emergency_contact_name,
DROP COLUMN IF EXISTS emergency_contact_relationship,
DROP COLUMN IF EXISTS emergency_contact_phone,
DROP COLUMN IF EXISTS emergency_contact_address;

SELECT 'AFTER: Barangay Clearance Applications Structure (Privacy Compliant)' as info;
DESCRIBE barangay_clearance_applications;

-- =====================================================
-- 2. CEDULA - ADD MISSING REQUIRED FIELDS
-- =====================================================

-- Check current structure
SELECT 'BEFORE: Cedula Applications Structure' as info;
DESCRIBE cedula_applications;

-- Add missing fields required by Community Tax Certificate law
ALTER TABLE cedula_applications 
ADD COLUMN IF NOT EXISTS has_personal_property BOOLEAN DEFAULT FALSE AFTER has_real_property,
ADD COLUMN IF NOT EXISTS personal_property_value DECIMAL(15,2) DEFAULT 0 AFTER has_personal_property,
ADD COLUMN IF NOT EXISTS business_gross_receipts DECIMAL(15,2) DEFAULT 0 AFTER business_income;

-- Update existing records to have default values
UPDATE cedula_applications 
SET has_personal_property = FALSE, 
    personal_property_value = 0,
    business_gross_receipts = COALESCE(business_income, 0)
WHERE has_personal_property IS NULL;

SELECT 'AFTER: Cedula Applications Structure (Legally Complete)' as info;
DESCRIBE cedula_applications;

-- =====================================================
-- 3. VERIFY CHANGES
-- =====================================================

-- Show final structures
SELECT 'FINAL: Barangay Clearance Applications' as info;
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'barangay_management_system' 
  AND TABLE_NAME = 'barangay_clearance_applications'
ORDER BY ORDINAL_POSITION;

SELECT 'FINAL: Cedula Applications' as info;
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'barangay_management_system' 
  AND TABLE_NAME = 'cedula_applications'
ORDER BY ORDINAL_POSITION;

-- =====================================================
-- 4. LEGAL COMPLIANCE SUMMARY
-- =====================================================

SELECT 'LEGAL COMPLIANCE ACHIEVED' as status,
       'Barangay Clearance: Privacy compliant, minimal data collection' as barangay_clearance,
       'Cedula: Complete tax declaration as required by law' as cedula,
       'Database now matches frontend legal requirements' as result;
