const { executeQuery } = require('./src/config/database');

async function debugPaymentAmounts() {
  try {
    console.log('🔍 Checking document_requests table...');
    
    // Check recent document requests
    const requests = await executeQuery(`
      SELECT id, base_fee, processing_fee, 
             (base_fee + processing_fee) as total_fee,
             document_type_id, payment_method_id, status_id
      FROM document_requests 
      ORDER BY id DESC 
      LIMIT 5
    `);
    
    console.log('📋 Recent Document Requests:');
    console.table(requests);
    
    // Check document types
    const docTypes = await executeQuery(`
      SELECT id, type_name, base_fee 
      FROM document_types 
      LIMIT 5
    `);
    
    console.log('📄 Document Types:');
    console.table(docTypes);
    
    // Check payment methods
    const paymentMethods = await executeQuery(`
      SELECT id, method_name, processing_fee_fixed, processing_fee_percentage 
      FROM payment_methods 
      LIMIT 5
    `);
    
    console.log('💳 Payment Methods:');
    console.table(paymentMethods);
    
    // Check payment transactions
    const transactions = await executeQuery(`
      SELECT id, request_id, amount, processing_fee, net_amount, status
      FROM payment_transactions 
      ORDER BY id DESC 
      LIMIT 5
    `);
    
    console.log('💰 Recent Payment Transactions:');
    console.table(transactions);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugPaymentAmounts();
