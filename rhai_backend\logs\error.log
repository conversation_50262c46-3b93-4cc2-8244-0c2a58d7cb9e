{"timestamp":"2025-06-19T07:40:59.567Z","level":"ERROR","message":"Failed to initialize email transporter:","error":"nodemailer.createTransporter is not a function"}
{"timestamp":"2025-06-19T09:01:43.747Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-19T09:01:43.754Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-19T09:33:00.384Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-19T09:33:00.395Z","level":"ERROR","message":"Client login failed","username":"albert44<PERSON>","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-19T09:33:24.344Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-19T09:33:24.347Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-19T10:40:48.392Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-19T10:40:48.411Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-19T10:41:18.288Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-19T10:41:18.294Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-19T10:41:24.456Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-19T10:41:24.458Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-19T10:42:24.863Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-19T10:42:24.865Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-19T10:42:32.497Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-19T10:42:32.499Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-19T10:46:03.565Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"Email already registered"}
{"timestamp":"2025-06-19T10:46:03.571Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"Email already registered","ip":"::1"}
{"timestamp":"2025-06-19T12:07:36.815Z","level":"ERROR","message":"Client account registration failed","username":"aldfff","error":"Email already registered"}
{"timestamp":"2025-06-19T12:07:36.820Z","level":"ERROR","message":"Client account registration failed","username":"aldfff","error":"Email already registered","ip":"::1"}
{"timestamp":"2025-06-19T12:35:12.558Z","level":"ERROR","message":"Client login failed","username":"fdgfdgfd","error":"Invalid username or password"}
{"timestamp":"2025-06-19T12:35:12.582Z","level":"ERROR","message":"Client login failed","username":"fdgfdgfd","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-22T11:11:43.445Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-22T11:11:43.451Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-22T11:11:55.858Z","level":"ERROR","message":"Client login failed","username":"albert12345","error":"Invalid username or password"}
{"timestamp":"2025-06-22T11:11:55.859Z","level":"ERROR","message":"Client login failed","username":"albert12345","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-22T11:11:56.528Z","level":"ERROR","message":"Client login failed","username":"albert12345","error":"Invalid username or password"}
{"timestamp":"2025-06-22T11:11:56.529Z","level":"ERROR","message":"Client login failed","username":"albert12345","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-22T11:11:57.098Z","level":"ERROR","message":"Client login failed","username":"albert12345","error":"Invalid username or password"}
{"timestamp":"2025-06-22T11:11:57.099Z","level":"ERROR","message":"Client login failed","username":"albert12345","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-22T11:11:57.277Z","level":"ERROR","message":"Client login failed","username":"albert12345","error":"Invalid username or password"}
{"timestamp":"2025-06-22T11:11:57.278Z","level":"ERROR","message":"Client login failed","username":"albert12345","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-22T12:49:24.158Z","level":"ERROR","message":"Failed to send OTP:","email":"<EMAIL>","purpose":"admin_registration","error":"ApiResponse.error is not a function","ip":"::1"}
{"timestamp":"2025-06-22T12:52:44.427Z","level":"ERROR","message":"Failed to send OTP:","email":"<EMAIL>","purpose":"admin_registration","error":"ApiResponse.error is not a function","ip":"::1"}
{"timestamp":"2025-06-22T12:58:09.060Z","level":"ERROR","message":"Failed to send OTP:","email":"<EMAIL>","purpose":"admin_registration","error":"ApiResponse.error is not a function","ip":"::1"}
{"timestamp":"2025-06-22T13:05:28.068Z","level":"ERROR","message":"Failed to send OTP:","email":"<EMAIL>","purpose":"admin_registration","error":"ApiResponse.error is not a function","ip":"::1"}
{"timestamp":"2025-06-22T13:07:22.378Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"ApiResponse.created is not a function","ip":"::1"}
{"timestamp":"2025-06-22T13:07:34.880Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"Username already exists"}
{"timestamp":"2025-06-22T13:07:34.880Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"Username already exists","ip":"::1"}
{"timestamp":"2025-06-22T13:09:20.487Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"Username already exists"}
{"timestamp":"2025-06-22T13:09:20.487Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"Username already exists","ip":"::1"}
{"timestamp":"2025-06-22T13:09:54.768Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"Username already exists"}
{"timestamp":"2025-06-22T13:09:54.769Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"Username already exists","ip":"::1"}
{"timestamp":"2025-06-22T13:11:37.302Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Account is pending verification. Please complete your registration."}
{"timestamp":"2025-06-22T13:11:37.304Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Account is pending verification. Please complete your registration.","ip":"::1"}
{"timestamp":"2025-06-22T13:30:39.338Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:30:39.342Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:31:47.173Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:31:47.174Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:32:00.667Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:32:00.668Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:32:01.794Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:32:01.796Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:32:01.957Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:32:01.962Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:32:20.093Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:32:20.098Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:32:21.789Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:32:21.790Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:32:22.009Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:32:22.011Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:32:22.205Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:32:22.207Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:32:22.389Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:32:22.390Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:36:35.560Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:36:35.562Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:46:51.065Z","level":"ERROR","message":"Failed to generate and send OTP:","email":"<EMAIL>","purpose":"registration","error":"Please wait 10 minutes before requesting a new OTP"}
{"timestamp":"2025-06-22T13:46:51.066Z","level":"ERROR","message":"Failed to send OTP:","email":"<EMAIL>","purpose":"registration","error":"Please wait 10 minutes before requesting a new OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:57:15.018Z","level":"ERROR","message":"Failed to generate and send OTP:","email":"<EMAIL>","purpose":"registration","error":"Please wait 10 minutes before requesting a new OTP"}
{"timestamp":"2025-06-22T13:57:15.020Z","level":"ERROR","message":"Failed to send OTP:","email":"<EMAIL>","purpose":"registration","error":"Please wait 10 minutes before requesting a new OTP","ip":"::1"}
{"timestamp":"2025-06-22T14:03:10.039Z","level":"ERROR","message":"Client login failed","username":"nonexistent","error":"Invalid username or password"}
{"timestamp":"2025-06-22T14:03:10.040Z","level":"ERROR","message":"Client login failed","username":"nonexistent","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-22T14:03:10.162Z","level":"ERROR","message":"Client login failed","username":"nonexistent","error":"Invalid username or password"}
{"timestamp":"2025-06-22T14:03:10.163Z","level":"ERROR","message":"Client login failed","username":"nonexistent","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-22T14:03:10.285Z","level":"ERROR","message":"Client login failed","username":"nonexistent","error":"Invalid username or password"}
{"timestamp":"2025-06-22T14:03:10.286Z","level":"ERROR","message":"Client login failed","username":"nonexistent","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T13:19:52.168Z","level":"ERROR","message":"Client login failed","username":"client","error":"Invalid username or password"}
{"timestamp":"2025-07-07T13:19:52.169Z","level":"ERROR","message":"Client login failed","username":"client","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T13:20:21.167Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password"}
{"timestamp":"2025-07-07T13:20:21.169Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T13:20:21.790Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password"}
{"timestamp":"2025-07-07T13:20:21.792Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T13:20:21.959Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password"}
{"timestamp":"2025-07-07T13:20:21.959Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T13:20:22.123Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password"}
{"timestamp":"2025-07-07T13:20:22.124Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T13:20:22.285Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password"}
{"timestamp":"2025-07-07T13:20:22.286Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T13:26:45.508Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-07-07T13:26:45.509Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T13:29:30.982Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:29:30.984Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:30:03.081Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Account not found"}
{"timestamp":"2025-07-07T13:30:04.434Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:30:04.435Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:38.458Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Account not found"}
{"timestamp":"2025-07-07T13:31:42.346Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:42.347Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:42.531Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:42.532Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:42.731Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:42.731Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:42.914Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:42.915Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.097Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.098Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.363Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.363Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.542Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.543Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.693Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.694Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.863Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.864Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:46:08.071Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:46:08.072Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:48:45.772Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:48:45.773Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:48:45.799Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:48:45.799Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:49:18.777Z","level":"ERROR","message":"Failed to resend admin verification email","email":"<EMAIL>","error":"Account not found"}
{"timestamp":"2025-07-07T13:49:21.098Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"Your OTP Code - Barangay Management System","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-07T13:49:21.099Z","level":"ERROR","message":"Failed to send OTP email:","email":"<EMAIL>","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-07T13:49:21.100Z","level":"ERROR","message":"Failed to generate and send OTP:","email":"<EMAIL>","purpose":"registration","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-07T13:49:50.456Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:49:50.457Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:56:58.259Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:56:58.260Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:11.120Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Account not found"}
{"timestamp":"2025-07-07T13:57:13.308Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:13.308Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:13.489Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:13.490Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:13.672Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:13.672Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:13.838Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:13.839Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.016Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.017Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.201Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.202Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.373Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.374Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.551Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.552Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.710Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.710Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.889Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.890Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:15.057Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:15.058Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:15.224Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:15.224Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:38.008Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Account not found"}
{"timestamp":"2025-07-07T13:57:39.536Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:39.537Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:39.759Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:39.759Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:39.943Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:39.944Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.120Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.120Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.288Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.289Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.470Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.470Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.669Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.670Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.839Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.840Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.040Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.041Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.225Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.226Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.406Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.407Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.586Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.587Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.760Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.765Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.939Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.940Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:42.125Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:42.126Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:42.305Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:42.306Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:58:05.226Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:58:05.228Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:58:16.931Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:58:16.933Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:59:46.493Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:59:46.504Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:59:55.123Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Account not found"}
{"timestamp":"2025-07-07T14:00:18.574Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Account not found"}
{"timestamp":"2025-07-07T20:59:33.575Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password"}
{"timestamp":"2025-07-07T20:59:33.579Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T21:02:38.936Z","level":"ERROR","message":"Client account registration failed","username":"testclient","error":"Username already exists"}
{"timestamp":"2025-07-07T21:02:38.937Z","level":"ERROR","message":"Client account registration failed","username":"testclient","error":"Username already exists","ip":"::1"}
{"timestamp":"2025-07-07T21:09:39.094Z","level":"ERROR","message":"Error submitting document request","error":"queries is not iterable","clientId":12}
{"timestamp":"2025-07-07T21:09:39.097Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:238:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":2,"purpose_category_id":1,"purpose_details":"For job application at ABC Company","payment_method_id":1,"delivery_method":"pickup","priority":"normal","has_pending_cases":false,"voter_registration_number":"VR123456789","precinct_number":"001A","emergency_contact_name":"Juan Dela Cruz","emergency_contact_relationship":"Father","emergency_contact_phone":"09123456789","emergency_contact_address":"123 Main St, Barangay Test, Test City"}}
{"timestamp":"2025-07-07T21:11:52.768Z","level":"ERROR","message":"Error submitting document request","error":"queries is not iterable","clientId":12}
{"timestamp":"2025-07-07T21:11:52.770Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:239:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":1,"purpose_category_id":3,"purpose_details":"For job application at ABC Company","payment_method_id":6,"delivery_method":"pickup","priority":"normal","has_pending_cases":false,"voter_registration_number":"VR123456789","precinct_number":"001A","emergency_contact_name":"Juan Dela Cruz","emergency_contact_relationship":"Father","emergency_contact_phone":"09123456789","emergency_contact_address":"123 Main St, Barangay Test, Test City"}}
{"timestamp":"2025-07-07T21:14:43.619Z","level":"ERROR","message":"Error submitting document request","error":"queries is not iterable","clientId":12}
{"timestamp":"2025-07-07T21:14:43.621Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":1,"purpose_category_id":3,"purpose_details":"For job application at ABC Company","payment_method_id":6,"delivery_method":"pickup","priority":"normal","has_pending_cases":false,"voter_registration_number":"VR123456789","precinct_number":"001A","emergency_contact_name":"Juan Dela Cruz","emergency_contact_relationship":"Father","emergency_contact_phone":"09123456789","emergency_contact_address":"123 Main St, Barangay Test, Test City"}}
{"timestamp":"2025-07-07T21:18:04.841Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:18:04.846Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:18:04.848Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":1,"purpose_category_id":3,"purpose_details":"For job application at ABC Company","payment_method_id":6,"delivery_method":"pickup","priority":"normal","has_pending_cases":false,"voter_registration_number":"VR123456789","precinct_number":"001A","emergency_contact_name":"Juan Dela Cruz","emergency_contact_relationship":"Father","emergency_contact_phone":"09123456789","emergency_contact_address":"123 Main St, Barangay Test, Test City"}}
{"timestamp":"2025-07-07T21:18:58.578Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:18:58.581Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:18:58.583Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":2,"purpose_category_id":1,"purpose_details":"For job application at ABC Company","payment_method_id":1,"delivery_method":"pickup","priority":"normal","has_pending_cases":false,"voter_registration_number":"VR123456789","precinct_number":"001A","emergency_contact_name":"Juan Dela Cruz","emergency_contact_relationship":"Father","emergency_contact_phone":"09123456789","emergency_contact_address":"123 Main St, Barangay Test, Test City"}}
{"timestamp":"2025-07-07T21:20:19.374Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:20:19.376Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:20:19.377Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":2,"purpose_category_id":1,"purpose_details":"For job application at ABC Company","payment_method_id":1,"delivery_method":"pickup","priority":"normal","has_pending_cases":false,"voter_registration_number":"VR123456789","precinct_number":"001A","emergency_contact_name":"Juan Dela Cruz","emergency_contact_relationship":"Father","emergency_contact_phone":"09123456789","emergency_contact_address":"123 Main St, Barangay Test, Test City"}}
{"timestamp":"2025-07-07T21:21:21.968Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:21:21.970Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:21:21.971Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":2,"purpose_category_id":1,"purpose_details":"For job application at ABC Company","payment_method_id":1,"delivery_method":"pickup","priority":"normal","has_pending_cases":false,"voter_registration_number":"VR123456789","precinct_number":"001A","emergency_contact_name":"Juan Dela Cruz","emergency_contact_relationship":"Father","emergency_contact_phone":"09123456789","emergency_contact_address":"123 Main St, Barangay Test, Test City"}}
{"timestamp":"2025-07-07T21:22:52.616Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:22:52.626Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:22:52.629Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":2,"purpose_category_id":1,"purpose_details":"For job application at ABC Company","payment_method_id":1,"delivery_method":"pickup","priority":"normal","has_pending_cases":false,"voter_registration_number":"VR123456789","precinct_number":"001A","emergency_contact_name":"Juan Dela Cruz","emergency_contact_relationship":"Father","emergency_contact_phone":"09123456789","emergency_contact_address":"123 Main St, Barangay Test, Test City"}}
{"timestamp":"2025-07-07T21:39:59.177Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:39:59.180Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:39:59.275Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:39:59.277Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:39:59.304Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:39:59.305Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-07T21:39:59.395Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:39:59.396Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:41:02.461Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:41:02.462Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:41:02.562Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:41:02.563Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:41:02.589Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:41:02.590Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-07T21:41:02.782Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:41:02.787Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:43:50.155Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:43:50.157Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:43:50.231Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:43:50.233Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:43:50.256Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:43:50.258Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-07T21:43:50.339Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:43:50.340Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T23:34:25.865Z","level":"ERROR","message":"Failed to get user notifications:"}
{"timestamp":"2025-07-07T23:34:25.867Z","level":"ERROR","message":"Get notifications error:"}
{"timestamp":"2025-07-07T23:34:25.877Z","level":"ERROR","message":"Failed to get unread notification count:"}
{"timestamp":"2025-07-07T23:34:25.878Z","level":"ERROR","message":"Get unread count error:"}
{"timestamp":"2025-07-07T23:34:25.901Z","level":"ERROR","message":"Get notification statistics error:"}
{"timestamp":"2025-07-07T23:42:57.343Z","level":"ERROR","message":"Failed to get user notifications:"}
{"timestamp":"2025-07-07T23:42:57.351Z","level":"ERROR","message":"Get notifications error:"}
{"timestamp":"2025-07-07T23:42:57.383Z","level":"ERROR","message":"Failed to get unread notification count:"}
{"timestamp":"2025-07-07T23:42:57.386Z","level":"ERROR","message":"Get unread count error:"}
{"timestamp":"2025-07-07T23:42:57.439Z","level":"ERROR","message":"Get notification statistics error:"}
{"timestamp":"2025-07-08T00:06:00.309Z","level":"ERROR","message":"Failed to get unread notification count:"}
{"timestamp":"2025-07-08T00:06:00.317Z","level":"ERROR","message":"Get unread count error:"}
{"timestamp":"2025-07-08T00:07:05.500Z","level":"ERROR","message":"Failed to get user notifications:"}
{"timestamp":"2025-07-08T00:07:05.507Z","level":"ERROR","message":"Get notifications error:"}
{"timestamp":"2025-07-08T00:07:05.546Z","level":"ERROR","message":"Failed to get unread notification count:"}
{"timestamp":"2025-07-08T00:07:05.547Z","level":"ERROR","message":"Get unread count error:"}
{"timestamp":"2025-07-08T00:13:16.938Z","level":"ERROR","message":"Failed to get unread notification count:"}
{"timestamp":"2025-07-08T00:13:16.943Z","level":"ERROR","message":"Get unread count error:"}
{"timestamp":"2025-07-08T01:30:32.025Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-08T01:30:32.095Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-08T01:30:32.152Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":1,"purpose_category_id":1,"purpose_details":"It serves as proof that you are a resident of a certain city or municipality, which is often needed when applying for a job or processing government documents.","annual_income":10000,"income_source":"Employment","business_name":"","business_address":"","business_nature":"","has_real_property":false,"property_value":0,"property_location":"","payment_method_id":2,"agree_to_terms":true,"monthly_income":0,"business_income":0,"property_assessed_value":0,"computed_tax":55,"total_fee":60}}
{"timestamp":"2025-07-08T03:05:35.241Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-08T03:05:35.247Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-08T03:05:35.248Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":2,"purpose_category_id":2,"purpose_details":"For business, and automotive shop","emergency_contact_name":"Roco Joma Manalo","emergency_contact_relationship":"Friend","emergency_contact_phone":"***********","emergency_contact_address":"291 Lacumbre Street. Caloocan City","has_pending_cases":false,"pending_cases_details":"","is_registered_voter":true,"additional_notes":"","payment_method_id":6,"agree_to_terms":true,"total_fee":50}}
{"timestamp":"2025-07-08T03:07:25.757Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-08T03:07:25.761Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-08T03:07:25.762Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":2,"purpose_category_id":2,"purpose_details":"Automotive shop","emergency_contact_name":"Roco Joma Manalo","emergency_contact_relationship":"Friend","emergency_contact_phone":"***********","emergency_contact_address":"Lacumbre 219, Manila City","has_pending_cases":false,"pending_cases_details":"","is_registered_voter":true,"additional_notes":"","payment_method_id":6,"agree_to_terms":true,"total_fee":50}}
{"timestamp":"2025-07-08T03:13:48.591Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-08T03:13:48.597Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-08T03:13:48.598Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":2,"purpose_category_id":2,"purpose_details":"Automotive Shop Relocation","payment_method_id":6,"delivery_method":"pickup","priority":"normal","emergency_contact_name":"Roco Joma Manalo","emergency_contact_relationship":"Friend","emergency_contact_phone":"***********","emergency_contact_address":"Lacumbre 121, Manila City","has_pending_cases":false,"pending_cases_details":"","is_registered_voter":true,"additional_notes":"","total_fee":50}}
{"timestamp":"2025-07-08T03:14:42.174Z","level":"ERROR","message":"Error cancelling request","error":"queries is not iterable","requestId":9,"clientId":12}
{"timestamp":"2025-07-08T03:14:42.175Z","level":"ERROR","message":"Controller error - cancelRequest","error":"queries is not iterable","stack":"TypeError: queries is not iterable\n    at executeTransaction (D:\\rhai_front_and_back\\rhai_backend\\src\\config\\database.js:53:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async DocumentRequest.updateStatus (D:\\rhai_front_and_back\\rhai_backend\\src\\models\\DocumentRequest.js:314:5)\n    at async DocumentRequestService.cancelRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:456:7)\n    at async cancelRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:156:22)","requestId":"9","clientId":12,"reason":"Cancelled by user"}
{"timestamp":"2025-07-08T03:18:11.113Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-08T03:18:11.126Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-08T03:18:11.129Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":2,"purpose_category_id":2,"purpose_details":"Automotive Shop, Relocation","payment_method_id":6,"delivery_method":"pickup","priority":"normal","emergency_contact_name":"Roco Joma Manalo","emergency_contact_relationship":"Friend","emergency_contact_phone":"***********","emergency_contact_address":"19 Lacumbre Street, Manila City","has_pending_cases":false,"pending_cases_details":null,"is_registered_voter":true,"additional_notes":"The Automotive Shop, Relocation will be located at the Marcos High Way","total_fee":50}}
{"timestamp":"2025-07-08T03:18:29.335Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-08T03:18:29.340Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-08T03:18:29.341Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":2,"purpose_category_id":2,"purpose_details":"Automotive Shop, Relocation","payment_method_id":6,"delivery_method":"pickup","priority":"normal","emergency_contact_name":"Roco Joma Manalo","emergency_contact_relationship":"Friend","emergency_contact_phone":"***********","emergency_contact_address":"19 Lacumbre Street, Manila City","has_pending_cases":false,"pending_cases_details":null,"is_registered_voter":true,"additional_notes":"The Automotive Shop, Relocation will be located at the Marcos High Way","total_fee":50}}
{"timestamp":"2025-07-08T03:36:56.963Z","level":"ERROR","message":"Error cancelling request","error":"queries is not iterable","requestId":12,"clientId":12}
{"timestamp":"2025-07-08T03:36:56.967Z","level":"ERROR","message":"Controller error - cancelRequest","error":"queries is not iterable","stack":"TypeError: queries is not iterable\n    at executeTransaction (D:\\rhai_front_and_back\\rhai_backend\\src\\config\\database.js:53:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async DocumentRequest.updateStatus (D:\\rhai_front_and_back\\rhai_backend\\src\\models\\DocumentRequest.js:314:5)\n    at async DocumentRequestService.cancelRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:488:7)\n    at async cancelRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:156:22)","requestId":"12","clientId":12,"reason":"Cancelled by user"}
{"timestamp":"2025-07-08T03:37:13.057Z","level":"ERROR","message":"Error cancelling request","error":"queries is not iterable","requestId":12,"clientId":12}
{"timestamp":"2025-07-08T03:37:13.058Z","level":"ERROR","message":"Controller error - cancelRequest","error":"queries is not iterable","stack":"TypeError: queries is not iterable\n    at executeTransaction (D:\\rhai_front_and_back\\rhai_backend\\src\\config\\database.js:53:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async DocumentRequest.updateStatus (D:\\rhai_front_and_back\\rhai_backend\\src\\models\\DocumentRequest.js:314:5)\n    at async DocumentRequestService.cancelRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:488:7)\n    at async cancelRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:156:22)","requestId":"12","clientId":12,"reason":"Cancelled by user"}
{"timestamp":"2025-07-08T03:43:56.912Z","level":"ERROR","message":"Error cancelling request","error":"Unknown column 'notes' in 'field list'","requestId":12,"clientId":12}
{"timestamp":"2025-07-08T03:45:17.862Z","level":"ERROR","message":"Error cancelling request","error":"Cannot add or update a child row: a foreign key constraint fails (`barangay_management_system`.`request_status_history`, CONSTRAINT `request_status_history_ibfk_4` FOREIGN KEY (`changed_by`) REFERENCES `admin_employee_accounts` (`id`))","requestId":12,"clientId":12}
{"timestamp":"2025-07-08T15:41:24.593Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password"}
{"timestamp":"2025-07-08T15:41:24.594Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-08T16:47:34.524Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-08T16:47:34.528Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-08T16:52:41.970Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-08T16:52:41.971Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-08T16:55:13.880Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-08T16:55:13.885Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-08T16:55:40.108Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-08T16:56:39.112Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-08T16:56:39.113Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-08T16:57:21.644Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-08T16:57:21.645Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-08T16:58:50.907Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-08T16:59:02.283Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-08T17:03:36.640Z","level":"ERROR","message":"Client login failed","username":"jerome123","error":"Invalid username or password"}
{"timestamp":"2025-07-08T17:03:36.643Z","level":"ERROR","message":"Client login failed","username":"jerome123","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-08T17:07:27.135Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-08T17:07:27.136Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-08T17:07:27.577Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-08T17:07:27.578Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-08T17:07:27.962Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-08T17:07:27.963Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-08T17:07:28.347Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-08T17:07:28.348Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-08T17:07:28.694Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-08T17:07:28.695Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-08T17:07:29.056Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-08T17:07:29.057Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-08T17:07:29.451Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password"}
{"timestamp":"2025-07-08T17:07:29.452Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-08T17:07:29.844Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password"}
{"timestamp":"2025-07-08T17:07:29.845Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-08T17:07:30.201Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password"}
{"timestamp":"2025-07-08T17:07:30.202Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-08T17:07:30.557Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password"}
{"timestamp":"2025-07-08T17:07:30.558Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-08T17:07:30.922Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password"}
{"timestamp":"2025-07-08T17:07:30.923Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-08T17:07:31.260Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password"}
{"timestamp":"2025-07-08T17:07:31.260Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-08T17:11:49.892Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-08T17:11:50.908Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-08T17:12:55.813Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-08T17:12:56.828Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-08T17:12:57.878Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-08T17:16:59.230Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-08T17:18:04.764Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-08T17:18:04.765Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-08T17:18:05.633Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-08T17:18:05.634Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-08T17:18:16.911Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-09T04:03:03.450Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-09T04:03:12.268Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-09T04:03:12.269Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-09T04:06:28.731Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-09T04:06:28.733Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-09T04:28:45.559Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-09T04:28:45.561Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-09T04:28:46.769Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-09T04:28:46.769Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-09T04:28:46.957Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-09T04:28:46.958Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-09T04:28:47.117Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-09T04:28:47.118Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-09T04:28:47.305Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-09T04:28:47.306Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-09T04:43:19.684Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-09T04:48:27.939Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-09T04:48:48.601Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-09T04:48:48.602Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-09T04:49:21.042Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-09T05:01:42.838Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-09T15:08:42.455Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-09T16:09:48.420Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-09T16:13:17.120Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-09T16:13:17.121Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-09T16:16:22.672Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-13T13:45:35.577Z","level":"ERROR","message":"Payment link creation failed","error":"Unknown column 'dt.document_name' in 'field list'","stack":"Error: Unknown column 'dt.document_name' in 'field list'\n    at PromisePool.execute (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (D:\\rhai_front_and_back\\rhai_backend\\src\\config\\database.js:37:34)\n    at initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:41:36)\n    at Layer.handle [as handle_request] (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (D:\\rhai_front_and_back\\rhai_backend\\src\\routes\\paymentRoutes.js:15:3)\n    at Layer.handle [as handle_request] (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T13:47:19.418Z","level":"ERROR","message":"Payment link creation failed","error":"Unknown column 'dt.document_name' in 'field list'","stack":"Error: Unknown column 'dt.document_name' in 'field list'\n    at PromisePool.execute (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (D:\\rhai_front_and_back\\rhai_backend\\src\\config\\database.js:37:34)\n    at initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:41:36)\n    at Layer.handle [as handle_request] (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (D:\\rhai_front_and_back\\rhai_backend\\src\\routes\\paymentRoutes.js:15:3)\n    at Layer.handle [as handle_request] (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","body":{"request_id":13,"payment_method_id":2},"userId":12}
{"timestamp":"2025-07-13T13:49:10.468Z","level":"ERROR","message":"Payment link creation failed","error":"Unknown column 'dt.document_name' in 'field list'","stack":"Error: Unknown column 'dt.document_name' in 'field list'\n    at PromisePool.execute (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (D:\\rhai_front_and_back\\rhai_backend\\src\\config\\database.js:37:34)\n    at initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:41:36)\n    at Layer.handle [as handle_request] (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (D:\\rhai_front_and_back\\rhai_backend\\src\\routes\\paymentRoutes.js:15:3)\n    at Layer.handle [as handle_request] (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","body":{"request_id":13,"payment_method_id":2},"userId":12}
{"timestamp":"2025-07-13T13:52:54.776Z","level":"ERROR","message":"Payment link creation failed","error":"Cannot read properties of undefined (reading 'paymongoService')","stack":"TypeError: Cannot read properties of undefined (reading 'paymongoService')\n    at initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:86:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","body":{"request_id":13,"payment_method_id":2},"userId":12}
{"timestamp":"2025-07-13T13:54:36.778Z","level":"ERROR","message":"Payment link creation failed","error":"Cannot read properties of undefined (reading 'paymongoService')","stack":"TypeError: Cannot read properties of undefined (reading 'paymongoService')\n    at initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:29:17)\n    at Layer.handle [as handle_request] (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (D:\\rhai_front_and_back\\rhai_backend\\src\\routes\\paymentRoutes.js:15:3)\n    at Layer.handle [as handle_request] (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T13:55:41.114Z","level":"ERROR","message":"Payment link creation failed","error":"Cannot read properties of undefined (reading 'paymongoService')","stack":"TypeError: Cannot read properties of undefined (reading 'paymongoService')\n    at initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:29:17)\n    at Layer.handle [as handle_request] (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (D:\\rhai_front_and_back\\rhai_backend\\src\\routes\\paymentRoutes.js:15:3)\n    at Layer.handle [as handle_request] (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T13:56:23.328Z","level":"ERROR","message":"Payment link creation failed","error":"Cannot read properties of undefined (reading 'paymongoService')","stack":"TypeError: Cannot read properties of undefined (reading 'paymongoService')\n    at initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:34:17)\n    at Layer.handle [as handle_request] (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (D:\\rhai_front_and_back\\rhai_backend\\src\\routes\\paymentRoutes.js:15:3)\n    at Layer.handle [as handle_request] (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T13:58:17.919Z","level":"ERROR","message":"Failed to create PayMongo payment link","error":"Request failed with status code 400","response":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"linkData":{"amount":5575,"description":"BOSFDR - Barangay Clearance Request #12","remarks":"Payment for Barangay Clearance via PayMongo - Bank Transfer","metadata":{"request_id":"12","transaction_id":"TXN_1752415097311_285bbd3a","client_id":"12","document_type":"Barangay Clearance","payment_method":"PayMongo - Bank Transfer"}}}
{"timestamp":"2025-07-13T13:58:17.921Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:82:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:118:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T13:58:26.549Z","level":"ERROR","message":"Failed to create PayMongo payment link","error":"Request failed with status code 400","response":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"linkData":{"amount":5575,"description":"BOSFDR - Barangay Clearance Request #12","remarks":"Payment for Barangay Clearance via PayMongo - Bank Transfer","metadata":{"request_id":"12","transaction_id":"TXN_1752415106064_e47e19a2","client_id":"12","document_type":"Barangay Clearance","payment_method":"PayMongo - Bank Transfer"}}}
{"timestamp":"2025-07-13T13:58:26.550Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:82:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:118:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T13:58:33.701Z","level":"ERROR","message":"Failed to create PayMongo payment link","error":"Request failed with status code 400","response":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"linkData":{"amount":5575,"description":"BOSFDR - Barangay Clearance Request #12","remarks":"Payment for Barangay Clearance via PayMongo - Bank Transfer","metadata":{"request_id":"12","transaction_id":"TXN_1752415113369_1fc0fad2","client_id":"12","document_type":"Barangay Clearance","payment_method":"PayMongo - Bank Transfer"}}}
{"timestamp":"2025-07-13T13:58:33.702Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:82:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:118:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T13:58:59.711Z","level":"ERROR","message":"Failed to create PayMongo payment link","error":"Request failed with status code 400","response":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"linkData":{"amount":5575,"description":"BOSFDR - Barangay Clearance Request #12","remarks":"Payment for Barangay Clearance via PayMongo - Bank Transfer","metadata":{"request_id":"12","transaction_id":"TXN_1752415139259_db18efe4","client_id":"12","document_type":"Barangay Clearance","payment_method":"PayMongo - Bank Transfer"}}}
{"timestamp":"2025-07-13T13:58:59.712Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:82:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:118:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T13:59:14.351Z","level":"ERROR","message":"Failed to create PayMongo payment link","error":"Request failed with status code 400","response":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"linkData":{"amount":5575,"description":"BOSFDR - Barangay Clearance Request #12","remarks":"Payment for Barangay Clearance via PayMongo - Bank Transfer","metadata":{"request_id":"12","transaction_id":"TXN_1752415153939_e0413d6a","client_id":"12","document_type":"Barangay Clearance","payment_method":"PayMongo - Bank Transfer"}}}
{"timestamp":"2025-07-13T13:59:14.352Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:82:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:130:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T13:59:59.145Z","level":"ERROR","message":"Failed to create PayMongo payment link","error":"Request failed with status code 400","response":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"linkData":{"amount":5575,"description":"BOSFDR - Barangay Clearance Request #12","remarks":"Payment for Barangay Clearance via PayMongo - Bank Transfer","metadata":{"request_id":"12","transaction_id":"TXN_1752415198698_e57dfc82","client_id":"12","document_type":"Barangay Clearance","payment_method":"PayMongo - Bank Transfer"}}}
{"timestamp":"2025-07-13T13:59:59.146Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:82:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:130:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:01:55.105Z","level":"ERROR","message":"Failed to create PayMongo payment link","error":"Request failed with status code 400","response":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"linkData":{"amount":5575,"description":"BOSFDR - Barangay Clearance Request #12","remarks":"Payment for Barangay Clearance via PayMongo - Bank Transfer","metadata":{"request_id":"12","transaction_id":"TXN_1752415314549_d723d035","client_id":"12","document_type":"Barangay Clearance","payment_method":"PayMongo - Bank Transfer"}}}
{"timestamp":"2025-07-13T14:01:55.106Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:84:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:130:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:02:06.545Z","level":"ERROR","message":"Failed to create PayMongo payment link","error":"Request failed with status code 400","response":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"linkData":{"amount":5575,"description":"BOSFDR - Barangay Clearance Request #12","remarks":"Payment for Barangay Clearance via PayMongo - Bank Transfer","metadata":{"request_id":"12","transaction_id":"TXN_1752415326245_92c5bce1","client_id":"12","document_type":"Barangay Clearance","payment_method":"PayMongo - Bank Transfer"}}}
{"timestamp":"2025-07-13T14:02:06.546Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:84:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:130:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:02:29.325Z","level":"ERROR","message":"Failed to create PayMongo payment link","error":"Request failed with status code 400","response":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"linkData":{"amount":5575,"description":"BOSFDR - Barangay Clearance Request #12","remarks":"Payment for Barangay Clearance via PayMongo - Bank Transfer","metadata":{"request_id":"12","transaction_id":"TXN_1752415349004_a7d27042","client_id":"12","document_type":"Barangay Clearance","payment_method":"PayMongo - Bank Transfer"}}}
{"timestamp":"2025-07-13T14:02:29.326Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:84:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:130:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:07:24.946Z","level":"ERROR","message":"Failed to create PayMongo payment link","error":"Request failed with status code 400","response":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"linkData":{"amount":5575,"description":"BOSFDR - Barangay Clearance Request #12","remarks":"Payment for Barangay Clearance via PayMongo - Bank Transfer","metadata":{"request_id":"12","transaction_id":"TXN_1752415644500_c828a8d5","client_id":"12","document_type":"Barangay Clearance","payment_method":"PayMongo - Bank Transfer"}}}
{"timestamp":"2025-07-13T14:07:24.947Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:84:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:151:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:08:53.582Z","level":"ERROR","message":"PayMongo API Error Details:","status":400,"statusText":"Bad Request","errorData":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"requestAmount":5575,"requestDescription":"BOSFDR - Barangay Clearance Request #12"}
{"timestamp":"2025-07-13T14:08:53.583Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:151:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:11:53.139Z","level":"ERROR","message":"PayMongo API Error Details:","status":400,"statusText":"Bad Request","errorData":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"requestAmount":5575,"requestDescription":"BOSFDR - Barangay Clearance Request #12"}
{"timestamp":"2025-07-13T14:11:53.141Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:151:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:19:33.893Z","level":"ERROR","message":"PayMongo API Error Details:","status":400,"statusText":"Bad Request","errorData":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"requestAmount":5575,"requestDescription":"BOSFDR - Barangay Clearance Request #12"}
{"timestamp":"2025-07-13T14:19:33.896Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:151:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:20:11.324Z","level":"ERROR","message":"PayMongo API Error Details:","status":400,"statusText":"Bad Request","errorData":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"requestAmount":5575,"requestDescription":"BOSFDR - Barangay Clearance Request #12"}
{"timestamp":"2025-07-13T14:20:11.325Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:151:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:22:00.225Z","level":"ERROR","message":"PayMongo API Error Details:","status":400,"statusText":"Bad Request","errorData":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"requestAmount":5575,"requestDescription":"BOSFDR - Barangay Clearance Request #12"}
{"timestamp":"2025-07-13T14:22:00.226Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:151:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:22:56.990Z","level":"ERROR","message":"PayMongo API Error Details:","status":400,"statusText":"Bad Request","errorData":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"requestAmount":5575,"requestDescription":"BOSFDR - Barangay Clearance Request #12"}
{"timestamp":"2025-07-13T14:22:56.991Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:151:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:23:22.412Z","level":"ERROR","message":"PayMongo API Error Details:","status":400,"statusText":"Bad Request","errorData":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"requestAmount":5575,"requestDescription":"BOSFDR - Barangay Clearance Request #12"}
{"timestamp":"2025-07-13T14:23:22.413Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:151:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:24:23.478Z","level":"ERROR","message":"PayMongo API Error Details:","status":400,"statusText":"Bad Request","errorData":{"errors":[{"code":"parameter_below_minimum","detail":"Please enter an amount at least Php 100.00."}]},"requestAmount":6675,"requestDescription":"BOSFDR - Barangay Clearance Request #13"}
{"timestamp":"2025-07-13T14:24:23.479Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:151:27)","body":{"request_id":13,"payment_method_id":2},"userId":12}
{"timestamp":"2025-07-13T14:25:59.635Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:151:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:26:49.327Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:151:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:45:50.837Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:159:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:46:44.124Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:159:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:46:45.506Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:159:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:46:46.434Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:159:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:47:06.633Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:159:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:47:48.331Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:159:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:47:52.620Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:159:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T14:47:56.374Z","level":"ERROR","message":"Payment link creation failed","error":"PayMongo payment link creation failed: Request failed with status code 400","stack":"Error: PayMongo payment link creation failed: Request failed with status code 400\n    at PayMongoService.createPaymentLink (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\paymongoService.js:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PaymentController.initiatePayment (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:159:27)","body":{"request_id":12,"payment_method_id":6},"userId":12}
{"timestamp":"2025-07-13T15:02:34.109Z","level":"ERROR","message":"Invalid webhook signature format","signature":"test-signature"}
{"timestamp":"2025-07-13T15:03:56.724Z","level":"ERROR","message":"Webhook processing failed","error":"Unknown column 'resource_type' in 'field list'","stack":"Error: Unknown column 'resource_type' in 'field list'\n    at PromisePool.execute (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (D:\\rhai_front_and_back\\rhai_backend\\src\\config\\database.js:37:34)\n    at PaymentController.handleWebhook (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:268:13)\n    at D:\\rhai_front_and_back\\rhai_backend\\src\\routes\\paymentRoutes.js:66:35\n    at Layer.handle [as handle_request] (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\rhai_front_and_back\\rhai_backend\\node_modules\\express\\lib\\router\\index.js:346:12)","body":{"data":{"id":"evt_1752419036658","type":"event","attributes":{"type":"link.payment.paid","livemode":false,"data":{"id":"link_1752419036658","type":"link","attributes":{"amount":10000,"description":"BOSFDR - Barangay Clearance Request #12","status":"paid","checkout_url":"https://checkout.paymongo.com/links/link_1752419036658","reference_number":"REF_1752419036658","payments":[{"id":"pay_1752419036658","type":"payment","attributes":{"amount":10000,"status":"paid","paid_at":"2025-07-13T15:03:56.658Z"}}],"metadata":{"request_id":"12","transaction_id":"TXN_1752419036659_test","client_id":"12"}}},"created_at":"2025-07-13T15:03:56.659Z"}}}}
{"timestamp":"2025-07-13T15:27:07.544Z","level":"ERROR","message":"Document request not found","request_id":15,"client_id":12}
{"timestamp":"2025-07-13T15:27:09.443Z","level":"ERROR","message":"Document request not found","request_id":15,"client_id":12}
{"timestamp":"2025-07-13T15:27:10.311Z","level":"ERROR","message":"Document request not found","request_id":15,"client_id":12}
{"timestamp":"2025-07-13T15:27:10.517Z","level":"ERROR","message":"Document request not found","request_id":15,"client_id":12}
{"timestamp":"2025-07-13T15:27:10.741Z","level":"ERROR","message":"Document request not found","request_id":15,"client_id":12}
{"timestamp":"2025-07-13T15:27:10.952Z","level":"ERROR","message":"Document request not found","request_id":15,"client_id":12}
{"timestamp":"2025-07-13T15:27:11.202Z","level":"ERROR","message":"Document request not found","request_id":15,"client_id":12}
{"timestamp":"2025-07-13T15:27:11.437Z","level":"ERROR","message":"Document request not found","request_id":15,"client_id":12}
{"timestamp":"2025-07-13T15:27:11.679Z","level":"ERROR","message":"Document request not found","request_id":15,"client_id":12}
{"timestamp":"2025-07-13T15:27:11.883Z","level":"ERROR","message":"Document request not found","request_id":15,"client_id":12}
{"timestamp":"2025-07-13T15:27:12.115Z","level":"ERROR","message":"Document request not found","request_id":15,"client_id":12}
{"timestamp":"2025-07-13T15:38:24.162Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-13T15:38:51.901Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-13T15:39:23.608Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-13T15:39:23.609Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-13T17:48:08.416Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password"}
{"timestamp":"2025-07-13T17:48:08.418Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-13T17:59:54.819Z","level":"ERROR","message":"Client account registration failed","username":"revo4438","error":"Username already exists"}
{"timestamp":"2025-07-13T17:59:54.821Z","level":"ERROR","message":"Client account registration failed","username":"revo4438","error":"Username already exists","ip":"::1"}
{"timestamp":"2025-07-13T18:07:46.355Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-13T18:07:46.360Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-13T18:13:47.687Z","level":"ERROR","message":"Client account registration failed","username":"revo4438","error":"Username already exists"}
{"timestamp":"2025-07-13T18:13:47.688Z","level":"ERROR","message":"Client account registration failed","username":"revo4438","error":"Username already exists","ip":"::1"}
{"timestamp":"2025-07-13T20:33:33.939Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-13T20:33:33.940Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-13T20:41:47.159Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-13T20:42:23.361Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-13T20:42:45.580Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-13T21:14:47.980Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-13T21:18:41.961Z","level":"ERROR","message":"Email connection verification failed:","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-13T21:19:17.319Z","level":"ERROR","message":"Email connection verification failed:","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-13T21:45:25.648Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-13T22:03:12.441Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-13T22:03:19.204Z","level":"ERROR","message":"Table 'barangay_management_system.request_statuses' doesn't exist","code":"ER_NO_SUCH_TABLE","errno":1146,"sql":"\n        SELECT dr.*, c.first_name, c.last_name, c.email,\n               dt.type_name, rs_old.status_name as old_status,\n               rs_new.status_name as new_status\n        FROM document_requests dr\n        JOIN client_profiles c ON dr.client_id = c.account_id\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN request_statuses rs_old ON ? = rs_old.id\n        JOIN request_statuses rs_new ON dr.status_id = rs_new.id\n        WHERE dr.id = ?\n      ","sqlState":"42S02","sqlMessage":"Table 'barangay_management_system.request_statuses' doesn't exist"}
{"timestamp":"2025-07-14T19:48:35.251Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"Document Request Status Update - Cedula","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-14T19:48:35.253Z","level":"ERROR","message":"Failed to send status change email:","code":"EAUTH","command":"API"}
{"timestamp":"2025-07-14T19:48:35.253Z","level":"ERROR","message":"Failed to send email notification:","code":"EAUTH","command":"API"}
{"timestamp":"2025-07-14T19:49:14.733Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"Document Request Status Update - Cedula","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-14T19:49:14.733Z","level":"ERROR","message":"Failed to send status change email:","code":"EAUTH","command":"API"}
{"timestamp":"2025-07-14T19:49:14.734Z","level":"ERROR","message":"Failed to send email notification:","code":"EAUTH","command":"API"}
{"timestamp":"2025-07-14T19:49:14.749Z","level":"ERROR","message":"Unknown column 'aea.is_active' in 'where clause'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT aep.email, aep.first_name, aep.last_name\n        FROM admin_employee_profiles aep\n        JOIN admin_employee_accounts aea ON aep.account_id = aea.id\n        WHERE aea.is_active = 1 AND aep.email IS NOT NULL AND aep.email != ''\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'aea.is_active' in 'where clause'"}
{"timestamp":"2025-07-14T19:49:14.749Z","level":"ERROR","message":"Unknown column 'aea.is_active' in 'where clause'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT aep.email, aep.first_name, aep.last_name\n        FROM admin_employee_profiles aep\n        JOIN admin_employee_accounts aea ON aep.account_id = aea.id\n        WHERE aea.is_active = 1 AND aep.email IS NOT NULL AND aep.email != ''\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'aea.is_active' in 'where clause'"}
{"timestamp":"2025-07-14T19:49:49.782Z","level":"ERROR","message":"Unknown column 'aea.is_active' in 'where clause'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT aep.email, aep.first_name, aep.last_name\n        FROM admin_employee_profiles aep\n        JOIN admin_employee_accounts aea ON aep.account_id = aea.id\n        WHERE aea.is_active = 1 AND aep.email IS NOT NULL AND aep.email != ''\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'aea.is_active' in 'where clause'"}
{"timestamp":"2025-07-14T19:49:49.783Z","level":"ERROR","message":"Unknown column 'aea.is_active' in 'where clause'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT aep.email, aep.first_name, aep.last_name\n        FROM admin_employee_profiles aep\n        JOIN admin_employee_accounts aea ON aep.account_id = aea.id\n        WHERE aea.is_active = 1 AND aep.email IS NOT NULL AND aep.email != ''\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'aea.is_active' in 'where clause'"}
{"timestamp":"2025-07-14T20:04:40.020Z","level":"ERROR","message":"Unknown column 'aea.is_active' in 'where clause'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT aep.email, aep.first_name, aep.last_name\n        FROM admin_employee_profiles aep\n        JOIN admin_employee_accounts aea ON aep.account_id = aea.id\n        WHERE aea.is_active = 1 AND aep.email IS NOT NULL AND aep.email != ''\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'aea.is_active' in 'where clause'"}
{"timestamp":"2025-07-14T20:04:40.021Z","level":"ERROR","message":"Unknown column 'aea.is_active' in 'where clause'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT aep.email, aep.first_name, aep.last_name\n        FROM admin_employee_profiles aep\n        JOIN admin_employee_accounts aea ON aep.account_id = aea.id\n        WHERE aea.is_active = 1 AND aep.email IS NOT NULL AND aep.email != ''\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'aea.is_active' in 'where clause'"}
{"timestamp":"2025-07-14T20:53:10.951Z","level":"ERROR","message":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null"}
{"timestamp":"2025-07-14T20:53:10.953Z","level":"ERROR","message":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null"}
{"timestamp":"2025-07-14T20:53:10.960Z","level":"ERROR","message":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null"}
{"timestamp":"2025-07-14T20:53:50.118Z","level":"ERROR","message":"Unknown column 'aea.is_active' in 'where clause'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT aep.email, aep.first_name, aep.last_name\n        FROM admin_employee_profiles aep\n        JOIN admin_employee_accounts aea ON aep.account_id = aea.id\n        WHERE aea.is_active = 1 AND aep.email IS NOT NULL AND aep.email != ''\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'aea.is_active' in 'where clause'"}
{"timestamp":"2025-07-14T20:53:50.118Z","level":"ERROR","message":"Unknown column 'aea.is_active' in 'where clause'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT aep.email, aep.first_name, aep.last_name\n        FROM admin_employee_profiles aep\n        JOIN admin_employee_accounts aea ON aep.account_id = aea.id\n        WHERE aea.is_active = 1 AND aep.email IS NOT NULL AND aep.email != ''\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'aea.is_active' in 'where clause'"}
{"timestamp":"2025-07-14T20:54:34.204Z","level":"ERROR","message":"Unknown column 'aea.is_active' in 'where clause'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT aep.email, aep.first_name, aep.last_name\n        FROM admin_employee_profiles aep\n        JOIN admin_employee_accounts aea ON aep.account_id = aea.id\n        WHERE aea.is_active = 1 AND aep.email IS NOT NULL AND aep.email != ''\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'aea.is_active' in 'where clause'"}
{"timestamp":"2025-07-14T20:54:34.205Z","level":"ERROR","message":"Unknown column 'aea.is_active' in 'where clause'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT aep.email, aep.first_name, aep.last_name\n        FROM admin_employee_profiles aep\n        JOIN admin_employee_accounts aea ON aep.account_id = aea.id\n        WHERE aea.is_active = 1 AND aep.email IS NOT NULL AND aep.email != ''\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'aea.is_active' in 'where clause'"}
{"timestamp":"2025-07-14T21:04:39.497Z","level":"ERROR","message":"Unknown column 'aea.is_active' in 'where clause'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT aep.email, aep.first_name, aep.last_name\n        FROM admin_employee_profiles aep\n        JOIN admin_employee_accounts aea ON aep.account_id = aea.id\n        WHERE aea.is_active = 1 AND aep.email IS NOT NULL AND aep.email != ''\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'aea.is_active' in 'where clause'"}
{"timestamp":"2025-07-14T21:04:39.498Z","level":"ERROR","message":"Unknown column 'aea.is_active' in 'where clause'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT aep.email, aep.first_name, aep.last_name\n        FROM admin_employee_profiles aep\n        JOIN admin_employee_accounts aea ON aep.account_id = aea.id\n        WHERE aea.is_active = 1 AND aep.email IS NOT NULL AND aep.email != ''\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'aea.is_active' in 'where clause'"}
{"timestamp":"2025-07-14T21:05:21.194Z","level":"ERROR","message":"Unknown column 'aea.is_active' in 'where clause'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT aep.email, aep.first_name, aep.last_name\n        FROM admin_employee_profiles aep\n        JOIN admin_employee_accounts aea ON aep.account_id = aea.id\n        WHERE aea.is_active = 1 AND aep.email IS NOT NULL AND aep.email != ''\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'aea.is_active' in 'where clause'"}
{"timestamp":"2025-07-14T21:05:21.194Z","level":"ERROR","message":"Unknown column 'aea.is_active' in 'where clause'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT aep.email, aep.first_name, aep.last_name\n        FROM admin_employee_profiles aep\n        JOIN admin_employee_accounts aea ON aep.account_id = aea.id\n        WHERE aea.is_active = 1 AND aep.email IS NOT NULL AND aep.email != ''\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'aea.is_active' in 'where clause'"}
{"timestamp":"2025-07-14T21:06:09.963Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"New Document Request - Cedula","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-14T21:30:48.912Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"New Document Request - Cedula","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-14T21:32:40.535Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"New Document Request - Barangay Clearance","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-14T21:46:08.252Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"New Document Request - Barangay Clearance","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-14T21:47:43.773Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T21:47:43.774Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T21:56:14.148Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"New Document Request - Cedula","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-14T22:05:26.755Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T22:05:26.756Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T22:05:27.022Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T22:05:27.022Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T22:05:27.304Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T22:05:27.306Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T22:05:27.572Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T22:05:27.573Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T23:18:57.993Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T23:18:57.996Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T23:19:05.845Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T23:19:05.846Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T23:19:06.861Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T23:19:06.862Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T23:19:07.200Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T23:19:07.201Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T23:19:07.688Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T23:19:07.689Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T23:19:32.593Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T23:19:32.595Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T23:21:40.205Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T23:21:40.206Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T23:21:40.904Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T23:21:40.905Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T23:21:41.068Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T23:21:41.068Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T23:21:41.234Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T23:21:41.234Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T23:21:41.413Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T23:21:41.414Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T23:21:41.585Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T23:21:41.585Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T23:21:52.673Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T23:21:52.674Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T23:21:53.849Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T23:21:53.850Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T23:21:54.023Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T23:21:54.024Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T23:21:54.207Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T23:21:54.207Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-14T23:21:54.397Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-14T23:21:54.397Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-15T02:55:15.622Z","level":"ERROR","message":"Cannot add or update a child row: a foreign key constraint fails (`barangay_management_system`.`request_status_history`, CONSTRAINT `request_status_history_ibfk_4` FOREIGN KEY (`changed_by`) REFERENCES `admin_employee_accounts` (`id`))","code":"ER_NO_REFERENCED_ROW_2","errno":1452,"sql":"\n          INSERT INTO request_status_history\n          (request_id, old_status_id, new_status_id, changed_by, change_reason)\n          VALUES (?, ?, ?, ?, ?)\n        ","sqlState":"23000","sqlMessage":"Cannot add or update a child row: a foreign key constraint fails (`barangay_management_system`.`request_status_history`, CONSTRAINT `request_status_history_ibfk_4` FOREIGN KEY (`changed_by`) REFERENCES `admin_employee_accounts` (`id`))"}
{"timestamp":"2025-07-15T02:56:26.678Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"Document Request Status Update - Cedula","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-15T02:56:26.679Z","level":"ERROR","message":"Failed to send status change email:","code":"EAUTH","command":"API"}
{"timestamp":"2025-07-15T02:56:26.679Z","level":"ERROR","message":"Failed to send email notification:","code":"EAUTH","command":"API"}
{"timestamp":"2025-07-15T03:15:51.672Z","level":"ERROR","message":"❌ Failed to send real-time notification:"}
{"timestamp":"2025-07-15T03:15:51.686Z","level":"ERROR","message":"❌ Failed to send real-time notification:"}
{"timestamp":"2025-07-15T03:16:28.427Z","level":"ERROR","message":"❌ Failed to send real-time notification:"}
{"timestamp":"2025-07-15T03:16:28.478Z","level":"ERROR","message":"❌ Failed to send real-time notification:"}
{"timestamp":"2025-07-15T05:29:52.138Z","level":"ERROR","message":"❌ Failed to send real-time notification:"}
{"timestamp":"2025-07-15T05:31:32.292Z","level":"ERROR","message":"❌ Failed to send real-time notification:"}
{"timestamp":"2025-07-15T05:31:33.303Z","level":"ERROR","message":"❌ AdminDocumentService: Status update failed:"}
{"timestamp":"2025-07-15T05:37:59.477Z","level":"ERROR","message":"❌ Failed to send real-time notification:"}
{"timestamp":"2025-07-15T05:38:00.505Z","level":"ERROR","message":"❌ Failed to send real-time notification:"}
{"timestamp":"2025-07-15T05:46:22.913Z","level":"ERROR","message":"Error retrieving request details","error":"Request not found","requestId":62,"clientId":12}
{"timestamp":"2025-07-15T05:46:22.917Z","level":"ERROR","message":"Controller error - getRequestDetails","error":"Request not found","stack":"Error: Request not found\n    at DocumentRequestService.getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:506:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:135:22)","requestId":"62","clientId":12}
{"timestamp":"2025-07-15T05:46:23.913Z","level":"ERROR","message":"Error retrieving request details","error":"Request not found","requestId":62,"clientId":12}
{"timestamp":"2025-07-15T05:46:23.914Z","level":"ERROR","message":"Controller error - getRequestDetails","error":"Request not found","stack":"Error: Request not found\n    at DocumentRequestService.getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:506:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:135:22)","requestId":"62","clientId":12}
{"timestamp":"2025-07-15T05:46:24.194Z","level":"ERROR","message":"Error retrieving request details","error":"Request not found","requestId":62,"clientId":12}
{"timestamp":"2025-07-15T05:46:24.195Z","level":"ERROR","message":"Controller error - getRequestDetails","error":"Request not found","stack":"Error: Request not found\n    at DocumentRequestService.getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:506:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:135:22)","requestId":"62","clientId":12}
{"timestamp":"2025-07-15T05:46:24.368Z","level":"ERROR","message":"Error retrieving request details","error":"Request not found","requestId":62,"clientId":12}
{"timestamp":"2025-07-15T05:46:24.369Z","level":"ERROR","message":"Controller error - getRequestDetails","error":"Request not found","stack":"Error: Request not found\n    at DocumentRequestService.getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:506:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:135:22)","requestId":"62","clientId":12}
{"timestamp":"2025-07-15T05:46:24.539Z","level":"ERROR","message":"Error retrieving request details","error":"Request not found","requestId":62,"clientId":12}
{"timestamp":"2025-07-15T05:46:24.540Z","level":"ERROR","message":"Controller error - getRequestDetails","error":"Request not found","stack":"Error: Request not found\n    at DocumentRequestService.getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:506:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:135:22)","requestId":"62","clientId":12}
{"timestamp":"2025-07-15T05:46:25.854Z","level":"ERROR","message":"Error retrieving request details","error":"Request not found","requestId":62,"clientId":12}
{"timestamp":"2025-07-15T05:46:25.855Z","level":"ERROR","message":"Controller error - getRequestDetails","error":"Request not found","stack":"Error: Request not found\n    at DocumentRequestService.getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:506:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:135:22)","requestId":"62","clientId":12}
{"timestamp":"2025-07-15T05:46:26.185Z","level":"ERROR","message":"Error retrieving request details","error":"Request not found","requestId":62,"clientId":12}
{"timestamp":"2025-07-15T05:46:26.186Z","level":"ERROR","message":"Controller error - getRequestDetails","error":"Request not found","stack":"Error: Request not found\n    at DocumentRequestService.getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:506:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:135:22)","requestId":"62","clientId":12}
{"timestamp":"2025-07-15T05:47:11.153Z","level":"ERROR","message":"❌ AdminDocumentService: Status update failed:"}
{"timestamp":"2025-07-15T05:47:43.099Z","level":"ERROR","message":"❌ Failed to send real-time notification:"}
{"timestamp":"2025-07-15T05:57:33.905Z","level":"ERROR","message":"Error retrieving request details","error":"Request not found","requestId":65,"clientId":12}
{"timestamp":"2025-07-15T05:57:33.906Z","level":"ERROR","message":"Controller error - getRequestDetails","error":"Request not found","stack":"Error: Request not found\n    at DocumentRequestService.getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:506:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:135:22)","requestId":"65","clientId":12}
{"timestamp":"2025-07-15T06:13:34.216Z","level":"ERROR","message":"Error retrieving request details","error":"Request not found","requestId":65,"clientId":12}
{"timestamp":"2025-07-15T06:13:34.217Z","level":"ERROR","message":"Controller error - getRequestDetails","error":"Request not found","stack":"Error: Request not found\n    at DocumentRequestService.getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:506:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:135:22)","requestId":"65","clientId":12}
{"timestamp":"2025-07-15T06:13:36.883Z","level":"ERROR","message":"Error retrieving request details","error":"Request not found","requestId":65,"clientId":12}
{"timestamp":"2025-07-15T06:13:36.884Z","level":"ERROR","message":"Controller error - getRequestDetails","error":"Request not found","stack":"Error: Request not found\n    at DocumentRequestService.getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:506:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:135:22)","requestId":"65","clientId":12}
{"timestamp":"2025-07-15T07:55:13.870Z","level":"ERROR","message":"Error retrieving request details","error":"Request not found","requestId":74,"clientId":12}
{"timestamp":"2025-07-15T07:55:13.872Z","level":"ERROR","message":"Controller error - getRequestDetails","error":"Request not found","stack":"Error: Request not found\n    at DocumentRequestService.getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:506:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:135:22)","requestId":"74","clientId":12}
{"timestamp":"2025-07-15T08:49:44.394Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-15T08:49:44.396Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-15T08:55:46.649Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"New Document Request - Cedula","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-15T08:55:49.333Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"New Document Request - Barangay Clearance","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-15T09:15:42.752Z","level":"ERROR","message":"Error retrieving request details","error":"Request not found","requestId":76,"clientId":12}
{"timestamp":"2025-07-15T09:15:42.753Z","level":"ERROR","message":"Controller error - getRequestDetails","error":"Request not found","stack":"Error: Request not found\n    at DocumentRequestService.getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:506:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRequestDetails (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:135:22)","requestId":"76","clientId":12}
{"timestamp":"2025-07-15T10:18:46.282Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-15T10:18:46.284Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-15T10:20:51.400Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"New Document Request - Cedula","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-15T10:20:55.150Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"New Document Request - Barangay Clearance","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-15T10:35:55.422Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"New Document Request - Cedula","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-15T10:35:58.282Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"Document Request Status Update - Cedula","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-15T10:35:58.283Z","level":"ERROR","message":"Failed to send status change email:","code":"EAUTH","command":"API"}
{"timestamp":"2025-07-15T10:35:58.283Z","level":"ERROR","message":"Failed to send email notification:","code":"EAUTH","command":"API"}
{"timestamp":"2025-07-15T10:36:02.742Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"Document Request Status Update - Cedula","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-15T10:36:02.743Z","level":"ERROR","message":"Failed to send status change email:","code":"EAUTH","command":"API"}
{"timestamp":"2025-07-15T10:36:02.744Z","level":"ERROR","message":"Failed to send email notification:","code":"EAUTH","command":"API"}
{"timestamp":"2025-07-15T10:49:50.816Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"New Document Request - Cedula","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-15T10:49:54.294Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"Document Request Status Update - Cedula","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-15T10:49:54.295Z","level":"ERROR","message":"Failed to send status change email:","code":"EAUTH","command":"API"}
{"timestamp":"2025-07-15T10:49:54.295Z","level":"ERROR","message":"Failed to send email notification:","code":"EAUTH","command":"API"}
{"timestamp":"2025-07-15T11:01:41.234Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"Document Request Status Update - Cedula","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-15T11:01:41.235Z","level":"ERROR","message":"Failed to send status change email:","code":"EAUTH","command":"API"}
{"timestamp":"2025-07-15T11:01:41.235Z","level":"ERROR","message":"Failed to send email notification:","code":"EAUTH","command":"API"}
{"timestamp":"2025-07-15T11:04:18.332Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-15T11:04:18.333Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-15T11:06:38.332Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-15T11:06:38.333Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-15T11:07:06.829Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-15T11:07:06.830Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-15T11:12:35.366Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-15T11:12:35.367Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-15T11:14:34.100Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-15T11:14:34.101Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-15T11:25:13.961Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"New Document Request - Cedula","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-15T11:25:16.429Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"Document Request Status Update - undefined","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-15T11:25:16.430Z","level":"ERROR","message":"Failed to send status change email:","code":"EAUTH","command":"API"}
{"timestamp":"2025-07-15T11:25:16.430Z","level":"ERROR","message":"Failed to send email notification","operationId":"status-change-90-1752578715003","email":"<EMAIL>","requestId":90,"error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-15T11:25:19.813Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"Document Request Status Update - undefined","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-15T11:25:19.814Z","level":"ERROR","message":"Failed to send status change email:","code":"EAUTH","command":"API"}
{"timestamp":"2025-07-15T11:25:19.814Z","level":"ERROR","message":"Failed to send email notification","operationId":"status-change-90-1752578718043","email":"<EMAIL>","requestId":90,"error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-15T11:40:58.111Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-15T11:41:22.743Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"Document Request Status Update - Cedula","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-15T11:41:22.744Z","level":"ERROR","message":"Failed to send status change email:","code":"EAUTH","command":"API"}
{"timestamp":"2025-07-15T11:41:22.745Z","level":"ERROR","message":"Failed to send email notification (non-critical)","error":"Missing credentials for \"PLAIN\"","requestId":92,"email":"<EMAIL>"}
{"timestamp":"2025-07-15T11:41:45.488Z","level":"ERROR","message":"Error retrieving request details","error":"Request not found","requestId":92,"clientId":12}
{"timestamp":"2025-07-15T11:41:45.490Z","level":"ERROR","message":"Controller error - getRequestDetails","error":"Request not found","stack":"Error: Request not found\n    at DocumentRequestService.getRequestDetails (D:\\cap2_rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:507:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRequestDetails (D:\\cap2_rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:135:22)","requestId":"92","clientId":12}
{"timestamp":"2025-07-16T04:09:36.598Z","level":"ERROR","message":"Request not approved for payment","status_id":10,"required_status":4}
{"timestamp":"2025-07-16T04:09:47.200Z","level":"ERROR","message":"Request not approved for payment","status_id":10,"required_status":4}
{"timestamp":"2025-07-16T04:12:31.469Z","level":"ERROR","message":"Error cancelling request","error":"Request cannot be cancelled at this stage","requestId":97,"clientId":12}
{"timestamp":"2025-07-16T04:12:31.471Z","level":"ERROR","message":"Controller error - cancelRequest","error":"Request cannot be cancelled at this stage","stack":"Error: Request cannot be cancelled at this stage\n    at DocumentRequestService.cancelRequest (D:\\cap2_rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:488:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async cancelRequest (D:\\cap2_rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:165:22)","requestId":"97","clientId":12,"reason":"I change my mind"}
{"timestamp":"2025-07-16T04:12:49.467Z","level":"ERROR","message":"Error cancelling request","error":"Request cannot be cancelled at this stage","requestId":97,"clientId":12}
{"timestamp":"2025-07-16T04:12:49.469Z","level":"ERROR","message":"Controller error - cancelRequest","error":"Request cannot be cancelled at this stage","stack":"Error: Request cannot be cancelled at this stage\n    at DocumentRequestService.cancelRequest (D:\\cap2_rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:488:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async cancelRequest (D:\\cap2_rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:165:22)","requestId":"97","clientId":12,"reason":"dfsdfsdfdfsd"}
{"timestamp":"2025-07-16T04:21:35.105Z","level":"ERROR","message":"Error cancelling request","error":"Request cannot be cancelled at this stage","requestId":97,"clientId":12}
{"timestamp":"2025-07-16T04:21:35.107Z","level":"ERROR","message":"Controller error - cancelRequest","error":"Request cannot be cancelled at this stage","stack":"Error: Request cannot be cancelled at this stage\n    at DocumentRequestService.cancelRequest (D:\\cap2_rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:488:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async cancelRequest (D:\\cap2_rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:165:22)","requestId":"97","clientId":12,"reason":"I have traveled in Davao"}
{"timestamp":"2025-07-16T10:47:06.417Z","level":"ERROR","message":"Payment link creation failed","error":"processingFee is not defined","stack":"ReferenceError: processingFee is not defined\n    at PaymentController.initiatePayment (D:\\cap2_rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:193:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","body":{"request_id":98,"payment_method_id":3},"userId":12}
{"timestamp":"2025-07-16T10:47:11.594Z","level":"ERROR","message":"Payment link creation failed","error":"processingFee is not defined","stack":"ReferenceError: processingFee is not defined\n    at PaymentController.initiatePayment (D:\\cap2_rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:193:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","body":{"request_id":98,"payment_method_id":3},"userId":12}
{"timestamp":"2025-07-16T10:47:20.048Z","level":"ERROR","message":"Payment link creation failed","error":"processingFee is not defined","stack":"ReferenceError: processingFee is not defined\n    at PaymentController.initiatePayment (D:\\cap2_rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:193:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","body":{"request_id":98,"payment_method_id":3},"userId":12}
{"timestamp":"2025-07-16T10:52:00.825Z","level":"ERROR","message":"Payment link creation failed","error":"processingFee is not defined","stack":"ReferenceError: processingFee is not defined\n    at PaymentController.initiatePayment (D:\\cap2_rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:194:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","body":{"request_id":98,"payment_method_id":3},"userId":12}
{"timestamp":"2025-07-16T10:53:12.221Z","level":"ERROR","message":"Payment link creation failed","error":"processingFee is not defined","stack":"ReferenceError: processingFee is not defined\n    at PaymentController.initiatePayment (D:\\cap2_rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:194:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","body":{"request_id":98,"payment_method_id":3},"userId":12}
{"timestamp":"2025-07-16T10:53:53.201Z","level":"ERROR","message":"Payment link creation failed","error":"baseAmount is not defined","stack":"ReferenceError: baseAmount is not defined\n    at PaymentController.initiatePayment (D:\\cap2_rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:226:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","body":{"request_id":98,"payment_method_id":3},"userId":12}
{"timestamp":"2025-07-16T10:54:32.703Z","level":"ERROR","message":"Payment link creation failed","error":"baseAmount is not defined","stack":"ReferenceError: baseAmount is not defined\n    at PaymentController.initiatePayment (D:\\cap2_rhai_front_and_back\\rhai_backend\\src\\controllers\\paymentController.js:226:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","body":{"request_id":98,"payment_method_id":3},"userId":12}
{"timestamp":"2025-07-16T16:10:47.585Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password"}
{"timestamp":"2025-07-16T16:10:47.587Z","level":"ERROR","message":"Client login failed","username":"revo4438","error":"Invalid username or password","ip":"::1"}
