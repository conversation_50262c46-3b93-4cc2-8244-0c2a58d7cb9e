{"name": "rhai_backend", "version": "1.0.0", "description": "Node.js MVC Backend with MySQL", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup-db": "node scripts/setup-db.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["nodejs", "express", "mvc", "mysql", "api", "backend"], "author": "Your Name", "license": "MIT", "dependencies": {"@vee-validate/rules": "^4.15.1", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "mysql2": "^3.6.5", "nodemailer": "^7.0.3", "uuid": "^11.1.0", "vee-validate": "^4.15.1", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}