// Example: How to access PayMongo data arrays in your code

const axios = require('axios');
require('dotenv').config();

// PayMongo API configuration
const PAYMONGO_SECRET_KEY = process.env.PAYMONGO_SECRET_KEY;
const BASE_URL = 'https://api.paymongo.com/v1';

function getAuthHeader() {
  return {
    'Authorization': `Basic ${Buffer.from(PAYMONGO_SECRET_KEY + ':').toString('base64')}`,
    'Content-Type': 'application/json'
  };
}

// Example 1: Get all payments and process the array
async function processPaymentsArray() {
  try {
    const response = await axios.get(`${BASE_URL}/payments`, {
      headers: getAuthHeader()
    });

    // Access the main data array
    const payments = response.data.data;
    
    console.log('📊 Processing', payments.length, 'payments...');
    
    // Loop through each payment in the array
    payments.forEach((payment, index) => {
      // Access payment properties
      const paymentId = payment.id;
      const amount = payment.attributes.amount; // in centavos
      const amountInPHP = amount / 100; // convert to PHP
      const status = payment.attributes.status;
      const description = payment.attributes.description;
      const paymentMethod = payment.attributes.source?.type;
      const fee = payment.attributes.fee;
      const netAmount = payment.attributes.net_amount;
      
      console.log(`Payment ${index + 1}:`);
      console.log(`  ID: ${paymentId}`);
      console.log(`  Amount: ₱${amountInPHP.toFixed(2)}`);
      console.log(`  Status: ${status}`);
      console.log(`  Method: ${paymentMethod}`);
      console.log(`  Fee: ₱${(fee / 100).toFixed(2)}`);
      console.log(`  Net: ₱${(netAmount / 100).toFixed(2)}`);
      console.log(`  Description: ${description}`);
      console.log('---');
    });

    return payments;
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
    return [];
  }
}

// Example 2: Filter payments by status
async function getSuccessfulPayments() {
  try {
    const response = await axios.get(`${BASE_URL}/payments`, {
      headers: getAuthHeader()
    });

    const allPayments = response.data.data;
    
    // Filter only successful payments
    const successfulPayments = allPayments.filter(payment => 
      payment.attributes.status === 'paid'
    );
    
    console.log('✅ Successful payments:', successfulPayments.length);
    
    return successfulPayments;
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
    return [];
  }
}

// Example 3: Find payments by description (your document requests)
async function findPaymentsByDescription(searchTerm) {
  try {
    const response = await axios.get(`${BASE_URL}/payments`, {
      headers: getAuthHeader()
    });

    const allPayments = response.data.data;
    
    // Filter payments that match the search term
    const matchingPayments = allPayments.filter(payment => 
      payment.attributes.description && 
      payment.attributes.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    console.log(`🔍 Found ${matchingPayments.length} payments matching "${searchTerm}"`);
    
    matchingPayments.forEach(payment => {
      console.log(`- ${payment.attributes.description} (₱${(payment.attributes.amount / 100).toFixed(2)})`);
    });
    
    return matchingPayments;
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
    return [];
  }
}

// Example 4: Get payment statistics
async function getPaymentStatistics() {
  try {
    const response = await axios.get(`${BASE_URL}/payments`, {
      headers: getAuthHeader()
    });

    const payments = response.data.data;
    
    // Calculate statistics
    const totalPayments = payments.length;
    const successfulPayments = payments.filter(p => p.attributes.status === 'paid').length;
    const totalAmount = payments.reduce((sum, p) => sum + p.attributes.amount, 0) / 100;
    const totalFees = payments.reduce((sum, p) => sum + (p.attributes.fee || 0), 0) / 100;
    const totalNetAmount = payments.reduce((sum, p) => sum + (p.attributes.net_amount || 0), 0) / 100;
    
    // Group by payment method
    const paymentMethods = {};
    payments.forEach(payment => {
      const method = payment.attributes.source?.type || 'unknown';
      paymentMethods[method] = (paymentMethods[method] || 0) + 1;
    });
    
    console.log('📊 PAYMENT STATISTICS:');
    console.log('='.repeat(40));
    console.log(`Total Payments: ${totalPayments}`);
    console.log(`Successful: ${successfulPayments}`);
    console.log(`Total Amount: ₱${totalAmount.toFixed(2)}`);
    console.log(`Total Fees: ₱${totalFees.toFixed(2)}`);
    console.log(`Net Amount: ₱${totalNetAmount.toFixed(2)}`);
    console.log('Payment Methods:', paymentMethods);
    
    return {
      totalPayments,
      successfulPayments,
      totalAmount,
      totalFees,
      totalNetAmount,
      paymentMethods
    };
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
    return null;
  }
}

// Example 5: Sync PayMongo payments with your database
async function syncPaymentsWithDatabase() {
  try {
    const { executeQuery } = require('./src/config/database');
    
    const response = await axios.get(`${BASE_URL}/payments`, {
      headers: getAuthHeader()
    });

    const payments = response.data.data;
    
    console.log('🔄 Syncing', payments.length, 'payments with database...');
    
    for (const payment of payments) {
      // Check if payment exists in database
      const existingPayment = await executeQuery(
        'SELECT * FROM payment_transactions WHERE external_transaction_id = ?',
        [payment.id]
      );
      
      if (existingPayment.length === 0) {
        console.log(`📝 New payment found: ${payment.id} - ₱${(payment.attributes.amount / 100).toFixed(2)}`);
        
        // You could insert it into your database here
        // await executeQuery('INSERT INTO payment_transactions ...', [...]);
      } else {
        // Update existing payment status if needed
        const currentStatus = existingPayment[0].status;
        const paymongoStatus = payment.attributes.status;
        
        if (currentStatus !== paymongoStatus) {
          console.log(`🔄 Updating payment ${payment.id}: ${currentStatus} → ${paymongoStatus}`);
          
          // await executeQuery('UPDATE payment_transactions SET status = ? WHERE external_transaction_id = ?', 
          //   [paymongoStatus, payment.id]);
        }
      }
    }
    
    console.log('✅ Sync completed');
    
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

// Run examples
async function runExamples() {
  console.log('🚀 PayMongo Data Array Examples\n');
  
  // Example 1: Process all payments
  console.log('1️⃣ Processing all payments:');
  await processPaymentsArray();
  
  console.log('\n2️⃣ Getting successful payments:');
  await getSuccessfulPayments();
  
  console.log('\n3️⃣ Finding BOSFDR payments:');
  await findPaymentsByDescription('BOSFDR');
  
  console.log('\n4️⃣ Payment statistics:');
  await getPaymentStatistics();
  
  console.log('\n5️⃣ Database sync (simulation):');
  // await syncPaymentsWithDatabase(); // Uncomment to run
}

if (require.main === module) {
  runExamples();
}

module.exports = {
  processPaymentsArray,
  getSuccessfulPayments,
  findPaymentsByDescription,
  getPaymentStatistics,
  syncPaymentsWithDatabase
};
