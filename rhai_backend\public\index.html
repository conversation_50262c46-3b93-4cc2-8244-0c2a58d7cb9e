<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Barangay Management System - Database Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #555;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏢 Barangay Management System - Database Test</h1>
        <div class="info"></div>
            <strong>🔧 Database Connection Test</strong><br>
            This page tests the connection to your barangay management database and verifies the schema.
        </div>

        <div class="warning">
            <strong>📝 Setup Instructions:</strong><br>
            1. Make sure MySQL is running<br>
            2. Import the <code>barangay_database_complete.sql</code> file in phpMyAdmin<br>
            3. Update the <code>.env</code> file with your database credentials<br>
            4. Click the test buttons below to verify everything is working
        </div>
    </div>

    <div class="container">
        <h2>🔍 Quick Tests</h2>
        <div class="grid">
            <button class="btn btn-success" onclick="testConnection()">Test Database Connection</button>
            <button class="btn btn-warning" onclick="testTables()">Check Database Tables</button>
            <button class="btn" onclick="testStats()">Get Database Statistics</button>
            <button class="btn" onclick="testSampleData()">View Sample Data</button>
            <button class="btn" onclick="testAdminCheck()">Check Admin Accounts</button>
            <button class="btn" onclick="testFunctions()">Test Database Functions</button>
        </div>
    </div>

    <div class="container">
        <h2>📊 Test Results</h2>
        <div id="results">
            <div class="loading">Click a test button above to see results...</div>
        </div>
    </div>

    <script>
        async function makeRequest(endpoint) {
            try {
                const response = await fetch(endpoint);
                const data = await response.json();
                return data;
            } catch (error) {
                return {
                    success: false,
                    message: 'Network error',
                    error: error.message
                };
            }
        }

        function displayResult(title, data) {
            const resultsDiv = document.getElementById('results');
            const statusClass = data.success ? 'success' : 'error';
            const icon = data.success ? '✅' : '❌';

            resultsDiv.innerHTML = `
                <div class="test-section">
                    <h3>${icon} ${title}</h3>
                    <div class="${statusClass}">
                        <strong>Status:</strong> ${data.success ? 'SUCCESS' : 'FAILED'}<br>
                        <strong>Message:</strong> ${data.message}<br>
                        ${data.error ? `<strong>Error:</strong> ${data.error}<br>` : ''}
                        <strong>Timestamp:</strong> ${data.timestamp || new Date().toISOString()}
                    </div>
                    ${data.data ? `
                        <h4>📋 Data:</h4>
                        <pre>${JSON.stringify(data.data, null, 2)}</pre>
                    ` : ''}
                </div>
            `;
        }

        async function testConnection() {
            displayResult('Database Connection Test', { success: false, message: 'Testing...', timestamp: new Date().toISOString() });
            const result = await makeRequest('/api/test/connection');
            displayResult('Database Connection Test', result);
        }

        async function testTables() {
            displayResult('Database Tables Check', { success: false, message: 'Checking tables...', timestamp: new Date().toISOString() });
            const result = await makeRequest('/api/test/tables');
            displayResult('Database Tables Check', result);
        }

        async function testStats() {
            displayResult('Database Statistics', { success: false, message: 'Getting statistics...', timestamp: new Date().toISOString() });
            const result = await makeRequest('/api/test/stats');
            displayResult('Database Statistics', result);
        }

        async function testSampleData() {
            displayResult('Sample Data Test', { success: false, message: 'Loading sample data...', timestamp: new Date().toISOString() });
            const result = await makeRequest('/api/test/sample-data');
            displayResult('Sample Data Test', result);
        }

        async function testAdminCheck() {
            displayResult('Admin Accounts Check', { success: false, message: 'Checking admin accounts...', timestamp: new Date().toISOString() });
            const result = await makeRequest('/api/test/admin-check');
            displayResult('Admin Accounts Check', result);
        }

        async function testFunctions() {
            displayResult('Database Functions Test', { success: false, message: 'Testing database functions...', timestamp: new Date().toISOString() });
            const result = await makeRequest('/api/test/test-functions');
            displayResult('Database Functions Test', result);
        }

        // Auto-run connection test on page load
        window.onload = function() {
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
