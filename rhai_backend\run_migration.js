const mysql = require('mysql2/promise');

async function runMigration() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'barangay_management_system'
    });

    console.log('🔄 Starting Legal Compliance Database Migration...\n');

    // =====================================================
    // 1. BARANGAY CLEARANCE - REMOVE UNNECESSARY FIELDS
    // =====================================================
    
    console.log('📋 BEFORE: Barangay Clearance Applications Structure');
    const [beforeBarangay] = await connection.execute('DESCRIBE barangay_clearance_applications');
    console.table(beforeBarangay);

    console.log('\n🗑️ Removing unnecessary fields (Data Privacy Act compliance)...');
    
    // Check if columns exist before dropping them
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'barangay_management_system' 
        AND TABLE_NAME = 'barangay_clearance_applications'
        AND COLUMN_NAME IN (
          'voter_registration_number', 
          'precinct_number', 
          'emergency_contact_name',
          'emergency_contact_relationship',
          'emergency_contact_phone',
          'emergency_contact_address'
        )
    `);

    for (const column of columns) {
      try {
        await connection.execute(`ALTER TABLE barangay_clearance_applications DROP COLUMN ${column.COLUMN_NAME}`);
        console.log(`✅ Removed: ${column.COLUMN_NAME}`);
      } catch (error) {
        console.log(`⚠️ Column ${column.COLUMN_NAME} already removed or doesn't exist`);
      }
    }

    console.log('\n📋 AFTER: Barangay Clearance Applications Structure (Privacy Compliant)');
    const [afterBarangay] = await connection.execute('DESCRIBE barangay_clearance_applications');
    console.table(afterBarangay);

    // =====================================================
    // 2. CEDULA - ADD MISSING REQUIRED FIELDS
    // =====================================================
    
    console.log('\n📋 BEFORE: Cedula Applications Structure');
    const [beforeCedula] = await connection.execute('DESCRIBE cedula_applications');
    console.table(beforeCedula);

    console.log('\n➕ Adding missing required fields (Legal compliance)...');

    // Check if columns already exist
    const [existingCedulaColumns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'barangay_management_system' 
        AND TABLE_NAME = 'cedula_applications'
        AND COLUMN_NAME IN ('has_personal_property', 'personal_property_value', 'business_gross_receipts')
    `);

    const existingColumnNames = existingCedulaColumns.map(col => col.COLUMN_NAME);

    if (!existingColumnNames.includes('has_personal_property')) {
      await connection.execute(`
        ALTER TABLE cedula_applications 
        ADD COLUMN has_personal_property BOOLEAN DEFAULT FALSE AFTER has_real_property
      `);
      console.log('✅ Added: has_personal_property');
    }

    if (!existingColumnNames.includes('personal_property_value')) {
      await connection.execute(`
        ALTER TABLE cedula_applications 
        ADD COLUMN personal_property_value DECIMAL(15,2) DEFAULT 0 AFTER has_personal_property
      `);
      console.log('✅ Added: personal_property_value');
    }

    if (!existingColumnNames.includes('business_gross_receipts')) {
      await connection.execute(`
        ALTER TABLE cedula_applications 
        ADD COLUMN business_gross_receipts DECIMAL(15,2) DEFAULT 0 AFTER business_income
      `);
      console.log('✅ Added: business_gross_receipts');
    }

    // Update existing records
    await connection.execute(`
      UPDATE cedula_applications 
      SET has_personal_property = FALSE, 
          personal_property_value = 0,
          business_gross_receipts = COALESCE(business_income, 0)
      WHERE has_personal_property IS NULL
    `);

    console.log('\n📋 AFTER: Cedula Applications Structure (Legally Complete)');
    const [afterCedula] = await connection.execute('DESCRIBE cedula_applications');
    console.table(afterCedula);

    console.log('\n🎉 LEGAL COMPLIANCE MIGRATION COMPLETED!');
    console.log('✅ Barangay Clearance: Privacy compliant, minimal data collection');
    console.log('✅ Cedula: Complete tax declaration as required by law');
    console.log('✅ Database now matches frontend legal requirements');

  } catch (error) {
    console.error('❌ Migration Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

runMigration();
