const { executeQuery } = require('./src/config/database');

async function setupWebhookTables() {
  try {
    console.log('🔧 Setting up webhook tables...');

    // Create payment_webhooks table if it doesn't exist
    const createWebhookTableQuery = `
      CREATE TABLE IF NOT EXISTS payment_webhooks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        webhook_id VARCHAR(255) UNIQUE NOT NULL,
        event_type VARCHAR(100) NOT NULL,
        payload TEXT NOT NULL,
        processed BOOLEAN DEFAULT FALSE,
        processed_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_webhook_id (webhook_id),
        INDEX idx_event_type (event_type),
        INDEX idx_processed (processed),
        INDEX idx_created_at (created_at)
      )
    `;

    await executeQuery(createWebhookTableQuery);
    console.log('✅ payment_webhooks table created/verified');

    // Add external_transaction_id column to payment_transactions if missing
    try {
      const addExternalIdQuery = `
        ALTER TABLE payment_transactions 
        ADD COLUMN external_transaction_id VARCHAR(255) NULL,
        ADD INDEX idx_external_transaction_id (external_transaction_id)
      `;
      await executeQuery(addExternalIdQuery);
      console.log('✅ external_transaction_id column added');
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ external_transaction_id column already exists');
      } else {
        throw error;
      }
    }

    // Add failure_reason column if missing
    try {
      const addFailureReasonQuery = `
        ALTER TABLE payment_transactions 
        ADD COLUMN failure_reason TEXT NULL
      `;
      await executeQuery(addFailureReasonQuery);
      console.log('✅ failure_reason column added');
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ failure_reason column already exists');
      } else {
        throw error;
      }
    }

    // Check current table structure
    const describeWebhooks = await executeQuery('DESCRIBE payment_webhooks');
    console.log('📋 payment_webhooks table structure:');
    console.table(describeWebhooks);

    const describeTransactions = await executeQuery('DESCRIBE payment_transactions');
    console.log('📋 payment_transactions table structure:');
    console.table(describeTransactions);

    console.log('🎉 Database setup completed successfully!');

  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    throw error;
  }
}

if (require.main === module) {
  setupWebhookTables()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

module.exports = { setupWebhookTables };
