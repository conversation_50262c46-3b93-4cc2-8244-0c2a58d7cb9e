const { validationResult } = require('express-validator');
const DocumentRequestService = require('../services/documentRequestService');
const SupportingDocument = require('../models/SupportingDocument');
const { ApiResponse } = require('../utils/response');
const logger = require('../utils/logger');
const { getFileInfo, deleteFile } = require('../middleware/fileUpload');

class DocumentRequestController {
  // Get document types
  async getDocumentTypes(req, res) {
    try {
      const result = await DocumentRequestService.getDocumentTypes();
      return ApiResponse.success(res, result.data, result.message);
    } catch (error) {
      logger.error('Controller error - getDocumentTypes', {
        error: error.message,
        stack: error.stack
      });
      return ApiResponse.serverError(res, error.message);
    }
  }

  // Get purpose categories
  async getPurposeCategories(req, res) {
    try {
      const result = await DocumentRequestService.getPurposeCategories();
      return ApiResponse.success(res, result.data, result.message);
    } catch (error) {
      logger.error('Controller error - getPurposeCategories', {
        error: error.message,
        stack: error.stack
      });
      return ApiResponse.serverError(res, error.message);
    }
  }

  // Get payment methods
  async getPaymentMethods(req, res) {
    try {
      const result = await DocumentRequestService.getPaymentMethods();
      return ApiResponse.success(res, result.data, result.message);
    } catch (error) {
      logger.error('Controller error - getPaymentMethods', {
        error: error.message,
        stack: error.stack
      });
      return ApiResponse.serverError(res, error.message);
    }
  }

  // Submit document request
  async submitRequest(req, res) {
    try {
      console.log('🎯 DocumentRequestController.submitRequest called');
      console.log('👤 Client ID:', req.user?.id);
      console.log('📋 Request data:', req.body);

      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.log('❌ Validation errors:', errors.array());
        return ApiResponse.validationError(res, errors.array());
      }

      const clientId = req.user.id;
      const requestData = req.body;

      console.log('🔄 Calling DocumentRequestService.submitRequest...');
      const result = await DocumentRequestService.submitRequest(requestData, clientId);
      console.log('✅ DocumentRequestService.submitRequest completed:', result);

      return ApiResponse.created(res, result.data, result.message);
    } catch (error) {
      logger.error('Controller error - submitRequest', {
        error: error.message,
        stack: error.stack,
        clientId: req.user?.id,
        requestData: req.body
      });

      if (error.message.includes('Invalid document type')) {
        return ApiResponse.badRequest(res, error.message);
      }

      return ApiResponse.serverError(res, error.message);
    }
  }

  // Get client's requests
  async getClientRequests(req, res) {
    try {
      const clientId = req.user.id;
      const filters = {
        status: req.query.status,
        document_type: req.query.document_type,
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 10,
        search: req.query.search,
        sort_by: req.query.sort_by || 'created_at',
        sort_order: req.query.sort_order || 'DESC'
      };

      // Validate pagination limits
      if (filters.limit > 50) {
        filters.limit = 50;
      }

      const result = await DocumentRequestService.getClientRequests(clientId, filters);
      
      return ApiResponse.success(res, {
        requests: result.data,
        pagination: result.pagination
      }, result.message);
    } catch (error) {
      logger.error('Controller error - getClientRequests', {
        error: error.message,
        stack: error.stack,
        clientId: req.user?.id,
        filters: req.query
      });
      return ApiResponse.serverError(res, error.message);
    }
  }

  // Get request details
  async getRequestDetails(req, res) {
    try {
      const requestId = parseInt(req.params.id);
      const clientId = req.user.id;

      if (!requestId || isNaN(requestId)) {
        return ApiResponse.badRequest(res, 'Invalid request ID');
      }

      const result = await DocumentRequestService.getRequestDetails(requestId, clientId);
      
      return ApiResponse.success(res, result.data, result.message);
    } catch (error) {
      logger.error('Controller error - getRequestDetails', {
        error: error.message,
        stack: error.stack,
        requestId: req.params.id,
        clientId: req.user?.id
      });

      if (error.message === 'Request not found') {
        return ApiResponse.notFound(res, error.message);
      }

      return ApiResponse.serverError(res, error.message);
    }
  }

  // Cancel request
  async cancelRequest(req, res) {
    try {
      console.log('🔔 DocumentRequestController.cancelRequest called');
      console.log('📋 Request params:', req.params);
      console.log('📋 Request body:', req.body);
      console.log('📋 User:', req.user);

      const requestId = parseInt(req.params.id);
      const clientId = req.user.id;
      const { reason } = req.body;

      if (!requestId || isNaN(requestId)) {
        return ApiResponse.badRequest(res, 'Invalid request ID');
      }

      const result = await DocumentRequestService.cancelRequest(requestId, clientId, reason);
      
      return ApiResponse.success(res, result.data, result.message);
    } catch (error) {
      console.error('❌ DocumentRequestController error:', error);
      logger.error('Controller error - cancelRequest', {
        error: error.message,
        stack: error.stack,
        requestId: req.params.id,
        clientId: req.user?.id,
        reason: req.body?.reason
      });

      if (error.message === 'Request not found') {
        return ApiResponse.notFound(res, error.message);
      }

      if (error.message.includes('Unauthorized') || error.message.includes('cannot be cancelled')) {
        return ApiResponse.badRequest(res, error.message);
      }

      return ApiResponse.serverError(res, error.message);
    }
  }

  // Get request history
  async getRequestHistory(req, res) {
    try {
      const requestId = parseInt(req.params.id);
      const clientId = req.user.id;

      if (!requestId || isNaN(requestId)) {
        return ApiResponse.badRequest(res, 'Invalid request ID');
      }

      const result = await DocumentRequestService.getRequestHistory(requestId, clientId);
      
      return ApiResponse.success(res, result.data, result.message);
    } catch (error) {
      logger.error('Controller error - getRequestHistory', {
        error: error.message,
        stack: error.stack,
        requestId: req.params.id,
        clientId: req.user?.id
      });

      if (error.message === 'Request not found') {
        return ApiResponse.notFound(res, error.message);
      }

      return ApiResponse.serverError(res, error.message);
    }
  }

  // Calculate Cedula tax (utility endpoint)
  async calculateCedulaTax(req, res) {
    try {
      const { annual_income, property_assessed_value } = req.body;

      if (!annual_income || isNaN(annual_income) || annual_income < 0) {
        return ApiResponse.badRequest(res, 'Valid annual income is required');
      }

      const propertyValue = parseFloat(property_assessed_value || 0);
      
      // Import CedulaApplication for tax calculation
      const CedulaApplication = require('../models/CedulaApplication');
      const taxCalculation = CedulaApplication.calculateTax(
        parseFloat(annual_income),
        propertyValue
      );

      logger.info('Cedula tax calculated', {
        annual_income,
        property_assessed_value: propertyValue,
        calculated_tax: taxCalculation.total_tax,
        clientId: req.user?.id
      });

      return ApiResponse.success(res, taxCalculation, 'Tax calculated successfully');
    } catch (error) {
      logger.error('Controller error - calculateCedulaTax', {
        error: error.message,
        stack: error.stack,
        requestData: req.body,
        clientId: req.user?.id
      });
      return ApiResponse.serverError(res, error.message);
    }
  }

  // Get processing fee for payment method
  async getProcessingFee(req, res) {
    try {
      const { payment_method_id, base_amount } = req.query;

      if (!payment_method_id || !base_amount || isNaN(base_amount)) {
        return ApiResponse.badRequest(res, 'Payment method ID and base amount are required');
      }

      const amount = parseFloat(base_amount);
      
      const query = `
        SELECT processing_fee_percentage, processing_fee_fixed, method_name
        FROM payment_methods 
        WHERE id = ? AND is_active = 1
      `;
      
      const { executeQuery } = require('../config/database');
      const results = await executeQuery(query, [payment_method_id]);
      
      if (results.length === 0) {
        return ApiResponse.notFound(res, 'Payment method not found');
      }

      const paymentMethod = results[0];
      const processing_fee = parseFloat(paymentMethod.processing_fee_fixed || 0) + 
                           (amount * parseFloat(paymentMethod.processing_fee_percentage || 0) / 100);

      const response = {
        payment_method: paymentMethod.method_name,
        base_amount: amount,
        processing_fee: parseFloat(processing_fee.toFixed(2)),
        total_amount: parseFloat((amount + processing_fee).toFixed(2))
      };

      return ApiResponse.success(res, response, 'Processing fee calculated successfully');
    } catch (error) {
      logger.error('Controller error - getProcessingFee', {
        error: error.message,
        stack: error.stack,
        query: req.query
      });
      return ApiResponse.serverError(res, error.message);
    }
  }

  // Upload supporting documents
  async uploadDocuments(req, res) {
    try {
      const requestId = parseInt(req.params.id);
      const clientId = req.user.id;

      if (!requestId || isNaN(requestId)) {
        return ApiResponse.badRequest(res, 'Invalid request ID');
      }

      // Verify request belongs to client
      const request = await DocumentRequestService.getRequestDetails(requestId, clientId);
      if (!request.data) {
        return ApiResponse.notFound(res, 'Request not found');
      }

      // Check if files were uploaded
      if (!req.files || Object.keys(req.files).length === 0) {
        return ApiResponse.badRequest(res, 'No files uploaded');
      }

      const uploadedDocuments = [];
      const errors = [];

      // Process each uploaded file
      for (const [fieldName, files] of Object.entries(req.files)) {
        for (const file of files) {
          try {
            const fileInfo = getFileInfo(file);

            const documentData = {
              request_id: requestId,
              document_name: fileInfo.originalName,
              document_type: fieldName,
              file_path: fileInfo.path,
              file_size: fileInfo.size,
              mime_type: fileInfo.mimetype,
              uploaded_by: clientId
            };

            const document = await SupportingDocument.create(documentData);
            uploadedDocuments.push(document.toJSON());

            logger.info('Document uploaded successfully', {
              requestId,
              clientId,
              documentId: document.id,
              fileName: fileInfo.originalName,
              fileSize: fileInfo.size
            });

          } catch (error) {
            logger.error('Error saving document record', {
              error: error.message,
              file: file.originalname,
              requestId,
              clientId
            });

            // Clean up uploaded file if database save failed
            deleteFile(file.path);
            errors.push(`Failed to save ${file.originalname}: ${error.message}`);
          }
        }
      }

      if (errors.length > 0 && uploadedDocuments.length === 0) {
        return ApiResponse.serverError(res, 'Failed to upload any documents', { errors });
      }

      const response = {
        uploaded_documents: uploadedDocuments,
        total_uploaded: uploadedDocuments.length,
        errors: errors.length > 0 ? errors : undefined
      };

      return ApiResponse.success(res, response, 'Documents uploaded successfully');

    } catch (error) {
      logger.error('Controller error - uploadDocuments', {
        error: error.message,
        stack: error.stack,
        requestId: req.params.id,
        clientId: req.user?.id
      });
      return ApiResponse.serverError(res, error.message);
    }
  }

  // Get uploaded documents for a request
  async getDocuments(req, res) {
    try {
      const requestId = parseInt(req.params.id);
      const clientId = req.user.id;

      if (!requestId || isNaN(requestId)) {
        return ApiResponse.badRequest(res, 'Invalid request ID');
      }

      // Verify request belongs to client
      const request = await DocumentRequestService.getRequestDetails(requestId, clientId);
      if (!request.data) {
        return ApiResponse.notFound(res, 'Request not found');
      }

      const documents = await SupportingDocument.getByRequestId(requestId);
      const documentsJson = documents.map(doc => doc.toJSON());

      return ApiResponse.success(res, documentsJson, 'Documents retrieved successfully');

    } catch (error) {
      logger.error('Controller error - getDocuments', {
        error: error.message,
        stack: error.stack,
        requestId: req.params.id,
        clientId: req.user?.id
      });
      return ApiResponse.serverError(res, error.message);
    }
  }

  // Delete uploaded document
  async deleteDocument(req, res) {
    try {
      const requestId = parseInt(req.params.id);
      const documentId = parseInt(req.params.documentId);
      const clientId = req.user.id;

      if (!requestId || isNaN(requestId) || !documentId || isNaN(documentId)) {
        return ApiResponse.badRequest(res, 'Invalid request ID or document ID');
      }

      // Verify request belongs to client
      const request = await DocumentRequestService.getRequestDetails(requestId, clientId);
      if (!request.data) {
        return ApiResponse.notFound(res, 'Request not found');
      }

      // Get document and verify it belongs to the request
      const document = await SupportingDocument.findById(documentId);
      if (!document) {
        return ApiResponse.notFound(res, 'Document not found');
      }

      if (document.request_id !== requestId) {
        return ApiResponse.badRequest(res, 'Document does not belong to this request');
      }

      // Delete the document
      await document.delete();

      logger.info('Document deleted successfully', {
        requestId,
        documentId,
        clientId,
        fileName: document.document_name
      });

      return ApiResponse.success(res, null, 'Document deleted successfully');

    } catch (error) {
      logger.error('Controller error - deleteDocument', {
        error: error.message,
        stack: error.stack,
        requestId: req.params.id,
        documentId: req.params.documentId,
        clientId: req.user?.id
      });
      return ApiResponse.serverError(res, error.message);
    }
  }
}

module.exports = new DocumentRequestController();
