const { body, param, query } = require('express-validator');

// Validation for submitting document request
const validateSubmitRequest = [
  body('document_type_id')
    .isInt({ min: 1 })
    .withMessage('Valid document type ID is required'),
  
  body('purpose_category_id')
    .isInt({ min: 1 })
    .withMessage('Valid purpose category ID is required'),
  
  body('purpose_details')
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Purpose details must be between 10 and 500 characters'),
  
  body('payment_method_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid payment method ID is required'),
  
  body('delivery_method')
    .optional()
    .isIn(['pickup', 'delivery'])
    .withMessage('Delivery method must be either pickup or delivery'),
  
  body('delivery_address')
    .if(body('delivery_method').equals('delivery'))
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Delivery address is required when delivery method is selected'),
  
  body('priority')
    .optional()
    .isIn(['normal', 'urgent'])
    .withMessage('Priority must be either normal or urgent'),

  // Barangay Clearance specific validations
  body('has_pending_cases')
    .optional()
    .isBoolean()
    .withMessage('Has pending cases must be a boolean'),
  
  body('pending_cases_details')
    .if(body('has_pending_cases').equals(true))
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Pending cases details are required when has_pending_cases is true'),
  
  body('voter_registration_number')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Voter registration number must not exceed 50 characters'),
  
  body('precinct_number')
    .optional()
    .trim()
    .isLength({ max: 20 })
    .withMessage('Precinct number must not exceed 20 characters'),
  
  body('emergency_contact_name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Emergency contact name must be between 2 and 200 characters'),
  
  body('emergency_contact_relationship')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Emergency contact relationship must be between 2 and 50 characters'),
  
  body('emergency_contact_phone')
    .optional()
    .trim()
    .matches(/^(\+63|0)[0-9]{10}$/)
    .withMessage('Emergency contact phone must be a valid Philippine phone number'),
  
  body('emergency_contact_address')
    .optional()
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Emergency contact address must be between 10 and 500 characters'),

  // Cedula specific validations
  body('occupation')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Occupation must be between 2 and 100 characters'),
  
  body('employer_name')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Employer name must not exceed 200 characters'),
  
  body('employer_address')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Employer address must not exceed 500 characters'),
  
  body('monthly_income')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Monthly income must be a positive number'),
  
  body('annual_income')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Annual income must be a positive number'),
  
  body('business_name')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Business name must not exceed 200 characters'),
  
  body('business_address')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Business address must not exceed 500 characters'),
  
  body('business_type')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Business type must not exceed 100 characters'),
  
  body('business_income')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Business income must be a positive number'),
  
  body('has_real_property')
    .optional()
    .isBoolean()
    .withMessage('Has real property must be a boolean'),
  
  body('property_assessed_value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Property assessed value must be a positive number'),
  
  body('property_location')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Property location must not exceed 500 characters'),
  
  body('tin_number')
    .optional()
    .trim()
    .matches(/^[0-9]{3}-[0-9]{3}-[0-9]{3}-[0-9]{3}$/)
    .withMessage('TIN number must be in format XXX-XXX-XXX-XXX'),
  
  body('previous_ctc_number')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Previous CTC number must not exceed 50 characters'),
  
  body('previous_ctc_date_issued')
    .optional()
    .isISO8601()
    .withMessage('Previous CTC date issued must be a valid date'),
  
  body('previous_ctc_place_issued')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Previous CTC place issued must not exceed 100 characters')
];

// Validation for request ID parameter
const validateRequestId = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Valid request ID is required')
];

// Validation for cancel request
const validateCancelRequest = [
  ...validateRequestId,
  body('reason')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Cancellation reason must not exceed 500 characters')
];

// Validation for get requests query parameters
const validateGetRequests = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
  
  query('status')
    .optional()
    .isIn(['pending', 'under_review', 'additional_info_required', 'approved', 'processing', 'ready_for_pickup', 'completed', 'cancelled', 'rejected'])
    .withMessage('Invalid status value'),
  
  query('document_type')
    .optional()
    .isIn(['Cedula', 'Barangay Clearance'])
    .withMessage('Invalid document type'),
  
  query('search')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Search term must not exceed 100 characters'),
  
  query('sort_by')
    .optional()
    .isIn(['created_at', 'status_name', 'document_type', 'total_fee'])
    .withMessage('Invalid sort field'),
  
  query('sort_order')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Sort order must be ASC or DESC')
];

// Validation for calculate Cedula tax
const validateCalculateCedulaTax = [
  body('annual_income')
    .isFloat({ min: 0 })
    .withMessage('Annual income must be a positive number'),
  
  body('property_assessed_value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Property assessed value must be a positive number')
];

// Validation for get processing fee
const validateGetProcessingFee = [
  query('payment_method_id')
    .isInt({ min: 1 })
    .withMessage('Valid payment method ID is required'),
  
  query('base_amount')
    .isFloat({ min: 0 })
    .withMessage('Base amount must be a positive number')
];

module.exports = {
  validateSubmitRequest,
  validateRequestId,
  validateCancelRequest,
  validateGetRequests,
  validateCalculateCedulaTax,
  validateGetProcessingFee
};
