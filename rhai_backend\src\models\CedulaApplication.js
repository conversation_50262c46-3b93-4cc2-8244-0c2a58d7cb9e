const { executeQuery } = require('../config/database');

class CedulaApplication {
  constructor(data) {
    this.id = data.id;
    this.request_id = data.request_id;
    this.occupation = data.occupation;
    this.employer_name = data.employer_name;
    this.employer_address = data.employer_address;
    this.monthly_income = data.monthly_income;
    this.annual_income = data.annual_income;
    this.business_name = data.business_name;
    this.business_address = data.business_address;
    this.business_type = data.business_type;
    this.business_income = data.business_income;
    this.has_real_property = data.has_real_property;
    this.property_assessed_value = data.property_assessed_value;
    this.property_location = data.property_location;
    this.tin_number = data.tin_number;
    this.previous_ctc_number = data.previous_ctc_number;
    this.previous_ctc_date_issued = data.previous_ctc_date_issued;
    this.previous_ctc_place_issued = data.previous_ctc_place_issued;
    this.computed_tax = data.computed_tax;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // Find by request ID
  static async findByRequestId(requestId) {
    const query = 'SELECT * FROM cedula_applications WHERE request_id = ?';
    const results = await executeQuery(query, [requestId]);
    return results.length > 0 ? new CedulaApplication(results[0]) : null;
  }

  // Create new cedula application
  static async create(applicationData) {
    const {
      request_id,
      occupation,
      employer_name,
      employer_address,
      monthly_income,
      annual_income,
      business_name,
      business_address,
      business_type,
      business_income,
      has_real_property = false,
      property_assessed_value,
      property_location,
      tin_number,
      previous_ctc_number,
      previous_ctc_date_issued,
      previous_ctc_place_issued,
      computed_tax
    } = applicationData;

    const query = `
      INSERT INTO cedula_applications (
        request_id, occupation, employer_name, employer_address,
        monthly_income, annual_income, business_name, business_address,
        business_type, business_income, has_real_property, property_assessed_value,
        property_location, tin_number, previous_ctc_number, previous_ctc_date_issued,
        previous_ctc_place_issued, computed_tax
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      request_id, occupation, employer_name, employer_address,
      monthly_income, annual_income, business_name, business_address,
      business_type, business_income, has_real_property, property_assessed_value,
      property_location, tin_number, previous_ctc_number, previous_ctc_date_issued,
      previous_ctc_place_issued, computed_tax
    ];

    try {
      const result = await executeQuery(query, params);
      return await CedulaApplication.findByRequestId(request_id);
    } catch (error) {
      console.error('Error creating cedula application:', error);
      throw error;
    }
  }

  // Update application
  async update(updateData) {
    const {
      occupation,
      employer_name,
      employer_address,
      monthly_income,
      annual_income,
      business_name,
      business_address,
      business_type,
      business_income,
      has_real_property,
      property_assessed_value,
      property_location,
      tin_number,
      previous_ctc_number,
      previous_ctc_date_issued,
      previous_ctc_place_issued,
      computed_tax
    } = updateData;

    const query = `
      UPDATE cedula_applications 
      SET 
        occupation = ?, employer_name = ?, employer_address = ?,
        monthly_income = ?, annual_income = ?, business_name = ?, 
        business_address = ?, business_type = ?, business_income = ?,
        has_real_property = ?, property_assessed_value = ?, property_location = ?,
        tin_number = ?, previous_ctc_number = ?, previous_ctc_date_issued = ?,
        previous_ctc_place_issued = ?, computed_tax = ?, updated_at = CURRENT_TIMESTAMP
      WHERE request_id = ?
    `;

    const params = [
      occupation, employer_name, employer_address, monthly_income, annual_income,
      business_name, business_address, business_type, business_income,
      has_real_property, property_assessed_value, property_location, tin_number,
      previous_ctc_number, previous_ctc_date_issued, previous_ctc_place_issued,
      computed_tax, this.request_id
    ];

    await executeQuery(query, params);

    // Update instance properties
    Object.assign(this, updateData);
    return this;
  }

  // Delete application
  async delete() {
    const query = 'DELETE FROM cedula_applications WHERE request_id = ?';
    await executeQuery(query, [this.request_id]);
    return true;
  }

  // Get application with request details
  async getWithRequestDetails() {
    const query = `
      SELECT 
        ca.*,
        dr.request_number,
        dr.status_id,
        rs.status_name,
        dr.created_at as request_created_at,
        cac.username as client_username,
        cp.first_name, cp.middle_name, cp.last_name,
        cp.email, cp.phone_number
      FROM cedula_applications ca
      JOIN document_requests dr ON ca.request_id = dr.id
      JOIN request_status rs ON dr.status_id = rs.id
      JOIN client_accounts cac ON dr.client_id = cac.id
      LEFT JOIN client_profiles cp ON cac.id = cp.account_id
      WHERE ca.request_id = ?
    `;

    const results = await executeQuery(query, [this.request_id]);
    return results.length > 0 ? results[0] : null;
  }

  // Calculate tax based on income, property, and business (Legal Compliance)
  static calculateTax(annualIncome, realPropertyValue = 0, personalPropertyValue = 0, businessGrossReceipts = 0) {
    let incomeTax = 0;
    let realPropertyTax = 0;
    let personalPropertyTax = 0;
    let businessTax = 0;

    // Income tax calculation based on brackets
    if (annualIncome <= 10000) {
      incomeTax = annualIncome * 0.005; // 0.5%
    } else if (annualIncome <= 50000) {
      incomeTax = 50 + ((annualIncome - 10000) * 0.01); // ₱50 + 1% of excess
    } else if (annualIncome <= 100000) {
      incomeTax = 450 + ((annualIncome - 50000) * 0.015); // ₱450 + 1.5% of excess
    } else {
      incomeTax = 1200 + ((annualIncome - 100000) * 0.02); // ₱1200 + 2% of excess
    }

    // Real property tax calculation (1% of assessed value)
    if (realPropertyValue > 0) {
      realPropertyTax = realPropertyValue * 0.01;
    }

    // Personal property tax calculation (required if ≥₱1,000)
    if (personalPropertyValue >= 1000) {
      if (personalPropertyValue <= 10000) {
        personalPropertyTax = personalPropertyValue * 0.0025; // 0.25%
      } else if (personalPropertyValue <= 50000) {
        personalPropertyTax = personalPropertyValue * 0.005; // 0.5%
      } else {
        personalPropertyTax = personalPropertyValue * 0.01; // 1%
      }
    }

    // Business tax calculation (gross receipts/professional fees)
    if (businessGrossReceipts > 0) {
      if (businessGrossReceipts <= 50000) {
        businessTax = businessGrossReceipts * 0.005; // 0.5%
      } else if (businessGrossReceipts <= 200000) {
        businessTax = businessGrossReceipts * 0.01; // 1%
      } else if (businessGrossReceipts <= 500000) {
        businessTax = businessGrossReceipts * 0.015; // 1.5%
      } else {
        businessTax = businessGrossReceipts * 0.02; // 2%
      }
    }

    const totalPropertyTax = realPropertyTax + personalPropertyTax;
    const totalTax = Math.max(30, incomeTax + totalPropertyTax + businessTax); // Minimum ₱30

    return {
      income_tax: parseFloat(incomeTax.toFixed(2)),
      real_property_tax: parseFloat(realPropertyTax.toFixed(2)),
      personal_property_tax: parseFloat(personalPropertyTax.toFixed(2)),
      business_tax: parseFloat(businessTax.toFixed(2)),
      total_property_tax: parseFloat(totalPropertyTax.toFixed(2)),
      total_tax: parseFloat(totalTax.toFixed(2))
    };
  }

  // Get tax computation details (Legal Compliance)
  getTaxComputation() {
    return CedulaApplication.calculateTax(
      parseFloat(this.annual_income || 0),
      parseFloat(this.property_assessed_value || 0),
      parseFloat(this.personal_property_value || 0),
      parseFloat(this.business_gross_receipts || 0)
    );
  }

  // Convert to JSON
  toJSON() {
    return { ...this };
  }
}

module.exports = CedulaApplication;
