const express = require('express');
const router = express.Router();
const rateLimit = require('express-rate-limit');

const documentRequestController = require('../controllers/documentRequestController');
const { authenticateClient } = require('../middleware/auth');
const { uploadDocuments } = require('../middleware/fileUpload');
const {
  validateSubmitRequest,
  validateRequestId,
  validateCancelRequest,
  validateGetRequests,
  validateCalculateCedulaTax,
  validateGetProcessingFee
} = require('../middleware/documentRequestValidation');

// Rate limiting for document request submission
const submitRequestLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each client to 5 requests per windowMs
  message: {
    success: false,
    message: 'Too many document requests submitted. Please try again later.',
    timestamp: new Date().toISOString()
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Rate limiting for general API calls
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each client to 100 requests per windowMs
  message: {
    success: false,
    message: 'Too many requests. Please try again later.',
    timestamp: new Date().toISOString()
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Apply general rate limiting to all routes
router.use(generalLimiter);

// Apply client authentication to all routes
router.use(authenticateClient);

// GET /api/client/document-requests/document-types - Get available document types
router.get('/document-types', documentRequestController.getDocumentTypes);

// GET /api/client/document-requests/purpose-categories - Get purpose categories
router.get('/purpose-categories', documentRequestController.getPurposeCategories);

// GET /api/client/document-requests/payment-methods - Get payment methods
router.get('/payment-methods', documentRequestController.getPaymentMethods);

// POST /api/client/document-requests/calculate-cedula-tax - Calculate Cedula tax
router.post('/calculate-cedula-tax', 
  validateCalculateCedulaTax,
  documentRequestController.calculateCedulaTax
);

// GET /api/client/document-requests/processing-fee - Get processing fee for payment method
router.get('/processing-fee',
  validateGetProcessingFee,
  documentRequestController.getProcessingFee
);

// POST /api/client/document-requests - Submit new document request
router.post('/',
  submitRequestLimiter,
  validateSubmitRequest,
  documentRequestController.submitRequest
);

// GET /api/client/document-requests - Get client's document requests
router.get('/',
  validateGetRequests,
  documentRequestController.getClientRequests
);

// GET /api/client/document-requests/:id - Get specific request details
router.get('/:id',
  validateRequestId,
  documentRequestController.getRequestDetails
);

// PUT /api/client/document-requests/:id/cancel - Cancel a request
router.put('/:id/cancel',
  validateCancelRequest,
  documentRequestController.cancelRequest
);

// GET /api/client/document-requests/:id/history - Get request status history
router.get('/:id/history',
  validateRequestId,
  documentRequestController.getRequestHistory
);

// POST /api/client/document-requests/:id/documents - Upload supporting documents
router.post('/:id/documents',
  validateRequestId,
  uploadDocuments,
  documentRequestController.uploadDocuments
);

// GET /api/client/document-requests/:id/documents - Get uploaded documents
router.get('/:id/documents',
  validateRequestId,
  documentRequestController.getDocuments
);

// DELETE /api/client/document-requests/:id/documents/:documentId - Delete uploaded document
router.delete('/:id/documents/:documentId',
  validateRequestId,
  documentRequestController.deleteDocument
);

module.exports = router;
