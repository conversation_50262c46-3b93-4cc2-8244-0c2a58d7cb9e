const express = require('express');
const { body, param, validationResult } = require('express-validator');
const paymentController = require('../controllers/paymentController');
const { protect } = require('../middleware/auth');
const ApiResponse = require('../utils/response');

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return ApiResponse.badRequest(res, 'Validation failed', errors.array());
  }
  next();
};

// Validation rules
const validatePaymentInitiation = [
  body('request_id')
    .isInt({ min: 1 })
    .withMessage('Valid request ID is required'),
  body('payment_method_id')
    .isInt({ min: 1 })
    .withMessage('Valid payment method ID is required'),
  body('return_url')
    .optional()
    .isURL()
    .withMessage('Return URL must be a valid URL'),
  body('customer_email')
    .optional()
    .isEmail()
    .withMessage('Customer email must be valid')
];

const validateTransactionId = [
  param('transactionId')
    .notEmpty()
    .withMessage('Transaction ID is required')
    .matches(/^TXN_\d+_[a-zA-Z0-9]{8}$/)
    .withMessage('Invalid transaction ID format')
];

/**
 * @route   POST /api/payments/initiate
 * @desc    Initiate payment process
 * @access  Private
 * @body    { request_id, payment_method_id, return_url?, customer_email? }
 */
router.post('/initiate',
  protect,
  validatePaymentInitiation,
  handleValidationErrors,
  (req, res) => paymentController.initiatePayment(req, res)
);

/**
 * @route   POST /api/payments/webhook
 * @desc    Handle PayMongo webhooks
 * @access  Public (verified by signature)
 * @body    PayMongo webhook payload
 */
router.post('/webhook',
  // No auth middleware - webhooks come from PayMongo
  // Signature verification is done in the controller
  (req, res) => paymentController.handleWebhook(req, res)
);

/**
 * @route   GET /api/payments/status/:transactionId
 * @desc    Get payment status
 * @access  Private
 * @params  transactionId - Transaction ID
 */
router.get('/status/:transactionId',
  protect,
  validateTransactionId,
  handleValidationErrors,
  (req, res) => paymentController.getPaymentStatus(req, res)
);

/**
 * @route   GET /api/payments/config
 * @desc    Get PayMongo configuration for frontend
 * @access  Private
 */
router.get('/config',
  protect,
  (req, res) => paymentController.getPaymentConfig(req, res)
);

module.exports = router;
