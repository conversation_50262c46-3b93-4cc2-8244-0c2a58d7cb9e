const express = require('express');
const crypto = require('crypto');
const { executeQuery } = require('../config/database');
const logger = require('../utils/logger');

const router = express.Router();

// PayMongo webhook signature verification
function verifyPayMongoSignature(payload, signature, secret) {
  if (!signature || !secret) {
    return false;
  }

  try {
    // PayMongo uses HMAC SHA256
    const computedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload, 'utf8')
      .digest('hex');

    // PayMongo signature format: "sha256=<signature>"
    const expectedSignature = `sha256=${computedSignature}`;
    
    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  } catch (error) {
    logger.error('Signature verification error:', error);
    return false;
  }
}

// Function to send admin notification about payment status
async function notifyAdminsPaymentStatus(requestId, paymentData, status) {
  try {
    console.log(`🔔 Sending ${status} payment notification to admins...`);
    
    // Get request details
    const requestQuery = `
      SELECT dr.*, c.first_name, c.last_name, dt.type_name
      FROM document_requests dr
      JOIN client_profiles c ON dr.client_id = c.account_id
      JOIN document_types dt ON dr.document_type_id = dt.id
      WHERE dr.id = ?
    `;
    
    const requestResults = await executeQuery(requestQuery, [requestId]);
    if (requestResults.length === 0) {
      console.log('❌ Request not found:', requestId);
      return;
    }
    
    const request = requestResults[0];
    
    // Get all admin users with their profile information
    const adminQuery = `
      SELECT
        aea.id,
        aea.username,
        aep.email,
        aep.first_name,
        aep.last_name
      FROM admin_employee_accounts aea
      LEFT JOIN admin_employee_profiles aep ON aea.id = aep.account_id
      WHERE aea.status = 'active'
    `;
    
    const admins = await executeQuery(adminQuery);
    console.log('👥 Found', admins.length, 'active admins');
    
    // Create notifications for each admin
    for (const admin of admins) {
      const notificationData = {
        recipient_id: admin.id,
        recipient_type: 'admin',
        type: status === 'paid' ? 'payment_confirmed' : 'payment_failed',
        title: status === 'paid' ? 'Payment Confirmed' : 'Payment Failed',
        message: status === 'paid'
          ? `Payment confirmed for ${request.type_name} request #${request.request_number} by ${request.first_name} ${request.last_name}`
          : `Payment failed for ${request.type_name} request #${request.request_number} by ${request.first_name} ${request.last_name}`,
        data: JSON.stringify({
          request_id: requestId,
          request_number: request.request_number,
          document_type: request.type_name,
          client_name: `${request.first_name} ${request.last_name}`,
          amount: paymentData.amount,
          payment_id: paymentData.id,
          status: status
        }),
        priority: status === 'paid' ? 'high' : 'normal'
      };

      const insertNotificationQuery = `
        INSERT INTO notifications (
          recipient_id, recipient_type, type, title, message, data, priority, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `;

      await executeQuery(insertNotificationQuery, [
        notificationData.recipient_id,
        notificationData.recipient_type,
        notificationData.type,
        notificationData.title,
        notificationData.message,
        notificationData.data,
        notificationData.priority
      ]);
      
      console.log(`✅ Notification created for admin ${admin.username}`);
    }
    
    console.log(`🎉 ${status} payment notifications sent to all admins`);
    
  } catch (error) {
    console.error('❌ Failed to send admin notifications:', error.message);
    throw error;
  }
}

// Function to process payment webhook
async function processPaymentWebhook(eventType, paymentData) {
  try {
    console.log(`🔄 Processing ${eventType} webhook for payment:`, paymentData.id);
    
    const paymentId = paymentData.id;
    const amount = paymentData.attributes.amount / 100; // Convert from centavos
    const status = paymentData.attributes.status;
    const description = paymentData.attributes.description;
    const paidAt = paymentData.attributes.paid_at;
    
    // Extract request ID from description
    const requestMatch = description?.match(/Request #(\d+)/);
    if (!requestMatch) {
      console.log('⚠️ No request ID found in description:', description);
      return { success: false, message: 'No request ID found' };
    }
    
    const requestId = parseInt(requestMatch[1]);
    console.log(`📋 Processing for request ID: ${requestId}`);
    
    // Find the payment transaction
    const transactionQuery = `
      SELECT * FROM payment_transactions 
      WHERE request_id = ? 
      ORDER BY created_at DESC 
      LIMIT 1
    `;
    
    const transactions = await executeQuery(transactionQuery, [requestId]);
    
    if (transactions.length === 0) {
      console.log('❌ No payment transaction found for request', requestId);
      return { success: false, message: 'No payment transaction found' };
    }
    
    const transaction = transactions[0];
    console.log(`💰 Found transaction: ${transaction.transaction_id}`);
    
    // Update payment transaction
    const updateTransactionQuery = `
      UPDATE payment_transactions 
      SET 
        status = ?, 
        external_transaction_id = ?,
        completed_at = ?,
        webhook_data = ?,
        updated_at = NOW()
      WHERE id = ?
    `;
    
    await executeQuery(updateTransactionQuery, [
      status,
      paymentId,
      paidAt ? new Date(paidAt * 1000) : null,
      JSON.stringify(paymentData),
      transaction.id
    ]);
    
    console.log(`✅ Transaction updated: ${transaction.status} → ${status}`);
    
    // Update document request based on payment status
    if (status === 'paid') {
      // Payment successful - update to payment_confirmed status
      const updateRequestQuery = `
        UPDATE document_requests 
        SET 
          payment_status = 'paid',
          paid_at = ?,
          status_id = 11,
          updated_at = NOW()
        WHERE id = ?
      `;
      
      await executeQuery(updateRequestQuery, [
        paidAt ? new Date(paidAt * 1000) : new Date(),
        requestId
      ]);
      
      console.log(`🎯 Request updated to payment_confirmed status`);
      
      // Send success notification to admins
      await notifyAdminsPaymentStatus(requestId, {
        id: paymentId,
        amount: amount,
        status: status
      }, 'paid');
      
    } else if (status === 'failed') {
      // Payment failed - update status but keep as approved for retry
      const updateRequestQuery = `
        UPDATE document_requests 
        SET 
          payment_status = 'failed',
          updated_at = NOW()
        WHERE id = ?
      `;
      
      await executeQuery(updateRequestQuery, [requestId]);
      
      console.log(`❌ Request marked as payment failed`);
      
      // Send failure notification to admins
      await notifyAdminsPaymentStatus(requestId, {
        id: paymentId,
        amount: amount,
        status: status
      }, 'failed');
    }
    
    return { success: true, message: 'Webhook processed successfully' };
    
  } catch (error) {
    console.error('❌ Error processing payment webhook:', error.message);
    throw error;
  }
}

// PayMongo webhook endpoint
router.post('/paymongo', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    console.log('🔗 Received PayMongo webhook');
    
    const signature = req.headers['paymongo-signature'];
    const payload = req.body;
    const secret = process.env.PAYMONGO_WEBHOOK_SECRET;
    
    // Verify webhook signature (if secret is configured)
    if (secret && !verifyPayMongoSignature(payload, signature, secret)) {
      console.log('❌ Invalid webhook signature');
      return res.status(401).json({ error: 'Invalid signature' });
    }
    
    // Parse the payload
    const event = JSON.parse(payload.toString());
    const eventType = event.data.attributes.type;
    const eventData = event.data.attributes.data;
    
    console.log('📨 Event type:', eventType);
    console.log('🆔 Event ID:', event.data.id);
    
    // Store webhook in database for audit trail
    const insertWebhookQuery = `
      INSERT INTO payment_webhooks (
        webhook_id, event_type, payload, created_at
      ) VALUES (?, ?, ?, NOW())
    `;
    
    await executeQuery(insertWebhookQuery, [
      event.data.id,
      eventType,
      payload.toString()
    ]);
    
    // Process based on event type
    let result;
    switch (eventType) {
      case 'payment.paid':
      case 'link.payment.paid':
        result = await processPaymentWebhook(eventType, eventData);
        break;
        
      case 'payment.failed':
        result = await processPaymentWebhook(eventType, eventData);
        break;
        
      case 'payment.refunded':
        console.log('💸 Payment refunded:', eventData.id);
        // Handle refund logic here
        result = { success: true, message: 'Refund processed' };
        break;
        
      default:
        console.log('⚠️ Unhandled event type:', eventType);
        result = { success: true, message: 'Event type not handled' };
    }
    
    // Mark webhook as processed
    const updateWebhookQuery = `
      UPDATE payment_webhooks 
      SET processed = TRUE, processed_at = NOW() 
      WHERE webhook_id = ?
    `;
    
    await executeQuery(updateWebhookQuery, [event.data.id]);
    
    console.log('✅ Webhook processed successfully');
    
    // Always respond with 200 OK to acknowledge receipt
    res.status(200).json({ 
      success: true, 
      message: result.message,
      event_id: event.data.id 
    });
    
  } catch (error) {
    console.error('❌ Webhook processing error:', error.message);
    
    // Still respond with 200 to prevent retries for parsing errors
    res.status(200).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// Test endpoint for webhook
router.get('/paymongo/test', (req, res) => {
  res.json({ 
    message: 'PayMongo webhook endpoint is active',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
