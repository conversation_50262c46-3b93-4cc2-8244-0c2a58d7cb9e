const jwt = require('jsonwebtoken');
const ClientAccount = require('../models/ClientAccount');
const ClientProfile = require('../models/ClientProfile');
const otpService = require('./otpService');
const emailService = require('./emailService');
const logger = require('../utils/logger');

class ClientAuthService {
  // Generate JWT token for client
  static generateToken(clientId) {
    return jwt.sign(
      { 
        id: clientId,
        type: 'client' // Distinguish from admin tokens
      },
      process.env.JWT_SECRET,
      {
        expiresIn: process.env.JWT_EXPIRE || '30d'
      }
    );
  }

  // Register new client (Step 1: Create account)
  static async registerAccount(accountData) {
    try {
      const { username, password, email } = accountData;

      // Check if username already exists
      const existingAccount = await ClientAccount.findByUsername(username);
      if (existingAccount) {
        throw new Error('Username already exists');
      }

      // Check if email is already used in profiles
      if (email) {
        const existingProfile = await ClientProfile.findByEmail(email);
        if (existingProfile) {
          throw new Error('Email already registered');
        }
      }

      // Create client account
      const clientAccount = await ClientAccount.create({
        username,
        password
      });

      logger.info('Client account created', {
        clientId: clientAccount.id,
        username: clientAccount.username
      });

      return {
        success: true,
        data: {
          accountId: clientAccount.id,
          username: clientAccount.username,
          status: clientAccount.status
        },
        message: 'Account created successfully. Please complete your profile.'
      };
    } catch (error) {
      logger.error('Client account registration failed', {
        username: accountData.username,
        error: error.message
      });
      throw error;
    }
  }

  // Complete client registration (Step 2: Create profile)
  static async completeRegistration(accountId, profileData) {
    try {
      // Verify account exists and is pending verification
      const clientAccount = await ClientAccount.findById(accountId);
      if (!clientAccount) {
        throw new Error('Account not found');
      }

      if (clientAccount.status !== 'pending_verification') {
        throw new Error('Account is not in pending verification status');
      }

      // Check if profile already exists
      const existingProfile = await ClientProfile.findByAccountId(accountId);
      if (existingProfile) {
        throw new Error('Profile already exists for this account');
      }

      // Check if email is already used
      if (profileData.email) {
        const existingEmailProfile = await ClientProfile.findByEmail(profileData.email);
        if (existingEmailProfile) {
          throw new Error('Email already registered');
        }
      }

      // Create client profile
      const clientProfile = await ClientProfile.create({
        account_id: accountId,
        ...profileData
      });

      // Send OTP for email verification if email provided
      let otpSent = false;
      if (profileData.email) {
        try {
          await otpService.generateAndSendOTP(
            profileData.email,
            'email_verification',
            profileData.first_name
          );
          otpSent = true;
        } catch (otpError) {
          logger.warn('Failed to send verification OTP', {
            accountId,
            email: profileData.email,
            error: otpError.message
          });
        }
      }

      logger.info('Client profile created', {
        accountId,
        profileId: clientProfile.id,
        email: profileData.email,
        otpSent
      });

      return {
        success: true,
        data: {
          accountId,
          profileId: clientProfile.id,
          otpSent
        },
        message: otpSent 
          ? 'Profile created successfully. Please check your email for verification code.'
          : 'Profile created successfully.'
      };
    } catch (error) {
      logger.error('Client profile creation failed', {
        accountId,
        error: error.message
      });
      throw error;
    }
  }

  // Verify email with OTP
  static async verifyEmail(email, otp) {
    try {
      // Verify OTP
      await otpService.verifyOTP(email, otp, 'email_verification');

      // Find profile by email
      const clientProfile = await ClientProfile.findByEmail(email);
      if (!clientProfile) {
        throw new Error('Profile not found');
      }

      // Update account email verification status
      const clientAccount = await ClientAccount.findById(clientProfile.account_id);
      await clientAccount.updateEmailVerification(true);

      // Update account status to active if both email and phone are verified
      // For now, just email verification is enough to activate
      await clientAccount.updateStatus('active');

      logger.info('Client email verified', {
        accountId: clientProfile.account_id,
        email
      });

      return {
        success: true,
        message: 'Email verified successfully. Your account is now active.'
      };
    } catch (error) {
      logger.error('Email verification failed', {
        email,
        error: error.message
      });
      throw error;
    }
  }

  // Client login
  static async login(credentials) {
    try {
      const { username, password } = credentials;

      // Find client account
      const clientAccount = await ClientAccount.findByUsername(username);
      if (!clientAccount) {
        throw new Error('Invalid username or password');
      }

      // Verify password
      const isPasswordValid = await clientAccount.verifyPassword(password);
      if (!isPasswordValid) {
        throw new Error('Invalid username or password');
      }

      // Check account status
      if (clientAccount.status === 'suspended') {
        throw new Error('Account is suspended. Please contact administrator.');
      }

      if (clientAccount.status === 'inactive') {
        throw new Error('Account is inactive. Please contact administrator.');
      }

      if (clientAccount.status === 'pending_verification') {
        throw new Error('Account is pending verification. Please complete your registration.');
      }

      // Update last login
      await clientAccount.updateLastLogin();

      // Get account with profile
      const accountWithProfile = await clientAccount.getWithProfile();

      // Generate token
      const token = this.generateToken(clientAccount.id);

      logger.info('Client login successful', {
        clientId: clientAccount.id,
        username: clientAccount.username
      });

      return {
        success: true,
        data: {
          client: {
            id: clientAccount.id,
            username: clientAccount.username,
            status: clientAccount.status,
            email_verified: clientAccount.email_verified,
            phone_verified: clientAccount.phone_verified,
            profile: accountWithProfile ? {
              first_name: accountWithProfile.first_name,
              last_name: accountWithProfile.last_name,
              email: accountWithProfile.email,
              phone_number: accountWithProfile.phone_number,
              profile_picture: accountWithProfile.profile_picture,
              is_verified: accountWithProfile.profile_verified
            } : null
          },
          token
        },
        message: 'Login successful'
      };
    } catch (error) {
      logger.error('Client login failed', {
        username: credentials.username,
        error: error.message
      });
      throw error;
    }
  }

  // Get client profile
  static async getProfile(clientId) {
    try {
      const clientAccount = await ClientAccount.findById(clientId);
      if (!clientAccount) {
        throw new Error('Account not found');
      }

      const accountWithProfile = await clientAccount.getWithProfile();
      
      return {
        success: true,
        data: accountWithProfile
      };
    } catch (error) {
      logger.error('Failed to get client profile', {
        clientId,
        error: error.message
      });
      throw error;
    }
  }

  // Resend verification email
  static async resendVerificationEmail(email) {
    try {
      const clientProfile = await ClientProfile.findByEmail(email);
      if (!clientProfile) {
        throw new Error('Email not found');
      }

      const clientAccount = await ClientAccount.findById(clientProfile.account_id);
      if (clientAccount.email_verified) {
        throw new Error('Email is already verified');
      }

      await otpService.resendOTP(
        email,
        'email_verification',
        clientProfile.first_name
      );

      return {
        success: true,
        message: 'Verification email sent successfully'
      };
    } catch (error) {
      logger.error('Failed to resend verification email', {
        email,
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = ClientAuthService;
