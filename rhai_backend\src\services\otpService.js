const crypto = require('crypto');
const { executeQuery } = require('../config/database');
const emailService = require('./emailService');
const logger = require('../utils/logger');

class OTPService {
  constructor() {
    this.logger = logger;
  }

  // Generate random OTP
  generateOTP(length = 6) {
    const digits = '0123456789';
    let otp = '';
    
    for (let i = 0; i < length; i++) {
      otp += digits[Math.floor(Math.random() * digits.length)];
    }
    
    return otp;
  }

  // Create OTP table if it doesn't exist
  async createOTPTable() {
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS otps (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) NOT NULL,
        otp_code VARCHAR(10) NOT NULL,
        purpose ENUM('registration', 'password_reset', 'email_verification', 'login') DEFAULT 'registration',
        expires_at DATETIME NOT NULL,
        is_used BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email_purpose (email, purpose),
        INDEX idx_otp_code (otp_code),
        INDEX idx_expires_at (expires_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    try {
      await executeQuery(createTableQuery);
      this.logger.info('OTP table created or already exists');
    } catch (error) {
      this.logger.error('Failed to create OTP table:', { error: error.message });
      throw error;
    }
  }

  // Generate and send OTP
  async generateAndSendOTP(email, purpose = 'registration', firstName = '') {
    try {
      // Ensure OTP table exists
      await this.createOTPTable();

      // Clean up expired OTPs for this email and purpose
      await this.cleanupExpiredOTPs(email, purpose);

      // Check if there's a recent valid OTP (rate limiting) - DISABLED
      // const recentOTP = await this.getRecentOTP(email, purpose);
      // if (recentOTP) {
      //   const timeLeft = Math.ceil((new Date(recentOTP.expires_at) - new Date()) / 1000 / 60);
      //   throw new Error(`Please wait ${timeLeft} minutes before requesting a new OTP`);
      // }

      // Generate new OTP
      const otpLength = parseInt(process.env.OTP_LENGTH) || 6;
      const otp = this.generateOTP(otpLength);
      
      // Calculate expiry time
      const expiryMinutes = parseInt(process.env.OTP_EXPIRY_MINUTES) || 10;
      const expiresAt = new Date(Date.now() + expiryMinutes * 60 * 1000);

      // Save OTP to database
      const insertQuery = `
        INSERT INTO otps (email, otp_code, purpose, expires_at)
        VALUES (?, ?, ?, ?)
      `;
      
      await executeQuery(insertQuery, [email, otp, purpose, expiresAt]);

      // Send OTP email
      await emailService.sendOTPEmail(email, otp, firstName);

      this.logger.info('OTP generated and sent successfully', {
        email,
        purpose,
        expiresAt
      });

      return {
        success: true,
        message: 'OTP sent successfully',
        expiresAt: expiresAt.toISOString()
      };
    } catch (error) {
      this.logger.error('Failed to generate and send OTP:', {
        email,
        purpose,
        error: error.message
      });
      throw error;
    }
  }

  // Verify OTP
  async verifyOTP(email, otpCode, purpose = 'registration') {
    try {
      // Find valid OTP
      const findQuery = `
        SELECT * FROM otps
        WHERE email = ? AND otp_code = ? AND purpose = ?
        AND expires_at > NOW() AND is_used = FALSE
        ORDER BY created_at DESC
        LIMIT 1
      `;

      const otpRecords = await executeQuery(findQuery, [email, otpCode, purpose]);

      if (otpRecords.length === 0) {
        throw new Error('Invalid or expired OTP');
      }

      const otpRecord = otpRecords[0];

      // Mark OTP as used
      const updateQuery = `
        UPDATE otps 
        SET is_used = TRUE, updated_at = NOW() 
        WHERE id = ?
      `;
      
      await executeQuery(updateQuery, [otpRecord.id]);

      this.logger.info('OTP verified successfully', {
        email,
        purpose,
        otpId: otpRecord.id
      });

      return {
        success: true,
        message: 'OTP verified successfully'
      };
    } catch (error) {
      this.logger.error('OTP verification failed:', {
        email,
        purpose,
        error: error.message
      });
      throw error;
    }
  }

  // Get recent OTP (for rate limiting)
  async getRecentOTP(email, purpose) {
    const query = `
      SELECT * FROM otps 
      WHERE email = ? AND purpose = ? 
      AND expires_at > NOW() AND is_used = FALSE
      ORDER BY created_at DESC 
      LIMIT 1
    `;
    
    const results = await executeQuery(query, [email, purpose]);
    return results.length > 0 ? results[0] : null;
  }



  // Clean up expired OTPs
  async cleanupExpiredOTPs(email = null, purpose = null) {
    let query = 'DELETE FROM otps WHERE expires_at <= NOW()';
    const params = [];

    if (email && purpose) {
      query += ' AND email = ? AND purpose = ?';
      params.push(email, purpose);
    }

    try {
      const result = await executeQuery(query, params);
      this.logger.info('Expired OTPs cleaned up', {
        deletedCount: result.affectedRows,
        email,
        purpose
      });
    } catch (error) {
      this.logger.error('Failed to cleanup expired OTPs:', { error: error.message });
    }
  }

  // Clean up all expired OTPs (can be called periodically)
  async cleanupAllExpiredOTPs() {
    try {
      const result = await executeQuery('DELETE FROM otps WHERE expires_at <= NOW()');
      this.logger.info('All expired OTPs cleaned up', {
        deletedCount: result.affectedRows
      });
      return result.affectedRows;
    } catch (error) {
      this.logger.error('Failed to cleanup all expired OTPs:', { error: error.message });
      throw error;
    }
  }

  // Get OTP statistics
  async getOTPStats() {
    try {
      const statsQuery = `
        SELECT 
          purpose,
          COUNT(*) as total,
          SUM(CASE WHEN is_used = TRUE THEN 1 ELSE 0 END) as used,
          SUM(CASE WHEN expires_at > NOW() AND is_used = FALSE THEN 1 ELSE 0 END) as active,
          SUM(CASE WHEN expires_at <= NOW() AND is_used = FALSE THEN 1 ELSE 0 END) as expired
        FROM otps 
        GROUP BY purpose
      `;
      
      const stats = await executeQuery(statsQuery);
      return stats;
    } catch (error) {
      this.logger.error('Failed to get OTP stats:', { error: error.message });
      throw error;
    }
  }

  // Resend OTP (invalidate old one and send new)
  async resendOTP(email, purpose = 'registration', firstName = '') {
    try {
      // Invalidate any existing OTPs for this email and purpose
      const invalidateQuery = `
        UPDATE otps 
        SET is_used = TRUE, updated_at = NOW()
        WHERE email = ? AND purpose = ? AND is_used = FALSE
      `;
      
      await executeQuery(invalidateQuery, [email, purpose]);

      // Generate and send new OTP
      return await this.generateAndSendOTP(email, purpose, firstName);
    } catch (error) {
      this.logger.error('Failed to resend OTP:', {
        email,
        purpose,
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = new OTPService();
