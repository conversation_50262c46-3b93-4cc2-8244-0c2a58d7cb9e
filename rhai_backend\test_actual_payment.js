const axios = require('axios');

async function testActualPaymentAPI() {
  try {
    console.log('🧪 Testing actual payment initiation API...');
    console.log('📡 URL: http://localhost:7000/api/payments/initiate');
    
    // Test with a real request that exists
    const paymentData = {
      request_id: 99,
      payment_method_id: 3,
      customer_email: '<EMAIL>'
    };
    
    console.log('📋 Payment data:', JSON.stringify(paymentData, null, 2));
    
    const response = await axios.post('http://localhost:7000/api/payments/initiate', paymentData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Payment API Response:');
    console.log('📊 Status:', response.status);
    console.log('💰 Response data:', JSON.stringify(response.data, null, 2));
    
    if (response.data.success && response.data.payment_link) {
      console.log('\n🔗 Payment link created successfully!');
      console.log('🌐 Checkout URL:', response.data.payment_link.checkout_url);
      console.log('🆔 Payment Link ID:', response.data.payment_link.id);
      console.log('💵 Amount:', response.data.amount);
      
      // Check if payment transaction was created
      const { executeQuery } = require('./src/config/database');
      const transactions = await executeQuery(
        'SELECT * FROM payment_transactions WHERE request_id = ? ORDER BY created_at DESC LIMIT 1',
        [paymentData.request_id]
      );
      
      if (transactions.length > 0) {
        const transaction = transactions[0];
        console.log('\n💳 Payment transaction created:');
        console.log('   Transaction ID:', transaction.transaction_id);
        console.log('   Status:', transaction.status);
        console.log('   Amount:', transaction.amount);
        console.log('   External ID:', transaction.external_transaction_id);
        console.log('   Created:', transaction.created_at);
      } else {
        console.log('\n❌ No payment transaction found in database');
      }
      
      return response.data;
    } else {
      console.log('❌ Payment initiation failed');
      return null;
    }
    
  } catch (error) {
    console.error('❌ Payment API test failed:');
    console.error('   Status:', error.response?.status);
    console.error('   Message:', error.response?.data?.message || error.message);
    console.error('   Error:', error.response?.data?.error || 'Unknown error');
    
    if (error.response?.data) {
      console.log('\n📋 Full error response:', JSON.stringify(error.response.data, null, 2));
    }
    
    return null;
  }
}

// Test webhook endpoint
async function testWebhookEndpoint() {
  try {
    console.log('\n🔗 Testing webhook endpoint...');
    
    const response = await axios.get('http://localhost:7000/api/webhooks/paymongo/test');
    
    console.log('✅ Webhook endpoint response:');
    console.log('📊 Status:', response.status);
    console.log('📋 Data:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ Webhook endpoint test failed:', error.message);
  }
}

// Test with a new request
async function testWithNewRequest() {
  try {
    console.log('\n🆕 Testing with a new request...');
    
    // First, let's see what requests are available
    const { executeQuery } = require('./src/config/database');
    const requests = await executeQuery(`
      SELECT id, request_number, status_id, payment_status, base_fee, processing_fee
      FROM document_requests 
      WHERE status_id = 4 AND payment_status != 'paid'
      ORDER BY id DESC 
      LIMIT 3
    `);
    
    console.log('📋 Available unpaid approved requests:');
    if (requests.length === 0) {
      console.log('   ❌ No unpaid approved requests found');
      return;
    }
    
    requests.forEach(req => {
      console.log(`   Request #${req.id} (${req.request_number}): Status ${req.status_id}, Payment: ${req.payment_status}, Fee: ₱${(parseFloat(req.base_fee) + parseFloat(req.processing_fee)).toFixed(2)}`);
    });
    
    // Test with the first available request
    const testRequest = requests[0];
    console.log(`\n🎯 Testing payment for request #${testRequest.id}...`);
    
    const paymentData = {
      request_id: testRequest.id,
      payment_method_id: 3,
      customer_email: '<EMAIL>'
    };
    
    const response = await axios.post('http://localhost:7000/api/payments/initiate', paymentData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ New payment test successful!');
    console.log('🔗 Payment URL:', response.data.payment_link?.checkout_url);
    
    return response.data;
    
  } catch (error) {
    console.error('❌ New payment test failed:', error.response?.data || error.message);
  }
}

async function main() {
  console.log('🚀 Testing Actual Payment API Flow');
  console.log('='.repeat(50));
  
  // Test webhook endpoint first
  await testWebhookEndpoint();
  
  // Test actual payment API
  await testActualPaymentAPI();
  
  // Test with a new request
  await testWithNewRequest();
  
  console.log('\n🎉 Payment API testing completed!');
}

if (require.main === module) {
  main();
}

module.exports = {
  testActualPaymentAPI,
  testWebhookEndpoint,
  testWithNewRequest
};
