const { notifyAdminsPaymentConfirmed } = require('./sync_paymongo_payments');

async function testNotification() {
  try {
    console.log('🧪 Testing admin notification for payment confirmation...');
    
    // Test with request #99 which was just synced
    await notifyAdminsPaymentConfirmed(99, {
      id: 'pay_whvFGSweG3EG6XCPu8TCPeSh',
      amount: 160.60,
      status: 'paid'
    });
    
    console.log('✅ Test notification completed!');
    
  } catch (error) {
    console.error('❌ Test notification failed:', error.message);
  }
}

if (require.main === module) {
  testNotification();
}

module.exports = { testNotification };
