const axios = require('axios');

// Test with authentication
async function testAuthenticatedPayment() {
  try {
    console.log('🔐 Testing authenticated payment flow...');
    
    // Step 1: Login to get JWT token
    console.log('1️⃣ Logging in to get authentication token...');
    
    const loginData = {
      username: 'revo4438',  // Use the actual username
      password: 'password123'  // Use your actual password
    };

    const loginResponse = await axios.post('http://localhost:7000/api/client/auth/login', loginData);
    
    if (!loginResponse.data.success) {
      console.log('❌ Login failed:', loginResponse.data.message);
      return;
    }
    
    const token = loginResponse.data.token;
    const user = loginResponse.data.user;
    
    console.log('✅ Login successful!');
    console.log('👤 User:', user.username, `(ID: ${user.id})`);
    console.log('🔑 Token:', token.substring(0, 20) + '...');
    
    // Step 2: Test payment initiation with authentication
    console.log('\n2️⃣ Testing payment initiation with authentication...');
    
    const paymentData = {
      request_id: 101,  // Use an existing unpaid request
      payment_method_id: 3,
      customer_email: user.email || '<EMAIL>'
    };
    
    console.log('📋 Payment data:', JSON.stringify(paymentData, null, 2));
    
    const paymentResponse = await axios.post('http://localhost:7000/api/payments/initiate', paymentData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ Payment initiation successful!');
    console.log('📊 Status:', paymentResponse.status);
    console.log('💰 Response:', JSON.stringify(paymentResponse.data, null, 2));
    
    if (paymentResponse.data.success && paymentResponse.data.payment_link) {
      console.log('\n🔗 Payment Link Details:');
      console.log('🌐 Checkout URL:', paymentResponse.data.payment_link.checkout_url);
      console.log('🆔 Link ID:', paymentResponse.data.payment_link.id);
      console.log('💵 Amount:', `₱${paymentResponse.data.amount}`);
      
      // Step 3: Check if payment transaction was created in database
      console.log('\n3️⃣ Checking payment transaction in database...');
      
      const { executeQuery } = require('./src/config/database');
      const transactions = await executeQuery(
        'SELECT * FROM payment_transactions WHERE request_id = ? ORDER BY created_at DESC LIMIT 1',
        [paymentData.request_id]
      );
      
      if (transactions.length > 0) {
        const transaction = transactions[0];
        console.log('✅ Payment transaction created:');
        console.log('   🆔 Transaction ID:', transaction.transaction_id);
        console.log('   📊 Status:', transaction.status);
        console.log('   💰 Amount:', `₱${transaction.amount}`);
        console.log('   🔗 External ID:', transaction.external_transaction_id || 'Not set');
        console.log('   📅 Created:', transaction.created_at);
        
        // Step 4: Simulate webhook call (this is what PayMongo would send)
        console.log('\n4️⃣ Simulating PayMongo webhook...');
        await simulatePayMongoWebhook(transaction, paymentResponse.data.payment_link);
        
      } else {
        console.log('❌ No payment transaction found in database');
      }
      
      return paymentResponse.data;
    }
    
  } catch (error) {
    console.error('❌ Authenticated payment test failed:');
    console.error('   Status:', error.response?.status);
    console.error('   Message:', error.response?.data?.message || error.message);
    console.error('   Error:', error.response?.data?.error || 'Unknown error');
    
    if (error.response?.data) {
      console.log('\n📋 Full error response:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Simulate what PayMongo webhook would send when payment is completed
async function simulatePayMongoWebhook(transaction, paymentLink) {
  try {
    console.log('🔗 Simulating PayMongo webhook for payment completion...');
    
    // This is the structure PayMongo sends when a payment is completed
    const webhookPayload = {
      data: {
        id: 'evt_test_' + Date.now(),
        type: 'event',
        attributes: {
          type: 'payment.paid',
          livemode: false,
          data: {
            id: 'pay_test_' + Date.now(),
            type: 'payment',
            attributes: {
              amount: Math.round(parseFloat(transaction.amount) * 100), // Convert to centavos
              currency: 'PHP',
              description: `BOSFDR - Test Payment for Request #${transaction.request_id}`,
              status: 'paid',
              paid_at: Math.floor(Date.now() / 1000),
              fee: Math.round(parseFloat(transaction.amount) * 100 * 0.025), // Simulate 2.5% fee
              net_amount: Math.round(parseFloat(transaction.amount) * 100 * 0.975),
              source: {
                type: 'gcash'
              },
              metadata: {
                request_id: transaction.request_id.toString()
              }
            }
          },
          created_at: Math.floor(Date.now() / 1000),
          updated_at: Math.floor(Date.now() / 1000)
        }
      }
    };
    
    console.log('📨 Webhook payload:', JSON.stringify(webhookPayload, null, 2));
    
    // Send webhook to our endpoint
    const webhookResponse = await axios.post('http://localhost:7000/api/webhooks/paymongo', webhookPayload, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'PayMongo/1.0'
      }
    });
    
    console.log('✅ Webhook processed successfully!');
    console.log('📊 Webhook response:', JSON.stringify(webhookResponse.data, null, 2));
    
    // Step 5: Check if the webhook updated the database
    console.log('\n5️⃣ Checking if webhook updated the database...');
    
    const { executeQuery } = require('./src/config/database');
    
    // Check updated transaction
    const updatedTransaction = await executeQuery(
      'SELECT * FROM payment_transactions WHERE id = ?',
      [transaction.id]
    );
    
    if (updatedTransaction.length > 0) {
      const updated = updatedTransaction[0];
      console.log('💳 Updated transaction:');
      console.log('   📊 Status:', `${transaction.status} → ${updated.status}`);
      console.log('   🔗 External ID:', updated.external_transaction_id || 'Not set');
      console.log('   ✅ Completed at:', updated.completed_at || 'Not set');
    }
    
    // Check updated document request
    const updatedRequest = await executeQuery(
      'SELECT id, status_id, payment_status, paid_at FROM document_requests WHERE id = ?',
      [transaction.request_id]
    );
    
    if (updatedRequest.length > 0) {
      const request = updatedRequest[0];
      console.log('📄 Updated document request:');
      console.log('   📊 Status ID:', request.status_id);
      console.log('   💰 Payment Status:', request.payment_status);
      console.log('   📅 Paid at:', request.paid_at || 'Not set');
    }
    
    // Check for admin notifications
    const notifications = await executeQuery(
      'SELECT * FROM notifications WHERE type = "payment_confirmed" ORDER BY created_at DESC LIMIT 1'
    );
    
    if (notifications.length > 0) {
      const notification = notifications[0];
      console.log('🔔 Admin notification created:');
      console.log('   📧 Title:', notification.title);
      console.log('   📝 Message:', notification.message);
      console.log('   👤 Recipient:', `${notification.recipient_type} #${notification.recipient_id}`);
      console.log('   📅 Created:', notification.created_at);
    }
    
  } catch (error) {
    console.error('❌ Webhook simulation failed:', error.response?.data || error.message);
  }
}

async function main() {
  console.log('🚀 Testing Complete Authenticated Payment Flow');
  console.log('='.repeat(60));
  
  await testAuthenticatedPayment();
  
  console.log('\n🎉 Complete payment flow test completed!');
  console.log('📝 This test simulates the entire flow:');
  console.log('   1. User login');
  console.log('   2. Payment initiation');
  console.log('   3. PayMongo webhook');
  console.log('   4. Database updates');
  console.log('   5. Admin notifications');
}

if (require.main === module) {
  main();
}

module.exports = {
  testAuthenticatedPayment,
  simulatePayMongoWebhook
};
