const axios = require('axios');

// Test webhook with a proper PayMongo-like payload
const testWebhook = async () => {
  try {
    const payload = {
      data: {
        id: "evt_test123",
        type: "event",
        attributes: {
          type: "link.payment.paid",
          livemode: false,
          data: {
            id: "link_test123",
            type: "link",
            attributes: {
              amount: 15725, // ₱157.25
              currency: "PHP",
              description: "BOSFDR - Barangay Clearance Request #97",
              status: "paid",
              metadata: {
                request_id: "97",
                transaction_id: "txn_test123",
                client_id: "12"
              },
              payments: [
                {
                  id: "pay_test123",
                  type: "payment",
                  attributes: {
                    amount: 15725,
                    currency: "PHP",
                    status: "paid"
                  }
                }
              ]
            }
          }
        }
      }
    };

    console.log('🔔 Testing webhook with payload:', JSON.stringify(payload, null, 2));

    const response = await axios.post('http://localhost:7000/api/payments/webhook', payload, {
      headers: {
        'Content-Type': 'application/json',
        'paymongo-signature': 'test-signature' // This will fail verification but that's ok for testing
      }
    });

    console.log('✅ Webhook test successful:', response.data);
  } catch (error) {
    console.log('❌ Webhook test failed:', error.response?.data || error.message);
  }
};

testWebhook();
