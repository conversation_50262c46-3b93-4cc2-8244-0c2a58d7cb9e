const { executeQuery } = require('./src/config/database');

async function updateBarangayClearanceFee() {
  try {
    console.log('🔄 Updating Barangay Clearance fee to ₱150.00...');

    // Update document_types table
    await executeQuery('UPDATE document_types SET base_fee = 150.00 WHERE id = 2');
    console.log('✅ Updated document_types table');

    // Update existing document requests
    await executeQuery('UPDATE document_requests SET base_fee = 150.00 WHERE document_type_id = 2 AND payment_status = "pending"');
    console.log('✅ Updated pending document requests');

    // Verify the changes
    const docTypes = await executeQuery('SELECT id, type_name, base_fee FROM document_types WHERE id = 2');
    console.log('📋 Current Barangay Clearance fee:', docTypes[0]);

    console.log('🎉 Barangay Clearance fee updated successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Failed to update fee:', error);
    process.exit(1);
  }
}

updateBarangayClearanceFee();
