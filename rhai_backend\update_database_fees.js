const { executeQuery } = require('./src/config/database');

async function updateFees() {
  try {
    console.log('Updating document fees...');

    // REVERT: Restore original fees
    await executeQuery('UPDATE document_types SET base_fee = 30.00 WHERE id = 1');
    console.log('✅ Reverted Cedula fee to ₱30.00 in document_types');

    // Update Barangay Clearance fee in document_types
    await executeQuery('UPDATE document_types SET base_fee = 50.00 WHERE id = 2');
    console.log('✅ Reverted Barangay Clearance fee to ₱50.00 in document_types');

    // Update existing document requests to use original fees
    await executeQuery('UPDATE document_requests SET base_fee = 30.00 WHERE document_type_id = 1');
    console.log('✅ Reverted existing Cedula requests to ₱30.00');

    await executeQuery('UPDATE document_requests SET base_fee = 50.00 WHERE document_type_id = 2');
    console.log('✅ Reverted existing Barangay Clearance requests to ₱50.00');

    // Verify updates
    const docTypes = await executeQuery('SELECT id, type_name, description, base_fee FROM document_types');
    console.log('\n📋 Document type fees:');
    docTypes.forEach(doc => {
      console.log(`- ${doc.type_name}: ₱${doc.base_fee}`);
    });

    const requests = await executeQuery('SELECT id, document_type_id, base_fee FROM document_requests LIMIT 5');
    console.log('\n📋 Sample document request fees:');
    requests.forEach(req => {
      console.log(`- Request #${req.id} (Type ${req.document_type_id}): ₱${req.base_fee}`);
    });

    console.log('\n🎉 Database fees updated successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error updating fees:', error);
    process.exit(1);
  }
}

updateFees();
